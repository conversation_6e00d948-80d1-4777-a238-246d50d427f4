"""
A background task in the Workspace which transfers percepts from the
Perceptual buffer to the Current Situational Model.

This module provides the implementation of the UpdateCsmBackgroundTask class.
"""

import logging
from typing import Dict, Any, Optional

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import <PERSON><PERSON><PERSON><PERSON>ame
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.CoalitionImpl import CoalitionImpl
from linars.edu.memphis.ccrg.lida.Workspace.Workspace import Workspace


class UpdateCsmBackgroundTask(FrameworkTaskImpl):
    """
    A background task in the Workspace which transfers percepts from the
    Perceptual buffer to the Current Situational Model.
    """

    def __init__(self):
        """
        Initialize the UpdateCsmBackgroundTask.
        """
        super().__init__()
        self.logger = logging.getLogger("UpdateCsmBackgroundTask")

        # Workspace buffers
        self.perceptual_buffer = None
        self.csm = None
        self.vision = None
        self.listen = None
        self.text = None
        self.vv = None
        self.vlisten = None

        # Associated workspace
        self.workspace = None

    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module.

        Args:
            module: The module to associate
            module_usage: The usage of the module
        """
        if isinstance(module, Workspace):
            self.workspace = module

            # Get workspace buffers
            self.perceptual_buffer = module.get_submodule(ModuleName.PerceptualBuffer)
            # 使用CurrentSM而不是CurrentSituationalModel
            try:
                self.csm = module.get_submodule(ModuleName.CurrentSM)
                if self.csm is None:
                    self.csm = module.get_submodule(ModuleName.CurrentSituationalModel)
                    if self.csm is not None:
                        self.logger.info("Found CurrentSituationalModel as fallback for CurrentSM")
            except Exception as e:
                self.logger.warning(f"Error getting CurrentSM: {e}")
                self.csm = None

            # Get additional buffers
            self.vision = module.get_submodule(ModuleName.VisionGraph)
            self.listen = module.get_submodule(ModuleName.ListenGraph)
            self.text = module.get_submodule(ModuleName.TextGraph)
            self.vv = module.get_submodule(ModuleName.VVGraph)
            self.vlisten = module.get_submodule(ModuleName.VListenGraph)

            self.logger.info(f"Associated with Workspace module: {module}")
        else:
            self.logger.warning(f"Cannot associate with module: {module}")

    def run_this_framework_task(self) -> None:
        """
        Retrieves nodes from PAM and provides them to attentional codelets.
        This function gets PAM's nodes and provides them to CurrentSituationalModel,
        which will be accessed by attentional codelets.
        """
        current_tick = TaskManager.get_current_tick()
        self.logger.debug(f"Updating CSM with perceptual buffer content at tick {current_tick}")

        # Get GlobalWorkspace
        global_workspace = None
        try:
            # Try to get GlobalWorkspace from AgentStarter
            if hasattr(AgentStarter, 'agent') and AgentStarter.agent is not None:
                agent = AgentStarter.agent
                global_workspace = agent.get_submodule(ModuleName.GlobalWorkspace)
        except Exception as e:
            self.logger.warning(f"Error getting GlobalWorkspace: {e}")

        # Get CSM content
        csm_content = None
        if self.csm is not None:
            try:
                csm_content = self.csm.get_buffer_content(None)
            except Exception as e:
                self.logger.warning(f"Error getting CSM content: {e}")

        # Get content from other buffers
        vision_ns = None
        listen_ns = None
        text_ns = None
        vv_ns = None
        vlisten_ns = None

        try:
            if self.vision is not None:
                vision_ns = self.vision.get_buffer_content(None)
            if self.listen is not None:
                listen_ns = self.listen.get_buffer_content(None)
            if self.text is not None:
                text_ns = self.text.get_buffer_content(None)
            if self.vv is not None:
                vv_ns = self.vv.get_buffer_content(None)
            if self.vlisten is not None:
                vlisten_ns = self.vlisten.get_buffer_content(None)
        except Exception as e:
            self.logger.warning(f"Error getting buffer content: {e}")

        # Process CSM content
        if csm_content is None:
            self.logger.warning(f"Null WorkspaceContent returned at tick {current_tick}. Coalition cannot be formed.")
        elif csm_content.get_linkable_count() > 0:
            # Create coalition from CSM content
            try:
                if global_workspace is not None:
                    coalition = CoalitionImpl(csm_content)
                    global_workspace.add_coalition(coalition)
                    self.logger.info(f"Added new coalition with activation {coalition.get_activation()} at tick {current_tick}")
                    self.set_next_ticks_per_run(8)
                else:
                    self.logger.warning("GlobalWorkspace is null, cannot add coalition")
            except Exception as e:
                self.logger.warning(f"Error creating or adding coalition: {e}")

        # Optional: Process modality-specific content
        # self.pay_attention(vision_ns, listen_ns, text_ns, vv_ns, vlisten_ns)

    def pay_attention(self, vision_ns, listen_ns, text_ns, vv_ns, vlisten_ns):
        """
        Process modality-specific content and determine which modality to attend to.

        Args:
            vision_ns: Vision node structure
            listen_ns: Listen node structure
            text_ns: Text node structure
            vv_ns: VV node structure
            vlisten_ns: VListen node structure
        """
        # Skip implementation for now as it's complex and depends on many other components
        self.logger.info("pay_attention method called but not fully implemented")

        # Calculate activation for each modality
        v_act = 0.0
        l_act = 0.0
        vv_act = 0.0
        t_act = 0.0
        vl_act = 0.0

        # Process each modality if available
        if vision_ns is not None and hasattr(vision_ns, 'get_nodes'):
            for node in vision_ns.get_nodes():
                v_act += node.get_activation()

        if listen_ns is not None and hasattr(listen_ns, 'get_nodes'):
            for node in listen_ns.get_nodes():
                l_act += node.get_activation()

        if text_ns is not None and hasattr(text_ns, 'get_nodes'):
            for node in text_ns.get_nodes():
                t_act += node.get_activation()

        if vv_ns is not None and hasattr(vv_ns, 'get_nodes'):
            for node in vv_ns.get_nodes():
                vv_act += node.get_activation()

        if vlisten_ns is not None and hasattr(vlisten_ns, 'get_nodes'):
            for node in vlisten_ns.get_nodes():
                vl_act += node.get_activation()

        # Create modality maps
        mt_ns_map = {}
        if vision_ns is not None:
            mt_ns_map["vact"] = vision_ns
        if listen_ns is not None:
            mt_ns_map["lact"] = listen_ns
        if vv_ns is not None:
            mt_ns_map["vvact"] = vv_ns
        if text_ns is not None:
            mt_ns_map["tact"] = text_ns
        if vlisten_ns is not None:
            mt_ns_map["vlact"] = vlisten_ns

        mt_map = {
            "vact": v_act,
            "lact": l_act,
            "vvact": vv_act,
            "tact": t_act,
            "vlact": vl_act
        }

        # Find modality with maximum activation
        max_activation = -1.0
        final_modality = ""

        for modality, act in mt_map.items():
            if act > max_activation:
                max_activation = act
                final_modality = modality

        # Set current modality in AgentStarter
        if hasattr(AgentStarter, 'current_modality'):
            AgentStarter.current_modality = final_modality

        if hasattr(AgentStarter, 'current_atten_num'):
            AgentStarter.current_atten_num += 1

        # Control thread stage
        if hasattr(AgentStarter, 'is_attent_now'):
            AgentStarter.is_attent_now = True

        # Process tasks based on modality
        # This part is complex and depends on task management system
        # Simplified implementation

        # End control thread stage
        if hasattr(AgentStarter, 'is_attent_now'):
            AgentStarter.is_attent_now = False
