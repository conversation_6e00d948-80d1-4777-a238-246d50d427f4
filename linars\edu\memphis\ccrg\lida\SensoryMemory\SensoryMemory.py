# LIDA Cognitive Framework
"""
This is the interface to be implemented by sensory memory modules.
Implementing modules sense the environment, store the sensed data, and
process it.
"""

from abc import abstractmethod
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemoryListener import SensoryMemoryListener

class SensoryMemory(FrameworkModule):
    """
    This is the interface to be implemented by sensory memory modules.
    Implementing modules sense the environment, store the sensed data, and
    process it.
    """
    
    @abstractmethod
    def add_sensory_memory_listener(self, listener: SensoryMemoryListener) -> None:
        """
        Adds a listener to this memory. This listener constantly checks for
        information being sent from this memory to other modules (Perceptual
        Associative Memory and Sensory Motor Memory).
        
        Args:
            listener: The listener added to this memory
        """
        pass
    
    @abstractmethod
    def run_sensors(self) -> None:
        """
        Runs all the sensors associated with this memory. The sensors get the
        information from the environment and store in this memory for later
        processing and passing to the perceptual memory module.
        """
        pass
    
    @abstractmethod
    def get_sensory_content(self, modality: str, params: Optional[Dict[str, Any]]) -> Any:
        """
        Returns content from this SensoryMemory.
        Intended to be used by feature detectors to get specific parts of the sensory memory.
        
        Args:
            modality: User may optionally use this parameter to specify modality
            params: Optional parameters
            
        Returns:
            Content from this SensoryMemory
        """
        pass
