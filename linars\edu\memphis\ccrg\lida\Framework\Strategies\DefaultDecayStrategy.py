# LIDA认知框架
"""
默认衰减策略实现
"""

from typing import Dict, Any
from linars.edu.memphis.ccrg.lida.Framework.Strategies.DecayStrategy import DecayStrategy

class DefaultDecayStrategy(DecayStrategy):
    """
    默认衰减策略实现
    """

    def __init__(self):
        """
        初始化默认衰减策略
        """
        self.decay_rate = 0.1

    def decay(self, current_activation: float, base_level_activation: float, ticks: int) -> float:
        """
        使用给定参数衰减对象

        参数:
            current_activation: 对象的当前激活值
            base_level_activation: 对象的基础激活水平
            ticks: 衰减的ticks数

        返回:
            新的激活值
        """
        result = current_activation
        for _ in range(ticks):
            result = result - (result - base_level_activation) * self.decay_rate
        return result

    def init(self, params: Dict[str, Any]) -> None:
        """
        使用给定参数初始化此策略

        参数:
            params: 初始化参数
        """
        if "decayRate" in params:
            self.decay_rate = float(params["decayRate"])
