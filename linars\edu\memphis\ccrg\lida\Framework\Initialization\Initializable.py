# LIDA Cognitive Framework
"""
Interface for objects that can be initialized.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any

class Initializable(ABC):
    """
    Interface for objects that can be initialized.
    """
    
    @abstractmethod
    def init(self, params: Dict[str, Any]) -> None:
        """
        Initialize this object with the given parameters.
        
        Args:
            params: Parameters for initialization
        """
        pass
