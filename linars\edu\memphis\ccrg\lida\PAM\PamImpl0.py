"""
PamImpl0 - Implementation of PAM with additional functionality
Extends PAMImpl with specific methods for propagating activation to parents
"""

import logging
import threading
from typing import Dict, List, Set, Optional, Any, Tuple, Union

from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import <PERSON><PERSON><PERSON><PERSON><PERSON>
from linars.edu.memphis.ccrg.lida.PAM.PAMemoryImpl import PAMemoryImpl

# 检查是否可以导入NARS相关类
try:
    from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
    from linars.edu.memphis.ccrg.linars.term import Term
    from linars.org.opennars.language.statement import Statement
    from linars.org.opennars.language.variable import Variable
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False

# 定义Tense枚举
class Tense:
    """时态枚举"""
    Past = "PAST"
    Present = "PRESENT"
    Future = "FUTURE"

class PamImpl0(PAMemoryImpl):
    """
    Extended implementation of PAM with additional functionality
    """

    def __init__(self):
        """Initialize PamImpl0"""
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

        # 初始化属性
        self.nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None
        self.scene_list = []
        self.scene_map = {}
        self.for_map = {}
        self.ifelse_map = {}

        # 线程锁
        self.lock = threading.RLock()

        # 传播参数
        self.propagate_params = {}
        self.upscale_factor = self.DEFAULT_UPSCALE_FACTOR

        # 增强的激活扩散参数
        self.activation_decay_rate = 0.8  # 激活衰减率
        self.modal_weights = {  # 模态权重
            "listen": 1.0,
            "see": 0.9,
            "touch": 0.8,
            "smell": 0.7,
            "taste": 0.7,
            "feel": 1.2,
            "get": 0.6,
            "pam": 0.5
        }
        self.link_type_weights = {  # 链接类型权重
            "相似": 1.0,
            "对等": 0.9,
            "顺承": 0.8,
            "语序": 0.7,
            "顺接": 0.7,
            "否定": 0.6,
            "feel": 1.1
        }

        # 如果NARS可用，初始化相关属性
        if NARS_AVAILABLE and self.nar:
            if hasattr(self.nar, 'narsese'):
                self.narsese = self.nar.narsese

    def propagate_activation_to_parents(self, pn, deep=0, from0="pam"):
        """
        将激活从一个节点传播到其父节点 - 增强版本
        基于Java版本的复杂模态处理逻辑进行优化

        Args:
            pn: 要传播激活的节点
            deep: 当前传播深度
            from0: 激活来源
        """
        try:
            # 获取节点名称
            pname = pn.get_node_name() if hasattr(pn, "get_node_name") else ""
            if hasattr(pn, "getTNname"):
                pname = pn.getTNname()

            sname = ""
            sink = None
            source = None
            deep += 1

            # 动态深度控制 - 根据节点类型和来源调整
            deep_threshold = self.calculate_dynamic_threshold(pn, from0)
            self.put_map(pn, pname)
            tense = None

            # 扩散两大关键：模态区分+点边类型 (参考Java版本注释)
            if from0 in ["listen", "see", "touch", "smell", "taste", "feel", "get"]:
                tense = Tense.Present
                if self.nar and hasattr(self.nar, "add_input"):
                    self.nar.add_input(f"{pname}. :|:")
                    if hasattr(self.nar, "cycles"):
                        self.nar.cycles(1)

                # 感知输入允许更深的激活
                if from0 in ["listen", "see"]:
                    deep_threshold += 2

            # 检查深度阈值
            if deep > deep_threshold:
                return

            # 获取父链接
            parent_link_set = self.pam_node_structure.getConnectedSinks(pn)
            if parent_link_set is None:
                return

            # 处理每个父链接
            for parent in parent_link_set:
                try:
                    sink = parent.getSink()
                    source = parent.getSource()

                    # 获取节点名称
                    if hasattr(sink, "get_node_name"):
                        sname = sink.get_node_name()
                    elif hasattr(sink, "getTNname"):
                        sname = sink.getTNname()
                    else:
                        sname = ""

                    # 避免循环激活
                    if hasattr(pn, "get_fromnodeid") and hasattr(sink, "get_node_id") and pn.get_fromnodeid() == sink.get_node_id():
                        continue

                    self.put_map(sink, sname)

                    # 获取链接类型
                    link_type = None
                    if hasattr(parent, "getCategory") and parent.getCategory() is not None:
                        category = parent.getCategory()
                        if hasattr(category, "get_node_name"):
                            link_type = category.get_node_name()
                        elif hasattr(category, "getName"):
                            link_type = category.getName()
                        elif isinstance(category, dict) and "label" in category:
                            link_type = category["label"]

                    # 使用增强的传播量计算
                    amount_to_propagate = self.calculate_propagation_amount(pn, parent, sink, from0)

                    # 保持向后兼容性
                    self.propagate_params["upscale"] = self.upscale_factor
                    if hasattr(pn, "getTotalActivation"):
                        self.propagate_params["totalActivation"] = pn.getTotalActivation()
                    elif hasattr(pn, "getActivation"):
                        self.propagate_params["totalActivation"] = pn.getActivation()
                    else:
                        self.propagate_params["totalActivation"] = 0.0

                    # 如果有传统策略，也使用它
                    if hasattr(self, "propagation_strategy") and self.propagation_strategy is not None:
                        traditional_amount = self.propagation_strategy.get_activation_to_propagate(self.propagate_params)
                        # 取两种方法的平均值
                        amount_to_propagate = (amount_to_propagate + traditional_amount) / 2.0

                    # 设置源节点ID以避免循环激活
                    if hasattr(parent, "getSource") and parent.getSource() is not None:
                        source_node = parent.getSource()
                        if hasattr(source_node, "set_fromnodeid") and hasattr(pn, "get_node_id"):
                            source_node.set_fromnodeid(pn.get_node_id())
                        elif hasattr(source_node, "setFromnodeid") and hasattr(pn, "getNodeId"):
                            source_node.setFromnodeid(pn.getNodeId())

                    # 通知监听器用于CSM
                    if hasattr(self, "pam_listeners") and self.pam_listeners:
                        for listener in self.pam_listeners:
                            listener.receive_percept(pn, ModuleName.CurrentSM)
                            listener.receive_percept(sink, ModuleName.CurrentSM)
                            listener.receive_percept(parent, ModuleName.CurrentSM)

                    # 如果NAR可用，添加到NARS内存
                    if self.nar and hasattr(self.nar, "memory"):
                        if hasattr(self.nar.memory, "add_default_node") and hasattr(self.nar.memory, "add_default_link"):
                            self.nar.memory.add_default_node(pn)
                            self.nar.memory.add_default_node(sink)
                            self.nar.memory.add_default_link(parent)
                        elif hasattr(self.nar.memory, "addDefaultNode") and hasattr(self.nar.memory, "addDefaultLink"):
                            self.nar.memory.addDefaultNode(pn)
                            self.nar.memory.addDefaultNode(sink)
                            self.nar.memory.addDefaultLink(parent)

                    # 处理特殊链接类型
                    is_match = False

                    # 如果链接类型为空，尝试再次获取
                    if link_type is None and hasattr(parent, "getCategory") and parent.getCategory() is not None:
                        category = parent.getCategory()
                        if hasattr(category, "get_node_name"):
                            link_type = category.get_node_name()
                        elif hasattr(category, "getName"):
                            link_type = category.getName()

                        # 如果仍然为空，记录警告
                        if link_type is None:
                            self.logger.warning(f"Link type is None for link between {pname} and {sname}")

                    # 处理不同类型的链接
                    if link_type in ["相似", "对等", "顺承"]:
                        is_match = True
                        # 添加到目标图
                        if hasattr(self, "pam_listeners") and self.pam_listeners:
                            for listener in self.pam_listeners:
                                listener.receive_percept(pn, ModuleName.GoalGraph)
                                listener.receive_percept(sink, ModuleName.GoalGraph)
                                listener.receive_percept(parent, ModuleName.GoalGraph)
                    elif link_type in ["语序", "顺接"]:
                        is_match = True
                        # 添加到语法图
                        if hasattr(self, "pam_listeners") and self.pam_listeners:
                            for listener in self.pam_listeners:
                                listener.receive_percept(pn, ModuleName.GrammarGraph)
                                listener.receive_percept(sink, ModuleName.GrammarGraph)
                                listener.receive_percept(parent, ModuleName.GrammarGraph)
                    elif link_type == "否定":
                        is_match = True
                    elif link_type is not None and link_type.startswith("arg"):
                        is_match = True
                        # 处理语义分析
                        # 在实际实现中，您将创建并添加SemanticAnalyzTask
                    elif link_type == "feel":
                        is_match = True
                        self.feel_and_do(pn, parent, sname)

                    # 处理不规则类型
                    if not is_match and "_" in sname:
                        strs = sname.split("_")
                        last = strs[-1]
                        last0 = strs[-2] if len(strs) > 1 else ""

                        # 检查是否是语法结构
                        if last in ["NP", "IP", "VP", "PP"] or last0 in ["NP", "IP", "VP", "PP"]:
                            is_match = True
                            if hasattr(self, "pam_listeners") and self.pam_listeners:
                                for listener in self.pam_listeners:
                                    listener.receive_percept(pn, ModuleName.GrammarGraph)
                                    listener.receive_percept(sink, ModuleName.GrammarGraph)
                                    listener.receive_percept(parent, ModuleName.GrammarGraph)

                    # 设置源信息
                    if hasattr(sink, "set_fromsceneid") and hasattr(pn, "get_fromnodeid"):
                        sink.set_fromsceneid(pn.get_fromnodeid())
                    elif hasattr(sink, "setFromsceneid") and hasattr(pn, "getFromnodeid"):
                        sink.setFromsceneid(pn.getFromnodeid())

                    if hasattr(sink, "set_fromnodeid") and hasattr(pn, "get_node_id"):
                        sink.set_fromnodeid(pn.get_node_id())
                    elif hasattr(sink, "setFromnodeid") and hasattr(pn, "getNodeId"):
                        sink.setFromnodeid(pn.getNodeId())

                    # 处理非空链接类型
                    if link_type is not None:
                        if hasattr(sink, 'set_from_link_type'):
                            sink.set_from_link_type(link_type)
                        elif hasattr(sink, 'setFromLinkType'):
                            sink.setFromLinkType(link_type)

                    # 检查术语是否在内存中
                    all_in_mem = True
                    term = None

                    if NARS_AVAILABLE and self.nar:
                        try:
                            # 使用Narsese解析术语
                            if hasattr(AgentStarter, "narsese") and hasattr(AgentStarter.narsese, "parse_term"):
                                term = AgentStarter.narsese.parse_term(sname)
                            elif hasattr(self, "narsese") and hasattr(self.narsese, "parse_term"):
                                term = AgentStarter.narsese.parse_term(sname)

                            # 检查复合术语组件是否在内存中
                            if term is not None:
                                if isinstance(term, CompoundTerm):
                                    # 使用静态方法isAllInMemVar检查复合术语
                                    # all_in_mem = PamImpl0.isAllInMemVar(term)
                                    all_in_mem = self.is_all_in_mem(term, all_in_mem, [])
                                    # 打印调试信息
                                    self.logger.debug(f"isAllInMemVar check for {term}: {all_in_mem}")
                                elif hasattr(self.nar.memory, "get_node") and self.nar.memory.get_node(str(term)) is None:
                                    all_in_mem = False
                                elif hasattr(self.nar.memory, "getNodeByName") and self.nar.memory.getNodeByName(str(term)) is None:
                                    all_in_mem = False
                        except Exception as e:
                            self.logger.error(f"Error parsing term: {e}")

                    # 如果所有组件都在内存中，则传播激活
                    if all_in_mem:
                        if deep >= deep_threshold:
                            self.logger.debug(f"Deep threshold reached: {deep} >= {deep_threshold}")
                            return
                        # 使用增强的传播方法
                        self.enhanced_propagate_activation(sink, parent, amount_to_propagate, deep, from0)
                    elif deep != deep_threshold:
                        # 再传播一级
                        self.enhanced_propagate_activation(sink, parent, amount_to_propagate, deep_threshold - 2, from0)
                except Exception as e:
                    # 静默处理，避免大量错误输出
                    # self.logger.error(f"Error processing parent link: {e}")
                    pass
        except Exception as e:
            self.logger.error(f"Error in propagate_activation_to_parents: {e}")

    def calculate_dynamic_threshold(self, node, from_source):
        """
        动态计算激活传播深度阈值
        基于节点类型、来源和当前系统状态

        Args:
            node: 当前节点
            from_source: 激活来源

        Returns:
            int: 动态深度阈值
        """
        base_threshold = 6

        # 根据来源调整
        if from_source in ["listen", "see"]:
            base_threshold += 2
        elif from_source in ["feel", "touch"]:
            base_threshold += 1
        elif from_source == "pam":
            base_threshold -= 1

        # 根据节点类型调整
        node_name = ""
        if hasattr(node, "get_node_name"):
            node_name = node.get_node_name()
        elif hasattr(node, "getTNname"):
            node_name = node.getTNname()

        # 语法节点允许更深传播
        if "_NP" in node_name or "_VP" in node_name or "_IP" in node_name:
            base_threshold += 1

        # 情感节点允许更深传播
        if "happy" in node_name or "开心" in node_name:
            base_threshold += 2

        return max(3, min(base_threshold, 10))  # 限制在3-10之间

    def calculate_propagation_amount(self, source_node, link, target_node, from_source):
        """
        计算差异化传播量
        基于Java版本的复杂传播策略

        Args:
            source_node: 源节点
            link: 连接链路
            target_node: 目标节点
            from_source: 激活来源

        Returns:
            float: 传播激活量
        """
        try:
            # 获取源节点激活量
            source_activation = 0.0
            if hasattr(source_node, "getTotalActivation"):
                source_activation = source_node.getTotalActivation()
            elif hasattr(source_node, "getActivation"):
                source_activation = source_node.getActivation()

            if source_activation <= 0:
                return 0.0

            # 基础传播量
            base_amount = source_activation * self.upscale_factor

            # 模态权重调整
            modal_weight = self.modal_weights.get(from_source, 0.5)
            base_amount *= modal_weight

            # 链接类型权重调整
            link_type = None
            if hasattr(link, "getCategory") and link.getCategory() is not None:
                category = link.getCategory()
                if hasattr(category, "get_node_name"):
                    link_type = category.get_node_name()
                elif hasattr(category, "getName"):
                    link_type = category.getName()
                elif isinstance(category, dict) and "label" in category:
                    link_type = category["label"]

            if link_type:
                link_weight = self.link_type_weights.get(link_type, 0.5)
                base_amount *= link_weight

            # 目标节点状态调整
            if hasattr(target_node, "getActivation"):
                target_activation = target_node.getActivation()
                # 如果目标节点已经高度激活，减少传播量
                if target_activation > 0.8:
                    base_amount *= 0.5

            return max(0.01, base_amount)  # 确保最小传播量

        except Exception as e:
            self.logger.error(f"Error calculating propagation amount: {e}")
            return 0.01

    def integrate_with_search_mechanism(self, activated_nodes, search_context=None):
        """
        与搜索机制集成，激活扩散触发搜索
        基于Java版本的搜索集成逻辑

        Args:
            activated_nodes: 被激活的节点列表
            search_context: 搜索上下文
        """
        try:
            if not activated_nodes:
                return

            # 收集激活模式
            activation_patterns = self.collect_activation_patterns(activated_nodes)

            # 触发相关搜索
            for pattern in activation_patterns:
                self.trigger_pattern_search(pattern, search_context)

        except Exception as e:
            self.logger.error(f"Error in search integration: {e}")

    def collect_activation_patterns(self, nodes):
        """
        收集激活模式用于搜索

        Args:
            nodes: 激活的节点列表

        Returns:
            list: 激活模式列表
        """
        patterns = []
        try:
            # 按链接类型分组
            type_groups = {}
            for node in nodes:
                node_name = ""
                if hasattr(node, "get_node_name"):
                    node_name = node.get_node_name()
                elif hasattr(node, "getTNname"):
                    node_name = node.getTNname()

                # 根据节点名称推断类型
                if "_NP" in node_name or "_VP" in node_name:
                    node_type = "grammar"
                elif "happy" in node_name or "开心" in node_name:
                    node_type = "emotion"
                else:
                    node_type = "concept"

                if node_type not in type_groups:
                    type_groups[node_type] = []
                type_groups[node_type].append(node)

            # 为每个类型组创建搜索模式
            for node_type, group_nodes in type_groups.items():
                if len(group_nodes) >= 2:  # 至少两个节点才形成模式
                    patterns.append({
                        "type": node_type,
                        "nodes": group_nodes,
                        "strength": sum(self.get_node_activation(n) for n in group_nodes)
                    })

        except Exception as e:
            self.logger.error(f"Error collecting activation patterns: {e}")

        return patterns

    def trigger_pattern_search(self, pattern, search_context):
        """
        基于激活模式触发搜索

        Args:
            pattern: 激活模式
            search_context: 搜索上下文
        """
        try:
            # 根据模式类型选择搜索策略
            if pattern["type"] == "grammar":
                self.trigger_grammar_search(pattern, search_context)
            elif pattern["type"] == "emotion":
                self.trigger_emotion_search(pattern, search_context)
            else:
                self.trigger_concept_search(pattern, search_context)

        except Exception as e:
            self.logger.error(f"Error triggering pattern search: {e}")

    def get_node_activation(self, node):
        """
        获取节点激活值

        Args:
            node: 节点

        Returns:
            float: 激活值
        """
        try:
            if hasattr(node, "getTotalActivation"):
                return node.getTotalActivation()
            elif hasattr(node, "getActivation"):
                return node.getActivation()
            else:
                return 0.0
        except:
            return 0.0

    def trigger_grammar_search(self, pattern, search_context):
        """
        触发语法相关搜索

        Args:
            pattern: 语法激活模式
            search_context: 搜索上下文
        """
        try:
            # 创建语法搜索任务
            if hasattr(self, "task_spawner") and self.task_spawner:
                try:
                    from linars.edu.memphis.ccrg.lida.PAM.Tasks.SearchSB import SearchSB
                    search_task = SearchSB(pattern["nodes"], "grammar", self)
                    self.task_spawner.add_task(search_task)
                except ImportError:
                    self.logger.warning("SearchSB not available for grammar search")

        except Exception as e:
            self.logger.error(f"Error in grammar search trigger: {e}")

    def trigger_emotion_search(self, pattern, search_context):
        """
        触发情感相关搜索

        Args:
            pattern: 情感激活模式
            search_context: 搜索上下文
        """
        try:
            # 情感搜索通常涉及目标和行动
            if hasattr(self, "task_spawner") and self.task_spawner:
                try:
                    from linars.edu.memphis.ccrg.lida.PAM.Tasks.SearchSB import SearchSB
                    search_task = SearchSB(pattern["nodes"], "emotion", self)
                    self.task_spawner.add_task(search_task)
                except ImportError:
                    self.logger.warning("SearchSB not available for emotion search")

        except Exception as e:
            self.logger.error(f"Error in emotion search trigger: {e}")

    def trigger_concept_search(self, pattern, search_context):
        """
        触发概念相关搜索

        Args:
            pattern: 概念激活模式
            search_context: 搜索上下文
        """
        try:
            # 概念搜索涉及语义关联
            if hasattr(self, "task_spawner") and self.task_spawner:
                try:
                    from linars.edu.memphis.ccrg.lida.PAM.Tasks.SearchSB import SearchSB
                    search_task = SearchSB(pattern["nodes"], "concept", self)
                    self.task_spawner.add_task(search_task)
                except ImportError:
                    self.logger.warning("SearchSB not available for concept search")

        except Exception as e:
            self.logger.error(f"Error in concept search trigger: {e}")

    def enhanced_propagate_activation(self, node, link, amount, depth, source):
        """
        增强的激活传播方法
        整合所有优化功能

        Args:
            node: 目标节点
            link: 连接链路
            amount: 传播量
            depth: 当前深度
            source: 激活源
        """
        try:
            # 调用原始传播方法
            if hasattr(self, "propagate_activation"):
                self.propagate_activation(node, link, amount, depth, source)

            # 收集激活节点用于搜索集成
            activated_nodes = [node]
            if hasattr(link, "getSource"):
                source_node = link.getSource()
                if source_node:
                    activated_nodes.append(source_node)

            # 触发搜索集成
            self.integrate_with_search_mechanism(activated_nodes, {
                "depth": depth,
                "source": source,
                "link_type": self.get_link_type(link)
            })

        except Exception as e:
            self.logger.error(f"Error in enhanced propagation: {e}")

    def get_link_type(self, link):
        """
        获取链接类型

        Args:
            link: 链接对象

        Returns:
            str: 链接类型
        """
        try:
            if hasattr(link, "getCategory") and link.getCategory() is not None:
                category = link.getCategory()
                if hasattr(category, "get_node_name"):
                    return category.get_node_name()
                elif hasattr(category, "getName"):
                    return category.getName()
                elif isinstance(category, dict) and "label" in category:
                    return category["label"]
            return "unknown"
        except:
            return "unknown"

    def feel_and_do(self, pn, parent, sname):
        """
        Handle feel links and perform actions

        Args:
            pn: The source node
            parent: The link
            sname: The sink node name
        """
        # Check for happy nodes
        if "happy" in sname:
            # Get similar links
            link_set = self.pam_node_structure.getNeoLinks(pn)
            similar_links = [link for link in link_set if link.getCategory()["label"] == "相似"]

            for link in similar_links:
                node = link.getSink()
                if NARS_AVAILABLE and hasattr(node, "toTerm"):
                    term = node.toTerm()
                    if hasattr(term, "is_operation") and term.is_operation():
                        # Handle operation
                        if hasattr(self.nar, "memory") and hasattr(self.goal_ns, "execute_operator"):
                            terms = [parent, term.get_arguments()]
                            feedback = self.goal_ns.execute_operator(term.get_operator(), terms, self.nar)
                            if feedback:
                                result_term = feedback[0].get_term()
                                self.logger.info(f"Executed operation: {result_term}")

    @staticmethod
    def isAllInMem(ct, all_in_mem, term_name_list):
        """
        静态方法，检查复合术语的所有组件是否在内存中

        Args:
            ct: 复合术语
            all_in_mem: 当前状态
            term_name_list: 用于存储术语名称的列表

        Returns:
            bool: 如果所有组件都在内存中则返回True
        """
        if not NARS_AVAILABLE:
            return all_in_mem

        try:
            # 获取NARS内存实例
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None
            if not nar or not hasattr(nar, 'memory'):
                return all_in_mem

            # 使用集合存储术语名称，避免重复
            term_names = set()
            # 从复合术语中获取所有术语名称
            if hasattr(ct, "get_term_names"):
                term_names = ct.get_term_names(term_names)
            elif hasattr(ct, "getTermNames"):
                term_names = ct.getTermNames(term_names)
            elif hasattr(ct, "term"):
                # 手动提取术语名称
                for sub_term in ct.term:
                    if hasattr(sub_term, "name"):
                        if hasattr(sub_term, "TNname"):
                            term_names.add(sub_term.TNname)
                        else:
                            term_names.add(str(sub_term.name()))
                    elif hasattr(sub_term, "__str__"):
                        term_names.add(str(sub_term))

            # 检查每个术语是否在内存中
            try:
                for term_name in term_names:
                    # 跳过变量和特殊术语
                    if not term_name.startswith("$") and not term_name.startswith("#") and term_name != "SELF" and term_name != "happy":
                        # 检查术语是否在内存中，并跳过特殊术语
                        memory_has_node = False

                        # if hasattr(nar.memory, "get_node"):
                        #     memory_has_node = nar.memory.get_node(term_name) is not None
                        # elif hasattr(nar.memory, "getNodeByName"):
                        if hasattr(nar.memory, "getNodeByName"):
                            # 这里难取到id，用name
                            memory_has_node = nar.memory.getNodeByName(term_name) is not None

                        special_terms = ["听到", "听到1", "回应", "不开心", "不说"]
                        is_special = term_name in special_terms or "^" in term_name

                        if not memory_has_node and not is_special:
                            all_in_mem = False
                            break
                        else:
                            # 确保term_name_list是列表类型
                            if isinstance(term_name_list, list):
                                term_name_list.append(term_name)
            except TypeError as e:
                logging.error(f"TypeError in isAllInMem when processing term_names: {e}")
                # 如果出错，假设所有组件都在内存中
                all_in_mem = True

            # 特殊情况处理
            if "开心" in str(ct):
                all_in_mem = True

            return all_in_mem
        except Exception as e:
            logging.error(f"Error in isAllInMem: {e}")
            return False

    @staticmethod
    def isAllInMemVar(ct):
        """
        静态方法，检查复合术语的所有组件是否在内存中，特别处理变量
        Args:
            ct: 复合术语
        Returns:
            bool: 如果所有组件都在内存中则返回True
        """
        if not NARS_AVAILABLE:
            return True

        # 特殊情况处理：如果术语包含“开心”，则返回true
        try:
            if hasattr(ct, "__str__") and "开心" in str(ct):
                return True
        except Exception as e:
            logging.error(f"Error checking for '开心' in isAllInMemVar: {e}")

        # 检查ct是否为None
        if ct is None:
            return False

        try:
            # 初始化
            all_in_mem = True
            term_name_list = []

            # 尝试调用isAllInMem方法检查复合术语的所有组件
            try:
                all_in_mem = PamImpl0.isAllInMem(ct, all_in_mem, term_name_list)
            except Exception as e:
                logging.error(f"Error in isAllInMemVar when calling isAllInMem: {e}")
                # 如果出错，尝试使用更简单的方法
                if hasattr(ct, "term"):
                    for sub_term in ct.term:
                        if hasattr(sub_term, "__str__") and not str(sub_term).startswith("$") and not str(sub_term).startswith("#"):
                            # 检查是否在内存中
                            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                            nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None
                            if nar and hasattr(nar, "memory") and hasattr(nar.memory, "getNodeByName"):
                                if nar.memory.getNodeByName(str(sub_term)) is None:
                                    all_in_mem = False
                                    break

            # 特殊情况处理：如果术语包含"开心"，则返回true
            if hasattr(ct, "__str__") and "开心" in str(ct):
                all_in_mem = True

            # 变量实例化逻辑
            # 如果术语中包含多个$变量，则进行变量实例化
            if all_in_mem and hasattr(ct, "__str__"):
                term_str = str(ct)
                # 检查是否包含多个$变量
                if term_str.find("$") != term_str.rfind("$"):
                    # 统计ct最外层里至少两个复合项
                    num = 0
                    subject = None
                    # 检查是否是Statement类型
                    if isinstance(ct, Statement):
                        # 获取主语
                        if hasattr(ct, "get_subject"):
                            subject = ct.get_subject()
                            # 如果主语是复合术语
                            if isinstance(subject, CompoundTerm):
                                # 遍历主语的所有组件
                                if hasattr(subject, "term"):
                                    for term1 in subject.term:
                                        # 如果组件是复合术语，则计数器+1
                                        if isinstance(term1, CompoundTerm):
                                            num += 1

                    # 如果有多个复合项，进行变量实例化
                    if num >= 1 and subject is not None and hasattr(subject, "term") and len(subject.term) >= 2:
                        match_list = []
                        sub_var = {}
                        re_match_list = []

                        # 调用varSub0方法进行变量替换
                        match = PamImpl0.varSub0(term_name_list, subject, match_list, sub_var)
                        logging.debug(f"matchlist = {match_list}, subVar = {sub_var}")

                        # 匹配变量
                        PamImpl0.matchVar(sub_var, match_list, subject, re_match_list)

                        # 将结果添加到NARS中
                        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                        nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None
                        if nar and hasattr(nar, "add_input"):
                            for term1 in re_match_list:
                                try:
                                    nar.add_input(str(term1) + ".")
                                    if hasattr(nar, "add_input_to"):
                                        goal_ns = PamImpl0.get_goal_ns()
                                        nar.add_input_to(str(term1) + ".", goal_ns)
                                        print(f"Added term to NARS for variable instantiation-----0-------: {term1}")
                                except Exception as e:
                                    logging.error(f"Error adding input to NARS for variable instantiation----0: {e}")

                                try:
                                    ct.set_subject(term1)
                                    ctt = ct.to_task('!')
                                    print(f"Set subject to {term1}")
                                    from linars.edu.memphis.ccrg.lida.PAM.Tasks.ProcessGTreeTask import ProcessGTreeTask
                                    taskt = ProcessGTreeTask(ctt, nar.memory)
                                    print(f"Created ProcessGTreeTask for {ctt.sentence.__str__()}")
                                    PamImpl0.task_spawner.add_task(taskt)
                                except Exception as e:
                                    logging.error(f"Error adding input to NARS for variable instantiation1: {e}")


                    # else:
                    #     # 简化处理，将术语添加到NARS中
                    #     from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                    #     nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None
                    #     if nar and hasattr(nar, "add_input"):
                    #         # 将术语添加到NARS中进行处理
                    #         nar.add_input(term_str + ".")
                    #         # 打印调试信息
                    #         print(f"Added term to NARS for variable instantiation----1----: {term_str}")

            return all_in_mem
        except Exception as e:
            logging.error(f"Error in isAllInMemVar: {e}")
            return False

    def get_task_spawner(self):
        """
        获取任务生成器对象

        Returns:
            TaskSpawner: 任务生成器对象
        """
        return self.task_spawner

    @staticmethod
    def get_goal_ns():
        """
        静态方法，获取goal_ns实例

        Returns:
            NodeStructure: goal_ns实例
        """
        try:
            # 尝试从 AgentStarter 获取
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName

            # 如果 AgentStarter.pam 存在，尝试获取 goal_ns
            if hasattr(AgentStarter, 'pam') and AgentStarter.pam is not None:
                # 如果 pam 实例有 goal_ns 属性，直接返回
                if hasattr(AgentStarter.pam, 'goal_ns'):
                    return AgentStarter.pam.goal_ns

                # 如果没有，尝试从工作空间获取
                if hasattr(AgentStarter.pam, 'get_listener'):
                    workspace = AgentStarter.pam.get_listener()

                    if hasattr(workspace, 'get_submodule'):
                        # 获取 GoalGraph 缓冲区
                        goal_graph_buffer = workspace.get_submodule(ModuleName.GoalGraph)

                        if goal_graph_buffer is not None and hasattr(goal_graph_buffer, 'get_buffer_content'):
                            # 获取缓冲区内容
                            goal_graph = goal_graph_buffer.get_buffer_content(None)
                            if goal_graph is not None:
                                return goal_graph

            # 如果上述方法失败，返回 None
            return None
        except Exception as e:
            logging.error(f"Error in get_goal_ns: {e}")
            return None

    @staticmethod
    def varSub0(not_var_list, ct, match_list0, sub_var):
        """
        静态方法，进行变量替换

        Args:
            not_var_list: 非变量列表
            ct: 复合术语
            match_list0: 匹配列表
            sub_var: 变量替换映射

        Returns:
            str: 匹配结果状态
        """
        try:
            if not hasattr(ct, "term"):
                return ""

            terms = ct.term
            # 未实例化的变量，只有一个，不用实例化，直接跳过
            # 匹配到多少是多少，匹配到的实例化，看缓存有没有，没有则加入mem
            done = 0
            # 还要考虑位置，组件相同，位置不同，无要求则可实例化，有要求则不可。同级
            ct_has_ct = False
            var_term_map = {}
            sub_done_list = []

            # 以ct为准，遍历各部分，再从list中匹配非变量部分，然后对该部分进行变量匹配
            for c in range(len(terms)):
                term = terms[c]
                # 单个变量项不用实例化，实例化就是自己
                if not isinstance(term, CompoundTerm):
                    if isinstance(term, Variable):
                        var_term_map[term] = c
                    continue

                ct_has_ct = True
                cttt = term
                result = ""
                has_cttt = False
                sub_match_list = []

                # 递归匹配，从最里层开始。子层级及以下匹配
                for cttt0 in cttt.term:
                    # 变量项不用实例化，放到上一层匹配。常量项一样。list内都是它自己一个
                    if isinstance(cttt0, CompoundTerm):
                        has_cttt = True
                        break

                if has_cttt:
                    result = PamImpl0.varSub0(not_var_list, cttt, sub_done_list, sub_var)
                    if result == "true":
                        PamImpl0.matchVar(sub_var, sub_done_list, cttt, sub_match_list)
                        match_list0.append(sub_match_list)
                        # 复合组件也加入mem
                        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                        nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None
                        if nar:
                            for term1 in sub_match_list:
                                # 添加到NARS内存
                                if hasattr(nar, "add_input"):
                                    # 使用add_input
                                    nar.add_input(str(term1) + ". :|:")
                                    # 获取goal_ns并使用add_input_to
                                    goal_ns = PamImpl0.get_goal_ns()
                                    if hasattr(nar, "add_input_to") and goal_ns is not None:
                                        nar.add_input_to(str(term1) + ". :|:", goal_ns)
                        done += 1
                        # 不是直接返回，而是继续往下匹配，因为这里只是一个复合组件，还没遍历完整个ct
                        continue
                    else:
                        # 复合组件不匹配，不用继续往下了，即使其他组件匹配，也不行，因为这里是且关系
                        return "false"

                # 处理非递归复合术语的情况
                # 遍历非变量列表，查找匹配项
                for not_var in not_var_list:
                    # 跳过特殊符号
                    if not_var.startswith("$") or not_var.startswith("#") or not_var == "SELF" or not_var == "happy":
                        continue

                    # 获取NARS内存中的概念
                    from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                    nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None
                    if not nar or not hasattr(nar, "memory") or not hasattr(nar.memory, "concepts"):
                        continue

                    concept = nar.memory.concepts.get(not_var)
                    if not concept or not hasattr(concept, "termLinks"):
                        continue

                    # 遍历概念的术语链接
                    for term_link in concept.termLinks:
                        if not hasattr(term_link, "target"):
                            continue

                        target = term_link.target
                        # 检查目标是否是复合术语
                        if not isinstance(target, CompoundTerm):
                            continue

                        ct1 = target
                        # 检查术语结构是否匹配
                        if not hasattr(ct1, "term") or not hasattr(cttt, "term") or len(ct1.term) != len(cttt.term):
                            continue

                        # 检查是否包含变量
                        has_var = False
                        var_term1 = None
                        term1 = None
                        not_var_count = 0

                        # 遍历术语的组件
                        for i in range(len(ct1.term)):
                            if i >= len(cttt.term):
                                break

                            t1 = ct1.term[i]
                            t2 = cttt.term[i]

                            # 检查变量
                            if isinstance(t2, Variable):
                                has_var = True
                                var_term1 = t2
                                term1 = t1
                            elif str(t1) == str(t2):
                                # 非变量项匹配
                                not_var_count += 1

                        # 如果只有一个变量，且其他项都匹配
                        if has_var and not_var_count == len(cttt.term) - 1 and term1 is not None and var_term1 is not None:
                            # 检查目标术语是否不包含变量
                            if not ct1.has_var():
                                # 添加变量替换
                                sub_var[var_term1] = term1
                                if not ct1 in sub_match_list:
                                    sub_match_list.append(ct1)

                                # 尝试使用变量实例化其他变量分句
                                # try:
                                #     PamImpl0.get_else(not_var_list, ct, terms, term, not_var, var_term1, term1)
                                # except Exception as e:
                                #     logging.error(f"Error in get_else call: {e}")

                if sub_match_list:
                    match_list0.append(sub_match_list)
                    done += 1
                else:
                    # 没有实例值，加入varTermMap，看其他已经实例化的变量能否实例化它
                    var_term_map[term] = c

            # 如果有单个变量varTermMap，且subvar有实例化值，则实例化单变量，并按index插入matchList0
            if var_term_map and sub_var and done >= 1:
                # 直接将匹配到的变量值加入到matchList0里，按index插入
                for var_term, index in var_term_map.items():
                    if isinstance(var_term, Variable):
                        term = sub_var.get(var_term)
                        if term is not None:
                            sub_match_list = [term]
                            if index < len(match_list0):
                                match_list0.insert(index, sub_match_list)
                            else:
                                match_list0.append(sub_match_list)
                    elif isinstance(var_term, CompoundTerm):
                        # 非单个变量，遍历varTerm，将变量项实例化
                        for term in var_term.term:
                            if isinstance(term, Variable):
                                term1 = sub_var.get(term)
                                if term1 is not None and hasattr(var_term, "deep_replace_var"):
                                    var_term.deep_replace_var(term, term1)
                        sub_match_list = [var_term]
                        if index < len(match_list0):
                            match_list0.insert(index, sub_match_list)
                        else:
                            match_list0.append(sub_match_list)

            if match_list0 and len(match_list0) >= len(terms) - 1:
                return "true"
            if not ct_has_ct:
                return "noCt"

            return ""
        except Exception as e:
            logging.error(f"Error in varSub0: {e}")
            return ""

    @staticmethod
    def matchVar(sub_var, sub_done_list, cttt, sub_match_list):
        """
        静态方法，匹配变量

        Args:
            sub_var: 变量替换映射
            sub_done_list: 已完成的替换列表
            cttt: 复合术语
            sub_match_list: 匹配列表
        """
        try:
            sub_dls = []
            sub_dl = []

            # 克隆复合术语以避免修改原始术语
            if hasattr(cttt, "clone_deep"):
                copy_term0 = cttt.clone_deep()
            else:
                # 如果没有cloneDeep方法，简单返回
                return

            # 组件各自完全匹配，组装各种可能语句
            PamImpl0.getAllDone0(sub_done_list, sub_dl, sub_dls, 0)

            for lt in sub_dls:
                if hasattr(copy_term0, "deep_replace_term") and hasattr(lt, "toArray"):
                    copy_term0.deep_replace_term(lt.toArray())
                elif hasattr(copy_term0, "deep_replace_term"):
                    # 如果lt没有toArray方法，尝试直接使用lt
                    copy_term0.deep_replace_term(lt)

                # 再次克隆以避免污染
                if hasattr(copy_term0, "clone_deep"):
                    copy_no_var = copy_term0.clone_deep()
                else:
                    continue

                # 变量和实例值集合
                subsconc = {}
                subsconc0 = {}

                # 检查是否有Variables类和findSubstitute方法
                from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None

                if nar and hasattr(nar, "memory") and hasattr(nar.memory, "randomNumber"):
                    try:
                        from linars.org.opennars.language.variables import Variables
                        from linars.org.opennars.io.symbols import VAR_INDEPENDENT
                        try:
                            # 组合是否匹配，如果匹配，则组合成立
                            conclusion_matches = Variables.find_substitute(
                                nar.memory.randomNumber,
                                VAR_INDEPENDENT,
                                copy_no_var,
                                cttt,
                                subsconc,
                                subsconc0
                            )
                        except Exception as e:
                            logging.error(f"Error importing or using Variables0: {e}")

                        if conclusion_matches:
                            sub_match_list.append(copy_no_var)
                            # 加入变量列表，可用于实例化单变量等
                            PamImpl0.appendToMap(subsconc0, sub_var)
                    except (ImportError, AttributeError) as e:
                        logging.error(f"Error importing or using Variables1: {e}")
        except Exception as e:
            logging.error(f"Error in matchVar: {e}")

    @staticmethod
    def getAllDone0(sub_done_list, sub_dl, sub_dls, index):
        """
        静态方法，递归获取所有完成的组合

        Args:
            sub_done_list: 已完成的替换列表
            sub_dl: 当前处理的替换列表
            sub_dls: 所有可能的替换组合列表
            index: 当前处理的索引
        """
        try:
            # 递归实现。因为不确定多少层
            if index >= len(sub_done_list):
                return

            for k in range(len(sub_done_list[index])):
                sub_dl.append(sub_done_list[index][k])
                if len(sub_done_list) >= index + 2:
                    PamImpl0.getAllDone0(sub_done_list, sub_dl, sub_dls, index + 1)
                    sub_dl = []
                else:
                    sub_dls.append(sub_dl.copy())
        except Exception as e:
            logging.error(f"Error in getAllDone0: {e}")

    @staticmethod
    def appendToMap(source_map, target_map):
        """
        静态方法，将源映射添加到目标映射

        Args:
            source_map: 源映射
            target_map: 目标映射
        """
        try:
            for key, value in source_map.items():
                target_map[key] = value
        except Exception as e:
            logging.error(f"Error in appendToMap: {e}")

    @staticmethod
    def get_else(not_var_list, ct, terms, term, not_var, var_term1, term1):
        """
        静态方法，尝试使用变量实例化其他变量分句

        Args:
            not_var_list: 非变量列表
            ct: 复合术语
            terms: 术语数组
            term: 当前术语
            not_var: 当前非变量
            var_term1: 变量术语
            term1: 变量实例化值
        """
        try:
            # 遍历术语数组
            for tt in terms:
                # 跳过当前术语和非复合术语
                if str(tt) == str(term) or not isinstance(tt, CompoundTerm):
                    continue

                # 遍历非变量列表
                for not_var0 in not_var_list:
                    # 跳过当前非变量
                    if not_var0 == not_var:
                        continue

                    # 检查术语是否包含非变量和变量
                    if hasattr(tt, "__str__") and not_var0 in str(tt) and str(var_term1) in str(tt):
                        # 用变量实值替换变量，再去非变量的termLinks里找，找到则实例化
                        cttt1 = tt

                        # 需要复制一份，否则替换后，cttt0也变了
                        if hasattr(cttt1, "clone_deep"):
                            cttt0 = cttt1.clone_deep()
                        else:
                            continue

                        # 实例化，深度替换，递归找到每一层的变量，替换为实例
                        if hasattr(cttt0, "deep_replace_var"):
                            cttt0.deep_replace_var(var_term1, term1)
                        else:
                            continue

                        # 获取NARS内存
                        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                        nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None
                        if not nar or not hasattr(nar, "memory") or not hasattr(nar.memory, "concepts"):
                            continue

                        # 获取非变量概念
                        nvc2 = nar.memory.concepts.get(not_var0)
                        if not nvc2 or not hasattr(nvc2, "termLinks"):
                            continue

                        # 遍历概念的术语链接
                        for term_link2 in nvc2.termLinks:
                            if not hasattr(term_link2, "target"):
                                continue

                            ct2 = term_link2.target
                            # 检查目标是否是复合术语
                            if not isinstance(ct2, CompoundTerm):
                                continue

                            # 检查是否匹配
                            if str(ct2) == str(cttt0):
                                # 整个ct实例化
                                ctcds = str(ct).replace(str(var_term1), str(term1))

                                # 添加到NARS内存
                                if hasattr(nar, "add_input"):
                                    nar.add_input(ctcds + ".")
                                    # 获取goal_ns并使用add_input_to
                                    goal_ns = PamImpl0.get_goal_ns()
                                    if hasattr(nar, "add_input_to") and goal_ns is not None:
                                        nar.add_input_to(ctcds + ".", goal_ns)
                                break

                        # 未匹配成功，cttt0换回来
                        if hasattr(cttt0, "deep_replace_var"):
                            cttt0.deep_replace_var(term1, var_term1)
        except Exception as e:
            logging.error(f"Error in get_else: {e}")

    def is_all_in_mem(self, ct, all_in_mem, term_name_list):
        """
        检查复合术语的所有组件是否在内存中
        实例方法版本，调用静态方法

        Args:
            ct: 复合术语
            all_in_mem: 当前状态
            term_name_list: 用于存储术语名称的列表

        Returns:
            bool: 如果所有组件都在内存中则返回True
        """
        # 调用静态方法
        return PamImpl0.isAllInMem(ct, all_in_mem, term_name_list)

    def set_scene_main_node(self, sink):
        """
        Set the main node for a scene

        Args:
            sink: The node to set as main
        """
        if self.scene_ns and hasattr(self.scene_ns, "getMainNodeId") and self.scene_ns.getMainNodeId() != 0:
            is_exit = False

            # Check if node is in scene list
            if hasattr(self, "scene_list"):
                for scene_id in self.scene_list:
                    if scene_id == str(sink.get_node_id()):
                        is_exit = True
                        break

            # Set main node ID
            if hasattr(self.scene_ns, "getNodes") and not self.scene_ns.getNodes():
                self.scene_ns.setMainNodeId(sink.get_node_id())
                if hasattr(self, "scene_list"):
                    self.scene_list.clear()
            elif not is_exit and hasattr(self, "scene_list"):
                self.scene_list.append(str(sink.get_node_id()))
        elif self.scene_ns and hasattr(self.scene_ns, "setMainNodeId"):
            self.scene_ns.setMainNodeId(sink.get_node_id())

    def get_scene_node(self, scene, scene_name, is_var=False):
        """
        获取场景的节点

        Args:
            scene: 场景节点
            scene_name: 场景名称
            is_var: 场景是否包含变量
        """
        try:
            # 查询以找到给定名称的节点作为源的关系
            query = f"match (n{{name:'{scene_name}'}})<-[r]-() return r"
            self.logger.debug(f"------- getSceneNode ------ {query}")

            # 使用Neo4j执行查询
            from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
            result = NeoUtil.execute_query(query)

            num = 0
            while result and hasattr(result, "has_next") and result.has_next():
                num += 1
                self.get_scene(result, is_var)

            # 如果语法被激活且这是主场景，触发语法任务
            if num > 0:
                # 检查这是否是主场景
                is_main_scene = False
                if hasattr(scene, "get_node_id") and hasattr(self, "scene_ns") and hasattr(self.scene_ns, "getMainNodeId"):
                    is_main_scene = scene.get_node_id() == self.scene_ns.getMainNodeId()
                elif hasattr(scene, "getNodeId") and hasattr(self, "scene_ns") and hasattr(self.scene_ns, "getMainNodeId"):
                    is_main_scene = scene.getNodeId() == self.scene_ns.getMainNodeId()

                if is_main_scene:
                    # 创建语法任务
                    try:
                        from linars.edu.memphis.ccrg.lida.PAM.Tasks.GrammarTask import GrammarTask
                        if hasattr(self, "yufa_ns") and hasattr(self, "scene_ns") and self.yufa_ns and self.scene_ns:
                            task = GrammarTask(self.yufa_ns, self.scene_ns, 1, self)
                            if hasattr(self, "task_spawner") and self.task_spawner:
                                self.task_spawner.add_task(task)
                    except ImportError as e:
                        self.logger.warning(f"Could not import GrammarTask: {e}")

                # 检查场景列表
                if hasattr(self, "scene_list") and self.scene_list:
                    scene_id_to_check = None
                    if hasattr(scene, "get_node_id"):
                        scene_id_to_check = str(scene.get_node_id())
                    elif hasattr(scene, "getNodeId"):
                        scene_id_to_check = str(scene.getNodeId())

                    if scene_id_to_check:
                        for scene_id in self.scene_list:
                            if scene_id == scene_id_to_check:
                                # 创建另一个语法任务
                                try:
                                    from linars.edu.memphis.ccrg.lida.PAM.Tasks.GrammarTask import GrammarTask
                                    if hasattr(self, "yufa_ns") and hasattr(self, "scene_ns") and self.yufa_ns and self.scene_ns:
                                        task = GrammarTask(self.yufa_ns, self.scene_ns, 1, self)
                                        if hasattr(self, "task_spawner") and self.task_spawner:
                                            self.task_spawner.add_task(task)
                                except ImportError as e:
                                    self.logger.warning(f"Could not import GrammarTask: {e}")
        except Exception as e:
            self.logger.error(f"Error in get_scene_node: {e}")
            import traceback
            traceback.print_exc()

    def get_scene(self, result, is_var):
        """
        处理场景结果

        Args:
            result: 查询结果
            is_var: 场景是否包含变量
        """
        try:
            from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil

            if not hasattr(result, "next") or not hasattr(result, "columns"):
                self.logger.error("Invalid result object in get_scene")
                return

            row = result.next()
            for key in result.columns():
                rel = row.get(key)
                if rel is None:
                    continue

                rel_type = None
                if hasattr(rel, "type"):
                    rel_type = rel.type
                elif hasattr(rel, "getType"):
                    rel_type = rel.getType()

                if rel_type is None:
                    continue

                # 跳过特定关系类型
                if rel_type in ["顺承", "时序", "时序首", "参数"]:
                    continue

                # 将Neo4j关系转换为LIDA链接
                link = None
                if hasattr(NeoUtil, "cast_neo_to_lida_link"):
                    link = NeoUtil.cast_neo_to_lida_link(rel)
                elif hasattr(NeoUtil, "CastNeoToLidaLink"):
                    link = NeoUtil.CastNeoToLidaLink(rel, None)

                if link is None:
                    continue

                to_node = link.getSink()
                if to_node is None:
                    continue

                # 将节点添加到PAM
                if hasattr(self, "pam_node_structure") and self.pam_node_structure:
                    if hasattr(self.pam_node_structure, "addNode"):
                        self.pam_node_structure.addNode(to_node, "PamNodeImpl")

                # 如果需要，处理变量替换
                if is_var:
                    result_map = self.get_isa_link(link.getSource(), to_node, link.getCategory(), self)
                    if result_map and isinstance(result_map, dict) and result_map.get("done") == "yes":
                        link = result_map.get("link")

                # 获取源节点
                from_node = link.getSource()
                if from_node is None:
                    continue

                # 将节点添加到映射
                from_node_name = None
                if hasattr(from_node, "get_node_name"):
                    from_node_name = from_node.get_node_name()
                elif hasattr(from_node, "getTNname"):
                    from_node_name = from_node.getTNname()

                if from_node_name:
                    self.put_map(from_node, from_node_name)

                to_node_name = None
                if hasattr(to_node, "get_node_name"):
                    to_node_name = to_node.get_node_name()
                elif hasattr(to_node, "getTNname"):
                    to_node_name = to_node.getTNname()

                if to_node_name:
                    self.put_map(to_node, to_node_name)

                # 递归处理场景元素
                if hasattr(self, "scene_map") and from_node_name and from_node_name in self.scene_map:
                    self.get_scene_node(from_node, from_node_name, is_var)

                # 激活语法链接
                self.activ_grammar_link(link, rel_type)
        except Exception as e:
            self.logger.error(f"Error in get_scene: {e}")
            import traceback
            traceback.print_exc()

    def get_isa_link(self, node, to_node, category, pam):
        """
        获取节点之间的ISA链接

        Args:
            node: 源节点
            to_node: 目标节点
            category: 链接类别
            pam: PAM实例

        Returns:
            dict: 包含链接信息的结果映射
        """
        try:
            result = {"done": "no"}

            # 检查nowisa链接
            if hasattr(self, "seq_ns") and self.seq_ns:
                node_id = None
                if hasattr(node, "get_node_id"):
                    node_id = node.get_node_id()
                elif hasattr(node, "getNodeId"):
                    node_id = node.getNodeId()

                if node_id is not None:
                    links = None
                    if hasattr(self.seq_ns, "getLinksOfSource"):
                        links = self.seq_ns.getLinksOfSource(node_id)

                    if links:
                        for link in links:
                            link_name = None
                            if hasattr(link, "get_node_name"):
                                link_name = link.get_node_name()
                            elif hasattr(link, "getTNname"):
                                link_name = link.getTNname()

                            if link_name == "nowisa":
                                # 递归检查sink节点
                                sink_node = None
                                if hasattr(link, "getSink"):
                                    sink_node = link.getSink()

                                if sink_node:
                                    sub_result = self.get_isa_link(sink_node, to_node, category, pam)
                                    if isinstance(sub_result, dict) and sub_result.get("done") == "yes":
                                        # 如果已经创建，返回结果
                                        return sub_result

                                    # 创建新链接
                                    new_link = None
                                    if hasattr(pam, "add_default_link"):
                                        new_link = pam.add_default_link(sink_node, to_node, category)
                                    elif hasattr(pam, "addDefaultLink"):
                                        new_link = pam.addDefaultLink(sink_node, to_node, category)

                                    if new_link:
                                        result["link"] = new_link
                                        result["done"] = "yes"

            return result
        except Exception as e:
            self.logger.error(f"Error in get_isa_link: {e}")
            import traceback
            traceback.print_exc()
            return {"done": "no"}

    def activ_grammar_link(self, link, rel_type):
        """
        激活语法链接

        Args:
            link: 要激活的链接
            rel_type: 关系类型
        """
        try:
            # 添加到场景图
            if hasattr(self, "pam_listeners") and self.pam_listeners:
                for listener in self.pam_listeners:
                    # 获取源节点和汇节点
                    source = None
                    sink = None
                    if hasattr(link, "getSource"):
                        source = link.getSource()
                    if hasattr(link, "getSink"):
                        sink = link.getSink()

                    if source and sink:
                        # 添加到场景图
                        listener.receive_percept(source, ModuleName.SceneGraph)
                        listener.receive_percept(sink, ModuleName.SceneGraph)
                        listener.receive_percept(link, ModuleName.SceneGraph)

                        # 添加到当前情境模型
                        listener.receive_percept(source, ModuleName.CurrentSM)
                        listener.receive_percept(sink, ModuleName.CurrentSM)
                        listener.receive_percept(link, ModuleName.CurrentSM)

                        # 添加到非意识图
                        listener.receive_percept(source, ModuleName.NonGraph)
                        listener.receive_percept(sink, ModuleName.NonGraph)
                        listener.receive_percept(link, ModuleName.NonGraph)

            # 从关系类型激活
            linkable = None
            if hasattr(self, "get_node"):
                linkable = self.get_node(rel_type)
            elif hasattr(self, "getNode"):
                linkable = self.getNode(rel_type)

            if linkable is None and hasattr(self, "pam_node_structure") and self.pam_node_structure:
                if hasattr(self.pam_node_structure, "getNeoNode"):
                    linkable = self.pam_node_structure.getNeoNode(rel_type)

                if linkable is not None:
                    if hasattr(self, "add_default_node"):
                        self.add_default_node(linkable)
                    elif hasattr(self, "addDefaultNode"):
                        self.addDefaultNode(linkable)

            if linkable:
                if hasattr(linkable, "set_activation"):
                    linkable.set_activation(0.8)
                elif hasattr(linkable, "setActivation"):
                    linkable.setActivation(0.8)

                # 直接从场景激活语法
                self.propagate_activation_to_parents(linkable, 1, "sentence")
        except Exception as e:
            self.logger.error(f"Error in activ_grammar_link: {e}")
            import traceback
            traceback.print_exc()

    def get_act_root(self, link, is_var=False, is_loop=False, act_stamp=None):
        """
        Get the root of an action sequence

        Args:
            link: The link to start from is_var: Whether the link contains variables
            is_loop: Whether this is a loop
            act_stamp: Timestamp for the action
        """
        sink = link.getSink()
        source = link.getSource()
        sname = sink.get_node_name() if hasattr(sink, "get_label") else ""

        # Add to map
        self.put_map(sink, sname)

        # Handle for loops
        if hasattr(self, "for_map") and sname in self.for_map and not is_loop:
            # If it's a for loop, execute the loop body first
            from linars.edu.memphis.ccrg.lida.PAM.Tasks.ForEachTask import ForEachTask
            task = ForEachTask(link, self, self.seq_ns, self.scene_ns, act_stamp)
            if self.task_spawner:
                self.task_spawner.add_task(task)
            return

        # Query to find the sequence start
        query = f"match (m)-[r:时序首]->(i) where id(m) = {sink.get_node_id()} return r"
        self.logger.debug(f"query = {query}")

        link0 = None
        try:
            from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
            result = NeoUtil.execute_query(query)

            if result and result.has_next():
                row = result.next()
                for key in result.columns():
                    rel = row.get(key)

                    # Convert to LIDA link
                    link0 = NeoUtil.cast_neo_to_lida_link(rel)
                    to_node = link0.getSink()

                    # Add to sequence graph
                    for listener in self.pam_listeners:
                        listener.receive_percept(to_node, ModuleName.SeqGraph)
                        listener.receive_percept(link0, ModuleName.SeqGraph)

                    # Set incentive salience
                    to_node.setIncentiveSalience(sink.getIncentiveSalience())

                    self.logger.debug(f"时序首---|||-{link0}")

                    # Propagate activation
                    self.propagate_activation(to_node, link0, 1.0, 1, "varmindplan")
        except Exception as e:
            self.logger.error(f"Error in get_act_root: {e}")

        # Add timestamp to sequence
        if self.seq_ns and hasattr(self.seq_ns, "getDoMainPath_map"):
            main_list = self.seq_ns.getDoMainPath_map().get(act_stamp)
            if main_list is None:
                main_list = []
                self.seq_ns.getDoMainPath_map()[act_stamp] = main_list

            if link0 is not None:
                if not is_var:
                    # Add to main path for possible nested sequences
                    main_list.append(link)

                # Recursively find and execute all child sequences
                self.get_act_root(link0, False, False, act_stamp)
            else:
                # End of sequence, execute specific logic based on node type
                if hasattr(self, "ifelse_map") and sname in self.ifelse_map:
                    # Handle if-else
                    from linars.edu.memphis.ccrg.lida.PAM.Tasks.DoSelectTreeTask import DoSelectTreeTask
                    task = DoSelectTreeTask(link, self, self.goal_ns, self.scene_ns, act_stamp)
                    if self.task_spawner:
                        self.task_spawner.add_task(task)
                else:
                    # Handle other types (variables, initialization, etc.)
                    from linars.edu.memphis.ccrg.lida.PAM.Tasks.DoMindActTask import DoMindActTask
                    task = DoMindActTask(sink, source, self, self.seq_ns, self.goal_ns, act_stamp)
                    if self.task_spawner:
                        self.task_spawner.add_task(task)
