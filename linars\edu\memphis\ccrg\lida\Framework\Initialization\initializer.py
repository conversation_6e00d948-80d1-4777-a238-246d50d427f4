"""
对象初始化器接口

本模块提供了初始化对象的接口
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Type, TypeVar

T = TypeVar('T')

class Initializer(ABC):
    """
    对象初始化器接口

    该接口定义了初始化对象的方法
    """

    @abstractmethod
    def initModule(self, obj: Any, agent: Any, params: Dict[str, Any]) -> None:
        """
        使用给定参数初始化模块

        参数:
            obj: 要初始化的对象
            agent: 代理
            params: 初始化参数
        """
        pass
