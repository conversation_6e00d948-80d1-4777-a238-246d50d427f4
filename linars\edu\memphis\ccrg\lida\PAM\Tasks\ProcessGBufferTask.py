#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task that processes tasks from the global buffer.
"""

from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTask import TaskStatus
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Nlanguage.SubGraphSet import SubGraphSet

class ProcessGBufferTask(FrameworkTaskImpl):
    """
    A task that processes tasks from the global buffer.
    """
    nar = AgentStarter.nar
    def __init__(self, pam, memory, target="both"):
        super().__init__(1, "tact")
        self.pam = pam
        self.memory = memory
        self.target = target

    def run_this_framework_task(self):
        """Run the task"""
        import logging
        logger = logging.getLogger(self.__class__.__name__)

        try:
            if self.get_task_status() == TaskStatus.CANCELED:
                return

            # 处理全局缓冲区中的任务
            try:
                if self.target == "both" or self.target == "global" or self.target == "belief" or self.target == "goal":
                    self._process_global_buffer()
            except Exception as e:
                logger.error(f"Error processing global buffer: {e}")
                import traceback
                traceback.print_exc()

            # 处理序列缓冲区中的任务
            try:
                if self.target == "both" or self.target == "sequence":
                    self._process_sequence_buffer()
            except Exception as e:
                logger.error(f"Error processing sequence buffer: {e}")
                import traceback
                traceback.print_exc()

            # Cancel the task when finished
            try:
                self.cancel()
            except Exception as e:
                logger.error(f"Error canceling task: {e}")

            # 添加任务到TaskSpawner
            try:
                from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
                if FrameworkModuleImpl.task_spawner:
                    FrameworkModuleImpl.task_spawner.receive_finished_task(self)
            except Exception as e:
                logger.error(f"Error adding task to TaskSpawner: {e}")
        except Exception as e:
            logger.error(f"Error in run_this_framework_task: {e}")
            import traceback
            traceback.print_exc()

    # def get_term(self):
    #     """获取任务的术语，这是为了兼容Task接口
    #
    #     返回:
    #         任务的术语，这里返回None因为ProcessGBufferTask不是真正的Task
    #     """
    #     return None

    def _process_global_buffer(self):
        """Process tasks from the global buffer"""
        import logging
        logger = logging.getLogger(self.__class__.__name__)
        isgraph = False
        if isinstance(self.memory, SubGraphSet):
            isgraph = True
        #     print("Processing global buffer for SubGraphSet-----0-----")
        try:
            if hasattr(self.memory, "globalBuffer") and self.memory.globalBuffer is not None:
                # 根据目标类型处理任务
                if self.target == "both" or self.target == "global":
                    try:
                        # 处理全局缓冲区中的任务
                        task = None
                        try:
                            task = self.memory.globalBuffer.take_out()
                        except Exception as e:
                            logger.error(f"Error taking task from global buffer: {e}")
                            return

                        if task:
                            try:
                                # Process the task
                                if not task.processed:
                                    task.processed = True
                                    if isgraph:
                                        term = task.get_term()
                                        # if "<SELF --> [happy]>" in str(term):
                                        #     print("Processing global buffer for SubGraphSet----------")
                                    try:
                                        # 检查task是否有效
                                        if hasattr(task, 'get_term') and callable(getattr(task, 'get_term')):
                                            term = task.get_term()
                                            if term is not None:
                                                self.memory.local_inference(task, self.nar.narParameters, self.nar, self.memory)
                                            else:
                                                logger.warning(f"Task {task} has no valid term")
                                        else:
                                            logger.warning(f"Task {task} has no get_term method")
                                    except Exception as e:
                                        logger.error(f"Error in local_inference: {e}")
                                        import traceback
                                        traceback.print_exc()
                            except Exception as e:
                                logger.error(f"Error processing task: {e}")

                            # try:
                                # Put the task back in the buffer
                                # self.memory.globalBuffer.put_back(task, self.nar.narParameters.GLOBAL_BUFFER_FORGET_DURATIONS, self.memory)
                            # except Exception as e:
                            #     logger.error(f"Error putting task back in buffer: {e}")
                    except Exception as e:
                        logger.error(f"Error in global buffer processing: {e}")

                # 如果目标是信念或目标，则根据类型处理
                elif self.target == "belief" or self.target == "goal":
                    try:
                        # 处理特定类型的任务
                        task = None
                        try:
                            task = self.memory.globalBuffer.take_out()
                        except Exception as e:
                            logger.error(f"Error taking task from global buffer: {e}")
                            return

                        if task:
                            try:
                                if not task.processed:
                                    task.processed = True
                                    try:
                                        # 检查task是否有效
                                        if hasattr(task, 'get_term') and callable(getattr(task, 'get_term')):
                                            term = task.get_term()
                                            # if isgraph:
                                            #     if "<SELF --> [happy]>" in str(term):
                                            #         print("Processing global buffer for SubGraphSet----------")
                                            if term is not None:
                                                self.memory.local_inference(task, self.nar.narParameters, self.nar, self.memory)
                                            else:
                                                logger.warning(f"Task {task} has no valid term")
                                        else:
                                            logger.warning(f"Task {task} has no get_term method")
                                    except Exception as e:
                                        logger.error(f"Error in local_inference: {e}")
                                        import traceback
                                        traceback.print_exc()
                            except Exception as e:
                                logger.error(f"Error processing task: {e}")

                            # try:
                            #     # 无论如何都将任务放回缓冲区
                            #     self.memory.globalBuffer.put_back(task, self.nar.narParameters.GLOBAL_BUFFER_FORGET_DURATIONS, self.memory)
                            # except Exception as e:
                            #     logger.error(f"Error putting task back in buffer: {e}")
                    except Exception as e:
                        logger.error(f"Error in belief/goal processing: {e}")
        except Exception as e:
            logger.error(f"Error in _process_global_buffer: {e}")
            import traceback
            traceback.print_exc()

    def _process_sequence_buffer(self):
        """Process tasks from the sequence buffer"""
        import logging
        logger = logging.getLogger(self.__class__.__name__)

        try:
            if (hasattr(self.memory, "globalBuffer") and
                hasattr(self.memory.globalBuffer, "seq_current") and
                self.memory.globalBuffer.seq_current is not None):

                # 根据目标类型处理任务
                if self.target == "both" or self.target == "sequence":
                    try:
                        # 处理序列缓冲区中的任务
                        task = None
                        try:
                            task = self.memory.globalBuffer.seq_current.take_out()
                        except Exception as e:
                            logger.error(f"Error taking task from sequence buffer: {e}")
                            return

                        if task:
                            try:
                                # Process the task
                                if not task.processed:
                                    task.processed = True
                                    try:
                                        # 检查task是否有效
                                        if hasattr(task, 'get_term') and callable(getattr(task, 'get_term')):
                                            term = task.get_term()
                                            if term is not None:
                                                self.memory.local_inference(task, self.nar.narParameters, self.nar, self.memory)
                                            else:
                                                logger.warning(f"Task {task} has no valid term")
                                        else:
                                            logger.warning(f"Task {task} has no get_term method")
                                    except Exception as e:
                                        logger.error(f"Error in local_inference: {e}")
                                        import traceback
                                        traceback.print_exc()
                            except Exception as e:
                                logger.error(f"Error processing task: {e}")

                            # try:
                            #     # Put the task back in the buffer
                            #     self.memory.globalBuffer.seq_current.put_back(task, self.nar.narParameters.SEQUENCE_BUFFER_FORGET_DURATIONS, self.memory)
                            # except Exception as e:
                            #     logger.error(f"Error putting task back in sequence buffer: {e}")
                    except Exception as e:
                        logger.error(f"Error in sequence buffer processing: {e}")

                # 如果目标是信念或目标，则根据类型处理
                elif self.target == "belief" or self.target == "goal":
                    try:
                        # 处理特定类型的任务
                        task = None
                        try:
                            task = self.memory.globalBuffer.seq_current.take_out()
                        except Exception as e:
                            logger.error(f"Error taking task from sequence buffer: {e}")
                            return

                        if task:
                            try:
                                # 检查任务类型
                                is_goal = False
                                is_belief = False
                                try:
                                    is_goal = task.sentence.is_goal() if hasattr(task, 'sentence') and hasattr(task.sentence, 'is_goal') else False
                                    is_belief = not is_goal and hasattr(task, 'sentence')
                                except Exception as e:
                                    logger.error(f"Error checking task type: {e}")

                                # 根据目标类型处理
                                should_process = (self.target == "belief" and is_belief) or (self.target == "goal" and is_goal)

                                if should_process and not task.processed:
                                    task.processed = True
                                    try:
                                        # 检查task是否有效
                                        if hasattr(task, 'get_term') and callable(getattr(task, 'get_term')):
                                            term = task.get_term()
                                            if term is not None:
                                                self.memory.local_inference(task, self.nar.narParameters, self.nar, self.memory)
                                            else:
                                                logger.warning(f"Task {task} has no valid term")
                                        else:
                                            logger.warning(f"Task {task} has no get_term method")
                                    except Exception as e:
                                        logger.error(f"Error in local_inference: {e}")
                                        import traceback
                                        traceback.print_exc()
                            except Exception as e:
                                logger.error(f"Error processing task: {e}")

                            # try:
                            #     # 无论如何都将任务放回缓冲区
                            #     self.memory.globalBuffer.seq_current.put_back(task, self.nar.narParameters.SEQUENCE_BUFFER_FORGET_DURATIONS, self.memory)
                            # except Exception as e:
                            #     logger.error(f"Error putting task back in sequence buffer: {e}")
                    except Exception as e:
                        logger.error(f"Error in belief/goal sequence processing: {e}")
        except Exception as e:
            logger.error(f"Error in _process_sequence_buffer: {e}")
            import traceback
            traceback.print_exc()
