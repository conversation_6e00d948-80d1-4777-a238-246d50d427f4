"""
NAL推理过程上下文。
包含推理过程中的所有状态信息。
"""
from typing import List, Optional, Callable, Any, Dict, Set, Tuple, TYPE_CHECKING
import copy

from linars.edu.memphis.ccrg.linars.term import Term
# from linars.edu.memphis.ccrg.linars.concept import Concept
from linars.org.opennars.entity.task import Task
from linars.org.opennars.entity.task_link import TaskLink
from linars.org.opennars.entity.term_link import TermLink
from linars.org.opennars.entity.sentence import Sentence
from linars.org.opennars.entity.stamp import Stamp
from linars.org.opennars.entity.budget_value import BudgetValue
from linars.org.opennars.entity.truth_value import TruthValue
from linars.org.opennars.language.implication import Implication
from linars.org.opennars.language.interval import Interval
from linars.org.opennars.language.variable import Variable
from linars.org.opennars.inference.truth_functions import TruthFunctions

# 延迟导入Memory以避免循环导入
if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.linars.memory import Memory

class DerivationContext:
    """
    NAL推理过程上下文类。
    管理推理过程中的所有状态信息，包括当前概念、任务、信念等。
    """

    class StampBuilder:
        """时间戳构建器接口。"""

        def build(self) -> Stamp:
            """构建时间戳对象。"""
            pass

    def __init__(self, memory, narParameters, time):
        """
        构造函数。

        参数:
            memory: 内存对象
            narParameters: NAR参数配置
            time: 时间对象
        """
        self.evidential_overlap = False

        self.memory = memory  # 类型为'Memory'
        self.current_term = None
        self.current_concept = None
        self.current_task = None
        self.current_belief_link = None
        self.current_task_link = None
        self.current_belief = None
        self.new_stamp = None
        self.new_stamp_builder = None

        self.narParameters = narParameters

        self.time = time

        self.nar = time

        self.original_time = 0

    def emit(self, c, *o):
        """
        触发事件。

        参数:
            c: 事件类
            *o: 事件参数
        """
        self.memory.emit(c, *o)

    def derived_task(self, task: Task, revised: bool, single: bool, overlap_allowed: bool, add_to_memory: bool = True) -> bool:
        """
        处理来自推理规则的派生任务。

        参数:
            task: 派生出的任务
            revised: 是否为修订的任务
            single: 是否来自单一前提
            overlap_allowed: 是否允许证据重叠
            add_to_memory: 是否将任务添加到内存

        返回:
            bool: 成功添加返回True
        """
        # Check if the task has sufficient budget
        if not task.budget.above_threshold():
            self.memory.remove_task(task, "Insufficient Budget")
            return False

        # Check if the task has sufficient confidence
        if task.sentence is not None and task.sentence.truth is not None:
            conf = task.sentence.truth.get_confidence()
            if conf < self.narParameters.TRUTH_EPSILON:
                self.memory.remove_task(task, "Ignored (zero confidence)")
                return False

        # Check if the task has a valid term
        if task.sentence.term.clone_deep() is None:
            self.memory.remove_task(task, "Wrong Format")
            return False

        stamp = task.sentence.stamp

        # Check for evidential overlap
        if not overlap_allowed:
            double_premise_evidential_base_overlap = not single and self.evidential_overlap
            if double_premise_evidential_base_overlap:
                self.memory.remove_task(task, "overlapping evidential base")
                return False

            self_overlap = stamp.evidence_is_cyclic()
            if self_overlap:
                self.memory.remove_task(task, "overlapping evidential base")
                return False

        # Set task properties
        task.set_elem_of_sequence_buffer(False)
        if not revised:
            task.budget.set_durability(task.budget.get_durability() * self.narParameters.DERIVATION_DURABILITY_LEAK)
            task.budget.set_priority(task.budget.get_priority() * self.narParameters.DERIVATION_PRIORITY_LEAK)

        # Emit task derive event
        self.memory.event.emit("TaskDerive", task, revised, single)

        # Add task to memory
        if add_to_memory:
            # print(f"DC.derivedTask: {task}")
            self.add_task(task, "Derived")

        return True

    def double_premise_task_revised(self, new_content: Term, new_truth: TruthValue, new_budget: BudgetValue, counter: int) -> bool:
        """
        双前提规则共享的最终操作(StructuralRules除外)。

        参数:
            new_content: 任务中句子的内容
            new_truth: 任务中句子的真值
            new_budget: 任务的预算值
            counter: 计数器

        返回:
            bool: 成功添加返回True
        """
        derived_stamp = self.get_the_new_stamp().clone()
        self.reset_occurrence_time()  # stamp was already absorbed

        is_counter_valid = counter != -1
        conclusion_term = new_content
        if is_counter_valid:
            # assert new_content is implication
            conclusion_subject = new_content.get_subject()
            conclusion_predicate = new_content.get_predicate()
            conclusion_term = Implication([conclusion_subject, conclusion_predicate], new_content.get_temporal_order(), counter)

        new_sentence = Sentence(
            conclusion_term,
            self.get_current_task().sentence.punctuation,
            new_truth,
            derived_stamp
        )

        new_sentence.is_revised = True
        new_budget.set_priority(0.999)  # Set priority to highest

        new_task = Task(new_sentence, new_budget, self.get_current_belief())

        return self.derived_task(new_task, True, False, True)  # allows overlap since overlap was already checked on revisable

    def double_premise_task(self, new_content: Term, new_truth: TruthValue, new_budget: BudgetValue,
                           temporal_induction: bool, overlap_allowed: bool, add_to_memory: bool = True) -> List[Task]:
        """
        双前提规则共享的最终操作(StructuralRules除外)。

        参数:
            new_content: 任务中句子的内容
            new_truth: 任务中句子的真值
            new_budget: 任务的预算值
            temporal_induction: 任务是否来自时间归纳
            overlap_allowed: 是否允许证据重叠
            add_to_memory: 是否将任务添加到内存

        返回:
            List[Task]: 派生出的任务列表
        """
        ret = []
        if new_content is None or not new_budget.above_threshold():
            return None

        if (new_content is not None and not isinstance(new_content, Interval) and
            not isinstance(new_content, Variable)):

            if new_content.subject_or_predicate_is_independent_var():
                return None

            derive_stamp = self.get_the_new_stamp().clone()  # because occurrence time will be reset
            self.reset_occurrence_time()  # stamp was already absorbed into task

            new_sentence = Sentence(
                new_content,
                self.get_current_task().sentence.punctuation,
                new_truth,
                derive_stamp
            )

            new_sentence.produced_by_temporal_induction = temporal_induction
            new_task = Task(new_sentence, new_budget, self.get_current_belief())

            if new_task is not None:
                added = self.derived_task(new_task, False, False, overlap_allowed, add_to_memory)
                if added:
                    ret.append(new_task)

            # "Since in principle it is always valid to eternalize a tensed belief"
            if temporal_induction and self.narParameters.IMMEDIATE_ETERNALIZATION:
                # temporal induction generated ones get eternalized directly
                truth_et = TruthFunctions.eternalize(new_truth, self.narParameters)
                st = derive_stamp.clone()
                st.set_eternal()
                new_sentence = Sentence(
                    new_content,
                    self.get_current_task().sentence.punctuation,
                    truth_et,
                    st
                )

                new_sentence.produced_by_temporal_induction = temporal_induction
                new_task = Task(new_sentence, new_budget, self.get_current_belief())
                if new_task is not None:
                    added = self.derived_task(new_task, False, False, overlap_allowed, add_to_memory)
                    if added:
                        ret.append(new_task)

            return ret

        return None

    def single_premise_task(self, new_content: Term, new_truth: TruthValue, new_budget: BudgetValue) -> bool:
        """
        单前提规则共享的最终操作(StructuralRules中调用)。

        参数:
            new_content: 任务中句子的内容
            new_truth: 任务中句子的真值
            new_budget: 任务的预算值

        返回:
            bool: 任务添加成功返回True
        """
        return self.single_premise_task_with_punctuation(new_content, self.get_current_task().sentence.punctuation,
                                                       new_truth, new_budget)

    def single_premise_task_with_punctuation(self, new_content: Term, punctuation: str,
                                           new_truth: TruthValue, new_budget: BudgetValue) -> bool:
        """
        单前提规则共享的最终操作(StructuralRules中调用)。

        参数:
            new_content: 任务中句子的内容
            punctuation: 任务中句子的标点符号
            new_truth: 任务中句子的真值
            new_budget: 任务的预算值

        返回:
            bool: 任务添加成功返回True
        """
        if not new_budget.above_threshold():
            return False

        task_sentence = self.get_current_task().sentence
        if task_sentence.is_goal() or task_sentence.is_judgment() or self.get_current_belief() is None:
            self.set_the_new_stamp(Stamp(task_sentence.stamp, self.get_time()))
        else:
            # to answer a question with negation in NAL-5
            self.set_the_new_stamp(Stamp(self.get_current_belief().stamp, self.get_time()))

        if new_content.subject_or_predicate_is_independent_var():
            return False
        if isinstance(new_content, Interval):
            return False

        derive_stamp = self.get_the_new_stamp().clone()
        self.reset_occurrence_time()  # stamp was already absorbed into task

        new_sentence = Sentence(
            new_content,
            punctuation,
            new_truth,
            derive_stamp
        )
        from linars.org.opennars.entity.task import EnumType
        new_task = Task(new_sentence, new_budget, EnumType.DERIVED)
        if new_task is not None:
            return self.derived_task(new_task, False, True, False)

        return False

    def single_premise_task_with_sentence(self, new_sentence: Sentence, new_budget: BudgetValue) -> bool:
        """
        单前提规则共享的最终操作(StructuralRules中调用)。

        参数:
            new_sentence: 任务中的句子
            new_budget: 任务的预算值

        返回:
            bool: 任务添加成功返回True
        """
        if not new_budget.above_threshold():
            return False
        from linars.org.opennars.entity.task import EnumType
        new_task = Task(new_sentence, new_budget, EnumType.DERIVED)
        return self.derived_task(new_task, False, True, False)

    def get_time(self) -> int:
        """
        获取当前时间。

        返回:
            int: 当前时间
        """
        return self.time.time()

    def get_new_stamp(self) -> Stamp:
        """
        获取新时间戳。

        返回:
            Stamp: 新时间戳对象
        """
        return self.new_stamp

    def set_new_stamp(self, new_stamp: Stamp):
        """
        Set the new stamp.

        Args:
            new_stamp: The new stamp
        """
        self.new_stamp = new_stamp

    def get_current_task(self) -> Task:
        """
        获取当前任务。

        返回:
            Task: 当前任务对象
        """
        return self.current_task

    def set_current_task(self, current_task: Task):
        """
        设置当前任务。

        参数:
            current_task: 当前任务对象
        """
        if current_task is None:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning("Attempted to set current_task to None in DerivationContext")
            # 不设置为 None，保持原来的值
            return

        self.current_task = current_task

    def set_current_concept(self, current_concept):
        """
        设置当前概念。

        参数:
            current_concept: 当前概念对象
        """
        self.current_concept = current_concept

    def get_the_new_stamp(self) -> Stamp:
        """
        获取创建的时间戳。

        返回:
            Stamp: 创建的时间戳对象
        """
        if self.new_stamp is None:
            # if new_stamp is None then new_stamp_builder must be available
            self.new_stamp = self.new_stamp_builder.build()
            self.original_time = self.new_stamp.get_occurrence_time()
            self.new_stamp_builder = None

        return self.new_stamp

    def reset_occurrence_time(self):
        """重置发生时间。"""
        self.new_stamp.set_occurrence_time(self.original_time)

    def set_the_new_stamp(self, new_stamp: Stamp) -> Stamp:
        """
        设置新的时间戳。

        参数:
            new_stamp: 新的时间戳对象

        返回:
            Stamp: 新的时间戳对象
        """
        self.new_stamp = new_stamp
        self.new_stamp_builder = None
        return new_stamp

    def copy(self) -> 'DerivationContext':
        """
        复制派生上下文。

        返回:
            DerivationContext: 复制后的派生上下文
        """
        ret = DerivationContext(self.memory, self.narParameters, self.time)
        ret.current_belief = self.current_belief
        ret.current_belief_link = self.current_belief_link
        ret.current_concept = self.current_concept
        ret.current_task = self.current_task
        ret.current_task_link = self.current_task_link
        ret.current_term = self.current_term
        ret.new_stamp = self.new_stamp
        ret.new_stamp_builder = self.new_stamp_builder
        return ret

    def set_the_new_stamp_builder(self, first: Stamp, second: Stamp, time: int):
        """
        设置新的时间戳构建器。

        参数:
            first: 第一个时间戳
            second: 第二个时间戳
            time: 时间值
        """
        self.new_stamp = None

        class Builder(self.StampBuilder):
            def __init__(self, first, second, time, narParameters):
                self.first = first
                self.second = second
                self.time = time
                self.narParameters = narParameters

            def build(self) -> Stamp:
                return Stamp(self.first, self.second, self.time, self.narParameters)

        self.new_stamp_builder = Builder(first, second, time, self.narParameters)

    def get_current_belief(self) -> Sentence:
        """
        获取当前信念。

        返回:
            Sentence: 当前信念对象
        """
        return self.current_belief

    def set_current_belief(self, current_belief: Sentence):
        """
        设置当前信念。

        参数:
            current_belief: 当前信念对象
        """
        self.current_belief = current_belief

    def get_current_belief_link(self) -> TermLink:
        """
        获取当前信念链接。

        返回:
            TermLink: 当前信念链接对象
        """
        return self.current_belief_link

    def set_current_belief_link(self, current_belief_link: TermLink):
        """
        设置当前信念链接。

        参数:
            current_belief_link: 当前信念链接对象
        """
        self.current_belief_link = current_belief_link

    def get_current_task_link(self) -> TaskLink:
        """
        获取当前任务链接。

        返回:
            TaskLink: 当前任务链接对象
        """
        return self.current_task_link

    def set_current_task_link(self, current_task_link: TaskLink):
        """
        设置当前任务链接。

        参数:
            current_task_link: 当前任务链接对象
        """
        self.current_task_link = current_task_link

    def get_current_term(self) -> Term:
        """
        获取当前术语。

        返回:
            Term: 当前术语对象
        """
        return self.current_term

    def set_current_term(self, current_term: Term):
        """
        设置当前术语。

        参数:
            current_term: 当前术语对象
        """
        self.current_term = current_term

    def get_current_concept(self):# -> Concept:
        """
        获取当前概念。

        返回:
            Concept: 当前概念对象
        """
        return self.current_concept

    def mem(self):# -> Memory:
        """
        获取内存对象。

        返回:
            Memory: 内存对象
        """
        return self.memory

    def add_task(self, t: Task, reason: str):
        """
        添加任务到内存。

        参数:
            t: 要添加的任务
            reason: 添加原因
        """
        if t.sentence.term is None:
            return
        self.memory.add_new_task(t, reason)

    def add_task_with_budget(self, current_task: Task, budget: BudgetValue, sentence: Sentence, candidate_belief: Sentence):
        """
        在MatchingRules.trySolution和Concept.processGoal中调用的激活任务。

        参数:
            current_task: 当前任务
            budget: 新任务的预算值
            sentence: 新任务的内容
            candidate_belief: 用于未来推理的候选信念
        """
        self.add_task(Task(sentence, budget, sentence, candidate_belief), "Activated")

    def __str__(self) -> str:
        """
        获取字符串表示。

        返回:
            str: 字符串表示
        """
        return f"DerivationContext[{self.current_concept},{self.current_task_link}]"
