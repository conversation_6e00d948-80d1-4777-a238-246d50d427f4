"""
XML loader for factories data.

This module provides a loader for factories data from XML.
"""
import logging
import xml.etree.ElementTree as ET
from typing import Dict, Any, Optional, List, Set

from linars.edu.memphis.ccrg.lida.Framework.Initialization.framework_task_def import FrameworkTaskDef
from linars.edu.memphis.ccrg.lida.Framework.Initialization.linkable_def import LinkableDef
from linars.edu.memphis.ccrg.lida.Framework.Initialization.module_usage import ModuleUsage
from linars.edu.memphis.ccrg.lida.Framework.Initialization.xml_utils import XmlUtils

class FactoriesDataXmlLoader:
    """
    XML loader for factories data.
    
    This class provides methods for loading factories data from XML.
    """
    
    def __init__(self):
        """Initialize the factories data XML loader."""
        self.logger = logging.getLogger(__name__)
        self.task_defs = []
        self.submodule_defs = []
        self.listener_defs = []
    
    def load_factories_data(self, root: ET.Element) -> None:
        """
        Load factories data from an XML element.
        
        Args:
            root: The root XML element
        """
        if root is None:
            return
        
        # Load tasks
        self.load_tasks(root)
        
        # Load submodules
        self.load_submodules(root)
        
        # Load listeners
        self.load_listeners(root)
    
    def load_tasks(self, root: ET.Element) -> None:
        """
        Load task definitions from an XML element.
        
        Args:
            root: The root XML element
        """
        # Find the tasks element
        tasks_element = root.find("tasks")
        if tasks_element is None:
            return
        
        # Load each task
        for task_element in XmlUtils.get_child_elements(tasks_element, "task"):
            try:
                # Get task attributes
                name = XmlUtils.get_attribute(task_element, "name")
                task_class = XmlUtils.get_attribute(task_element, "taskClass")
                ticks_per_run = XmlUtils.get_attribute(task_element, "ticksPerRun")
                
                # Create a task definition
                task_def = FrameworkTaskDef(name, task_class, ticks_per_run)
                
                # Add to the list
                self.task_defs.append(task_def)
            except Exception as e:
                self.logger.error(f"Error loading task: {e}")
    
    def load_submodules(self, root: ET.Element) -> None:
        """
        Load submodule definitions from an XML element.
        
        Args:
            root: The root XML element
        """
        # Find the submodules element
        submodules_element = root.find("submodules")
        if submodules_element is None:
            return
        
        # Load each submodule
        for submodule_element in XmlUtils.get_child_elements(submodules_element, "submodule"):
            try:
                # Get submodule attributes
                name = XmlUtils.get_attribute(submodule_element, "name")
                module_class = XmlUtils.get_attribute(submodule_element, "moduleClass")
                
                # Create a submodule definition
                submodule_def = LinkableDef(name, module_class)
                
                # Add to the list
                self.submodule_defs.append(submodule_def)
            except Exception as e:
                self.logger.error(f"Error loading submodule: {e}")
    
    def load_listeners(self, root: ET.Element) -> None:
        """
        Load listener definitions from an XML element.
        
        Args:
            root: The root XML element
        """
        # Find the listeners element
        listeners_element = root.find("listeners")
        if listeners_element is None:
            return
        
        # Load each listener
        for listener_element in XmlUtils.get_child_elements(listeners_element, "listener"):
            try:
                # Get listener attributes
                source_name = XmlUtils.get_attribute(listener_element, "source")
                listener_name = XmlUtils.get_attribute(listener_element, "listener")
                module_usage = XmlUtils.get_attribute(listener_element, "moduleUsage", ModuleUsage.DEFAULT_USAGE)
                
                # Create a listener definition
                listener_def = ModuleUsage(source_name, listener_name, module_usage)
                
                # Add to the list
                self.listener_defs.append(listener_def)
            except Exception as e:
                self.logger.error(f"Error loading listener: {e}")
    
    def get_task_defs(self) -> List[FrameworkTaskDef]:
        """
        Get the task definitions.
        
        Returns:
            A list of task definitions
        """
        return self.task_defs
    
    def get_submodule_defs(self) -> List[LinkableDef]:
        """
        Get the submodule definitions.
        
        Returns:
            A list of submodule definitions
        """
        return self.submodule_defs
    
    def get_listener_defs(self) -> List[ModuleUsage]:
        """
        Get the listener definitions.
        
        Returns:
            A list of listener definitions
        """
        return self.listener_defs
