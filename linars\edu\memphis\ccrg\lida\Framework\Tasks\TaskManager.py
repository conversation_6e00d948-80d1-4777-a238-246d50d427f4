# LIDA Cognitive Framework
"""
Manager for tasks in the LIDA framework.
"""

import logging
import threading
import time
import concurrent.futures
from typing import Dict, List, Set, Any, Optional
from concurrent.futures import ThreadPoolExecutor
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTask import FrameworkTask, TaskStatus
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawner import TaskSpawner
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule

class TaskManager:
    """
    Manager for tasks in the LIDA framework.
    """

    # Default values
    DEFAULT_TICK_DURATION = 100  # Default tick duration in milliseconds (增加到100ms)
    DEFAULT_SHUTDOWN_TICK = -1  # Default shutdown tick
    DEFAULT_MAX_THREADS = 50  # Default maximum number of threads

    # Class variables
    _current_tick = 0
    _instance = None
    _lock = threading.RLock()

    @staticmethod
    def get_instance() -> 'TaskManager':
        """
        Get the singleton instance of TaskManager.

        Returns:
            The singleton instance of TaskManager
        """
        with TaskManager._lock:
            if TaskManager._instance is None:
                TaskManager._instance = TaskManager()
        return TaskManager._instance

    @staticmethod
    def get_current_tick() -> int:
        """
        Get the current tick.

        Returns:
            The current tick
        """
        return TaskManager._current_tick

    def __init__(self, tick_duration: int = None, max_threads: int = None, shutdown_tick: int = None, post_execution_class: str = None):
        """
        Initialize a TaskManager.

        Args:
            tick_duration: The duration of a tick in milliseconds
            max_threads: The maximum number of threads to use
            shutdown_tick: The tick at which to shut down
            post_execution_class: The class to use for post-execution
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.tick_duration = tick_duration if tick_duration is not None else self.DEFAULT_TICK_DURATION
        max_workers = max_threads if max_threads is not None else self.DEFAULT_MAX_THREADS
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.shutdown_tick = shutdown_tick if shutdown_tick is not None else self.DEFAULT_SHUTDOWN_TICK
        self.post_execution_class = post_execution_class
        self.tasks: Dict[int, FrameworkTask] = {}
        self.task_queue: List[FrameworkTask] = []
        self.modules: Set[FrameworkModule] = set()
        self.running = False
        self.thread = None
        self.agent = None

        # 注册一个退出处理函数，确保在程序退出时关闭线程池
        import atexit
        atexit.register(self.shutdown)

    def set_agent(self, agent) -> None:
        """
        Set the agent for this TaskManager.

        Args:
            agent: The agent to set
        """
        self.agent = agent
        self.logger.debug(f"Agent set in TaskManager: {agent}")

    def set_decaying_modules(self, modules: List[FrameworkModule]) -> None:
        """
        Set the modules that will be decayed by this TaskManager.

        Args:
            modules: The modules to decay
        """
        self.modules = set(modules)

    def set_tick_duration(self, duration: int) -> None:
        """
        Set the duration of a tick in milliseconds.

        Args:
            duration: The duration of a tick in milliseconds
        """
        self.tick_duration = duration

    def get_tick_duration(self) -> int:
        """
        Get the duration of a tick in milliseconds.

        Returns:
            The duration of a tick in milliseconds
        """
        return self.tick_duration

    def resume_tasks(self) -> None:
        """
        Resume execution of tasks.
        """
        # 参考Java源码，在resume_tasks中启动线程
        self.start()
        self.logger.info("All tasks resumed.")

    def pause_tasks(self) -> None:
        """
        Pause execution of tasks.
        """
        self.running = False
        self.logger.info("All tasks paused.")

    def add_task(self, task: FrameworkTask) -> None:
        """
        Add a task to be executed.

        Args:
            task: The task to add
        """
        with TaskManager._lock:
            self.tasks[task.get_task_id()] = task
            self.task_queue.append(task)

    def schedule_task(self, task: FrameworkTask, in_x_ticks: int) -> bool:
        """
        Schedules the task for execution in currentTick + inXTicks.
        If inXTicks is negative or 0, the task is not scheduled.

        Args:
            task: The task to schedule
            in_x_ticks: The number of ticks in the future that the task will be scheduled for execution

        Returns:
            True if the task was scheduled, False otherwise
        """
        if task is None:
            self.logger.warning(f"Cannot schedule a null task at tick {TaskManager.get_current_tick()}")
            return False

        if in_x_ticks < 1:
            self.logger.warning(f"Task {task} was scheduled with in_x_ticks of {in_x_ticks} but this must be 1 or greater at tick {TaskManager.get_current_tick()}")
            return False

        with TaskManager._lock:
            if task.get_task_id() not in self.tasks:
                self.tasks[task.get_task_id()] = task

            if task not in self.task_queue:
                self.task_queue.append(task)

        return True

    def cancel_task(self, task: FrameworkTask) -> None:
        """
        Cancel a task.

        Args:
            task: The task to cancel
        """
        with TaskManager._lock:
            if task.get_task_id() in self.tasks:
                task.cancel()
                del self.tasks[task.get_task_id()]
                if task in self.task_queue:
                    self.task_queue.remove(task)

    def add_module(self, module: FrameworkModule) -> None:
        """
        Add a module to be decayed.

        Args:
            module: The module to add
        """
        with TaskManager._lock:
            self.modules.add(module)

    def remove_module(self, module: FrameworkModule) -> None:
        """
        Remove a module from being decayed.

        Args:
            module: The module to remove
        """
        with TaskManager._lock:
            if module in self.modules:
                self.modules.remove(module)

    def start(self) -> None:
        """
        Start the task manager.
        """
        with TaskManager._lock:
            if not self.running or self.thread is None or not self.thread.is_alive():
                self.running = True
                self.thread = threading.Thread(target=self._run)
                self.thread.daemon = True
                self.thread.start()
                self.logger.info("TaskManager thread started")

    def stop(self) -> None:
        """
        Stop the task manager.
        """
        with TaskManager._lock:
            self.running = False
            if self.thread is not None:
                self.thread.join()
                self.thread = None

    def shutdown(self) -> None:
        """
        Shutdown the task manager and clean up resources.
        """
        self.logger.info("Shutting down TaskManager...")
        self.stop()

        # 关闭线程池
        if hasattr(self, 'executor') and self.executor is not None:
            self.executor.shutdown(wait=False)
            self.logger.info("ThreadPoolExecutor shutdown.")

        self.logger.info("TaskManager shutdown complete.")

    def _run(self) -> None:
        """
        Run the task manager.
        """
        self.logger.info("TaskManager thread started")
        while self.running:
            start_time = time.time()

            # Increment the tick
            with TaskManager._lock:
                TaskManager._current_tick += 1
                current_tick = TaskManager._current_tick

            if current_tick % 100 == 0:  # 每100个tick输出一次日志
                self.logger.info(f"TaskManager tick: {current_tick}, tasks in queue: {len(self.task_queue)}")

            # 如果current_tick超过500，则直接退出循环，因为调试已足够
            # if current_tick > 500:
            #     self.logger.warning(f"current_tick超过500，则直接退出循环, tasks in queue: {len(self.task_queue)}")
            #     break

            # Decay modules
            for module in list(self.modules):
                try:
                    module.task_manager_decay_module(1)
                except Exception as e:
                    self.logger.warning(f"Exception occurred during module decay at tick {current_tick}: {e}")
                    import traceback
                    traceback.print_exc()

            # Execute tasks
            with TaskManager._lock:
                tasks_to_execute = [task for task in self.task_queue if current_tick % task.get_ticks_per_run() == 0]
                if tasks_to_execute:
                    self.logger.debug(f"Executing {len(tasks_to_execute)} tasks at tick {current_tick}")

            for task in tasks_to_execute:
                try:
                    # 尝试使用线程池执行任务
                    try:
                        future = self.executor.submit(task.call)
                        result = future.result()
                    except RuntimeError as re:
                        # 如果是线程池关闭异常，直接调用任务
                        if "cannot schedule new futures after shutdown" in str(re) or "cannot schedule new futures after interpreter shutdown" in str(re):
                            self.logger.warning(f"Exception occurred during task execution at tick {current_tick}: {re}")
                            # 重新创建线程池
                            if self.executor._shutdown:
                                self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=TaskManager.DEFAULT_MAX_THREADS)
                                self.logger.info(f"Recreated thread pool at tick {current_tick}")
                            # 直接调用任务
                            result = task.call()
                        else:
                            self.logger.error(f"Unexpected RuntimeError at tick {current_tick}: {re}")
                            raise

                    if result and result.get_task_status() == TaskStatus.CANCELED:
                        with TaskManager._lock:
                            if task.get_task_id() in self.tasks:
                                del self.tasks[task.get_task_id()]
                                if task in self.task_queue:
                                    self.task_queue.remove(task)

                    task_spawner = task.get_controlling_task_spawner()
                    if task_spawner is not None:
                        task_spawner.receive_finished_task(task)
                except Exception as e:
                    self.logger.warning(f"Exception occurred during task execution at tick {current_tick}: {e}")
                    # 如果是解释器关闭异常，不打印堆栈跟踪
                    if not (isinstance(e, RuntimeError) and "cannot schedule new futures after interpreter shutdown" in str(e)):
                        import traceback
                        traceback.print_exc()

            # Sleep until the next tick
            elapsed_time = time.time() - start_time
            sleep_time = self.tick_duration / 1000.0 - elapsed_time
            if sleep_time > 0:
                time.sleep(sleep_time)
