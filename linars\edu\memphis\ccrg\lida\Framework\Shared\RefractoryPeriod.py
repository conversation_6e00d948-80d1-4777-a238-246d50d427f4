# LIDA Cognitive Framework
"""
Interface for objects that have a refractory period.
"""

from abc import ABC, abstractmethod

class RefractoryPeriod(ABC):
    """
    Interface for objects that have a refractory period.
    The unit of the time period is in ticks, the unit of time in the framework.
    """
    
    @abstractmethod
    def set_refractory_period(self, ticks: int) -> None:
        """
        Sets the refractory period.
        
        Args:
            ticks: Length of refractory period in ticks
        """
        pass
    
    @abstractmethod
    def get_refractory_period(self) -> int:
        """
        Gets the refractory period.
        
        Returns:
            Length of refractory period in ticks
        """
        pass
