"""
Object detector implementation.

This module provides the implementation of the object detector for the ALife agent.
"""
import logging
from typing import Dict, Any, Optional, Set

from linars.edu.memphis.ccrg.alife.elements.alife_object import ALifeObject
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.PAM.Tasks.MultipleDetectionAlgorithm import MultipleDetectionAlgorithm
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemory import SensoryMemory
from linars.edu.memphis.ccrg.lida.Environment.Environment import Environment

class ObjectDetector(MultipleDetectionAlgorithm):
    """
    Object detector implementation.

    This class implements a detector that identifies objects in the agent's environment.
    """

    def __init__(self):
        """Initialize the object detector."""
        super().__init__()
        self.logger = logging.getLogger("ObjectDetector")
        self.detector_params = {}
        self.object_nodes = {}
        self.sensory_memory = None
        self.pam_memory = None
        self.environment = None

    def init(self, params=None):
        """
        Initialize the detector.

        Args:
            params: Optional parameters for initialization
        """
        super().init(params)

        # Store parameters if provided
        if params:
            for key, value in params.items():
                self.detector_params[key] = value

        # Log initialization
        self.logger.info(f"Initialized ObjectDetector with params: {self.detector_params}")

    def detect_linkables(self) -> Set[NodeStructure]:
        """
        Detect objects in the environment.

        Returns:
            A set of node structures containing the detected objects
        """
        results = set()

        # 检查 sensory_memory 是否为 None
        if self.sensory_memory is None:
            self.logger.warning("Sensory memory is not set for ObjectDetector")
            return results

        try:
            # Get objects in the current cell
            params_this = {"mode": "seethis"}
            objects_this = self.sensory_memory.get_state(params_this)

            # Get objects in the next cell
            params_next = {"mode": "seenext"}
            objects_next = self.sensory_memory.get_state(params_next)

            # Process objects in the current cell
            if objects_this:
                this_cell_ns = self.process_objects(objects_this, "this")
                if this_cell_ns:
                    results.add(this_cell_ns)

            # Process objects in the next cell
            if objects_next:
                next_cell_ns = self.process_objects(objects_next, "next")
                if next_cell_ns:
                    results.add(next_cell_ns)
        except Exception as e:
            self.logger.warning(f"Error in detect_linkables: {e}")

        return results

    def process_objects(self, objects: Set[ALifeObject], location: str) -> Optional[NodeStructure]:
        """
        Process a set of objects and create a node structure.

        Args:
            objects: The objects to process
            location: The location of the objects ("this" or "next")

        Returns:
            A node structure containing the processed objects
        """
        if not objects:
            return None

        # 检查 pam_memory 是否为 None
        if self.pam_memory is None:
            self.logger.warning("PAM memory is not set for ObjectDetector")
            return None

        try:
            result = NodeStructureImpl()

            # Create a node for the location
            location_node = self.get_or_create_node(f"{location}Cell")
            location_node.set_activation(1.0)
            result.add_node(location_node)

            # Process each object
            for obj in objects:
                obj_name = obj.get_name()
                if not obj_name:
                    continue

                # Create a node for the object type
                obj_type_node = self.get_or_create_node(obj_name)
                obj_type_node.set_activation(1.0)
                result.add_node(obj_type_node)

                # Create a link between location and object
                self.pam_memory.create_link(location_node, obj_type_node, "contains")

                # Add special properties for certain objects
                if "food" in obj_name.lower():
                    food_node = self.get_or_create_node("food")
                    food_node.set_activation(1.0)
                    result.add_node(food_node)
                    self.pam_memory.create_link(obj_type_node, food_node, "is")

                if "predator" in obj_name.lower():
                    danger_node = self.get_or_create_node("danger")
                    danger_node.set_activation(1.0)
                    result.add_node(danger_node)
                    self.pam_memory.create_link(obj_type_node, danger_node, "is")

            return result
        except Exception as e:
            self.logger.warning(f"Error in process_objects: {e}")
            return None

    def get_or_create_node(self, name: str) -> Node:
        """
        Get a node by name or create it if it doesn't exist.

        Args:
            name: The name of the node

        Returns:
            The node
        """
        if name in self.object_nodes:
            return self.object_nodes[name]

        # 检查 pam_memory 是否为 None
        if self.pam_memory is None:
            self.logger.warning(f"PAM memory is not set for ObjectDetector, cannot get or create node: {name}")
            node = NodeImpl(name)
            self.object_nodes[name] = node
            return node

        try:
            node = self.pam_memory.get_node(name)
            if node is None:
                node = NodeImpl(name)
                self.pam_memory.add_node(node)

            self.object_nodes[name] = node
            return node
        except Exception as e:
            self.logger.warning(f"Error in get_or_create_node for {name}: {e}")
            node = NodeImpl(name)
            self.object_nodes[name] = node
            return node

    def detect(self) -> float:
        """
        Detect objects in the environment.

        Returns:
            A value from 0.0 to 1.0 representing the degree to which objects are detected.
        """
        # Get objects in the current cell
        params_this = {"mode": "seethis"}
        objects_this = None

        try:
            if self.sensory_memory is not None:
                objects_this = self.sensory_memory.get_state(params_this)
        except Exception as e:
            self.logger.warning(f"Error getting objects in current cell: {e}")

        # Get objects in the next cell
        params_next = {"mode": "seenext"}
        objects_next = None

        try:
            if self.sensory_memory is not None:
                objects_next = self.sensory_memory.get_state(params_next)
        except Exception as e:
            self.logger.warning(f"Error getting objects in next cell: {e}")

        # Calculate detection value based on number of objects
        detection_value = 0.0

        if objects_this and isinstance(objects_this, (list, set)):
            detection_value += min(1.0, len(objects_this) * 0.2)

        if objects_next and isinstance(objects_next, (list, set)):
            detection_value += min(1.0, len(objects_next) * 0.1)

        # Normalize detection value
        detection_value = min(1.0, detection_value)

        return detection_value

    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module.

        Args:
            module: The module to associate
            module_usage: The usage of the module
        """
        if module_usage == "PAMemory" or module_usage == "PAM":
            self.pam_memory = module
            self.logger.info(f"Set PAM memory: {module}")
        elif module_usage == "SensoryMemory":
            self.sensory_memory = module
            self.logger.info(f"Set sensory memory: {module}")
        elif module_usage == "Environment":
            self.environment = module
            self.logger.info(f"Set environment: {module}")
        else:
            self.logger.warning(f"Cannot set associated module {module}, type not recognized")