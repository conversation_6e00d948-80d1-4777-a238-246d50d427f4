# LIDA Cognitive Framework
"""
Default implementation of StructureBuildingCodelet. Checks for sought
content in all accessible WorkspaceBuffers and adds all buffer
content to the Current Situational Model.
"""

import logging
from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceContent import WorkspaceContent
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WorkspaceBuffer import WorkspaceBuffer
from linars.edu.memphis.ccrg.lida.Workspace.StructureBuildingCodelets.StructureBuildingCodeletImpl import StructureBuildingCodeletImpl

class BasicStructureBuildingCodelet(StructureBuildingCodeletImpl):
    """
    Default implementation of StructureBuildingCodelet. Checks for sought
    content in all accessible WorkspaceBuffers and adds all buffer
    content to the Current Situational Model.
    """
    
    def __init__(self, ticks_per_run: int = 1):
        """
        Initialize a BasicStructureBuildingCodelet.
        
        Args:
            ticks_per_run: The number of ticks between runs of this codelet
        """
        super().__init__(ticks_per_run)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def run_this_framework_task(self) -> None:
        """
        Run this codelet.
        """
        self.logger.debug(f"SB codelet {self} being run at tick {TaskManager.get_current_tick()}")
        
        for readable_buffer in self.readable_buffers.values():
            if self.buffer_contains_sought_content(readable_buffer):
                self.writable_buffer.add_buffer_content(self.retrieve_workspace_content(readable_buffer))
        
        self.logger.debug(f"SB codelet {self} finishes one run at tick {TaskManager.get_current_tick()}")
    
    def retrieve_workspace_content(self, buffer: WorkspaceBuffer) -> NodeStructure:
        """
        Retrieves content from the specified WorkspaceBuffer.
        
        Args:
            buffer: The WorkspaceBuffer to retrieve content from Returns:
            The retrieved content
        """
        return buffer.get_buffer_content(None)
    
    def buffer_contains_sought_content(self, buffer: WorkspaceBuffer) -> bool:
        """
        Returns true if specified WorkspaceBuffer contains this codelet's sought content.
        
        Args:
            buffer: The WorkspaceBuffer to be checked for content
            
        Returns:
            True if the buffer contains the sought content, False otherwise
        """
        ns = buffer.get_buffer_content(None)
        
        for linkable in self.sought_content.get_linkables():
            if not ns.contains_linkable(linkable):
                return False
        
        self.logger.debug(f"SB codelet {self} found sought content at tick {TaskManager.get_current_tick()}")
        return True
