# # LIDA认知框架
# """
# 节点的默认实现
# """
#
import logging
from typing import Dict, Any, Optional

from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.linars.term import Term


class NodeImpl(Term, Node):
#     """
#     节点的默认实现
#     """
#
    # Class variable for ID generator
    _id_generator = 0

    def __init__(self, name: str = None):
        """
        初始化节点实现

        参数:
            name: 节点名称
        """
        super().__init__(name)
        self.node_id = NodeImpl._id_generator
        NodeImpl._id_generator += 1
        self.node_name = name
        # self.location = ""
        # self.grounding_pam_node = self
        # self.truth = 0
        # self.bcastid = ""
        # self.last_act = None
        # self.logger = logging.getLogger(self.__class__.__name__)

#     def get_node_id(self) -> int:
#         """
#         获取节点ID
#
#         返回:
#             节点ID
#         """
#         return self.node_id
#
#     def set_node_id(self, id: int) -> None:
#         """
#         设置节点ID
#
#         参数:
#             id: 要设置的ID
#         """
#         self.node_id = id
#
#     def setId(self, id: int) -> None:
#         """
#         设置节点ID(set_node_id的别名)
#
#         参数:
#             id: 要设置的ID
#         """
#         self.set_node_id(id)
#
#     def getId(self) -> int:
#         """
#         获取节点ID(get_node_id的别名)
#
#         返回:
#             节点ID
#         """
#         return self.get_node_id()
#
#     def get_id(self) -> int:
#         """
#         获取节点ID(get_node_id的别名)
#
#         返回:
#             节点ID
#         """
#         return self.get_node_id()
#
#     def get_node_name(self) -> str:
#         """
#         获取节点名称
#         返回:
#             节点名称
#         """
#         return self.node_name
#
#     def set_node_name(self, name: str) -> None:
#         """
#         设置节点名称
#         参数:
#             name: 要设置的名称
#         """
#         self.node_name = name
#
#     def get_location(self) -> str:
#         """
#         获取节点位置
#         返回:
#             节点位置
#         """
#         return self.location
#
#     def set_location(self, location: str) -> None:
#         """
#         设置节点位置
#         参数:
#             location: 要设置的位置
#         """
#         self.location = location
#
#     def get_grounding_pam_node(self) -> PamNode:
#         """
#         获取节点的基础PamNode
#
#         返回:
#             基础PamNode
#         """
#         return self.grounding_pam_node
#
#     def set_grounding_pam_node(self, node: PamNode) -> None:
#         """
#         设置节点的基础PamNode
#
#         参数:
#             node: 要设置的基础PamNode
#         """
#         self.grounding_pam_node = node
#
#     def update_node_values(self, node: Node) -> None:
#         """
#         Node的子类应重写此方法，使用指定Node的值设置其所有类型特定的成员数据
#         指定的Node必须是相同的子类类型
#
#         参数:
#             node: 用于更新值的节点
#         """
#         # This method should be overridden by subclasses
#         pass
#
#     def get_truth(self) -> int:
#         """
#         获取节点的真值
#         返回:
#             节点真值
#         """
#         return self.truth
#
#     def set_truth(self, truth: int) -> None:
#         """
#         设置节点的真值
#         参数:
#             truth: 要设置的真值
#         """
#         self.truth = truth
#
#     def get_bcastid(self) -> str:
#         """
#         获取节点的广播ID
#
#         返回:
#             节点广播ID
#         """
#         return self.bcastid
#
#     def set_bcastid(self, bcastid: str) -> None:
#         """
#         设置节点的广播ID
#
#         参数:
#             bcastid: 要设置的广播ID
#         """
#         self.bcastid = bcastid
#
#     def get_last_act(self) -> Optional[str]:
#         """
#         获取节点的最后激活值
#
#         返回:
#             节点最后激活值
#         """
#         return self.last_act
#
#     def set_last_act(self, last_act: str) -> None:
#         """
#         设置节点的最后激活值
#
#         参数:
#             last_act: 要设置的最后激活值
#         """
#         self.last_act = last_act
#
#     def get_extended_id(self) -> ExtendedId:
#         """
#         获取扩展ID
#
#         返回:
#             可链接对象的通用ID
#         """
#         return ExtendedId(self.node_id)
#
#     def is_node(self) -> bool:
#         """
#         返回此可链接对象是否为节点
#
#         返回:
#             如果是节点返回True，否则返回False
#         """
#         return True
#
#     def is_satisfied(self, content: NodeStructure) -> bool:
#         """
#         检查此条件是否被给定内容满足
#
#         参数:
#             content: 要检查的内容
#
#         返回:
#             如果条件满足返回True，否则返回False
#         """
#         if content is None:
#             return False
#
#         for node in content.get_nodes():
#             if node.get_node_id() == self.node_id:
#                 return True
#
#         return False
#
#     def get_condition_name(self) -> str:
#         """
#         获取此条件的名称
#
#         返回:
#             条件名称
#         """
#         return self.node_name
#
#     def set_condition_name(self, name: str) -> None:
#         """
#         设置此条件的名称
#
#         参数:
#             name: 要设置的名称
#         """
#         self.node_name = name
#
#     def __eq__(self, other) -> bool:
#         """
#         检查此节点是否与另一个相等
#
#         参数:
#             other: 要比较的另一个节点
#
#         返回:
#             如果节点具有相同的ID则返回True，否则返回False
#         """
#         if isinstance(other, Node):
#             return self.node_id == other.get_node_id()
#         return False
#
#     def __hash__(self) -> int:
#         """
#         返回此节点的哈希码
#
#         返回:
#             节点ID的哈希码
#         """
#         return self.node_id
#
#     def __str__(self) -> str:
#         """
#         返回此节点的字符串表示
#
#         返回:
#             节点的字符串表示
#         """
#         return f"{self.node_name}[{self.node_id}]"
