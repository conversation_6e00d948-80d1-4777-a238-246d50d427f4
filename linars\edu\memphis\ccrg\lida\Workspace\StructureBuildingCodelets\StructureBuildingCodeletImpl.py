# LIDA Cognitive Framework
"""
Basic implementation of StructureBuildingCodelet.
"""

import logging
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Tasks.CodeletImpl import CodeletImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WorkspaceBuffer import WorkspaceBuffer
from linars.edu.memphis.ccrg.lida.Workspace.StructureBuildingCodelets.StructureBuildingCodelet import StructureBuildingCodelet

class StructureBuildingCodeletImpl(CodeletImpl, StructureBuildingCodelet):
    """
    Basic implementation of StructureBuildingCodelet.
    """
    
    def __init__(self, ticks_per_run: int = 1):
        """
        Initialize a StructureBuildingCodeletImpl.
        
        Args:
            ticks_per_run: The number of ticks between runs of this codelet
        """
        super().__init__(ticks_per_run)
        self.readable_buffers: Dict[ModuleName, WorkspaceBuffer] = {}
        self.writable_buffer = None
        self.run_results = {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module for this codelet.
        
        Args:
            module: The module to associate with this codelet
            module_usage: How this codelet will use the module
        """
        if isinstance(module, WorkspaceBuffer):
            if module_usage == "TO_READ_FROM":
                self.readable_buffers[module.get_module_name()] = module
            elif module_usage == "TO_WRITE_TO":
                self.writable_buffer = module
            else:
                self.logger.warning(f"Specified usage is not supported. See ModuleUsage at tick {TaskManager.get_current_tick()}")
        else:
            self.logger.warning(f"Expected module to be a WorkspaceBuffer but it was not. Module not added at tick {TaskManager.get_current_tick()}")
    
    def run_this_framework_task(self) -> None:
        """
        Run this codelet.
        """
        # This method should be overridden by subclasses
        pass
    
    def reset(self) -> None:
        """
        Clears this codelet's fields in preparation for reuse. Idea is that the
        same codelet object is reconfigured at runtime after it finishes to be
        run as a different altogether codelet.
        """
        self.set_ticks_per_run(1)
        self.set_activation(0.0)
        
        self.readable_buffers.clear()
        self.writable_buffer = None
        self.sought_content = None
    
    def get_codelet_run_result(self) -> object:
        """
        Returns result of codelet's run.
        
        Returns:
            Current information about the codelet's progress
        """
        return self.run_results
    
    def buffer_contains_sought_content(self, buffer: WorkspaceBuffer) -> bool:
        """
        Returns true if specified WorkspaceBuffer contains this codelet's sought content.
        
        Args:
            buffer: The WorkspaceBuffer to be checked for content
            
        Returns:
            True if the buffer contains the sought content, False otherwise
        """
        # This method should be overridden by subclasses
        return False
    
    def retrieve_workspace_content(self, buffer: WorkspaceBuffer) -> NodeStructure:
        """
        Retrieves content from the specified WorkspaceBuffer.
        
        Args:
            buffer: The WorkspaceBuffer to retrieve content from Returns:
            The retrieved content
        """
        # This method should be overridden by subclasses
        return None
