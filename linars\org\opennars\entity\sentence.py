"""
根据NARS理论定义的句子

句子被用作所有推理规则的前提和结论。
"""
import copy
import enum
import functools
import hashlib
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar

from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.entity.stamp import Stamp
from linars.org.opennars.entity.truth_value import TruthValue

# Symbols
JUDGMENT_MARK = '.'
QUESTION_MARK = '?'
GOAL_MARK = '!'
QUEST_MARK = '@'
TERM_NORMALIZING_WORKAROUND_MARK = '$'

T = TypeVar('T', bound=Term)

class Sentence(Generic[T]):
    """
    根据NARS理论定义的句子

    句子被用作所有推理规则的前提和结论。
    """

    def __init__(self, term: T, punctuation: str, truth: Optional[TruthValue], stamp: Stamp, normalize: bool = True):
        """
        使用给定字段创建句子

        参数:
            term: 构成句子内容的术语
            punctuation: 表示句子类型的标点符号
            truth: 句子的真值，问题为None
            stamp: 句子的时间戳，指示其派生时间和基础
            normalize: 是否规范化术语
        """
        self.produced_by_temporal_induction = False

        # Cut interval at end for sentence in serial conjunction, and in between for parallel
        if punctuation != TERM_NORMALIZING_WORKAROUND_MARK:
            if hasattr(term, 'get_temporal_order') and hasattr(term, 'term'):
                # Handle Conjunction with intervals
                # This is a placeholder - actual implementation would depend on Conjunction and Interval handling
                pass

        self.punctuation = punctuation

        if truth is not None:
            # Check for invalid truth values
            if hasattr(term, 'subject_or_predicate_is_independent_var') and term.subject_or_predicate_is_independent_var():
                truth.set_confidence(0.0)

            if hasattr(term, 'is_interval') and term.is_interval() and punctuation != TERM_NORMALIZING_WORKAROUND_MARK:
                truth.set_confidence(0.0)

            if (not self.is_question() and not self.is_quest()) and (truth is None) and punctuation != TERM_NORMALIZING_WORKAROUND_MARK:
                raise ValueError("Judgment and Goal sentences require non-null truth value")
        try:
            if (self.is_question() or self.is_quest()) and punctuation != TERM_NORMALIZING_WORKAROUND_MARK and not stamp.is_eternal():
                stamp.set_eternal()
        except Exception as e:
            print("Stamp is not eternal---", e)

        self.truth = truth
        self.stamp = stamp
        self.revisible = (hasattr(term, 'operator') and term.operator() in ['==>', '<=>']) or not (hasattr(term, 'has_var_dep') and term.has_var_dep())

        # Variable name normalization
        # TODO: Implement variable normalization
        self.term = term

        # Is the sentence already revised
        self.is_revisied = False

        # Caches the 'get_key()' result
        self.key = None

        # Calculate hash
        try:
            # 安全地获取term的哈希值
            try:
                term_hash = hash(term) if hasattr(term, '__hash__') and term.__hash__ is not None else id(term)
            except Exception:
                term_hash = id(term)  # 使用对象的内存地址作为哈希值

            # 安全地获取truth的哈希值
            try:
                truth_hash = hash(truth) if truth is not None and hasattr(truth, '__hash__') and truth.__hash__ is not None else 0
            except Exception:
                truth_hash = 0 if truth is None else id(truth)

            # 安全地获取occurrence_time
            try:
                occurrence_time = stamp.get_occurrence_time()
            except Exception:
                occurrence_time = 0

            if self.is_not_termlink_normalizer():
                self.hash = hash((term_hash, punctuation, truth_hash, occurrence_time))
            else:
                self.hash = hash((term_hash, punctuation, truth_hash))
        except Exception as e:
            print(f"警告：计算句子哈希值时出错：{e}，使用备用哈希方法")
            # 备用哈希方法，使用对象ID
            self.hash = hash((id(term), punctuation, id(truth) if truth else 0, id(stamp) if stamp else 0))

    def is_not_termlink_normalizer(self) -> bool:
        """
        检查是否不是termlink规范化器

        返回:
            bool: 如果不是termlink规范化器则为True
        """
        return self.punctuation != TERM_NORMALIZING_WORKAROUND_MARK

    def __eq__(self, other: Any) -> bool:
        """
        检查两个句子是否相等

        参数:
            other: 另一个句子

        返回:
            bool: 两个句子是否具有相同内容
        """
        if self is other:
            return True

        if isinstance(other, Sentence):
            if self.hash != other.hash:
                return False

            if self.punctuation != other.punctuation:
                return False

            if self.is_not_termlink_normalizer():
                if self.stamp.get_occurrence_time() != other.stamp.get_occurrence_time():
                    return False

            if self.truth is None:
                if other.truth is not None:
                    return False
            elif other.truth is None:
                return False
            elif self.truth != other.truth:
                return False

            if self.term != other.term:
                return False

            if (hasattr(self.term, 'term_indices') and self.term.term_indices is not None and
                hasattr(other.term, 'term_indices') and other.term.term_indices is not None):
                for i in range(len(self.term.term_indices)):
                    if self.term.term_indices[i] != other.term.term_indices[i]:
                        return False  # Position or scale was different

            return self.stamp.equals(other.stamp, False, True, True)

        return False

    def __hash__(self) -> int:
        """
        生成句子的哈希码

        返回:
            int: 哈希码
        """
        if hasattr(self, 'hash') and self.hash is not None:
            return self.hash

        # 如果哈希值未初始化，使用备用方法计算
        try:
            # 安全地获取各个组件的哈希值
            try:
                term_hash = id(self.term)  # 直接使用对象ID避免调用name()方法
            except Exception:
                term_hash = 0

            try:
                truth_hash = id(self.truth) if self.truth else 0
            except Exception:
                truth_hash = 0

            try:
                occurrence_time = self.stamp.get_occurrence_time() if hasattr(self, 'stamp') and self.stamp else 0
            except Exception:
                occurrence_time = 0

            return hash((term_hash, self.punctuation, truth_hash, occurrence_time))
        except Exception as e:
            print(f"警告：在__hash__方法中计算句子哈希值时出错：{e}")
            return id(self)  # 最后的备用方案：使用对象的内存地址

    def clone(self) -> 'Sentence':
        """
        克隆句子

        返回:
            Sentence: 克隆后的句子
        """
        return self.clone_with_term(self.term)

    def clone_with_eternal(self, make_eternal: bool) -> 'Sentence':
        """
        带永恒选项的克隆

        参数:
            make_eternal: 是否使克隆永恒

        返回:
            Sentence: 克隆后的句子
        """
        clon = self.clone_with_term(self.term)
        if clon.stamp.get_occurrence_time() != Stamp.ETERNAL and make_eternal:
            # Change occurrence time of clone
            clon.stamp.set_eternal()
        return clon

    def clone_with_term(self, t: Term) -> 'Sentence':
        """
        使用不同术语克隆

        参数:
            t: 需要克隆的术语

        返回:
            Sentence: 以克隆术语为属性的句子
        """
        return Sentence(
            t,
            self.punctuation,
            TruthValue.from_truth(self.truth) if self.truth is not None else None,
            self.stamp.clone()
        )

    def projection(self, target_time: int, current_time: int, mem) -> 'Sentence':
        """
        将判断投射到不同的发生时间

        参数:
            target_time: 要投射到的时间
            current_time: 作为参考的当前时间
            mem: 内存

        返回:
            Sentence: 投射后的信念
        """
        new_truth = self.projection_truth(target_time, current_time, mem)
        eternalizing = hasattr(new_truth, 'is_eternalized') and new_truth.is_eternalized()

        new_stamp = (self.stamp.clone_with_new_occurrence_time(Stamp.ETERNAL) if eternalizing
                    else self.stamp.clone_with_new_occurrence_time(target_time))

        return Sentence(
            self.term,
            self.punctuation,
            new_truth,
            new_stamp,
            False
        )

    def projection_truth(self, target_time: int, current_time: int, mem) -> Optional[TruthValue]:
        """
        将真值投射到不同时间

        参数:
            target_time: 要投射到的时间
            current_time: 作为参考的当前时间
            mem: 内存

        返回:
            TruthValue: 投射后的真值，如果无法投射则返回None
        """
        from linars.org.opennars.inference.truth_functions import TruthFunctions
        import logging
        import traceback

        logger = logging.getLogger("Sentence")

        try:
            # 首先检查self.truth是否为None
            if self.truth is None:
                logger.debug(f"投射真值失败: 句子的真值为None，可能是问题句子")
                return None

            # 检查self.stamp是否为None
            if self.stamp is None:
                logger.warning(f"投射真值失败: 句子的时间戳为None")
                return None

            new_truth = None

            try:
                if not self.stamp.is_eternal():
                    new_truth = TruthFunctions.eternalize(self.truth, mem.narParameters)
                    if target_time != Stamp.ETERNAL:
                        occurrence_time = self.stamp.get_occurrence_time()
                        factor = TruthFunctions.temporal_projection(occurrence_time, target_time, current_time, mem.narParameters)
                        projected_confidence = factor * self.truth.get_confidence()
                        if new_truth is not None and projected_confidence > new_truth.get_confidence():
                            new_truth = TruthValue(self.truth.get_frequency(), projected_confidence, mem.narParameters)
            except Exception as e:
                logger.warning(f"投射真值过程中出错: {str(e)}")
                logger.debug(traceback.format_exc())
                # 出错时继续使用默认克隆方法

            if new_truth is None:
                try:
                    new_truth = self.truth.clone()
                    logger.debug(f"使用原始真值的克隆: {new_truth}")
                except Exception as e:
                    logger.error(f"克隆真值时出错: {str(e)}")
                    logger.debug(traceback.format_exc())
                    return None

            return new_truth

        except Exception as e:
            logger.error(f"投射真值方法中发生未处理的异常: {str(e)}")
            logger.debug(traceback.format_exc())
            return None

    def is_judgment(self) -> bool:
        """
        检查句子是否为判断

        返回:
            bool: 如果是判断则为True
        """
        return self.punctuation == JUDGMENT_MARK

    def is_question(self) -> bool:
        """
        检查句子是否为问题

        返回:
            bool: 如果是问题则为True
        """
        return self.punctuation == QUESTION_MARK

    def is_goal(self) -> bool:
        """
        检查句子是否为目标

        返回:
            bool: 如果是目标则为True
        """
        return self.punctuation == GOAL_MARK

    def is_quest(self) -> bool:
        """
        检查句子是否为探索

        返回:
            bool: 如果是探索则为True
        """
        return self.punctuation == QUEST_MARK

    def get_revisible(self) -> bool:
        """
        获取句子的可修订能力

        返回:
            bool: 如果可修订则为True
        """
        return self.revisible

    def set_revisible(self, b: bool):
        """
        设置句子的可修订能力

        参数:
            b: 新的可修订值
        """
        self.revisible = b

    def get_temporal_order(self) -> int:
        """
        获取时间顺序

        返回:
            int: 时间顺序
        """
        return self.term.get_temporal_order()

    def get_occurrence_time(self) -> int:
        """
        获取发生时间

        返回:
            int: 发生时间
        """
        return self.stamp.get_occurrence_time()

    def __str__(self) -> str:
        """
        获取句子的字符串表示

        返回:
            str: 字符串
        """
        return str(self.get_key())

    def get_key(self) -> str:
        """
        获取句子用于Task和TaskLink键的字符串表示

        返回:
            str: 字符串
        """
        if self.key is None:
            try:
                content_name = self.term.name()
            except Exception as e:
                # 如果无法获取term的name，使用安全的替代方案
                try:
                    content_name = str(self.term)
                except Exception:
                    content_name = f"TERM({id(self.term)})"

            show_occurrence_time = (self.punctuation == JUDGMENT_MARK or self.punctuation == QUESTION_MARK)

            conv = ""
            if hasattr(self.term, 'term_indices') and self.term.term_indices is not None:
                try:
                    conv = " [i,j,k,l]=["
                    for i in range(4):  # Skip min sizes
                        conv += str(self.term.term_indices[i]) + ","
                    conv = conv[:-1] + "]"
                except Exception:
                    conv = ""

            # Suffix = [punctuation][ ][truthString][ ][occurrenceTimeString]
            suffix = self.punctuation + conv

            if self.truth is not None:
                try:
                    suffix += " " + str(self.truth)
                except Exception:
                    suffix += f" TRUTH({id(self.truth)})"

            # if show_occurrence_time and self.stamp is not None:
            #     suffix += " " + self.stamp.get_occurrence_time_string()

            self.key = content_name + suffix # "[0]" # suffix

        return self.key

    def set_key(self, string: str):
        """
        设置键

        参数:
            string: 新键
        """
        self.key = string + " " + str(self.truth)

    def to_string_with_nar(self, nar, show_stamp: bool) -> str:
        """
        获取句子的人类可读文本表示

        参数:
            nar: 推理机实例
            show_stamp: 是否将时间戳附加到字符串

        返回:
            str: 文本表示
        """
        content_name = self.term.name()
        t = nar.time()

        diff = self.stamp.get_occurrence_time() - nar.time()
        diffabs = abs(diff)

        time_diff = ""
        if diffabs < nar.narParameters.DURATION:
            time_diff = "|"
        else:
            time_diff = "+" + str(diffabs) if diff > 0 else "-" + str(diffabs)

        if hasattr(nar, 'debug') and nar.debug.TEST:
            time_diff = "!" + str(self.stamp.get_occurrence_time())

        tense_string = ":" + time_diff + ":"
        if self.stamp.get_occurrence_time() == Stamp.ETERNAL:
            tense_string = ""

        stamp_string = self.stamp.name() if show_stamp else ""

        conv = ""
        if hasattr(self.term, 'term_indices') and self.term.term_indices is not None:
            conv = " [i,j,k,l]=["
            for i in range(4):  # Skip min sizes
                conv += str(self.term.term_indices[i]) + ","
            conv = conv[:-1] + "]"

        buffer = content_name + self.punctuation + conv

        if tense_string:
            buffer += " " + tense_string

        if self.truth is not None:
            buffer += " " + str(self.truth)

        if show_stamp:
            buffer += " " + stamp_string

        return buffer

    def discount_confidence(self, narParameters):
        """
        折扣句子的真值

        参数:
            narParameters: NAR参数
        """
        self.truth.set_confidence(self.truth.get_confidence() * narParameters.DISCOUNT_RATE).set_analytic(False)

    def is_eternal(self) -> bool:
        """
        检查句子是否为永恒真
        返回:
            bool: 如果永恒则为True
        """
        return self.stamp.is_eternal()

    def isEternal(self) -> bool:
        """
        检查句子是否为永恒真
        返回:
            bool: 如果永恒则为True
        """
        return self.stamp.is_eternal()

    def get_term(self) -> T:
        """
        获取句子的术语

        返回:
            T: 术语
        """
        return self.term

    def get_truth(self) -> Optional[TruthValue]:
        """
        获取句子的真值

        返回:
            TruthValue: 真值
        """
        return self.truth
