"""
全局初始化器实现

本模块提供了用于初始化对象的全局初始化器
"""
import logging
import importlib
from typing import Dict, Any, Optional, Type, TypeVar, List

from linars.edu.memphis.ccrg.lida.Framework.Initialization.initializer import Initializer
from linars.edu.memphis.ccrg.lida.Framework.Initialization.Initializable import Initializable
from linars.edu.memphis.ccrg.lida.Framework.Initialization.FullyInitializable import FullyInitializable
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule

T = TypeVar('T')

class GlobalInitializer(Initializer):
    """
    全局初始化器实现

    该类提供了用于初始化对象的全局初始化器
    """

    # Singleton instance
    _instance = None

    @classmethod
    def get_instance(cls) -> 'GlobalInitializer':
        """
        获取该初始化器的单例实例

        返回:
            该初始化器的单例实例
        """
        if cls._instance is None:
            cls._instance = GlobalInitializer()
        return cls._instance

    def __init__(self):
        """初始化全局初始化器"""
        self.logger = logging.getLogger(__name__)
        self.module_map = {}
        self.attributes = {}

    def initModule(self, obj: Any, agent: Any, params: Dict[str, Any]) -> None:
        """
        使用给定参数初始化模块

        参数:
            obj: 要初始化的对象
            agent: 代理
            params: 初始化参数
        """
        # GlobalInitializer doesn't need to do anything here
        pass

    def init_object(self, obj: Any, params: Dict[str, Any]) -> None:
        """
        使用给定参数初始化对象

        参数:
            obj: 要初始化的对象
            params: 初始化参数
        """
        if obj is None:
            return

        if isinstance(obj, Initializable):
            obj.init(params)

        if isinstance(obj, FullyInitializable):
            self.init_associated_modules(obj, params)

    def init_associated_modules(self, obj: FullyInitializable, params: Dict[str, Any]) -> None:
        """
        初始化完全可初始化对象的关联模块

        参数:
            obj: 要初始化的对象
            params: 初始化参数
        """
        # Get the class name
        class_name = obj.__class__.__name__

        # Look for module associations in the parameters
        for key, value in params.items():
            if key.startswith(f"{class_name}.module."):
                parts = key.split(".")
                if len(parts) >= 4:
                    module_usage = parts[3]
                    module_class_name = value

                    # Get the module
                    module = self.get_module(module_class_name)
                    if module is not None:
                        # Associate the module with the object
                        obj.set_associated_module(module, module_usage)
                    else:
                        self.logger.warning(f"Could not find module {module_class_name} for {class_name}")

    def get_module(self, module_class_name: str) -> Optional[FrameworkModule]:
        """
        通过类名获取模块

        参数:
            module_class_name: 模块的类名

        返回:
            找到的模块，如果未找到则返回None
        """
        # Check if the module is already in the map
        if module_class_name in self.module_map:
            return self.module_map[module_class_name]

        # Try to create the module
        try:
            # Split the class name into package and class
            parts = module_class_name.split(".")
            class_name = parts[-1]
            package_name = ".".join(parts[:-1])

            # Import the module
            module = importlib.import_module(package_name)

            # Get the class
            class_obj = getattr(module, class_name)

            # Create an instance
            instance = class_obj()

            # Store in the map
            self.module_map[module_class_name] = instance

            return instance
        except (ImportError, AttributeError, Exception) as e:
            self.logger.error(f"Error creating module {module_class_name}: {e}")
            return None

    def get_object(self, type_class: Type[T], params: Dict[str, Any]) -> Optional[T]:
        """
        获取指定类型的对象，并使用给定参数初始化

        参数:
            type_class: 要获取对象的类
            params: 初始化参数

        返回:
            初始化后的指定类型对象，如果创建失败则返回None
        """
        try:
            # Create an instance of the class
            obj = type_class()

            # Initialize the object
            self.init_object(obj, params)

            return obj
        except Exception as e:
            self.logger.error(f"Error creating object of type {type_class.__name__}: {e}")
            return None

    def set_attribute(self, key: str, value: Any) -> None:
        """
        设置全局属性

        参数:
            key: 属性键
            value: 属性值
        """
        self.logger.debug(f"Setting global attribute: {key}={value}")
        self.attributes[key] = value

    def get_attribute(self, key: str, default_value: Any = None) -> Any:
        """
        获取全局属性

        参数:
            key: 属性键
            default_value: 如果未找到属性则返回的默认值

        返回:
            属性值，如果未找到则返回默认值
        """
        return self.attributes.get(key, default_value)

    def clear_attributes(self) -> None:
        """
        清除模块映射和属性字典中的所有属性
        """
        self.module_map.clear()
        self.attributes.clear()
