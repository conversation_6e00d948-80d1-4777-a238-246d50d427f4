"""
AES Utility

This module provides utilities for AES encryption and decryption.
"""
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad

class AESUtil:
    """
    Utility class for AES encryption and decryption
    """
    
    # Default key and IV
    DEFAULT_KEY = "1234567890123456"  # 16 bytes for AES-128
    DEFAULT_IV = "1234567890123456"   # 16 bytes
    
    @staticmethod
    def encrypt(text, key=None, iv=None):
        """
        Encrypt text using AES
        
        Args:
            text: Text to encrypt
            key: Encryption key (16, 24, or 32 bytes)
            iv: Initialization vector (16 bytes)
            
        Returns:
            Base64 encoded encrypted text
        """
        if key is None:
            key = AESUtil.DEFAULT_KEY
        if iv is None:
            iv = AESUtil.DEFAULT_IV
            
        # Ensure key and IV are bytes
        if isinstance(key, str):
            key = key.encode('utf-8')
        if isinstance(iv, str):
            iv = iv.encode('utf-8')
        if isinstance(text, str):
            text = text.encode('utf-8')
            
        # Create cipher
        cipher = AES.new(key, AES.MODE_CBC, iv)
        
        # Pad and encrypt
        padded_data = pad(text, AES.block_size)
        encrypted = cipher.encrypt(padded_data)
        
        # Encode to base64
        return base64.b64encode(encrypted).decode('utf-8')
    
    @staticmethod
    def decrypt(encrypted_text, key=None, iv=None):
        """
        Decrypt text using AES
        
        Args:
            encrypted_text: Base64 encoded encrypted text
            key: Encryption key (16, 24, or 32 bytes)
            iv: Initialization vector (16 bytes)
            
        Returns:
            Decrypted text
        """
        if key is None:
            key = AESUtil.DEFAULT_KEY
        if iv is None:
            iv = AESUtil.DEFAULT_IV
            
        # Ensure key and IV are bytes
        if isinstance(key, str):
            key = key.encode('utf-8')
        if isinstance(iv, str):
            iv = iv.encode('utf-8')
            
        # Decode from base64
        encrypted = base64.b64decode(encrypted_text)
        
        # Create cipher
        cipher = AES.new(key, AES.MODE_CBC, iv)
        
        # Decrypt and unpad
        decrypted = cipher.decrypt(encrypted)
        unpadded = unpad(decrypted, AES.block_size)
        
        return unpadded.decode('utf-8')
