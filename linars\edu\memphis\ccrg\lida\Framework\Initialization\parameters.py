"""
Parameters class for storing various system parameters.

This module provides a class for storing various system parameters such as
sensory modalities, emotions, and attention settings.
"""

class Parameters:
    """
    Parameters class for storing various system parameters.
    
    This class stores parameters related to sensory modalities, emotions,
    and attention settings.
    """
    
    def __init__(self):
        """Initialize the parameters with default values."""
        # Default attention modality
        self.DEFAULT_MODULE = "SEE"
        
        # Reality senses (five senses + text)
        # Values below baseline are inhibited, above are excited
        self.see = 0.5
        self.listen = 0.5
        self.tast = 0.5
        self.smell = 0.5
        # Computer text, may have other format types
        self.textstr = 0.5
        # General body feeling
        self.feel = 0.5
        # Specific body feelings
        self.hungry = 0.5
        self.full = 0.5
        self.sick = 0.5
        
        # Thinking senses
        self.think_see = 0.5
        self.think_listen = 0.5
        self.think_feel = 0.5
        self.think_tast = 0.5
        self.think_smell = 0.5
        self.think_text = 0.5
        
        # Visual attention area
        self.see_area = "middle"
        self.see_area_atten = 0.5
        
        # Auditory attention frequency
        self.listen_hz = "middle"
        self.listen_area_atten = 0.5
        
        # Emotion values
        # Dopamine has baseline, positive feelings above 0, others at 0
        # No inhibition, only activation
        self.happy = 0.05
        self.sad = 0.0
        self.angry = 0.0
        self.guilty = 0.0
        self.scare = 0.0
        self.worry = 0.0
        self.ill = 0.0
        self.hate = 0.0
        self.boring = 0.0
