# LIDA认知框架
"""
Behavior接口的默认实现
"""

from typing import Collection, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.ActivatibleImpl import ActivatibleImpl
from linars.edu.memphis.ccrg.lida.ActionSelection.Behavior import Behavior
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Scheme import Scheme
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Condition import Condition

class BehaviorImpl(ActivatibleImpl, Behavior):
    """
    Behavior接口的默认实现
    """

    def __init__(self):
        """
        初始化BehaviorImpl实例
        """
        super().__init__()
        self.behavior_id = 0
        self.scheme = None
        self.broadcost_id = None

    def get_id(self) -> int:
        """
        获取行为的ID

        返回:
            行为的ID
        """
        return self.behavior_id

    def set_id(self, id: int) -> None:
        """
        设置行为的ID

        参数:
            id: 要设置的ID
        """
        self.behavior_id = id

    def get_name(self) -> str:
        """
        获取行为的名称

        返回:
            行为的名称
        """
        if self.scheme is not None:
            return self.scheme.get_name()
        return None

    def set_name(self, name: str) -> None:
        """
        设置行为的名称

        参数:
            name: 要设置的名称
        """
        # Java版本中未实现此方法
        pass

    def get_scheme(self) -> Scheme:
        """
        获取行为对应的Scheme

        返回:
            行为对应的Scheme
        """
        return self.scheme

    def set_scheme(self, scheme: Scheme) -> None:
        """
        设置行为对应的Scheme

        参数:
            scheme: 要设置的Scheme
        """
        self.scheme = scheme

    def get_context_conditions(self) -> Collection[Condition]:
        """
        获取行为的上下文条件

        返回:
            行为的上下文条件集合
        """
        if self.scheme is not None:
            return self.scheme.get_context_conditions()
        return []

    def get_adding_list(self) -> Collection[Condition]:
        """
        获取行为的添加条件列表

        返回:
            行为的添加条件列表
        """
        if self.scheme is not None:
            return self.scheme.get_adding_list()
        return []

    def get_deleting_list(self) -> Collection[Condition]:
        """
        获取行为的删除条件列表

        返回:
            行为的删除条件列表
        """
        if self.scheme is not None:
            return self.scheme.get_deleting_list()
        return []

    def get_action(self) -> Action:
        """
        获取行为对应的动作

        返回:
            行为对应的动作
        """
        if self.scheme is not None:
            return self.scheme.get_action()
        return None

    def get_label(self) -> str:
        """
        获取行为的标签

        返回:
            行为的标签
        """
        if self.scheme is not None:
            return self.scheme.get_name()
        return None

    def get_broadcost_id(self) -> Optional[int]:
        """
        获取行为的广播ID

        返回:
            行为的广播ID
        """
        return self.broadcost_id

    def set_broadcost_id(self, id: int) -> None:
        """
        设置行为的广播ID

        参数:
            id: 要设置的广播ID
        """
        self.broadcost_id = id

    def __str__(self) -> str:
        """
        返回行为的字符串表示

        返回:
            行为的字符串表示
        """
        if self.scheme is not None:
            return f"{self.scheme.get_name()}-{self.get_id()}"
        return f"Behavior-{self.get_id()}"
