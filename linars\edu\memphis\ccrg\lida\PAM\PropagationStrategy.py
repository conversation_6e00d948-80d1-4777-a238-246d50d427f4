# LIDA Cognitive Framework
"""
Interface for strategies that govern how activation is propagated in PAMemory.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any

class PropagationStrategy(ABC):
    """
    Interface for strategies that govern how activation is propagated in PAMemory.
    """
    
    @abstractmethod
    def get_activation_to_propagate(self, params: Dict[str, Any]) -> float:
        """
        Get the amount of activation to propagate.
        
        Args:
            params: Parameters for calculating the amount of activation to propagate
            
        Returns:
            The amount of activation to propagate
        """
        pass
