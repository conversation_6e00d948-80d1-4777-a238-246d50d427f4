# LIDA Cognitive Framework
"""
A submodule of the Workspace. Managed by WorkspaceImpl.
StructureBuildingCodelet read and write from them.
"""

from abc import abstractmethod
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceContent import WorkspaceContent

class WorkspaceBuffer(FrameworkModule):
    """
    A submodule of the Workspace. Managed by WorkspaceImpl.
    StructureBuildingCodelet read and write from them.
    """
    
    @abstractmethod
    def get_buffer_content(self, params: Optional[Dict[str, Any]] = None) -> WorkspaceContent:
        """
        Gets buffer content based on specified parameters.
        
        Args:
            params: Optional parameters to specify what content is returned
            
        Returns:
            The WorkspaceContent
        """
        pass
    
    @abstractmethod
    def add_buffer_content(self, content: WorkspaceContent) -> None:
        """
        Adds specified content to this workspace buffer.
        
        Args:
            content: The WorkspaceContent to add
        """
        pass
