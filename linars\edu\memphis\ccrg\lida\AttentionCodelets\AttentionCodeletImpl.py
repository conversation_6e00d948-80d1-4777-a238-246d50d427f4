# LIDA Cognitive Framework
"""
Abstract implementation of AttentionCodelet that checks the CSM for desired
content. If this is found it creates a Coalition and adds it to the GlobalWorkspace.
"""

import logging
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Tasks.CodeletImpl import CodeletImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.GlobalWorkspace import GlobalWorkspace
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.CoalitionImpl import CoalitionImpl
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WorkspaceBuffer import WorkspaceBuffer
from linars.edu.memphis.ccrg.lida.AttentionCodelets.AttentionCodelet import AttentionCodelet
from linars.edu.memphis.ccrg.lida.Framework.Shared.RefractoryPeriod import RefractoryPeriod

class AttentionCodeletImpl(CodeletImpl, AttentionCodelet, RefractoryPeriod):
    """
    Abstract implementation of AttentionCodelet that checks the CSM for desired
    content. If this is found it creates a Coalition and adds it to the GlobalWorkspace.
    """
    
    # Default values
    DEFAULT_CODELET_REFRACTORY_PERIOD = 20
    
    def __init__(self, ticks_per_run: int = 1):
        """
        Initialize an AttentionCodeletImpl.
        
        Args:
            ticks_per_run: The number of ticks between runs of this codelet
        """
        super().__init__(ticks_per_run)
        self.codelet_refractory_period = self.DEFAULT_CODELET_REFRACTORY_PERIOD
        self.current_situational_model = None
        self.global_workspace = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def init(self, params: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize this codelet with the given parameters.
        If this method is overridden, this init must be called first! i.e. super().init()
        Will set parameters with the following names:
        
        refractoryPeriod: Period in ticks that will pass after this codelet creates a coalition before it can create another
        
        If any parameter is not specified its default value will be used.
        
        Args:
            params: Parameters for initialization
        """
        super().init(params)
        refractory_period = self.get_param("refractoryPeriod", self.DEFAULT_CODELET_REFRACTORY_PERIOD)
        self.set_refractory_period(refractory_period)
    
    def get_param(self, name: str, default_value: Any) -> Any:
        """
        Get a parameter value with a default.
        
        Args:
            name: The name of the parameter
            default_value: The default value
            
        Returns:
            The parameter value or the default value
        """
        parameters = getattr(self, "parameters", {})
        if parameters and name in parameters:
            return parameters[name]
        return default_value
    
    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module for this codelet.
        
        Args:
            module: The module to associate with this codelet
            module_usage: How this codelet will use the module
        """
        if isinstance(module, WorkspaceBuffer):
            self.current_situational_model = module
        elif isinstance(module, GlobalWorkspace):
            self.global_workspace = module
        else:
            self.logger.warning(f"Module {module} cannot be associated at tick {TaskManager.get_current_tick()}")
    
    def run_this_framework_task(self) -> None:
        """
        If sought content is found in the CSM, then retrieve it
        and create a coalition from it finally adding it to the
        GlobalWorkspace.
        """
        if self.buffer_contains_sought_content(self.current_situational_model):
            csm_content = self.retrieve_workspace_content(self.current_situational_model)
            if csm_content is None:
                self.logger.warning(f"Null WorkspaceContent returned in {self}. Coalition cannot be formed at tick {TaskManager.get_current_tick()}")
            elif csm_content.get_linkable_count() > 0:
                coalition = CoalitionImpl(csm_content, self)
                self.global_workspace.add_coalition(coalition)
                self.logger.debug(f"Adds new coalition with activation {coalition.get_activation()} at tick {TaskManager.get_current_tick()}")
                self.set_next_ticks_per_run(self.codelet_refractory_period)
    
    def set_refractory_period(self, ticks: int) -> None:
        """
        Sets the refractory period.
        
        Args:
            ticks: Length of refractory period in ticks
        """
        if ticks > 0:
            self.codelet_refractory_period = ticks
        else:
            self.codelet_refractory_period = self.DEFAULT_CODELET_REFRACTORY_PERIOD
            self.logger.warning(f"Refractory period must be positive, using default value at tick {TaskManager.get_current_tick()}")
    
    def get_refractory_period(self) -> int:
        """
        Gets the refractory period.
        
        Returns:
            Length of refractory period in ticks
        """
        return self.codelet_refractory_period
    
    def set_global_workspace(self, gw: GlobalWorkspace) -> None:
        """
        Set the GlobalWorkspace for this AttentionCodelet.
        
        Args:
            gw: The GlobalWorkspace
        """
        self.global_workspace = gw
    
    def get_global_workspace(self) -> GlobalWorkspace:
        """
        Get the GlobalWorkspace for this AttentionCodelet.
        
        Returns:
            The GlobalWorkspace
        """
        return self.global_workspace
    
    def create_coalition(self, content: NodeStructure) -> None:
        """
        Create a Coalition with the specified content.
        
        Args:
            content: The content for the Coalition
        """
        if content is not None and content.get_linkable_count() > 0:
            coalition = CoalitionImpl(content, self)
            self.global_workspace.add_coalition(coalition)
    
    def buffer_contains_sought_content(self, buffer: WorkspaceBuffer) -> bool:
        """
        Returns true if specified WorkspaceBuffer contains this codelet's sought content.
        
        Args:
            buffer: The WorkspaceBuffer to be checked for content
            
        Returns:
            True if the buffer contains the sought content, False otherwise
        """
        # This method should be overridden by subclasses
        return False
    
    def retrieve_workspace_content(self, buffer: WorkspaceBuffer) -> NodeStructure:
        """
        Retrieves content from the specified WorkspaceBuffer.
        
        Args:
            buffer: The WorkspaceBuffer to retrieve content from Returns:
            The retrieved content
        """
        # This method should be overridden by subclasses
        return None
