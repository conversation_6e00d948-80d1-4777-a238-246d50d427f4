#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to create an action root.
"""

from typing import Optional
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil

# 导入 linars 组件
try:
    from linars.edu.memphis.ccrg.linars.memory import Memory
    from linars.edu.memphis.ccrg.linars.term import Term
    from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
    from linars.org.opennars.entity.task import Task
    from linars.org.opennars.control.derivation_context import DerivationContext
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False

class CreateActRootTask0(FrameworkTaskImpl):
    """
    A task to create an action root.
    """

    def __init__(self, root_node: Optional[str] = None, index: int = 0,
                pre_attent_str: Optional[str] = None, is_root: bool = False,
                act_stamp: Optional[str] = None):
        """
        Initialize a CreateActRootTask0.

        Args:
            root_node: The root node
            index: The index
            pre_attent_str: The previous attention string
            is_root: Whether this is a root
            act_stamp: The action stamp
        """
        super().__init__(1, "tact")
        self.root_node = root_node
        self.index = index  # 输入队列中的位置
        self.pre_attent_str = pre_attent_str
        self.is_root = is_root  # 是否是时序根节点
        self.act_stamp = act_stamp

        self.pam = None  # PAMemory
        self.link = None  # Link
        self.task = None  # Task
        self.mem = None  # Memory
        self.nal = None  # DerivationContext
        self.terms = None  # List[Term]

        self.is_daemon = False  # 是否是守护线程，守护线程不会阻止程序的结束
        self.source = None  # 时序节点来源，前一位节点
        self.link_type = None  # 时序边来源类型

    def run_this_framework_task(self):
        """
        Run the task.
        """
        # 多标志词限制，如首先+逗号分句+然后，中间的算第一大步，里面可能有多层小步骤，分句1+逗号+分句2，数量不定
        # 逗号后是其他分句或"然后"，分两种情况，需否定语句，标明分句n非然后？每句理解结果分别打包？矩阵式打包？
        # 原子步骤按类型驱动，什么类型就怎么构建。无法直接统筹构建=嵌套由高到低，需分步构建=一句一句来，因为逐句输入

        # 不能搞混先天时序构建后后天计划方法论，基础和应用区别。依赖变量句，变量句是零散的，不构成体系化的计划图程
        # 只有一分句也构建时序首？流式构建，来多少构建多少，而不是等待所有分句都到齐再构建。分句数量不定，不可预知
        # 大变量句也能一句表示整个大时序，左边原句，右边为要构建成的时序结构，左边推出右边，也就是执行时的结构，如nars1<=>心算时序
        # 变量句只是一条认知，原始nl能对应转化成什么narsese，不是操作，虽然里面可能包含操作。类似代码转ast，编译过程
        mark = ""
        mark2 = ""
        # 无根是构建方法名阶段，标志词可能为012个，零个是主动模仿学习，否则是被动学习，第二个可能是逗号等
        # 有根无源，则是构建时序首阶段，适用变量句如：($方法名,$次序标志1,$分句1)，多种形式，可无次序标志，甚至无方法名
        # 两者都有，则是构建时序节点阶段，变量句形式：($次序标志1,$分句1,$次序标志2,$分句2)，多种形式，分句数量不定
        # 变量句为过渡桥梁，依赖后天+理解。类似执行机制，数据驱动。逐句理解信息不足时，激活多个变量句，需等待理解结果
        # 次序标志词，一般是首位标志开始，否则会引起认知控制混乱，如第一个是"然后"，但也可自动纠正
        # if self.source is not None:
        #     mark = self.source
        # else:
        #     # 没有第二标志词
        #     pass

        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        input_queue_str = AgentStarter.input_queue_str
        node_name = ""

        # todo 逐步构建语句结构，无完整案例情况下用构式，有则直接激活

        for node in AgentStarter.nar.memory.get_nodes():
            node_name = node.get_tn_name()
            # todo 同类标志词搭配，解决多种不同类型标志词组合的问题，如首先+第一步等，实际语句中个别标志词会混乱，可纠正转为同类
            # 同类之间分句，再整体处理，非同类标志在当前处理中，当做普通分句，下一构建时序首阶段，再考虑标志词

            # 方案1=变量句枚举各种标志词组合和分句情况，难应对各种情况，且不易扩展，不可取，分段短变量句也麻烦
            # 方案2=同类标志词搭配，也就是共同指向上层概念，不同类标志词分开，还是得整体构建，分段麻烦
            # 方案3=流式构建，遇到标志词都特别处理，关键在如何触发构建，nars长整句转换=没法流式，执行同理

            # 就是普通嵌套构建，只是标记了图程，可调用，与"为什么"等同理。维护一个次序标志表，map，key是次序标志，value是位置
            # 每个分句对应一个变量句，（#，首先，其他）=/>（&/，其他）。&/=前向构建。
            # 无标志时，识别分句类型，按分句顺序即可，平铺无嵌套
            # 动词对应动作时序，但也可能是无关插入语。明确要构建的，明确不要构建的，不明确的再说

            attent_str0 = node.get_strs()
            link = None
            index0 = input_queue_str.find(attent_str0)
            # 如果attenStr在inputQueueStr中的位置等于index，则表示该节点是当前构建的节点，已构建到index
            if index0 == self.index + 1:
                is_same_type = False
                is_have_type_this = False  # 本句有类别
                is_have_type_pre = False  # 前句有类别
                # 看两句的类别分别是什么，看头结点为nodename和preAttentStr的，有无isa语句
                # 并判断类别，普通分句没有类别，无类别和有类别，相当于不同类别。
                # 需判断本句和前句类别，不能遍历记忆中的links，因为有延迟，记忆中还没有构建出来，需查数据库
                # 需分别拿到nodename和preAttentStr作为头结点的link，关系类型限制为isa
                query1 = f"match (n)-[r:isa]->(m) where n.name = '{node_name}' return r"
                links1 = NeoUtil.get_links_cypher(query1)
                query2 = f"match (n)-[r:isa]->(m) where n.name = '{self.pre_attent_str}' return r"
                if links1:
                    is_have_type_this = True
                    links2 = NeoUtil.get_links_cypher(query2)
                    if links2:
                        is_have_type_pre = True
                        # 如果两个link的尾节点相同，且link类别为isa，则两个link的头结点为同类
                        for link1 in links1:
                            for link2 in links2:
                                if link1.get_sink().get_tn_name() == link2.get_sink().get_tn_name():
                                    is_same_type = True

                if not is_have_type_this:
                    # 本句无类别，上面的前句循环也不会执行，所以这里要判断前句是否有类别
                    links2 = NeoUtil.get_links_cypher(query2)
                    if links2:
                        is_have_type_pre = True

                    if not is_have_type_pre:
                        # 前句也无类别，两句都是普通分句，同类
                        is_same_type = True

                # 如果当前句与前一句类别不同，就构建首尾（回溯到最近的同类级别），分首尾两种情况，后句可不考虑。无需标志表，
                # 中间是普通时序，顺承即可。与前面有同类则同层，不同类则新建一层，两类或两组类别不能交叉，只需跟主路线各点比较
                # 是否有无同类，是运行时机制，一个标志表，原本应后天，也是一种认知，可表征和推理，有路径，（a,b）->不同类
                if self.is_root or not is_same_type:
                    # 来自时序根节点的时序链接，无论如何都会构建，但分时序首与普通时序，构建方式不同。
                    # 不是同类=连续分句，且不是时序根节点，也构建时序首
                    # todo 各步也可能有方法名。另外可能要保留次序词作为单独分句，而不仅是时序首头结点。不过已理解沉淀的时序可按自主意图输出
                    link = NeoUtil.merge_link(None, "时序首", self.root_node, node_name, "Verb", "Verb")
                    # 可能是次序词，只构建一条边即可，也可能不是，是普通分句，需把分句也构建出来
                    if is_have_type_this:
                        # 如果有可能的后续嵌套时序构建，则将上位时序存入主路线，以便回溯执行。与执行分开
                        from linars.edu.memphis.ccrg.lida.PAM.PAMemoryImpl import PAMemoryImpl
                        PAMemoryImpl.seq_ns.get_create_main_path().append(link)

                        from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
                        create_act_root_task = CreateActRootTask0(mark, self.index + len(attent_str0) + 1,
                                                               node_name, False, self.act_stamp)
                        FrameworkModuleImpl.task_spawner.add_task(create_act_root_task)
                    else:
                        print("普通分句")
                        # 没有变量的，最简单的时序，直接构建
                        from linars.edu.memphis.ccrg.lida.PAM.Tasks.CreateSimpleSceneTask import CreateSimpleSceneTask
                        from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl

                        create_simple_scene_task = CreateSimpleSceneTask(link, node, self.act_stamp)
                        FrameworkModuleImpl.task_spawner.add_task(create_simple_scene_task)
                else:
                    # 同类，则看分句特征，触发对应构建线程。只有分句间才会同类
                    # 只构建时序首边，如多个分句中的中间分句，只是时序链接
                    print("同类")
                    link = NeoUtil.merge_link(None, "时序", self.root_node, node_name, "Verb", "Verb")

                    from linars.edu.memphis.ccrg.lida.PAM.Tasks.CreateSimpleSceneTask import CreateSimpleSceneTask
                    from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl

                    create_simple_scene_task = CreateSimpleSceneTask(link, node, self.act_stamp)
                    FrameworkModuleImpl.task_spawner.add_task(create_simple_scene_task)

                self.cancel()
                break
