#LIDA Cognitive Framework
"""
Responsible for storing and retrieving associations between perceptual
elements. Interacts with Sensory Memory, Situational Model, and Global Workspace.
Input: Sensory Stimuli and cues from Sensory Memory
Output: Local Associations, passed to others
"""
from threading import Lock
import logging
from typing import Collection, Dict, List, Optional, Set, Any

from linars.edu.memphis.ccrg.lida.Framework.ModuleName import <PERSON><PERSON><PERSON><PERSON><PERSON>
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategory import LinkCategory
from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable

# Define PAMemory as a class instead of a string
class PAMemory(FrameworkModule):
    """
    A main module which contains feature detectors, nodes, and links.
    """
    # Default constants
    DEFAULT_PERCEPT_THRESHOLD = 0.5
    DEFAULT_UPSCALE_FACTOR = 0.7
    DEFAULT_DOWNSCALE_FACTOR = 0.5
    DEFAULT_PROPAGATION_THRESHOLD = 0.5
    DEFAULT_EXCITATION_TASK_TICKS = 10
    DEFAULT_PROPAGATION_TASK_TICKS = 10

    def __init__(self):
        # Storing associations
        super().__init__()

    # Core PAMemory methods
    def add_default_node(self, label: str) -> Optional[Node]:
        """
        Adds a new PamNode of default type to PAM with specified label.
        Label must be unique. If not, existing node with specified label is returned.

        Args:
            label: The label of the new PamNode

        Returns:
            The new PamNode added to PAM, the existing Node with specified label or None
        """
        # Implementation needed
        pass

    def add_node(self, node_type: str, label: str) -> Optional[Node]:
        """
        Adds a new PamNode of specified type to PAM with specified label.

        Args:
            node_type: The type of the new PamNode
            label: The label of the new PamNode

        Returns:
            The new PamNode added to PAM, the existing Node with specified label or None
        """
        # Implementation needed
        pass

    def add_default_link(self, src: Node, snk: Linkable, cat: LinkCategory) -> Optional[Link]:
        """
        Adds a new PamLink of default type to PAM.

        Args:
            src: The link's source
            snk: The link's sink
            cat: The link's category

        Returns:
            The new PamLink, the existing PamLink, or None
        """
        # Implementation needed
        pass

    def add_link(self, link_type: str, src: Node, snk: Linkable, cat: LinkCategory) -> Optional[Link]:
        """
        Adds a new PamLink of specified type to PAM.

        Args:
            link_type: Link's type
            src: The link's source
            snk: The link's sink
            cat: The link's category

        Returns:
            The new PamLink, the existing PamLink, or None
        """
        # Implementation needed
        pass

    def add_pam_listener(self, listener) -> None:
        """
        Adds a PAM listener.

        Args:
            listener: The listener to add
        """
        if listener not in self.pam_listeners:
            self.pam_listeners.append(listener)

    def get_listener(self):
        """
        Gets the first PAM listener.

        Returns:
            The first PAM listener or None if no listeners are registered
        """
        if self.pam_listeners:
            return self.pam_listeners[0]
        return None

    def excite(self, object_name: str, amount: float, from_source: str) -> None:
        """
        Excites a node by name.

        Args:
            object_name: The name of the node to excite
            amount: The amount of activation
            from_source: The source of the excitation
        """
        # Implementation needed
        pass

    def receive_excitation(self, linkable: Linkable, amount: float, from_source: str) -> None:
        """
        Excites specified Linkable with an amount of activation.

        Args:
            linkable: The Linkable receiving the activation
            amount: The amount of activation to excite
            from_source: The source of the excitation
        """
        # Implementation needed
        pass

    def receive_excitation_set(self, linkables: Set[Linkable], amount: float, from_source: str) -> None:
        """
        Excites a set of Linkables with an amount of activation.

        Args:
            linkables: The Linkables to be excited
            amount: The amount of activation
            from_source: The source of the excitation
        """
        for linkable in linkables:
            self.receive_excitation(linkable, amount, from_source)

    def propagate_activation_to_parents(self, node: Node, depth: int, from_source: str) -> None:
        """
        Propagates activation from a Node to its parents.

        Args:
            node: The Node to propagate activation from
            depth: The depth of propagation
            from_source: The source of the propagation
        """
        # Implementation needed
        pass

    def add_to_percept(self, ns_or_node_or_link) -> None:
        """
        Adds a NodeStructure, Node, or Link to the percept.

        Args:
            ns_or_node_or_link: The NodeStructure, Node, or Link to add
        """
        # Implementation needed
        pass

    def set_percept_threshold(self, threshold: float) -> None:
        """
        Sets the percept threshold.

        Args:
            threshold: Threshold for a Linkable to become part of the percept
        """
        if 0.0 <= threshold <= 1.0:
            self.percept_threshold = threshold
        else:
            self.logger.warning("Percept threshold must be in range [0.0, 1.0]. Threshold will not be modified.")

    def get_percept_threshold(self) -> float:
        """
        Gets the percept threshold.

        Returns:
            The percept threshold
        """
        return self.percept_threshold

    def set_upscale_factor(self, factor: float) -> None:
        """
        Sets the upscale factor.

        Args:
            factor: Scale factor for feed-forward activation propagation
        """
        if factor < 0.0:
            self.upscale_factor = 0.0
        elif factor > 1.0:
            self.upscale_factor = 1.0
        else:
            self.upscale_factor = factor

    def get_upscale_factor(self) -> float:
        """
        Gets the upscale factor.

        Returns:
            Scale factor for feed-forward activation propagation
        """
        return self.upscale_factor

    def set_downscale_factor(self, factor: float) -> None:
        """
        Sets the downscale factor.

        Args:
            factor: Scale factor for top-down activation propagation
        """
        if factor < 0.0:
            self.downscale_factor = 0.0
        elif factor > 1.0:
            self.downscale_factor = 1.0
        else:
            self.downscale_factor = factor

    def get_downscale_factor(self) -> float:
        """
        Gets the downscale factor.

        Returns:
            Scale factor for top-down activation propagation
        """
        return self.downscale_factor

    def is_over_percept_threshold(self, linkable: Linkable) -> bool:
        """
        Returns whether Linkable is above percept threshold.

        Args:
            linkable: A Linkable

        Returns:
            True if Linkable's total activation is above percept threshold
        """
        return linkable.get_total_activation() > self.percept_threshold

    def get_node(self, id_or_label) -> Optional[Node]:
        """
        Returns the Node with specified id or label from this PAM or None.

        Args:
            id_or_label: The id or label of the node

        Returns:
            The Node or None
        """
        # Implementation needed
        pass

    def get_nodes(self) -> Collection[Node]:
        """
        Returns an unmodifiable collection of the Nodes in this PAM.

        Returns:
            The Nodes of this PAM
        """
        # Implementation needed
        pass

    def get_links(self) -> Collection[Link]:
        """
        Returns an unmodifiable collection of the Links in this PAM.

        Returns:
            The Links of this PAM
        """
        # Implementation needed
        pass

    def get_pam_node_structure(self):
        """
        Gets the PAM node structure.

        Returns:
            The PAM node structure
        """
        return self.pam_node_structure

    # Original PerceptualAssociativeMemory methods
    def notify(self, module):
        pass

    def add_association(self, cue: NodeImpl):
        # Add new associations
        if cue is not None and cue not in self.associations.getNodes():
            self.logger.debug(f"Storing association, node: {cue}")
            lock = Lock()
            lock.acquire()
            self.associations.addNode_(cue)
            lock.release()

    def retrieve_associations(self, cue: NodeStructureImpl):
        # Retrieving associations for the given cue
        self.logger.debug(f"Retrieved {len(cue.getLinkCount())} associations")
        return cue.getLinks()

    def retrieve_association(self, cue: NodeImpl):
        links = None
        if not NodeImpl:
            self.logger.debug(f"Unable to retrieve association for {cue}")
        else:
            if cue in self.associations.getNodes():
                links = self.associations.getConnectedSinks(cue)
                self.logger.debug(f"Retrieved {len(links)} associations")
            else:
                self.logger.debug(f"Unable to retrieve association for {cue}")
        return links

    def receive_broadcast(self, coalition):
        self.logger.debug(f"Received broadcast coalition {coalition}")
        map(self.add_association, coalition.getContent())

    def get_stored_nodes(self):
        return self.memory.getNodes() if self.memory else None

    def learn(self, cue):
        pass

    """
    NEED: to connect to sensory memory, use data as cue for PAM
    Possible implement of function that can extract patterns
    """

    """
    NEED: To communication with the situational Model
    Passes patterns or local associations for updates to Current Situational Model
    """

    """
    NEED: Implement the Perceptual Learning
    """

    def get_module_name(self) -> ModuleName:
        pass