"""
CSV Utility

This module provides utility functions for working with CSV files.
"""
import csv
import os
import logging

class CSVUtil:
    """CSV Utility class for working with CSV files."""
    
    logger = logging.getLogger(__name__)
    
    @staticmethod
    def read_csv_file(file):
        """
        Read a CSV file.
        
        Args:
            file: The file to read
            
        Returns:
            A list of lists containing the CSV data
        """
        data = []
        try:
            # 如果是上传的文件对象
            if hasattr(file, 'read'):
                # 将文件内容读取为字符串
                content = file.read().decode('utf-8')
                # 使用csv模块解析字符串
                reader = csv.reader(content.splitlines())
                for row in reader:
                    data.append(row)
            # 如果是文件路径
            else:
                with open(file, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    for row in reader:
                        data.append(row)
        except Exception as e:
            CSVUtil.logger.error(f"Error reading CSV file: {e}")
        
        return data
    
    @staticmethod
    def create_csv_file(data, save_path, filename):
        """
        Create a CSV file.
        
        Args:
            data: The data to write
            save_path: The path to save the file
            filename: The name of the file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # 确保目录存在
            if not os.path.exists(save_path):
                os.makedirs(save_path)
            
            # 构建完整的文件路径
            file_path = os.path.join(save_path, filename)
            
            # 写入CSV文件
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                for row in data:
                    writer.writerow(row)
            
            return True
        except Exception as e:
            CSVUtil.logger.error(f"Error creating CSV file: {e}")
            return False
