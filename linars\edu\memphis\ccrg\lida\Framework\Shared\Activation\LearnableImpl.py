# LIDA认知框架
"""
可学习接口的具体实现
"""

from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.ActivatibleImpl import ActivatibleImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.Learnable import Learnable

class LearnableImpl(ActivatibleImpl, Learnable):
    """
    可学习接口的具体实现
    """

    def __init__(self):
        """
        初始化可学习对象实现
        """
        super().__init__()
        self.incentive_salience = 0.0

    def get_incentive_salience(self) -> float:
        """
        获取此对象的激励显著性

        返回:
            此对象的激励显著性
        """
        return self.incentive_salience

    def set_incentive_salience(self, salience: float) -> None:
        """
        设置此对象的激励显著性

        参数:
            salience: 要设置的激励显著性
        """
        self.incentive_salience = salience

    def reinforce(self, amount: float) -> None:
        """
        按给定值强化此对象

        参数:
            amount: 强化值
        """
        self.incentive_salience += amount

    def init(self, params: Optional[Dict[str, Any]] = None) -> None:
        """
        使用给定参数初始化此对象

        参数:
            params: 初始化参数
        """
        super().init(params)
        if params is not None and "incentiveSalience" in params:
            self.incentive_salience = float(params["incentiveSalience"])
