# LIDA Cognitive Framework
"""
Interface for objects that have a refractory period.
"""

from abc import ABC, abstractmethod

class RefractoryPeriod(ABC):
    """
    Interface for objects that have a refractory period.
    """
    
    @abstractmethod
    def get_refractory_period(self) -> int:
        """
        Gets the refractory period.
        
        Returns:
            The number of ticks that must pass after a broadcast has been sent before a new one can be sent
        """
        pass
    
    @abstractmethod
    def set_refractory_period(self, period: int) -> None:
        """
        Sets the refractory period.
        
        Args:
            period: The number of ticks that must pass after a broadcast has been sent before a new one can be sent
        """
        pass
    
    @abstractmethod
    def get_tick_at_last_broadcast(self) -> int:
        """
        Gets the tick at which the last broadcast was sent.
        
        Returns:
            The tick at which the last broadcast was sent
        """
        pass
