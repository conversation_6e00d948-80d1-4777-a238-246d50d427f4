#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
LanGenImpl class for the LIDA framework.
"""

import logging
import threading
from collections import defaultdict
from typing import Dict, List, Set, Any, Optional

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import Module<PERSON>ame
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener import BroadcastListener
from linars.edu.memphis.ccrg.lida.Nlanguage.LanGen import LanGen
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.PAM.PamListener import PamListener
from linars.edu.memphis.ccrg.lida.Workspace.Workspace import Workspace
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceListener import WorkspaceListener


class LanGenImpl(FrameworkModuleImpl, LanGen, BroadcastListener, WorkspaceListener):
    """
    Default implementation of LanGen. Indexes scheme by context
    elements for quick access. Assumes that the Condition of Scheme are Node only.
    """

    class ConditionType:
        """
        The possible type of usage for a condition inside a Scheme
        """
        CONTEXT = "CONTEXT"
        ADDINGLIST = "ADDINGLIST"
        DELETINGLIST = "DELETINGLIST"
        NEGATEDCONTEXT = "NEGATEDCONTEXT"

    class InternalNodeStructure(NodeStructureImpl):
        """
        Allows Nodes to be added without copying.
        Warning: doing so allows the same python object of Node to exist in multiple places.
        """

        def __init__(self, node_type, link_type):
            """
            Initialize an InternalNodeStructure.

            Args:
                node_type: The type of nodes in this structure
                link_type: The type of links in this structure
            """
            super().__init__(node_type, link_type)

        def add_node(self, n, copy=False):
            """
            Add a node to this structure.

            Args:
                n: The node to add
                copy: Whether to copy the node

            Returns:
                The added node
            """
            return super().addNode(n, copy)

    def __init__(self):
        """
        Initialize a LanGenImpl.
        """
        super().__init__()
        self.logger = logging.getLogger("LanGenImpl")

        # Schemes indexed by Nodes in their context
        self.context_scheme_map = defaultdict(set)

        # Schemes indexed by Nodes in their adding list
        self.adding_scheme_map = defaultdict(set)

        # Set of all schemes current in the module
        self.scheme_set = set()

        # A pool of all conditions (context and adding) in all schemes in the procedural memory
        self.condition_pool = {}

        # Recent contents of consciousness that have not yet decayed away
        self.broadcast_buffer = self.InternalNodeStructure("PamNodeImpl", "PamLinkImpl")

        # Listeners of this Procedural Memory
        self.lan_listeners = []

        # Determines how much activation a scheme should have to be instantiated
        self.scheme_selection_threshold = 0.1

        # Qualified name of the Scheme class used by this module
        self.scheme_class = "source.ProceduralMemory.SchemeImpl"

        # DecayStrategy used by all conditions in the condition pool (and broadcast buffer)
        self.condition_decay = None

        # Workspace buffers
        self.scene_graph = None
        self.csm = None
        self.seq_graph = None
        self.goal_graph = None
        self.concent_graph = None
        self.grammar_graph = None

        # NodeStructures
        self.csm_ns = None
        self.non_ns = None
        self.feel_ns = None
        self.con_ns = None
        self.goal_ns = None
        self.seq_ns = None
        self.scene_ns = None
        self.yufa_ns = None

        # Other modules
        self.workspace = None
        self.pam = None

        # Language generation variables
        self.words = {}
        self.wordnum = 0
        self.isdone = False
        self.node = None
        self.level = 0

        # Maps
        self.scenemap = {}
        self.yufamap = {}

    def receive_workspace_content(self, originating_buffer, content):
        """
        Receive workspace content.

        Args:
            originating_buffer: The buffer that sent the content
            content: The content
        """
        pass

    def get_param(self, name: str, default_value: Any) -> Any:
        """
        Get a parameter value with a default.

        Args:
            name: The name of the parameter
            default_value: The default value

        Returns:
            The parameter value or the default value
        """
        parameters = getattr(self, "parameters", {})
        if parameters and name in parameters:
            return parameters[name]
        return default_value

    def init(self, params=None):
        """
        Initialize this module.
        """
        self.scheme_selection_threshold = self.get_param(
            "proceduralMemory.schemeSelectionThreshold", 0.1)
        decay_name = self.get_param("proceduralMemory.conditionDecayStrategy", "DefaultDecay")
        # In a real implementation, you would get the decay strategy from a factory
        # self.condition_decay = factory.get_decay_strategy(decay_name)
        self.scheme_class = self.get_param("proceduralMemory.schemeClass",
                                          "source.ProceduralMemory.SchemeImpl")

    def set_associated_module(self, m, usage):
        """
        Set an associated module.

        Args:
            m: The module to associate
            usage: The usage of the module
        """
        if isinstance(m, Workspace):
            self.csm = m.get_submodule(ModuleName.CurrentSM)
            self.seq_graph = m.get_submodule(ModuleName.SeqGraph)
            self.goal_graph = m.get_submodule(ModuleName.GoalGraph)
            self.concent_graph = m.get_submodule(ModuleName.ConcentGraph)
            self.scene_graph = m.get_submodule(ModuleName.SceneGraph)
            self.grammar_graph = m.get_submodule(ModuleName.GrammarGraph)

            self.csm_ns = self.csm.get_buffer_content(None)
            self.con_ns = self.concent_graph.get_buffer_content(None)
            self.seq_ns = self.seq_graph.get_buffer_content(None)
            self.scene_ns = self.scene_graph.get_buffer_content(None)
            self.yufa_ns = self.grammar_graph.get_buffer_content(None)
        elif isinstance(m, PAMemory):
            self.pam = m
        else:
            self.logger.warning(f"Cannot add module {m}")

    def add_listener(self, l):
        """
        Add a listener to this module.

        Args:
            l: The listener to add
        """
        if isinstance(l, PamListener):
            self.lan_listeners.append(l)
        else:
            self.logger.warning(f"Requires lanListeners but received {l}")

    def receive_broadcast(self, coalition):
        """
        Receive a broadcast from the Global Workspace.

        Args:
            coalition: The coalition that was broadcast
        """
        self.scenemap = self.scene_ns.get_main_map()
        self.yufamap = self.yufa_ns.get_main_map()
        self.words = {}

        # 等语法完善才生成
        if self.yufamap is not None and len(self.yufamap) > 0 and self.scenemap is not None and len(self.scenemap) > 0:
            pass
            # Implementation would go here

        # 注意 coalition 与 condition
        self.learn(coalition)

    def invoke(self, level0, upmapkey):
        """
        Invoke language generation.

        Args:
            level0: The level
            upmapkey: The map key
        """
        name = ""
        mapkey = f"{level0}_{self.node.get_tn_name()}"
        mainlinsks = self.yufamap.get(mapkey)
        self.isdone = False

        if mainlinsks is None:
            # 不是子场景，则直接在前一层查场景元素，匹配则替换，scenename序号和点都是前一层
            for scenelink in self.scenemap.get(upmapkey):
                if scenelink.get_tn_name() == self.node.get_tn_name():
                    self.lan_listeners[0].receive_percept(scenelink.get_source(), ModuleName.WordGraph)
                    name = scenelink.get_source().get_tn_name()
                    self.words[self.wordnum] = name
                    self.isdone = True

            # 没有场景元素匹配，直接拼接当前词
            if not self.isdone:
                name = self.node.get_tn_name()
                self.words[self.wordnum] = name
                self.lan_listeners[0].receive_percept(self.node, ModuleName.WordGraph)

            # todo 内部语言重理解，重新输入pam走激活过程，主要是知道自己说了啥和正确与否
            print(f"新词-----------{name}")
            self.wordnum += 1
        else:
            for i in range(len(mainlinsks)):
                self.node = mainlinsks[i].get_source()
                # 只能一个地方加加，深度担当，其他都按同层处理
                self.invoke(level0 + 1, mapkey)

    def learn(self, coalition):
        """
        Learn from a coalition.

        Args:
            coalition: The coalition to learn from
        """
        pass

    def decay_module(self, ticks):
        """
        Decay this module.

        Args:
            ticks: The number of ticks to decay
        """
        self.broadcast_buffer.decay_node_structure(ticks)

    def get_module_content(self, *params):
        """
        Get the content of this module.

        Args:
            params: Parameters specifying the content to get

        Returns:
            The requested content
        """
        if params[0] == "schemes":
            return self.scheme_set.copy()
        return None
