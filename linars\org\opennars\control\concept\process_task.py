from typing import Optional

from linars.edu.memphis.ccrg.lida.Nlanguage.SubGraphSet import SubGraphSet
from linars.edu.memphis.ccrg.lida.Nlanguage.TreeNode import TreeNode
from linars.edu.memphis.ccrg.linars.concept import Concept
from linars.edu.memphis.ccrg.linars.memory import Memory
from linars.edu.memphis.ccrg.linars.process_goal import ProcessGoal
from linars.org.opennars.control.concept.process_judgment import ProcessJudgment
from linars.org.opennars.control.concept.process_question import ProcessQuestion
from linars.org.opennars.control.derivation_context import DerivationContext
from linars.org.opennars.entity.task import Task
from linars.org.opennars.interfaces.timable import Timable
from linars.org.opennars.io.symbols import JUDGMENT_MARK, GOAL_MARK, QUESTION_MARK, QUEST_MARK
from linars.org.opennars.language.equivalence import Equivalence
from linars.org.opennars.language.similarity import Similarity
from linars.org.opennars.language.statement import Statement

class ProcessTask:
    """
    封装任务处理的分派
    """

    @staticmethod
    def processTask(concept: Concept, nal: DerivationContext, task: Task, time: Timable, mem: Memory) -> bool:
        """
        直接在概念内处理新任务。这里的任务可以是判断、目标、问题或探索。
        该函数在每个任务上精确调用一次。使用本地信息并在恒定时间内完成。
        还在任务的预算值中提供反馈：降低已完成的问题和目标的优先级
        如果信念证明有用，则提高其质量。完成重新优先级排序后，最终构建一个任务链接。
        对于输入事件，概念也被设置为可观察。

        Args:
            concept: 任务的概念
            nal: 推导上下文
            task: 要处理的任务
            time: 时间
            mem: 内存

        Returns:
            是否已处理
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            with concept:  # 去掉会好多报错，它作用是：同一时间只能有一个线程访问concept
                try:
                    concept.observable |= task.isInput()
                except Exception as e:
                    logger.error(f"Error setting concept observable: {e}")

                type_char = task.sentence.punctuation
                try:
                    if type_char == JUDGMENT_MARK:
                        try:
                            ProcessJudgment.processJudgment(concept, nal, task)
                        except Exception as e:
                            logger.error(f"Error processing judgment: {e}")
                            # 打印跟踪
                            import traceback
                            traceback.print_exc()
                    elif type_char == GOAL_MARK:
                        try:
                            ProcessGoal.process_goal(concept, nal, task)
                        except Exception as e:
                            logger.error(f"Error processing goal: {e}")
                            # 打印跟踪
                            import traceback
                            traceback.print_exc()
                    elif type_char == QUESTION_MARK or type_char == QUEST_MARK:
                        try:
                            ProcessQuestion.processQuestion(concept, nal, task)
                        except Exception as e:
                            logger.error(f"Error processing question: {e}")
                    else:
                        return False
                except Exception as e:
                    logger.error(f"Error determining task type: {e}")
                    return False

                # 通过成就减少优先级
                try:
                    task.set_priority(float(task.get_priority() * task.get_achievement()))
                except Exception as e:
                    logger.error(f"Error updating task priority: {e}")

                # 现在处理
                try:
                    if task.aboveThreshold():
                        # 仍然需要处理
                        taskl = None
                        try:
                            taskl = concept.link_to_task(task, nal)
                        except Exception as e:
                            logger.error(f"Error linking task to concept: {e}")

                        try:
                            term = task.get_term()
                            # 顺承、相似、等价，多类潜在动机竞争。两种途径提升为动机，一是普通推理，二是这里直接拉来竞争
                            if task.sentence.is_judgment() and isinstance(term, Statement):
                                try:
                                    if ProcessJudgment.isBeliefsHypothesis(task, nal):  # isExecutableHypothesis
                                        # 在link_to_task之后，因为现在组件都在那里
                                        try:
                                            ProcessJudgment.addToTargetConceptsPreconditions(task, mem)
                                        except Exception as e:
                                            logger.error(f"Error adding to target concepts preconditions: {e}")
                                except Exception as e:
                                    logger.error(f"Error checking if belief is hypothesis: {e}")
                        except Exception as e:
                            logger.error(f"Error processing term: {e}")

                        # 基于已知于概念邻居的信念，触发预测性推理
                        if taskl is not None:
                            try:
                                from linars.org.opennars.control.concept.process_anticipation import ProcessAnticipation
                                ProcessAnticipation.fire_predictions(task, concept, nal, time, taskl)
                            except Exception as e:
                                logger.error(f"Error firing predictions: {e}")
                except Exception as e:
                    logger.error(f"Error checking if task is above threshold: {e}")
        except Exception as e:
            logger.error(f"Error in processTask: {e}")
            return False

        return True

    @staticmethod
    def addTaskToConceptMotivation(task: Task, concept: Concept, nal: DerivationContext) -> None:
        """
        将任务添加到概念动机中

        Args:
            task: 要添加的任务
            concept: 概念
            nal: 推导上下文
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            memory = nal.memory
            if isinstance(memory, SubGraphSet):
                try:
                    subgraphset = memory
                    goalTree = subgraphset.goalTree
                    if goalTree is not None:
                        try:
                            # 找到目标树中包含的节点，有则加入，先取出task三元组的头尾节点
                            try:
                                pred = task.get_term().get_predicate()
                                subj = task.get_term().get_subject()
                            except Exception as e:
                                logger.error(f"Error getting term predicates and subjects: {e}")
                                return

                            try:
                                node = goalTree.find_node(pred)
                            except Exception as e:
                                logger.error(f"Error finding node in goal tree: {e}")
                                return

                            # todo 有问题，目前是顺推，需从下往上构建，从本能开始则是从上往下，可能需要两颗树同时构建？
                            # 双向同时构建，像最短路算法，且保留中间结果，避免重复计算，只是有些累赘，中间路径数据结构完整
                            # 直接用最短路算法，自定义遍历逻辑，只取到中间路径的id，而非完整数据结构，快捷而且节省空间

                            # nars的表已经构建了顺推树结构，可考虑构建一个相对稳定的从本能出发的逆推树结构，两者交互加速构建
                            # todo 更重要的是目标树=已确定要执行的动机树，可包含短中长目标。备选表只是目前激活=与目前任务相关
                            # 动机树和备选树分开，更清晰轻便，精简动机不用被大量备选干扰，要搜索备选，就去备选树搜即可，也即概念和时序表
                            try:
                                if node.is_present():
                                    try:
                                        node1 = node.get()
                                        node2 = goalTree.find_node(subj)
                                        if not node2.is_present():
                                            try:
                                                term = task.get_term()
                                                # 不存在则加入节点，并添加到目标树中
                                                # 假如task是相似、对等，则加入节点的alias属性，否则加入子节点
                                                try:
                                                    if isinstance(term, Similarity) or isinstance(term, Equivalence):
                                                        node1.add_alias(TreeNode(subj))
                                                    else:
                                                        node1.add_child(TreeNode(subj))
                                                except Exception as e:
                                                    logger.error(f"Error adding node to goal tree: {e}")
                                            except Exception as e:
                                                logger.error(f"Error getting term: {e}")
                                    except Exception as e:
                                        logger.error(f"Error processing nodes: {e}")
                            except Exception as e:
                                logger.error(f"Error checking if node is present: {e}")
                        except Exception as e:
                            logger.error(f"Error processing goal tree: {e}")
                except Exception as e:
                    logger.error(f"Error accessing subgraph set: {e}")
        except Exception as e:
            logger.error(f"Error in addTaskToConceptMotivation: {e}")
