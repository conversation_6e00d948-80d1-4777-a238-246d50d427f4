# LIDA Cognitive Framework
"""
A BroadcastTrigger that triggers a broadcast if any coalition is above a threshold.
"""

import logging
from typing import Collection
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.AggregateCoalitionActivationTrigger import AggregateCoalitionActivationTrigger

class IndividualCoalitionActivationTrigger(AggregateCoalitionActivationTrigger):
    """
    A BroadcastTrigger that triggers a broadcast if any coalition is above a threshold.
    """
    
    def __init__(self):
        """
        Initialize an IndividualCoalitionActivationTrigger.
        """
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def check_for_trigger_condition(self, coalitions: Collection[Coalition]) -> None:
        """
        Triggers a broadcast if any Coalition object's activation is over threshold.
        
        Args:
            coalitions: The coalitions in the GlobalWorkspace
        """
        for c in coalitions:
            if c.get_activation() > self.threshold:
                self.logger.debug(f"Individual Activation trigger fires at tick {TaskManager.get_current_tick()}")
                self.gw.trigger_broadcast(self)
                break
