# LIDA Cognitive Framework
"""
The default implementation of GlobalWorkspace which maintains the collection of
Coalition objects. It supports BroadcastTrigger tasks that are in charge
of triggering the new broadcast. This class maintains a list of
BroadcastListener which are the modules that are registered to receive winning coalitions.
"""

import logging
import threading
from queue import Queue
from typing import List, Set, Dict, Any, Optional, Collection

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Strategies.DecayStrategy import DecayStrategy
from linars.edu.memphis.ccrg.lida.Framework.Strategies.DefaultDecayStrategy import DefaultDecayStrategy
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener import BroadcastListener
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.GlobalWorkspace import GlobalWorkspace
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Tasks.StartTriggersTask import StartTriggersTask
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.BroadcastTrigger import BroadcastTrigger


class GlobalWorkspaceImpl(FrameworkModuleImpl, GlobalWorkspace):
    """
    The default implementation of GlobalWorkspace which maintains the collection of
    Coalition objects. It supports BroadcastTrigger tasks that are in charge
    of triggering the new broadcast. This class maintains a list of
    BroadcastListener which are the modules that are registered to receive winning coalitions.
    """

    # Default values
    DEFAULT_COALITION_REMOVAL_THRESHOLD = 0.0
    DEFAULT_COALITION_DECAY = "defaultDecay"
    DEFAULT_REFRACTORY_PERIOD = 40

    def __init__(self):
        """
        Initialize a GlobalWorkspaceImpl.
        """
        super().__init__()
        self.coalition_removal_threshold = self.DEFAULT_COALITION_REMOVAL_THRESHOLD
        self.coalition_decay_strategy = DefaultDecayStrategy()
        self.broadcast_refractory_period = self.DEFAULT_REFRACTORY_PERIOD
        self.broadcasts_sent_count = 0
        self.tick_at_last_broadcast = 0
        self.last_broadcast_trigger = None
        self.broadcast_started = threading.Event()
        self.broadcast_started.clear()

        self.broadcast_listeners: List[BroadcastListener] = []
        self.broadcast_triggers: List[BroadcastTrigger] = []
        self.coalitions: Queue[Coalition] = Queue()

        self.logger = logging.getLogger(self.__class__.__name__)

    def init(self, params=None) -> None:
        """
        Initialize this GlobalWorkspaceImpl.

        Args:
            params: Parameters for initialization, defaults to None
        """
        super().init(params)
        # print(f"GlobalWorkspaceImpl.init() called with params: {params}")
        print("GlobalWorkspaceImpl.init()")
        self.coalition_removal_threshold = self.get_param("globalWorkspace.coalitionRemovalThreshold", self.DEFAULT_COALITION_REMOVAL_THRESHOLD)

        coalition_decay_strategy_name = self.get_param("globalWorkspace.coalitionDecayStrategy", self.DEFAULT_COALITION_DECAY)
        self.coalition_decay_strategy = self.get_decay_strategy(coalition_decay_strategy_name)
        if self.coalition_decay_strategy is None:
            self.coalition_decay_strategy = DefaultDecayStrategy()
            self.logger.warning(f"Failed to obtain decay strategy {coalition_decay_strategy_name}, using default at tick {TaskManager.get_current_tick()}")

        refractory_period = self.get_param("globalWorkspace.refractoryPeriod", self.DEFAULT_REFRACTORY_PERIOD)
        self.set_refractory_period(refractory_period)

        # Create and add StartTriggersTask
        start_triggers_task = StartTriggersTask()
        start_triggers_task.set_associated_module(self, "GlobalWorkspace")
        self.get_assisting_task_spawner().add_task(start_triggers_task)

    def get_param(self, name: str, default_value: Any) -> Any:
        """
        Get a parameter value with a default.

        Args:
            name: The name of the parameter
            default_value: The default value

        Returns:
            The parameter value or the default value
        """
        parameters = getattr(self, "parameters", {})
        if parameters and name in parameters:
            return parameters[name]
        return default_value

    def get_decay_strategy(self, name: str) -> Optional[DecayStrategy]:
        """
        Get a decay strategy by name.

        Args:
            name: The name of the decay strategy

        Returns:
            The decay strategy or None if not found
        """
        # In a real implementation, this would use a factory to get the decay strategy
        # For now, we'll just return a DefaultDecayStrategy
        return DefaultDecayStrategy()

    def add_listener(self, listener: Any) -> None:
        """
        Add a listener to this GlobalWorkspace.

        Args:
            listener: The listener to add
        """
        if isinstance(listener, BroadcastListener):
            self.add_broadcast_listener(listener)
        else:
            self.logger.warning(f"Listener {listener} was not added, wrong type at tick {TaskManager.get_current_tick()}")

    def add_broadcast_listener(self, listener: BroadcastListener) -> None:
        """
        Adds and registers a BroadcastListener. Each registered
        BroadcastListener receives each conscious broadcast.

        Args:
            listener: The BroadcastListener that will be registered
        """
        self.broadcast_listeners.append(listener)

    def add_broadcast_trigger(self, trigger: BroadcastTrigger) -> None:
        """
        Adds and registers specified BroadcastTrigger.

        Args:
            trigger: A BroadcastTrigger which can initiate a broadcast
        """
        self.broadcast_triggers.append(trigger)

    def add_coalition(self, coalition: Coalition) -> bool:
        """
        Adds specified Coalition.

        Args:
            coalition: The Coalition to be added to the GlobalWorkspace

        Returns:
            True if coalition was added, False otherwise
        """
        coalition.set_decay_strategy(self.coalition_decay_strategy)
        coalition.set_removal_threshold(self.coalition_removal_threshold)

        try:
            self.coalitions.put(coalition)
            self.logger.debug(f"New Coalition added with activation {coalition.get_activation()} at tick {TaskManager.get_current_tick()}")
            self.new_coalition_event()
            return True
        except Exception as e:
            self.logger.error(f"Error adding coalition: {e} at tick {TaskManager.get_current_tick()}")
            return False

    def new_coalition_event(self) -> None:
        """
        Called when a new coalition is added to the GlobalWorkspace.
        """
        for trigger in self.broadcast_triggers:
            trigger.check_for_trigger_condition(list(self.coalitions.queue))

    def trigger_broadcast(self, trigger: BroadcastTrigger) -> None:
        """
        Listener must trigger a competition for consciousness and a conscious broadcast of the winner.

        Args:
            trigger: The trigger that is initiating the broadcast
        """
        if self.broadcast_started.is_set():
            self.logger.debug(f"Broadcast already in progress at tick {TaskManager.get_current_tick()}")
            return

        current_tick = TaskManager.get_current_tick()
        if current_tick - self.tick_at_last_broadcast < self.broadcast_refractory_period:
            self.logger.debug(f"In refractory period at tick {TaskManager.get_current_tick()}")
            return

        self.broadcast_started.set()
        self.last_broadcast_trigger = trigger

        self.logger.debug(f"Triggering broadcast at tick {TaskManager.get_current_tick()}")
        broadcast_was_sent = False

        winning_coalition = self.choose_coalition(0)

        if winning_coalition is None:
            self.logger.debug(f"No winning coalition at tick {TaskManager.get_current_tick()}")
            self.broadcast_started.clear()
            return

        ns = winning_coalition.get_content()
        if isinstance(ns, NodeStructure) and winning_coalition is not None:
            self.coalitions.queue.remove(winning_coalition)

            self.broadcasts_sent_count += 1
            ns.set_broad_scene_count(self.broadcasts_sent_count)

            for node in ns.get_nodes():
                if isinstance(node, Node):
                    # Set current period
                    node.set_bcastid(str(self.broadcasts_sent_count))
                    # Update virtual/real status
                    if node.get_last_act() is not None and self.broadcasts_sent_count > int(node.get_last_act()):
                        node_truth = node.get_truth()
                        if node_truth == 1:
                            node.set_truth(2)
                        elif node_truth == 3 or node_truth == 5:
                            node.set_truth(4)

            self.tick_at_last_broadcast = current_tick

            for listener in self.broadcast_listeners:
                try:
                    listener.receive_broadcast(winning_coalition)
                    broadcast_was_sent = True
                except Exception as e:
                    self.logger.error(f"Error sending broadcast to listener {listener}: {e} at tick {TaskManager.get_current_tick()}")

            if broadcast_was_sent:
                self.logger.debug(f"Broadcast sent at tick {TaskManager.get_current_tick()}")
                for trigger in self.broadcast_triggers:
                    trigger.reset()

        self.broadcast_started.clear()

    def choose_coalition(self, threshold: float) -> Optional[Coalition]:
        """
        Choose a coalition to broadcast.

        Args:
            threshold: The threshold for choosing a coalition

        Returns:
            The chosen coalition or None if no coalition is chosen
        """
        if self.coalitions.empty():
            return None

        winning_coalition = None
        max_activation = threshold

        for coalition in list(self.coalitions.queue):
            activation = coalition.get_activation()
            if activation > max_activation:
                max_activation = activation
                winning_coalition = coalition

        return winning_coalition

    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.

        Args:
            ticks: The number of ticks to decay by
        """
        for coalition in list(self.coalitions.queue):
            coalition.decay(ticks)
            if coalition.is_removable():
                self.coalitions.queue.remove(coalition)
                self.logger.debug(f"Coalition removed at tick {TaskManager.get_current_tick()}")

    def get_broadcast_sent_count(self) -> int:
        """
        Gets the number of broadcasts sent.

        Returns:
            The number of broadcasts sent
        """
        return self.broadcasts_sent_count

    def get_refractory_period(self) -> int:
        """
        Gets the refractory period.

        Returns:
            The number of ticks that must pass after a broadcast has been sent before a new one can be sent
        """
        return self.broadcast_refractory_period

    def get_tick_at_last_broadcast(self) -> int:
        """
        Gets the tick at which the last broadcast was sent.

        Returns:
            The tick at which the last broadcast was sent
        """
        return self.tick_at_last_broadcast

    def set_refractory_period(self, period: int) -> None:
        """
        Sets the refractory period.

        Args:
            period: The number of ticks that must pass after a broadcast has been sent before a new one can be sent
        """
        if period > 0:
            self.broadcast_refractory_period = period
        else:
            self.broadcast_refractory_period = self.DEFAULT_REFRACTORY_PERIOD
            self.logger.warning(f"Refractory period must be positive, using default value at tick {TaskManager.get_current_tick()}")

    def get_coalition_removal_threshold(self) -> float:
        """
        Gets the coalition removal threshold.

        Returns:
            The coalition removal threshold
        """
        return self.coalition_removal_threshold

    def set_coalition_removal_threshold(self, threshold: float) -> None:
        """
        Sets the coalition removal threshold.

        Args:
            threshold: The coalition removal threshold to set
        """
        self.coalition_removal_threshold = threshold

    def get_module_content(self, *params: Any) -> Collection[Coalition]:
        """
        Get the content of this module.

        Args:
            params: Parameters specifying what content to return

        Returns:
            The content of this module
        """
        if params and len(params) > 0 and params[0] == "lastBroadcastTrigger":
            return self.last_broadcast_trigger
        return list(self.coalitions.queue)

    def is_global_workspace(self) -> bool:
        """
        Check if this module is a GlobalWorkspace.

        Returns:
            True, as this is a GlobalWorkspace implementation
        """
        return True
