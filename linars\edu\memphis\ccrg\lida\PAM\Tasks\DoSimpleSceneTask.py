#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to handle simple scene processing.
"""

from typing import Optional
import logging
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.PAM.Tasks.DoSuccTask import DoSuccTask
from linars.edu.memphis.ccrg.lida.PAM.Tasks.IsaPamTask import IsaPamTask
from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil

class DoSimpleSceneTask(FrameworkTaskImpl):
    """
    A task to handle simple scene processing.
    """

    def __init__(self, sink: Node, source: Node, pam: PAMemory, goal_ns: NodeStructure, act_stamp: Optional[str] = None):
        """
        Initialize a DoSimpleSceneTask.

        Args:
            sink: The sink node
            source: The source node
            pam: The PAMemory
            goal_ns: The goal NodeStructure
            act_stamp: The action stamp
        """
        super().__init__(1, "tact")
        self.sink = sink
        self.source = source
        self.pam = pam
        self.goal_ns = goal_ns
        self.act_stamp = act_stamp
        self.logger = logging.getLogger("DoSimpleSceneTask")

    def run_this_framework_task(self):
        """
        Run the task.
        """
        # 用于语法与场景整合，适用场景与语法分离的情况。目前可能不分离
        self.pam.set_scene_main_node(self.sink)

        # 待生成父场景，根场景，单句
        # self.pam.get_scene_node(self.sink, self.sink.get_tn_name(), False)

        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        AgentStarter.nar.add_input_to(f"(^say,{{SELF}},{self.sink.get_tn_name()})! :|:", self.goal_ns)

        # 变量句，定义变量并赋值，用具体值实例化，提交到参数表中
        DoSimpleSceneTask.var_task(self.sink, self.sink.get_tn_name())
        # 用于某些初始化
        DoSimpleSceneTask.var_task(self.sink, None)

        print("执行时序中---|||-SimpleSceneTask")

        do_succ_task = DoSuccTask(self.sink, self.source, self.pam, 60, self.act_stamp)
        self.pam.get_assisting_task_spawner().add_task(do_succ_task)

        print(f"目前任务总数-----------------》 {len(self.pam.get_assisting_task_spawner().get_tasks())}")

        AgentStarter.is_do_var = True
        AgentStarter.do_start_tick = TaskManager.get_current_tick()

        self.cancel()

    @staticmethod
    def var_task(sink: Node, change: Optional[str]):
        """
        Handle variable task processing.

        Args:
            sink: The sink node
            change: The change string
        """
        query = ""
        if change is None or change == "":
            # 初始化变量，没有参考句
            query = f"match (n)-[r:初始句]->(m) where n.name = '{sink.get_tn_name()}' return r"
        else:
            query = f"match (n)-[r:变量句]->(m) where n.name = '{sink.get_tn_name()}' return r"

        print(f"query = {query}")
        link1 = None

        try:
            # 使用NeoUtil获取链接
            link1 = NeoUtil.get_link_cypher(query)
            if link1 is not None:
                to_node = link1.get_sink()

                if change is None or change == "":
                    # 初始化变量，没有参考句
                    g_terms = to_node.get_tn_name().split(",")
                    if "$" in g_terms[0]:
                        from linars.edu.memphis.ccrg.lida.Data.term_util import TermUtil
                        type_node = TermUtil.get_pam_node(g_terms[0], 20001 + 5)
                        value_node = TermUtil.get_pam_node(g_terms[1], 20001 + 5)
                        IsaPamTask.make_nowisa(type_node, value_node)
                else:
                    p_terms = change.split(",")
                    g_terms = to_node.get_tn_name().split(",")

                    if len(p_terms) != len(g_terms):
                        print(f"变量句中变量个数不匹配---{p_terms}----{g_terms}")
                        return
                    else:
                        for i in range(len(p_terms)):
                            if "$" in g_terms[i]:
                                # if "加数" in g_terms[i]:
                                #     continue

                                # .replace("$","")
                                from linars.edu.memphis.ccrg.lida.Data.term_util import TermUtil
                                type_node = TermUtil.get_pam_node(g_terms[i].replace(")", ""), 20001 + i)
                                value_node = TermUtil.get_pam_node(p_terms[i].replace(")", ""), 20001 + i)
                                IsaPamTask.make_nowisa(type_node, value_node)
                                print(f"---------时序---变量定义----------{type_node} = {value_node}")
        except Exception as e:
            print(f"Error in var_task: {e}")
