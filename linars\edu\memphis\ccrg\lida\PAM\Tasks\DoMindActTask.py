#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to handle mind action processing.
"""

from typing import Optional, List
import logging
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.PAM.Tasks.DoSuccTask import DoSuccTask
from linars.edu.memphis.ccrg.lida.PAM.Tasks.DoSimpleSceneTask import DoSimpleSceneTask
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import Task<PERSON>anager

# 导入 linars 组件
try:
    from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
    from linars.edu.memphis.ccrg.linars.term import Term
    from linars.org.opennars.operator.operation import Operation
    from linars.org.opennars.operator.operator import Operator
    from linars.org.opennars.entity.task import Task
    from linars.org.opennars.io.parser import Parser
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False

class DoMindActTask(FrameworkTaskImpl):
    """
    A task to handle mind action processing.
    """

    def __init__(self, sink: Node, source: Node, pam: PAMemory, seq_ns: NodeStructure,
                goal_ns: NodeStructure, act_stamp: Optional[str] = None):
        """
        Initialize a DoMindActTask.

        Args:
            sink: The sink node
            source: The source node
            pam: The PAMemory
            seq_ns: The sequence NodeStructure
            goal_ns: The goal NodeStructure
            act_stamp: The action stamp
        """
        # 时序线程，没有固定周期间隔？有defaultTicksPerRun，其实每个都会有间隔
        super().__init__(1, "tact")
        self.sink = sink
        self.source = source
        self.pam = pam
        self.seq_ns = seq_ns
        self.goal_ns = goal_ns
        self.act_stamp = act_stamp
        self.logger = logging.getLogger("DoMindActTask")

    def run_this_framework_task(self):
        """
        Run the task.
        """
        try:
            term = None
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            term1 = AgentStarter.narsese.parse_term(str(self.sink))
            if isinstance(term1, CompoundTerm):
                term = term1
            else:
                self.logger.info(f"---------不是复合词项----------term1 = {term1}")
                self.do_succ()
                return

            terms = term.term
            tt = str(term)
            # 简单分割，带有嵌套。如果是找到复合词项所有原子词项，则难复原嵌套关系。另外这也只是符号替换，不用做其他
            g_terms = tt.split(",")
            # 原方案变量实例化，有受事等硬编码
            if "$" in tt:
                # 遍历找到变量词项，然后到seqns中一一匹配，找到变量的值，然后替换
                for link in self.seq_ns.get_links():
                    if link.get_tn_name() == "nowisa":
                        cat_name = link.get_source().get_tn_name()
                        # 遍历gTerms，找到变量，然后全部替换
                        for i in range(len(g_terms)):
                            var_name = g_terms[i]
                            # 有不确定数量的右括号，先去掉，然后补上相同数量，先统计数量，再去掉
                            if ")" in var_name:
                                from linars.edu.memphis.ccrg.lida.Data.term_utilil import TermUtil
                                r_num = TermUtil.count_str(var_name, ")")
                                var_name = var_name.replace(")", "")
                                if var_name == cat_name:
                                    # 加上相同数量的右括号
                                    g_terms[i] = link.get_sink().get_tn_name()
                                    for j in range(r_num):
                                        g_terms[i] += ")"
                            else:
                                if var_name == cat_name:
                                    g_terms[i] = link.get_sink().get_tn_name()

                # 拼接字符串gTerms，然后解析成term，最后一个不要逗号
                g_terms1 = ""
                for i in range(len(g_terms)):
                    if i == len(g_terms) - 1:
                        g_terms1 += g_terms[i]
                    else:
                        g_terms1 += g_terms[i] + ","

                term = AgentStarter.narsese.parse_term(g_terms1)
                terms = term.term

            # 如果是操作，直接执行，返回结果
            # todo 假如是无符操作，查找最接近时序和原子带符操作，以相似、对等、isa等为准，然后执行
            for i in range(len(terms)):
                if isinstance(terms[i], Operation):
                    op = terms[i]
                    oper = op.get_operator()
                    feedback = oper.execute(op, op.get_arguments().term, self.goal_ns, AgentStarter.nar)
                    if feedback is not None:
                        terms[i] = feedback[0].get_term()

            terms1 = ""
            if "$" not in str(terms):
                # 拼接字符串terms，然后解析成term，最后一个不要逗号
                for i in range(len(terms)):
                    if i == len(terms) - 1:
                        terms1 += str(terms[i]) + ")"
                    elif i == 0:
                        terms1 += "(#," + str(terms[i]) + ","
                    else:
                        terms1 += str(terms[i]) + ","

                AgentStarter.nar.add_input_to(f"(^say,{{SELF}},{terms1})! :|:", self.goal_ns)

            # 原方案返回赋值。给下一环节变量实例化，位数等整体赋值由上一环节的变量实例化完成
            DoSimpleSceneTask.var_task(self.sink, terms1)
        except Parser.InvalidInputException as e:
            raise RuntimeError(e)

        self.do_succ()

    def do_succ(self):
        """
        Handle succession processing.
        """
        self.pam.set_scene_main_node(self.sink)
        # 变量句，定义变量并赋值，用具体值实例化，提交到参数表中
        DoSimpleSceneTask.var_task(self.sink, self.sink.get_tn_name())
        # 用于某些初始化
        DoSimpleSceneTask.var_task(self.sink, None)

        print(f"-----执行心理操作时序中-------|||---{self.sink.get_tn_name()}-----DoMindActTask")

        do_succ_task = DoSuccTask(self.sink, self.source, self.pam, 80, self.act_stamp)
        self.pam.get_assisting_task_spawner().add_task(do_succ_task)

        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        AgentStarter.is_do_var = True
        AgentStarter.do_start_tick = TaskManager.get_current_tick()

        self.cancel()
