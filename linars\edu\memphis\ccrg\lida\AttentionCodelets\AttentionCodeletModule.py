# LIDA Cognitive Framework
"""
Module that manages AttentionCodelets.
"""

import logging
from typing import Dict, Any, List, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Tasks.Codelet import Codelet
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener import BroadcastListener
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.ActionSelection.PreafferenceListener import PreafferenceListener
from linars.edu.memphis.ccrg.lida.AttentionCodelets.AttentionCodelet import AttentionCodelet
from linars.edu.memphis.ccrg.lida.AttentionCodelets.DefaultAttentionCodelet import DefaultAttentionCodelet

class AttentionCodeletModule(FrameworkModuleImpl, BroadcastListener, PreafferenceListener):
    """
    Module that manages AttentionCodelets.
    """

    # Default values
    DEFAULT_CODELET_ACTIVATION = 1.0
    DEFAULT_CODELET_REMOVAL_THRESHOLD = 0.0
    DEFAULT_CODELET_REINFORCEMENT = 0.5

    def __init__(self):
        """
        Initialize an AttentionCodeletModule.
        """
        super().__init__()
        self.codelet_activation = self.DEFAULT_CODELET_ACTIVATION
        self.codelet_removal_threshold = self.DEFAULT_CODELET_REMOVAL_THRESHOLD
        self.codelet_reinforcement = self.DEFAULT_CODELET_REINFORCEMENT
        self.modules_map: Dict[ModuleName, Any] = {}
        self.logger = logging.getLogger(self.__class__.__name__)

    def init(self) -> None:
        """
        Initialize this module.
        """
        self.codelet_activation = self.get_param("attentionModule.codeletActivation", self.DEFAULT_CODELET_ACTIVATION)
        self.codelet_removal_threshold = self.get_param("attentionModule.codeletRemovalThreshold", self.DEFAULT_CODELET_REMOVAL_THRESHOLD)
        self.codelet_reinforcement = self.get_param("attentionModule.codeletReinforcement", self.DEFAULT_CODELET_REINFORCEMENT)

    def get_param(self, name: str, default_value: Any) -> Any:
        """
        Get a parameter value with a default.

        Args:
            name: The name of the parameter
            default_value: The default value

        Returns:
            The parameter value or the default value
        """
        parameters = getattr(self, "parameters", {})
        if parameters and name in parameters:
            return parameters[name]
        return default_value

    def get_default_codelet(self) -> AttentionCodelet:
        """
        Get a default attention codelet.

        Returns:
            A default attention codelet
        """
        codelet = DefaultAttentionCodelet()
        codelet.set_activation(self.codelet_activation)
        codelet.set_removal_threshold(self.codelet_removal_threshold)
        return codelet

    def get_codelet(self, type_name: str, params: Dict[str, Any]) -> Optional[AttentionCodelet]:
        """
        Get an attention codelet of the specified type.

        Args:
            type_name: The type of the codelet
            params: Parameters for the codelet

        Returns:
            An attention codelet of the specified type
        """
        # In a real implementation, this would use a factory to get the codelet
        # For now, we'll just return a DefaultAttentionCodelet
        codelet = DefaultAttentionCodelet()
        codelet.init(params)
        codelet.set_activation(self.codelet_activation)
        codelet.set_removal_threshold(self.codelet_removal_threshold)
        return codelet

    def add_codelet(self, codelet: Codelet) -> None:
        """
        Add a codelet to this module.

        Args:
            codelet: The codelet to add
        """
        if isinstance(codelet, AttentionCodelet):
            self.get_assisting_task_spawner().add_task(codelet)
            self.logger.debug(f"New attention codelet: {codelet} added to run at tick {TaskManager.get_current_tick()}")
        else:
            self.logger.warning(f"Can only add an AttentionCodelet at tick {TaskManager.get_current_tick()}")

    def receive_preafference(self, add_set: NodeStructure, delete_set: NodeStructure) -> None:
        """
        Receive preafference from ActionSelection.

        Args:
            add_set: Expected additions in future percepts
            delete_set: Expected deletions in future percepts
        """
        if add_set is not None and add_set.get_node_count() > 0:
            codelet = self.get_default_codelet()
            codelet.set_sought_content(add_set.copy())
            self.add_codelet(codelet)
            self.logger.debug(f"Created new codelet for add_set at tick {TaskManager.get_current_tick()}")

        if delete_set is not None and delete_set.get_node_count() > 0:
            codelet = self.get_default_codelet()
            codelet.set_sought_content(delete_set.copy())
            self.add_codelet(codelet)
            self.logger.debug(f"Created new codelet for delete_set at tick {TaskManager.get_current_tick()}")

    def receive_broadcast(self, coalition: Coalition) -> None:
        """
        Receive a broadcast from the GlobalWorkspace.

        Args:
            coalition: The coalition that won the competition for consciousness
        """
        # This method is a placeholder for receiving broadcasts
        # It should be implemented by the receiving module
        pass

    def learn(self, coalition: Coalition) -> None:
        """
        Performs learning based on the AttentionCodelet that created the current
        winning Coalition.

        Args:
            coalition: Current Coalition winning competition for consciousness
        """
        codelet = coalition.get_creating_attention_codelet()

        if isinstance(codelet, DefaultAttentionCodelet):
            new_codelet = self.get_default_codelet()
            content = coalition.get_content()
            new_codelet.set_sought_content(content.copy())
            self.add_codelet(new_codelet)
            self.logger.debug(f"Created new codelet: {new_codelet} at tick {TaskManager.get_current_tick()}")
        elif codelet is not None:
            # Reinforcement amount might be a function of the broadcast's activation
            codelet.reinforce_base_level_activation(self.codelet_reinforcement)
            self.logger.debug(f"Reinforcing codelet: {codelet} at tick {TaskManager.get_current_tick()}")

    def get_module_content(self, *params: Any) -> Any:
        """
        Get the content of this module.

        Args:
            params: Parameters specifying what content to return

        Returns:
            The content of this module
        """
        if params and params[0] == "GlobalWorkspace":
            return self.modules_map.get(ModuleName.GlobalWorkspace)
        return None

    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.

        Args:
            ticks: The number of ticks to decay by
        """
        # TODO not yet implemented
        pass
