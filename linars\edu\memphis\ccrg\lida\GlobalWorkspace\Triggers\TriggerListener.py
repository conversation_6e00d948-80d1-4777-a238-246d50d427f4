# LIDA Cognitive Framework
"""
This interface should be implemented by the class that wants to receive BroadcastTrigger
notifications. In general, it is the same class that implements GlobalWorkspace interface.
"""

from abc import ABC, abstractmethod
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.BroadcastTrigger import BroadcastTrigger

class TriggerListener(ABC):
    """
    This interface should be implemented by the class that wants to receive BroadcastTrigger
    notifications. In general, it is the same class that implements GlobalWorkspace interface.
    """
    
    @abstractmethod
    def trigger_broadcast(self, trigger: BroadcastTrigger) -> None:
        """
        Listener must trigger a competition for consciousness and a conscious broadcast of the winner.
        
        Args:
            trigger: The trigger that is initiating the broadcast
        """
        pass
