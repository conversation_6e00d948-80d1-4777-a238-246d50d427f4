"""
Memory consists of the run-time state of a Nar, including:
  * term and concept memory
  * reasoner state
  * etc.

Excluding input/output channels which are managed by a Nar.

A memory is controlled by zero or one Nar's at a given time.

Memory is serializable so it can be persisted and transported.
"""
import logging
import random
import threading
import traceback
from typing import Dict, List, Optional, Set, Tuple, Union, Any, Iterator, Collection

from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategory import LinkCategory
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkImpl import LinkImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.entity.budget_value import BudgetValue
from linars.org.opennars.storage.bag1 import Bag1


# 延迟导入NodeStructureImpl以避免循环导入
class Memory(NodeStructureImpl):
    """
    Memory consists of the run-time state of a Nar
    """
    nar = AgentStarter.nar

    def __init__(self,
                 # narParameters=nar.narParameters,
                 # concepts=Bag1(nar.narParameters.CONCEPT_BAG_SIZE),
                 # globalBuffer=Buffer(nar, nar.narParameters.GLOBAL_BUFFER_LEVELS, nar.narParameters.GLOBAL_BUFFER_SIZE, nar.narParameters),
                 # seq_current=Buffer(nar, nar.narParameters.SEQUENCE_BAG_LEVELS, nar.narParameters.SEQUENCE_BAG_SIZE, nar.narParameters),
                 # recent_operations=Bag1(nar.narParameters.OPERATION_BAG_SIZE)
                 narParameters=None,
                 concepts=None,
                 globalBuffer=None,
                 seq_current=None,
                 recent_operations=None
                 ):
        """
        Constructor

        Args:
            narParameters: The NAR parameters
            concepts: The concept bag
            globalBuffer: The global buffer
            seq_current: The sequence buffer
            recent_operations: The recent operations bag
        """
        # 初始化NodeStructure相关的属性
        self.nodes = {}
        self.links = {}
        self.links_from = {}
        self.links_to = {}
        self.node_type = "NodeImpl"
        self.link_type = "LinkImpl"
        self.scene_time = ""
        self.scene_site = ""
        self.broad_scene_count = 0
        self.logger = logging.getLogger(self.__class__.__name__)

        # 如果直接在本类初始化，引入太多类，会循环导入
        # self.nar.init_mem(self)

        self.narParameters = narParameters

        # 初始化事件发射器
        from linars.org.opennars.io.events.event_emitter import EventEmitter
        self.event = EventEmitter()

        self.concepts = concepts if concepts else Bag1(100)  # Default size
        self.concepts_lock = threading.RLock()  # Lock for concepts bag
        self.narId = 0

        # Initialize buffers
        self.globalBuffer = globalBuffer
        if self.globalBuffer:
            self.globalBuffer.mem = self
            # Make sure seq_current is initialized
            if self.globalBuffer.seq_current:
                self.globalBuffer.seq_current.mem = self

        self.recent_operations = recent_operations

        # If seq_current is provided, use it instead of the one created in Buffer
        if seq_current and self.globalBuffer:
            self.globalBuffer.seq_current = seq_current
            self.globalBuffer.seq_current.mem = self

        # Initialize other fields
        self.operators = {}
        self.tasksMutex = True
        self.internalExperience = None
        self.lastDecision = None
        self.allowExecution = True
        self.multiAction = False
        self.lastAction = "default"
        self.last2Action = "default"
        self.randomSeed = 1
        self.randomNumber = random.Random(self.randomSeed)
        self.random_number = self.randomNumber
        self.emotion = None
        self.internalExperienceBuffer = None
        self.narseseChannel = None
        self.localInferenceMutex = False
        self.checked = False
        self.isJUnit = False

        # Initialize internal experience buffer
        if narParameters:
            levels = narParameters.INTERNAL_BUFFER_LEVELS
            capacity = narParameters.INTERNAL_BUFFER_SIZE
            # self.internalExperienceBuffer would be initialized here

        self.reset()

    def reset(self):
        """Reset the memory"""
        self.emit("ResetStart")

        # Clear concepts
        self.concepts.clear()

        # Clear buffers
        if self.globalBuffer:
            self.globalBuffer.clear()

        if hasattr(self.globalBuffer, 'seq_current') and self.globalBuffer.seq_current:
            self.globalBuffer.seq_current.clear()

        # Reset emotions
        if self.emotion:
            self.emotion.reset_emotions()

        # Clear internal experience buffer
        if self.internalExperienceBuffer:
            self.internalExperienceBuffer.clear()

        # Reset other fields
        self.lastDecision = None
        self.randomNumber = random.Random(self.randomSeed)

        self.emit("ResetEnd")

    def concept(self, t: Term):
        """
        Get an existing Concept for a given name

        Args:
            t: The name of a concept

        Returns:
            Concept: A Concept or None
        """
        if hasattr(CompoundTerm, 'replace_intervals'):
            t = CompoundTerm.replace_intervals(t)

        return self.concepts.get(str(t))

    def conceptualize(self, budget: BudgetValue, term: Term):
        """
        Get the Concept associated to a Term, or create it

        Args:
            budget: The budget value
            term: The term

        Returns:
            Concept: An existing Concept, or a new one, or None
        """
        try:
            # Skip intervals
            if hasattr(term, 'has_interval') and term.has_interval():
                return None

            if hasattr(CompoundTerm, 'replace_intervals'):
                term = CompoundTerm.replace_intervals(term)

            # Try to get existing concept
            concept = self.concepts.pick_out(str(term))

            if term.TNname == "<SELF --> [happy]>" and not isinstance(term, CompoundTerm):
                print("<SELF --> [happy]>----conceptualize---")

            # Create new concept if needed
            if concept is None:
                # 延迟导入Concept以避免循环导入
                from linars.edu.memphis.ccrg.linars.concept import Concept
                concept = Concept(budget, term, self)
                self.emit("ConceptNew", concept)
            else:
                # Apply budget to existing concept
                # BudgetFunctions.activate would be called here
                pass

            # Put back the concept
            displaced = self.concepts.put_back(concept, self.cycles(self.narParameters.CONCEPT_FORGET_DURATIONS), self)

            if displaced is None:
                # Added without replacing anything
                return concept
            elif displaced == concept:
                # Not able to insert
                self.concept_removed(displaced)
                return None
            else:
                self.concept_removed(displaced)
                return concept
        except Exception as e:
            print(f"DEBUG: Error in conceptualize for term '{term}': {e}")
            import traceback
            traceback.print_exc()
            return None

    def add_new_task(self, t, reason: str):
        """
        Add new task that waits to be processed in the next cycle

        Args:
            t: The task
            reason: The reason
        """
        if self.globalBuffer is None:
            print("Warning: globalBuffer is None in add_new_task")
            return

        self.globalBuffer.put_in(t)

        ts = t.sentence
        # Check if the term is a similarity, implication, or equivalence
        is_goal_related = False
        if hasattr(ts, 'term'):
            term_type = type(ts.term).__name__
            is_goal_related = term_type in ['Similarity', 'Implication', 'Equivalence']

        if ts.is_goal() or is_goal_related:
            # Add to goal buffer if needed
            pass

        self.emit("TaskAdd", t, reason)
        self.output(t)

    def put_in_buffer(self, t, reason: str, memory):
        """
        Put a task in the appropriate buffer

        Args:
            t: The task
            reason: The reason
            memory: The memory
        """
        if reason in ["Executed", "Derived", "emotion", "Internal"]:
            # These go to internal experience first
            if memory.internalExperienceBuffer:
                memory.internalExperienceBuffer.put_in(t)
            else:
                print("Warning: internalExperienceBuffer is None in put_in_buffer")
        else:
            if memory.globalBuffer:
                memory.globalBuffer.put_in(t)
            else:
                print("Warning: globalBuffer is None in put_in_buffer")

    @staticmethod
    def is_junit_test() -> bool:
        """
        Check if running in a JUnit test

        Returns:
            bool: True if running in a JUnit test
        """
        stack_trace = traceback.extract_stack()
        for frame in stack_trace:
            if frame.name.startswith("org.junit."):
                return True
        return False

    def input_task(self, time, task, emit_in: bool = True):
        """
        Input task processing

        Args:
            time: The time
            task: The task
            emit_in: Whether to emit an IN event
        """
        if not self.checked:
            self.checked = True
            self.isJUnit = self.is_junit_test()

        if task:
            s = task.sentence.stamp
            if s.get_creation_time() == -1:
                s.set_creation_time(time.time(), self.narParameters.DURATION)

            if emit_in:
                self.emit("IN", task)

    def remove_task(self, task, reason: str):
        """
        Remove a task

        Args:
            task: The task to remove
            reason: The reason
        """
        self.emit("TaskRemove", task, reason)

    def removeTask(self, task, reason: str):
        """
        Remove a task (Java compatibility method)

        Args:
            task: The task to remove
            reason: The reason
        """
        self.remove_task(task, reason)

    def executed_task(self, time, operation, truth):
        """
        Report an executed task

        Args:
            time: The time
            operation: The operation
            truth: The truth value
        """
        op_task = operation.get_task()

        # Create a new stamp
        stamp = None  # Stamp would be created here

        # Create a new sentence
        sentence = None  # Sentence would be created here

        # Create a new budget
        budget_for_new_task = None  # BudgetValue would be created here

        # Create a new task
        new_task = None  # Task would be created here
        new_task.set_elem_of_sequence_buffer(True)

        print(f"Memory.executedTask: {new_task}")

        self.add_new_task(new_task, "Executed")

    def output(self, t):
        """
        Output a task

        Args:
            t: The task
        """
        budget = t.budget.summary()
        noise_level = 1.0 - (self.narParameters.VOLUME / 100.0)

        if budget >= noise_level:
            self.emit("OUT", t)
            # Debug output would be added here

    def emit(self, c, *signal):
        """
        发出事件

        Args:
            c: 事件类
            *signal: 事件信号
        """
        if hasattr(self, 'event') and self.event is not None:
            self.event.emit(c, *signal)
        else:
            # 如果event为None，则打印警告信息
            print(f"Warning: event is None in emit({c}, {signal})")

    def emitting(self, channel) -> bool:
        """
        Check if a channel is active

        Args:
            channel: The channel

        Returns:
            bool: True if active
        """
        return self.event and self.event.is_active(channel)

    def concept_removed(self, c):
        """
        Handle concept removal

        Args:
            c: The removed concept
        """
        self.emit("ConceptForget", c)

    def cycle(self, nar):
        """
        Run a cycle

        Args:
            nar: The NAR
        """
        self.emit("CycleStart")
        self.channel2_task(nar)
        self.do_all(nar)
        self.emit("CycleEnd")
        if self.event:
            self.event.synch()

    def channel2_task(self, nar):
        """
        Process channels to tasks

        Args:
            nar: The NAR
        """
        # Process sensory channels
        for c in nar.sensory_channels.values():
            if c.item_size() == 0:
                continue

            task = c.take_out()
            if task:
                # Optional re-routing for vision Narsese
                if c == self.narseseChannel:
                    if nar.dispatch_to_sensory_channel(task):
                        continue

                print(f"Memory.channel2Task: {task}")
                self.add_new_task(task, "Perceived")

    def do_all(self, nar):
        """
        Do all processing steps

        Args:
            nar: The NAR
        """
        self.internal2_gb()
        self.do_g_buffer("both")
        self.do_reason(nar)

    def internal2_gb(self):
        """Process internal experience buffer to global buffer"""
        if self.internalExperienceBuffer and self.internalExperienceBuffer.mem is None:
            self.internalExperienceBuffer.mem = self

        # Process internal experience buffer
        if self.internalExperienceBuffer and self.globalBuffer:
            t_internal = self.internalExperienceBuffer.take_out()
            if t_internal:
                self.globalBuffer.put_in(t_internal)
                self.internalExperienceBuffer.put_back(t_internal, self.narParameters.INTERNAL_BUFFER_FORGET_DURATIONS, self)

    def do_g_buffer(self, target: str):
        """
        Process global buffer

        Args:
            target: The target ("both", "belief", or "goal")
        """
        # Import here to avoid circular imports
        from linars.edu.memphis.ccrg.lida.PAM.Tasks.ProcessGBufferTask import ProcessGBufferTask
        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl

        # Create a ProcessGBufferTask to process tasks from the global buffer
        process_g_buffer_task = ProcessGBufferTask(AgentStarter.pam, self, target)

        # Add the task to the task spawner
        if FrameworkModuleImpl.task_spawner:
            FrameworkModuleImpl.task_spawner.add_task(process_g_buffer_task)

    def do_reason(self, nar):
        """
        Apply reasoning by selecting a concept for inference

        Args:
            nar: The NAR instance
        """
        # Import here to avoid circular imports
        from linars.edu.memphis.ccrg.lida.PAM.Tasks.SelectConceptTask import SelectConceptTask
        from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl

        # Create a SelectConceptTask to select a concept for inference
        select_concept_task = SelectConceptTask(self, nar)

        # Add the task to the task spawner
        if FrameworkModuleImpl.task_spawner:
            FrameworkModuleImpl.task_spawner.add_task(select_concept_task)

    def local_inference(self, task, narParameters, time, mem):
        """
        执行本地推理

        Args:
            task: 要处理的任务
            narParameters: NAR参数
            time: 时间
            mem: 内存
        """
        # 检查任务是否有效
        if task is None:
            print("警告: local_inference 收到空任务")
            return

        # 检查任务是否有 get_term 方法
        if not hasattr(task, 'get_term') or not callable(getattr(task, 'get_term')):
            print(f"警告: 任务 {task} 没有 get_term 方法")
            return

        # 检查任务的术语是否有效
        term = None
        try:
            term = task.get_term()
            if term is None:
                print(f"警告: 任务 {task} 没有有效的术语")
                return
        except Exception as e:
            print(f"错误: 获取任务术语时出错: {e}")
            import traceback
            traceback.print_exc()
            return

        try:
            with threading.RLock():  # 本地推理互斥体，多线程问题导致name空值
                try:
                    # 创建推导上下文
                    from linars.org.opennars.control.derivation_context import DerivationContext
                    cont = DerivationContext(self, narParameters, time)

                    try:
                        # 设置当前任务和术语
                        cont.set_current_task(task)
                        cont.set_current_term(term)
                    except Exception as e:
                        print(f"错误: 设置当前任务和术语时出错: {e}")
                        import traceback
                        traceback.print_exc()
                        return

                    try:
                        # 概念化当前术语
                        concept = self.conceptualize(task.budget, cont.get_current_term())
                        cont.set_current_concept(concept)
                    except Exception as e:
                        print(f"错误: 概念化当前术语时出错: {e}")
                        import traceback
                        traceback.print_exc()
                        return

                    if cont.get_current_concept() is not None:
                        try:
                            # 处理任务
                            from linars.org.opennars.control.concept.process_task import ProcessTask
                            processed = ProcessTask.processTask(cont.get_current_concept(), cont, task, time, mem)
                            if processed:
                                try:
                                    self.emit("ConceptDirectProcessedTask", task)
                                except Exception as e:
                                    print(f"警告: 发出 ConceptDirectProcessedTask 事件时出错: {e}")
                        except Exception as e:
                            print(f"错误: 处理任务时出错: {e}")
                            import traceback
                            traceback.print_exc()

                    try:
                        # 处理非永恒句子
                        if hasattr(task, 'sentence') and hasattr(task.sentence, 'is_eternal'):
                            if not task.sentence.is_eternal() and not isinstance(task.sentence.term, type("Operation")):
                                if self.globalBuffer is not None:
                                    self.globalBuffer.event_inference(task, cont, False)
                                else:
                                    print("警告: globalBuffer 为空，无法执行 event_inference")
                    except Exception as e:
                        print(f"错误: 处理非永恒句子时出错: {e}")
                        import traceback
                        traceback.print_exc()

                    try:
                        # 发出事件
                        self.emit("TaskImmediateProcess", task, cont)
                    except Exception as e:
                        print(f"警告: 发出 TaskImmediateProcess 事件时出错: {e}")
                except Exception as e:
                    print(f"错误: 本地推理过程中出现未处理异常: {e}")
                    import traceback
                    traceback.print_exc()
        except Exception as e:
            print(f"严重错误: 获取本地推理互斥锁时出错: {e}")
            import traceback
            traceback.print_exc()

    def process_global_buffer_task(self, narParameters, time):
        """
        处理全局缓冲区中的任务

        Args:
            narParameters: NAR参数
            time: 时间
        """
        try:
            # 检查全局缓冲区是否存在
            if self.globalBuffer is None:
                print("警告: process_global_buffer_task 中 globalBuffer 为空")
                return

            # 从全局缓冲区中取出任务
            try:
                task = self.globalBuffer.take_out()
            except Exception as e:
                print(f"错误: 从全局缓冲区取出任务时出错: {e}")
                import traceback
                traceback.print_exc()
                return

            if task is not None:
                try:
                    # 如果任务未处理，则进行本地推理
                    if hasattr(task, 'processed') and not task.processed:
                        task.processed = True
                        self.local_inference(task, narParameters, time, self)
                except Exception as e:
                    print(f"错误: 处理任务时出错: {e}")
                    import traceback
                    traceback.print_exc()

                try:
                    # 将任务放回全局缓冲区
                    if self.globalBuffer is not None and hasattr(narParameters, 'GLOBAL_BUFFER_FORGET_DURATIONS'):
                        self.globalBuffer.put_back(task, narParameters.GLOBAL_BUFFER_FORGET_DURATIONS, self)
                    else:
                        print("警告: 无法将任务放回全局缓冲区，可能缓冲区为空或参数不完整")
                except Exception as e:
                    print(f"错误: 将任务放回全局缓冲区时出错: {e}")
                    import traceback
                    traceback.print_exc()
        except Exception as e:
            print(f"错误: 处理全局缓冲区任务时出现未处理异常: {e}")
            import traceback
            traceback.print_exc()

    def get_operator(self, op: str):
        """
        Get an operator by name

        Args:
            op: The operator name

        Returns:
            Any: The operator
        """
        return self.operators.get(op)

    def add_operator(self, op):
        """
        Add an operator

        Args:
            op: The operator

        Returns:
            Any: The operator
        """
        self.operators[op.name()] = op
        return op

    def remove_operator(self, op):
        """
        Remove an operator

        Args:
            op: The operator

        Returns:
            Any: The removed operator
        """
        return self.operators.pop(op.name(), None)

    def new_stamp_serial(self):
        """
        Create a new stamp serial

        Returns:
            Any: The new stamp serial
        """
        # BaseEntry would be created here
        return None

    def cycles(self, durations: float) -> float:
        """
        Convert durations to cycles

        Args:
            durations: The durations

        Returns:
            float: The cycles
        """
        return self.narParameters.DURATION * durations

    def __iter__(self):# -> Iterator[Concept]:
        """
        Iterator over concepts

        Returns:
            Iterator: Iterator over concepts
        """
        return iter(self.concepts)

    def new_stamp_serial(self) -> int:
        """
        Get a new stamp serial number

        Returns:
            int: The new stamp serial number
        """
        self.narId += 1
        return self.narId

    # 以下是NodeStructureImpl的关键方法

    def add_node(self, node: Node, copy: bool = True) -> Node:
        """
        添加节点到此结构

        参数:
            node: 要添加的节点
            copy: 是否复制节点

        返回:
            添加的节点
        """
        if node is None:
            return None

        node_id = node.get_node_id()
        if node_id in self.nodes:
            return self.nodes[node_id]

        if copy:
            # Create a new node of the same type
            new_node = NodeImpl(node.get_node_name())
            new_node.set_node_id(node_id)
            new_node.set_activation(node.get_activation())
            new_node.set_activation_threshold(node.get_activation_threshold())
            new_node.set_removal_threshold(node.get_removal_threshold())
            new_node.set_base_level_activation(node.get_base_level_activation())
            new_node.set_grounding_pam_node(node.get_grounding_pam_node())
            new_node.set_location(node.get_location())
            new_node.set_truth(node.get_truth())
            new_node.set_bcastid(node.get_bcastid())
            new_node.set_last_act(node.get_last_act())

            # Add the new node to this structure
            self.nodes[node_id] = new_node
            self.links_from[node_id] = set()
            return new_node
        else:
            # Add the node to this structure
            self.nodes[node_id] = node
            self.links_from[node_id] = set()
            return node

    def add_link(self, *args, **kwargs) -> Link:
        """
        添加链接到此结构

        参数:
            可以是以下两种形式之一:
            1. (link: Link, copy: bool = True) - 直接添加链接对象
            2. (link_type: str, source: Node, sink: Linkable, category: LinkCategory, activation: float = 0.0, removal_threshold: float = 0.0) - 创建并添加新链接

        返回:
            添加的链接
        """
        # 检查参数数量来确定调用哪个版本
        if len(args) == 1 or (len(args) == 2 and isinstance(args[1], bool)):
            # 第一种形式: (link, copy=True)
            return self._add_link_object(args[0], args[1] if len(args) > 1 else True)
        elif len(args) >= 4:
            # 第二种形式: (link_type, source, sink, category, activation=0.0, removal_threshold=0.0)
            link_type = args[0]
            source = args[1]
            sink = args[2]
            category = args[3]
            activation = args[4] if len(args) > 4 else 0.0
            removal_threshold = args[5] if len(args) > 5 else 0.0
            return self._add_link_components(link_type, source, sink, category, activation, removal_threshold)
        else:
            self.logger.error(f"无效的add_link参数: {args}, {kwargs}")
            return None

    def _add_link_object(self, link: Link, copy: bool = True) -> Link:
        """
        添加链接对象到此结构

        参数:
            link: 要添加的链接
            copy: 是否复制链接

        返回:
            添加的链接
        """
        if link is None:
            return None

        link_id = link.get_link_id()
        if link_id in self.links:
            return self.links[link_id]

        # Get source and sink nodes
        source = link.get_source()
        sink = link.get_sink()

        # Add source and sink nodes if they don't exist
        source_node = self.add_node(source, copy)
        sink_node = self.add_node(sink, copy)

        if source_node is None or sink_node is None:
            return None

        if copy:
            # Create a new link of the same type
            new_link = LinkImpl(source_node, sink_node, link.get_category())
            new_link.set_link_id(link_id)
            new_link.set_activation(link.get_activation())
            new_link.set_activation_threshold(link.get_activation_threshold())
            new_link.set_removal_threshold(link.get_removal_threshold())
            new_link.set_base_level_activation(link.get_base_level_activation())

            # Add the new link to this structure
            self.links[link_id] = new_link
            self.links_from[source_node.get_node_id()].add(new_link)
            if sink_node.get_node_id() not in self.links_to:
                self.links_to[sink_node.get_node_id()] = set()
            self.links_to[sink_node.get_node_id()].add(new_link)
            return new_link
        else:
            # Add the link to this structure
            self.links[link_id] = link
            self.links_from[source_node.get_node_id()].add(link)
            if sink_node.get_node_id() not in self.links_to:
                self.links_to[sink_node.get_node_id()] = set()
            self.links_to[sink_node.get_node_id()].add(link)
            return link

    def _add_link_components(self, link_type: str, source: Node, sink: Linkable, category: LinkCategory,
                             activation: float = 0.0, removal_threshold: float = 0.0) -> Link:
        """
        使用组件创建并添加链接到此结构

        参数:
            link_type: 链接类型
            source: 链接源端
            sink: 链接汇端
            category: 链接类别
            activation: 初始激活度
            removal_threshold: 移除阈值

        返回:
            添加的链接
        """
        if source is None or sink is None or category is None:
            return None

        # 检查源端和汇端是否在此结构中
        source_id = source.get_node_id()
        if source_id not in self.nodes:
            source = self.add_node(source, True)
        else:
            source = self.nodes[source_id]

        if sink.is_node():
            sink_id = sink.get_node_id()
            if sink_id not in self.nodes:
                sink = self.add_node(sink, True)
            else:
                sink = self.nodes[sink_id]

        # 检查链接是否已存在
        for link in self.links.values():
            if (link.get_source().get_node_id() == source.get_node_id() and
                link.get_sink().get_extended_id() == sink.get_extended_id() and
                link.get_category().get_id() == category.get_id()):
                return link

        # 创建新链接
        new_link = LinkImpl()
        new_link.set_source(source)
        new_link.set_sink(sink)
        new_link.set_category(category)
        new_link.set_activation(activation)
        new_link.set_removal_threshold(removal_threshold)

        # 添加链接到此结构
        link_id = new_link.get_id()
        self.links[link_id] = new_link

        # 添加链接到源端和汇端
        source_id = source.get_node_id()
        if source_id not in self.links_from:
            self.links_from[source_id] = set()
        self.links_from[source_id].add(new_link)

        sink_id = sink.get_extended_id()
        if sink_id not in self.links_to:
            self.links_to[sink_id] = set()
        self.links_to[sink_id].add(new_link)

        return new_link

    def get_node(self, node_id: int) -> Node:
        """
        获取指定节点ID的节点

        参数:
            node_id: 节点ID

        返回:
            节点对象，如果不存在则返回None
        """
        return self.nodes.get(node_id)

    def get_link(self, link_id: int) -> Link:
        """
        获取指定链接ID的链接

        参数:
            link_id: 链接ID

        返回:
            链接对象，如果不存在则返回None
        """
        return self.links.get(link_id)

    def get_nodes(self) -> Collection[Node]:
        """
        获取所有节点

        返回:
            节点集合
        """
        return self.nodes.values()

    def get_links(self) -> Collection[Link]:
        """
        获取所有链接

        返回:
            链接集合
        """
        return self.links.values()
