"""
推理机公共接口定义
定义NARS系统推理机对外暴露的标准接口
"""
from abc import ABC, abstractmethod
from typing import Optional

from linars.org.opennars.interfaces.multistepable import Multistepable
from linars.org.opennars.interfaces.timable import Timable

class Reasoner(Multistepable, Timable, ABC):
    """
    推理机公共接口
    继承自多步执行接口(Multistepable)和时间感知接口(Timable)
    定义了推理机必须实现的核心功能
    """

    @abstractmethod
    def is_running(self) -> bool:
        """
        检查推理机是否正在运行

        返回:
            bool: 推理机运行状态(True表示正在运行)
        """
        pass

    @abstractmethod
    def get_min_cycle_period_ms(self) -> int:
        """
        获取推理周期的最小延迟时间(毫秒)

        返回:
            int: 最小周期延迟时间(毫秒)
        """
        pass

    @abstractmethod
    def set_thread_yield(self, b: bool):
        """
        设置线程让步标志
        当b为True且minCyclePeriodMS==0(无延迟)时，每次run()迭代会调用Thread.yield
        用于在无延迟运行时提高程序响应性

        参数:
            b: 是否启用线程让步(True启用)
        """
        pass
