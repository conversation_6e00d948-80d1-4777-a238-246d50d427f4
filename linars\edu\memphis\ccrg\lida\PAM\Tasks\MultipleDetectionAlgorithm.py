#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
This class implements the FeatureDetector interface and provides default methods.
"""

import logging
from abc import abstractmethod
from typing import Optional

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.PAM.PamLinkable import PamLinkable
from linars.edu.memphis.ccrg.lida.PAM.Tasks.DetectionAlgorithm import DetectionAlgorithm


class MultipleDetectionAlgorithm(FrameworkTaskImpl, DetectionAlgorithm):
    """
    This class implements the FeatureDetector interface and provides default methods.
    Users should extend this class and overwrite the detect() and excitePam() methods.
    A convenience init() method is added to initialize the class. This method can be overwritten as well.
    This implementation is oriented to detect features from sensoryMemory, but the implementation can be
    used to detect and send excitation from other modules, like Workspace, emotions or internal states.

    这个类实现了FeatureDetector接口并提供了默认方法。用户应该扩展这个类并覆盖detect()和excitePam()方法。
    方便的init()方法用于初始化类。这个方法也可以被覆盖。这个实现是为了从sensoryMemory检测特征，
    但是这个实现可以用来检测和从其他模块发送激活，比如Workspace，情感或内部状态。
    """

    def __init__(self):
        """
        Default constructor.
        """
        super().__init__()
        self.logger = logging.getLogger("MultipleDetectionAlgorithm")

        # Map of PamLinkable
        self.pam_node_map = {}

        # The PAMemory
        self.pam = None

        # The SensoryMemory
        self.sensory_memory = None

        # The Environment
        self.environment = None

        # Message
        self.message0 = None

        # NodeStructure
        self.node_structure = NodeStructureImpl()

        # Sensor parameters
        self.sensor_param = {}

    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module.

        Args:
            module: The module to associate
            module_usage: The usage of the module
        """
        # 检查模块类型
        if isinstance(module, PAMemory):
            self.pam = module
            self.logger.info(f"MultipleDetectionAlgorithm: PAM module set to {module}")
        elif module_usage == "PAMemory" or module_usage == "PAM" or \
             (hasattr(module, 'get_module_name') and module.get_module_name() == "PAMemory"):
            self.pam = module
            self.logger.info(f"MultipleDetectionAlgorithm: PAM module set by usage type to {module}")
        # 尝试使用模块类名判断
        elif module.__class__.__name__ in ["PamImpl0", "PAMemoryImpl"]:
            self.pam = module
            self.logger.info(f"MultipleDetectionAlgorithm: PAM module set by class name to {module}")
        elif module_usage == "SensoryMemory":
            self.sensory_memory = module
            self.logger.info(f"MultipleDetectionAlgorithm: SensoryMemory module set to {module}")
        elif module_usage == "Environment":
            self.environment = module
            self.logger.info(f"MultipleDetectionAlgorithm: Environment module set to {module}")
        else:
            self.logger.warning(f"Cannot set associated module {module} with usage {module_usage}")

    def excite(self, object_name: str, amount: float, from_source: str) -> None:
        """
        Excite an object.

        Args:
            object_name: The name of the object to excite
            amount: The amount to excite
            from_source: The source of the excitation
        """
        if self.pam is not None:
            try:
                self.pam.excite(object_name, amount, from_source)
                if self.logger.isEnabledFor(logging.DEBUG):
                    self.logger.debug(f"Excited {object_name} with amount {amount} from {from_source}")
            except Exception as e:
                self.logger.warning(f"Error exciting {object_name}: {e}")
        else:
            # 尝试从全局变量中获取PAM模块
            try:
                from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter

                # 先尝试从全局PAM变量中获取
                if hasattr(AgentStarter, 'pam') and AgentStarter.pam is not None:
                    self.pam = AgentStarter.pam
                    self.logger.info(f"Retrieved PAM module from global variable: {self.pam}")
                    # 重新尝试激活
                    self.pam.excite(object_name, amount, from_source)
                    return

                # 如果全局PAM变量不可用，尝试从agent中获取
                if hasattr(AgentStarter, 'agent') and AgentStarter.agent is not None:
                    agent = AgentStarter.agent
                    pam_module = agent.get_submodule("PAMemory")
                    if pam_module is not None:
                        self.pam = pam_module
                        self.logger.info(f"Retrieved PAM module from Agent: {pam_module}")
                        # 重新尝试激活
                        self.pam.excite(object_name, amount, from_source)
                        return
            except Exception as e:
                self.logger.warning(f"Error retrieving PAM from global variables: {e}")

            self.logger.warning(f"Cannot excite {object_name}: PAM is None")

    def init(self, params=None) -> None:
        """
        This task can be initialized with the following parameters:

        nodes type=string: labels of the Nodes in PAMemory this algorithm detects
        """
        super().init(params)

    def add_pam_linkable(self, linkable: PamLinkable) -> None:
        """
        Add a PamLinkable.

        Args:
            linkable: The PamLinkable to add
        """
        self.pam_node_map[linkable.get_tn_name()] = linkable

    def run_this_framework_task(self) -> None:
        """
        Run this framework task.
        """
        self.detect_linkables()
        # Python的logging模块没有FINEST级别，使用DEBUG替代
        if self.logger.isEnabledFor(logging.DEBUG):
            self.logger.debug(f"Detection performed {self}")

    @abstractmethod
    def detect_linkables(self) -> None:
        """
        Detect linkables.
        """
        pass

    def set_message0(self, message0: str) -> None:
        """
        Set the message.

        Args:
            message0: The message
        """
        self.message0 = message0

    # Methods below are not applicable
    def detect(self) -> float:
        """
        Detect a feature.

        Returns:
            Value from 0.0 to 1.0 representing the degree to which the feature occurs.
        """
        return 0.0

    def get_pam_linkable(self) -> Optional[PamLinkable]:
        """
        Returns PamLinkable this algorithm can detect.

        Returns:
            The pam nodes
        """
        nodes = self.pam_node_map.values()
        if nodes:
            return next(iter(nodes))
        return None

    def set_pam_linkable(self, linkable: PamLinkable) -> None:
        """
        Adds PamLinkable that will be detected by this algorithm.

        Args:
            linkable: A PamLinkable
        """
        if linkable is not None:
            self.add_pam_linkable(linkable)
            self.logger.info(f"Set PamLinkable: {linkable}")
