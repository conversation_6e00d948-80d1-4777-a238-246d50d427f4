.shadergraph-graph {
  font: 12px sans-serif;
  line-height: 25px;
  position: relative;
}
.shadergraph-graph:after {
  content: " ";
  display: block;
  height: 0;
  font-size: 0;
  clear: both;
}
.shadergraph-graph svg {
  pointer-events: none;
}
.shadergraph-clear {
  clear: both;
}
.shadergraph-graph svg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  width: auto;
  height: auto;
}
.shadergraph-column {
  float: left;
}
.shadergraph-node .shadergraph-graph {
  float: left;
  clear: both;
  overflow: visible;
}
.shadergraph-node .shadergraph-graph .shadergraph-node {
  margin: 5px 15px 15px;
}
.shadergraph-node {
  margin: 5px 15px 25px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2), 0 1px 10px rgba(0, 0, 0, 0.2);
  min-height: 35px;
  float: left;
  clear: left;
  position: relative;
}
.shadergraph-type {
  font-weight: bold;
}
.shadergraph-header {
  font-weight: bold;
  text-align: center;
  height: 25px;
  background: rgba(0, 0, 0, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.25);
  color: #fff;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  margin-bottom: 5px;
  padding: 0 10px;
}
.shadergraph-outlet div {
}
.shadergraph-outlet-in .shadergraph-name {
  margin-right: 7px;
}
.shadergraph-outlet-out .shadergraph-name {
  margin-left: 7px;
}

.shadergraph-name {
  margin: 0 4px;
}
.shadergraph-point {
  margin: 6px;
  width: 11px;
  height: 11px;
  border-radius: 7.5px;
  background: rgba(255, 255, 255, 1);
}
.shadergraph-outlet-in {
  float: left;
  clear: left;
}
.shadergraph-outlet-in div {
  float: left;
}
.shadergraph-outlet-out {
  float: right;
  clear: right;
}
.shadergraph-outlet-out div {
  float: right;
}

.shadergraph-node-callback {
  background: rgba(205, 209, 221, 0.5);
  box-shadow: 0 1px 2px rgba(0, 10, 40, 0.2), 0 1px 10px rgba(0, 10, 40, 0.2);
}
.shadergraph-node-callback > .shadergraph-header {
  background: rgba(0, 20, 80, 0.3);
}
.shadergraph-graph .shadergraph-graph .shadergraph-node-callback {
  background: rgba(0, 20, 80, 0.1);
}

.shadergraph-node-call {
  background: rgba(209, 221, 205, 0.5);
  box-shadow: 0 1px 2px rgba(10, 40, 0, 0.2), 0 1px 10px rgba(10, 40, 0, 0.2);
}
.shadergraph-node-call > .shadergraph-header {
  background: rgba(20, 80, 0, 0.3);
}
.shadergraph-graph .shadergraph-graph .shadergraph-node-call {
  background: rgba(20, 80, 0, 0.1);
}

.shadergraph-node-isolate {
  background: rgba(221, 205, 209, 0.5);
  box-shadow: 0 1px 2px rgba(40, 0, 10, 0.2), 0 1px 10px rgba(40, 0, 10, 0.2);
}
.shadergraph-node-isolate > .shadergraph-header {
  background: rgba(80, 0, 20, 0.3);
}
.shadergraph-graph .shadergraph-graph .shadergraph-node-isolate {
  background: rgba(80, 0, 20, 0.1);
}

.shadergraph-node.shadergraph-has-code {
  cursor: pointer;
}
.shadergraph-node.shadergraph-has-code::before {
  position: absolute;
  content: " ";
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: none;
  border: 2px solid rgba(0, 0, 0, 0.25);
  border-radius: 5px;
}
.shadergraph-node.shadergraph-has-code:hover::before {
  display: block;
}
.shadergraph-code {
  z-index: 10000;
  display: none;
  position: absolute;
  background: #fff;
  color: #000;
  white-space: pre;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2), 0 1px 10px rgba(0, 0, 0, 0.2);
  font-family: monospace;
  font-size: 10px;
  line-height: 12px;
}

.shadergraph-overlay {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-top: 1px solid #ccc;
}
.shadergraph-overlay .shadergraph-view {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
}
.shadergraph-overlay .shadergraph-inside {
  width: 4000px;
  min-height: 100%;
  box-sizing: border-box;
}
.shadergraph-overlay .shadergraph-close {
  position: absolute;
  top: 5px;
  right: 5px;
  padding: 4px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.3);
  color: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  font-size: 24px;
  line-height: 24px;
  width: 24px;
  text-align: center;
  vertical-align: middle;
}
.shadergraph-overlay .shadergraph-close:hover {
  background: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 1);
}
.shadergraph-overlay .shadergraph-graph {
  padding-top: 10px;
  overflow: visible;
  min-height: 100%;
}
.shadergraph-overlay span {
  display: block;
  padding: 5px 15px;
  margin: 0;
  background: rgba(0, 0, 0, 0.1);
  font-weight: bold;
  font-family: sans-serif;
}

.mathbox-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  padding: 10px;
  border-radius: 50%;
  background: #fff;
}

.mathbox-loader.mathbox-exit {
  opacity: 0;
  -webkit-transition: opacity 0.15s ease-in-out;
  transition: opacity 0.15s ease-in-out;
}

.mathbox-progress {
  height: 10px;
  border-radius: 5px;
  width: 80px;
  margin: 0 auto 20px;
  box-shadow: 1px 1px 1px rgba(255, 255, 255, 0.2),
    1px -1px 1px rgba(255, 255, 255, 0.2), -1px 1px 1px rgba(255, 255, 255, 0.2),
    -1px -1px 1px rgba(255, 255, 255, 0.2);
  background: #ccc;
  overflow: hidden;
}

.mathbox-progress > div {
  display: block;
  width: 0px;
  height: 10px;
  background: #888;
}

.mathbox-logo {
  position: relative;
  width: 140px;
  height: 100px;
  margin: 0 auto 10px;
  -webkit-perspective: 200px;
  perspective: 200px;
}

.mathbox-logo > div {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.mathbox-logo > :nth-child(1) {
  -webkit-transform: rotateZ(22deg) rotateX(24deg) rotateY(30deg);
  transform: rotateZ(22deg) rotateX(24deg) rotateY(30deg);
}

.mathbox-logo > :nth-child(2) {
  -webkit-transform: rotateZ(11deg) rotateX(12deg) rotateY(15deg)
    scale3d(0.6, 0.6, 0.6);
  transform: rotateZ(11deg) rotateX(12deg) rotateY(15deg) scale3d(0.6, 0.6, 0.6);
}

.mathbox-logo > div > div {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -100px;
  margin-top: -100px;
  width: 200px;
  height: 200px;
  box-sizing: border-box;
  border-radius: 50%;
}

.mathbox-logo > div > :nth-child(1) {
  -webkit-transform: scale(0.5, 0.5);
  transform: rotateX(30deg) scale(0.5, 0.5);
}

.mathbox-logo > div > :nth-child(2) {
  -webkit-transform: rotateX(90deg) scale(0.42, 0.42);
  transform: rotateX(90deg) scale(0.42, 0.42);
}

.mathbox-logo > div > :nth-child(3) {
  -webkit-transform: rotateY(90deg) scale(0.35, 0.35);
  transform: rotateY(90deg) scale(0.35, 0.35);
}

.mathbox-logo > :nth-child(1) > :nth-child(1) {
  border: 16px solid #808080;
}
.mathbox-logo > :nth-child(1) > :nth-child(2) {
  border: 19px solid #a0a0a0;
}
.mathbox-logo > :nth-child(1) > :nth-child(3) {
  border: 23px solid #c0c0c0;
}
.mathbox-logo > :nth-child(2) > :nth-child(1) {
  border: 27px solid #808080;
}
.mathbox-logo > :nth-child(2) > :nth-child(2) {
  border: 32px solid #a0a0a0;
}
.mathbox-logo > :nth-child(2) > :nth-child(3) {
  border: 38px solid #c0c0c0;
}

.mathbox-splash-blue .mathbox-progress {
  background: #def;
}
.mathbox-splash-blue .mathbox-progress > div {
  background: #1979e7;
}
.mathbox-splash-blue .mathbox-logo > :nth-child(1) > :nth-child(1) {
  border-color: #1979e7;
}
.mathbox-splash-blue .mathbox-logo > :nth-child(1) > :nth-child(2) {
  border-color: #33b0ff;
}
.mathbox-splash-blue .mathbox-logo > :nth-child(1) > :nth-child(3) {
  border-color: #75eaff;
}
.mathbox-splash-blue .mathbox-logo > :nth-child(2) > :nth-child(1) {
  border-color: #18487f;
}
.mathbox-splash-blue .mathbox-logo > :nth-child(2) > :nth-child(2) {
  border-color: #33b0ff;
}
.mathbox-splash-blue .mathbox-logo > :nth-child(2) > :nth-child(3) {
  border-color: #75eaff;
}

.mathbox-overlays {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  transform-style: preserve-3d;
  overflow: hidden;
}
.mathbox-overlays > div {
  transform-style: preserve-3d;
}
.mathbox-overlay > div {
  position: absolute;
  will-change: transform, opacity;
}
.mathbox-label {
  font-family: sans-serif;
}
.mathbox-outline-1 {
  text-shadow: -1px -1px 0px rgb(255, 255, 255), 1px 1px 0px rgb(255, 255, 255),
    -1px 1px 0px rgb(255, 255, 255), 1px -1px 0px rgb(255, 255, 255),
    1px 0px 1px rgb(255, 255, 255), -1px 0px 1px rgb(255, 255, 255),
    0px -1px 1px rgb(255, 255, 255), 0px 1px 1px rgb(255, 255, 255);
}
.mathbox-outline-2 {
  text-shadow: 0px -2px 0px rgb(255, 255, 255), 0px 2px 0px rgb(255, 255, 255),
    -2px 0px 0px rgb(255, 255, 255), 2px 0px 0px rgb(255, 255, 255),
    -1px -2px 0px rgb(255, 255, 255), -2px -1px 0px rgb(255, 255, 255),
    -1px 2px 0px rgb(255, 255, 255), -2px 1px 0px rgb(255, 255, 255),
    1px 2px 0px rgb(255, 255, 255), 2px 1px 0px rgb(255, 255, 255),
    1px -2px 0px rgb(255, 255, 255), 2px -1px 0px rgb(255, 255, 255);
}
.mathbox-outline-3 {
  text-shadow: 3px 0px 0px rgb(255, 255, 255), -3px 0px 0px rgb(255, 255, 255),
    0px 3px 0px rgb(255, 255, 255), 0px -3px 0px rgb(255, 255, 255),
    -2px -2px 0px rgb(255, 255, 255), -2px 2px 0px rgb(255, 255, 255),
    2px 2px 0px rgb(255, 255, 255), 2px -2px 0px rgb(255, 255, 255),
    -1px -2px 1px rgb(255, 255, 255), -2px -1px 1px rgb(255, 255, 255),
    -1px 2px 1px rgb(255, 255, 255), -2px 1px 1px rgb(255, 255, 255),
    1px 2px 1px rgb(255, 255, 255), 2px 1px 1px rgb(255, 255, 255),
    1px -2px 1px rgb(255, 255, 255), 2px -1px 1px rgb(255, 255, 255);
}
.mathbox-outline-4 {
  text-shadow: 4px 0px 0px rgb(255, 255, 255), -4px 0px 0px rgb(255, 255, 255),
    0px 4px 0px rgb(255, 255, 255), 0px -4px 0px rgb(255, 255, 255),
    -3px -2px 0px rgb(255, 255, 255), -3px 2px 0px rgb(255, 255, 255),
    3px 2px 0px rgb(255, 255, 255), 3px -2px 0px rgb(255, 255, 255),
    -2px -3px 0px rgb(255, 255, 255), -2px 3px 0px rgb(255, 255, 255),
    2px 3px 0px rgb(255, 255, 255), 2px -3px 0px rgb(255, 255, 255),
    -1px -2px 1px rgb(255, 255, 255), -2px -1px 1px rgb(255, 255, 255),
    -1px 2px 1px rgb(255, 255, 255), -2px 1px 1px rgb(255, 255, 255),
    1px 2px 1px rgb(255, 255, 255), 2px 1px 1px rgb(255, 255, 255),
    1px -2px 1px rgb(255, 255, 255), 2px -1px 1px rgb(255, 255, 255);
}
.mathbox-outline-fill,
.mathbox-outline-fill * {
  color: #fff !important;
}
