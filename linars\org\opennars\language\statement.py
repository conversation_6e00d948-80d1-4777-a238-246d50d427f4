"""
语句项定义
按照NARS理论定义的复合项，由主语、谓语和中间的关系符号组成。
可以是第一阶或高阶语句。
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar
from enum import Enum
import sys

from linars.edu.memphis.ccrg.linars.term import Term
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.org.opennars.io.symbols import NativeOperator

# Import symbols
STATEMENT_OPENER = '<'
STATEMENT_CLOSER = '>'

# Temporal order constants
ORDER_NONE = 0
ORDER_FORWARD = 1
ORDER_CONCURRENT = 2
ORDER_BACKWARD = 3

# class NativeOperator(Enum):
#     """Native operators enum"""
#     INHERITANCE = "-->"
#     SIMILARITY = "<->"
#     INSTANCE = "{--"
#     PROPERTY = "--]"
#     INSTANCE_PROPERTY = "{-]"
#     IMPLICATION = "==>"
#     IMPLICATION_AFTER = "=/>"
#     IMPLICATION_BEFORE = "=\\>"
#     IMPLICATION_WHEN = "=|>"
#     EQUIVALENCE = "<=>"
#     EQUIVALENCE_AFTER = "</>"
#     EQUIVALENCE_WHEN = "<|>"

class EnumStatementSide(Enum):
    """Statement side enum"""
    SUBJECT = 0
    PREDICATE = 1

class Statement(CompoundTerm):
    """
    语句项类
    按照NARS理论定义的复合项，由主语、谓语和中间的关系符号组成。
    可以是第一阶或高阶语句。
    """

    def __init__(self, args: List[Term] = None):
        """
        构造函数

        参数:
            args: 项的组成部分列表
        """
        super().__init__()
        if args:
            self.init(args)

    def init(self, t: List[Term]):
        """
        初始化语句项

        参数:
            t: 项列表
        """
        if len(t) != 2:
            raise ValueError(f"Requires 2 terms: {t}")

        if t[0] is None:
            raise ValueError(f"Null subject: {self}")

        if t[1] is None:
            raise ValueError(f"Null predicate: {self}")

        # 如果关系符号是可交换的，且主语大于谓语，则抛出异常
        # java中Debug.DETAILED为False，直接注释掉
        # if self.is_commutative() and t[0].compare_to(t[1]) == 1:
        #     raise ValueError(f"Commutative term requires natural order of subject,predicate: {t}")

        super().init(t)

    @staticmethod
    def make_from_statement_type(statement: 'Statement', subj: Term, pred: Term) -> Optional['Statement']:
        """
        从给定组件创建语句项

        参数:
            statement: 提供类类型的示例语句
            subj: 第一个组件(主语)
            pred: 第二个组件(谓语)

        返回:
            Statement: 构建的语句项
        """
        from linars.org.opennars.language.inheritance import Inheritance
        from linars.org.opennars.language.similarity import Similarity
        from linars.org.opennars.language.implication import Implication
        from linars.org.opennars.language.equivalence import Equivalence

        if isinstance(statement, Inheritance):
            return Inheritance.make(subj, pred)

        if isinstance(statement, Similarity):
            return Similarity.make(subj, pred)

        if isinstance(statement, Implication):
            return Implication.make(subj, pred, statement.get_temporal_order())

        if isinstance(statement, Equivalence):
            return Equivalence.make(subj, pred, statement.get_temporal_order())

        return None

    @staticmethod
    def make(*args, **kwargs) -> Optional['Statement']:
        """
        从给定组件创建语句项

        可以接受两种不同的参数形式:
        1. (statement, subj, pred) - 从示例语句创建
        2. (op, subject, predicate, custom_order, order) - 从操作符创建

        参数:
            参见上述两种形式

        返回:
            Statement: 构建的语句项

        注意: 这是一个多功能方法，可以处理不同的参数组合
        """
        # 检查参数类型和数量
        if len(args) == 3 and isinstance(args[0], Statement):
            # 形式 1: (statement, subj, pred)
            return Statement.make_from_statement_type(args[0], args[1], args[2])
        elif len(args) >= 3 and isinstance(args[0], NativeOperator):
            # 形式 2: (op, subject, predicate, custom_order, order)
            op = args[0]
            subject = args[1]
            predicate = args[2]
            custom_order = args[3] if len(args) > 3 else kwargs.get('custom_order', False)
            order = args[4] if len(args) > 4 else kwargs.get('order', ORDER_NONE)
            return Statement.make_from_op(op, subject, predicate, custom_order, order)
        else:
            raise ValueError(f"Invalid arguments for Statement.make: {args}")

    @staticmethod
    def make_from_op(op: NativeOperator, subject: Term, predicate: Term, custom_order: bool = False, order: int = ORDER_NONE) -> Optional['Statement']:
        """
        从字符串创建语句项

        参数:
            op: 关系操作符
            subject: 第一个组件(主语)
            predicate: 第二个组件(谓语)
            custom_order: 是否使用自定义顺序
            order: 时间顺序

        返回:
            Statement: 构建的语句项，如果无法创建则返回None
        """
        try:
            # 参数验证
            if subject is None:
                print(f"警告: make_from_op 收到了空的主语")
                return None

            if predicate is None:
                print(f"警告: make_from_op 收到了空的谓语")
                return None

            # 确保操作符是有效的
            original_op = op
            # 确保 op 是 NativeOperator 枚举值
            if not isinstance(op, NativeOperator):
                try:
                    # 如果是元组，尝试找到对应的枚举值
                    if isinstance(op, tuple) and len(op) > 0:
                        symbol = op[0]
                        for enum_op in NativeOperator:
                            if enum_op.symbol == symbol:
                                op = enum_op
                                break
                    else:
                        # 如果不是元组也不是枚举值，尝试将其转换为字符串并找到对应的枚举值
                        symbol = str(op)
                        for enum_op in NativeOperator:
                            if enum_op.symbol == symbol:
                                op = enum_op
                                break

                    # 如果仍然不是NativeOperator，记录警告并返回None
                    if not isinstance(op, NativeOperator):
                        print(f"警告: 无法将 {original_op} 转换为有效的NativeOperator")
                        return None
                except Exception as e:
                    print(f"错误: 在转换操作符 {original_op} 时发生异常: {e}")
                    return None

            # 导入需要的类
            try:
                from linars.org.opennars.language.inheritance import Inheritance
                from linars.org.opennars.language.similarity import Similarity
                from linars.org.opennars.language.instance import Instance
                from linars.org.opennars.language.property import Property
                from linars.org.opennars.language.instance_property import InstanceProperty
                from linars.org.opennars.language.implication import Implication
                from linars.org.opennars.language.equivalence import Equivalence
            except ImportError as e:
                print(f"错误: 导入语句类型时发生异常: {e}")
                return None

            # 检查图像和乘积方面的相等子项
            try:
                if Statement.equal_sub_terms_in_respect_to_image_and_product(subject, predicate):
                    return None
            except Exception as e:
                print(f"警告: 在检查图像和乘积方面的相等子项时发生异常: {e}")
                # 继续执行，不要因为这个检查失败而中断整个方法

            # 根据操作符创建相应的语句
            try:
                if op == NativeOperator.INHERITANCE:
                    return Inheritance.make(subject, predicate)
                elif op == NativeOperator.SIMILARITY:
                    return Similarity.make(subject, predicate)
                elif op == NativeOperator.INSTANCE:
                    return Instance.make(subject, predicate)
                elif op == NativeOperator.PROPERTY:
                    return Property.make(subject, predicate)
                elif op == NativeOperator.INSTANCE_PROPERTY:
                    return InstanceProperty.make(subject, predicate)
                elif op == NativeOperator.IMPLICATION:
                    return Implication.make(subject, predicate, order if custom_order else ORDER_NONE)
                elif op == NativeOperator.IMPLICATION_AFTER:
                    return Implication.make(subject, predicate, order if custom_order else ORDER_FORWARD)
                elif op == NativeOperator.IMPLICATION_BEFORE:
                    return Implication.make(subject, predicate, order if custom_order else ORDER_BACKWARD)
                elif op == NativeOperator.IMPLICATION_WHEN:
                    return Implication.make(subject, predicate, order if custom_order else ORDER_CONCURRENT)
                elif op == NativeOperator.EQUIVALENCE:
                    return Equivalence.make(subject, predicate, order if custom_order else ORDER_NONE)
                elif op == NativeOperator.EQUIVALENCE_AFTER:
                    return Equivalence.make(subject, predicate, order if custom_order else ORDER_FORWARD)
                elif op == NativeOperator.EQUIVALENCE_WHEN:
                    return Equivalence.make(subject, predicate, order if custom_order else ORDER_CONCURRENT)
                else:
                    print(f"警告: 未知的操作符类型: {op}")
                    return None
            except Exception as e:
                print(f"错误: 在创建语句 '{op.symbol if hasattr(op, 'symbol') else op}' 时发生异常: {e}")
                return None

        except Exception as e:
            print(f"错误: make_from_op 方法发生未处理的异常: {e}")
            return None

        return None

    @staticmethod
    def make_with_order(op: NativeOperator, subj: Term, pred: Term, order: int) -> Optional['Statement']:
        """
        从给定项创建带顺序的语句项

        参数:
            op: 操作符
            subj: 第一个组件(主语)
            pred: 第二个组件(谓语)
            order: 时间顺序

        返回:
            Statement: 构建的语句项
        """
        return Statement.make_from_op(op, subj, pred, True, order)

    @staticmethod
    def make_from_statement(statement: 'Statement', subj: Term, pred: Term, order: int) -> Optional['Statement']:
        """
        从另一个语句项创建语句项

        参数:
            statement: 源语句项
            subj: 第一个组件(主语)
            pred: 第二个组件(谓语)
            order: 时间顺序

        返回:
            Statement: 构建的语句项
        """
        return Statement.make_from_op(statement.operator(), subj, pred, True, order)

    @staticmethod
    def make_sym(statement: 'Statement', subj: Term, pred: Term, order: int) -> Optional['Statement']:
        """
        从给定项和时间信息创建对称语句项
        参数:
            statement: 提供类类型的示例非对称语句
            subj: 第一个组件(主语)
            pred: 第二个组件(谓语)
            order: 时间顺序
        返回:
            Statement: 构建的语句项
        """
        from linars.org.opennars.language.inheritance import Inheritance
        from linars.org.opennars.language.similarity import Similarity
        from linars.org.opennars.language.implication import Implication
        from linars.org.opennars.language.equivalence import Equivalence

        if isinstance(statement, Inheritance):
            return Similarity.make(subj, pred)

        if isinstance(statement, Similarity):
            return Similarity.make(subj, pred)

        if isinstance(statement, Implication):
            return Equivalence.make(subj, pred, order)

        if isinstance(statement, Equivalence):
            return Equivalence.make(subj, pred, order)

        return None

    def make_name(self) -> str:
        """
        重写默认方法，从现有字段创建当前项的名称
        返回:
            str: 项的名称
        """
        return self.make_statement_name(self.get_subject(), self.operator(), self.get_predicate())

    @staticmethod
    def make_statement_name(subject: Term, relation: NativeOperator, predicate: Term) -> str:
        """
        创建语句名称
        参数:
            subject: 主语项
            relation: 关系操作符
            predicate: 谓语项
        返回:
            str: 语句名称
        """
        subject_name = subject.name()
        predicate_name = predicate.name()

        if subject_name is None:
            subject_name = subject.name()

        if predicate_name is None:
            predicate_name = predicate.name()

        return f"{STATEMENT_OPENER}{subject_name} {relation.symbol} {predicate_name}{STATEMENT_CLOSER}"

    @staticmethod
    def invalid_statement(subject: Term, predicate: Term, check_same_term_in_predicate_and_subject: bool = True) -> bool:
        """
        检查潜在语句的有效性
        参数:
            subject: 第一个组件(主语)
            predicate: 第二个组件(谓语)
            check_same_term_in_predicate_and_subject: 是否检查谓语和主语中的相同项
        返回:
            bool: 语句是否无效
        """
        if subject is None or predicate is None:
            return True

        if check_same_term_in_predicate_and_subject and subject == predicate:
            return True

        if check_same_term_in_predicate_and_subject and Statement.invalid_reflexive(subject, predicate):
            return True

        if check_same_term_in_predicate_and_subject and Statement.invalid_reflexive(predicate, subject):
            return True

        if isinstance(subject, Statement) and isinstance(predicate, Statement):
            s1 = subject
            s2 = predicate
            t11 = s1.get_subject()
            t22 = s2.get_predicate()
            t12 = s1.get_predicate()
            t21 = s2.get_subject()
            return t11 == t22 and t12 == t21

        return False

    @staticmethod
    def invalid_reflexive(t1: Term, t2: Term) -> bool:
        """
        检查一个项是否与另一个项相同或包含在其中(自反关系除外)

        参数:
            t1: 第一个项
            t2: 第二个项

        返回:
            bool: 它们是否不能在语句中关联
        """
        if not isinstance(t1, CompoundTerm):
            return False

        ct1 = t1

        # Check for ImageExt or ImageInt
        if hasattr(ct1, 'is_image_ext') and ct1.is_image_ext():
            return False

        if hasattr(ct1, 'is_image_int') and ct1.is_image_int():
            return False

        return ct1.contains_term_recursively(t2)

    @staticmethod
    def invalid_pair(s1: Term, s2: Term) -> bool:
        """
        检查一对项是否无效

        参数:
            s1: 第一个项
            s2: 第二个项

        返回:
            bool: 这对项是否无效
        """
        s1_indep = s1.has_var_indep()
        s2_indep = s2.has_var_indep()

        if s1_indep and not s2_indep:
            return True
        elif not s1_indep and s2_indep:
            return True

        return False

    def invalid(self) -> bool:
        """
        检查潜在语句的有效性

        返回:
            bool: 语句是否无效
        """
        return self.invalid_statement(self.get_subject(), self.get_predicate())

    def get_subject(self) -> Term:
        """
        获取语句的第一个组件(主语)

        返回:
            Term: 第一个组件
        """
        return self.term[0]

    def set_subject(self, term1: Term):
        """
        设置语句的主语

        参数:
            term1: 新的主语
        """
        self.term[0] = term1
        self.set_term_name(self.make_name())

    def get_predicate(self) -> Term:
        """
        获取语句的第二个组件(谓语)

        返回:
            Term: 第二个组件
        """
        return self.term[1]

    def set_predicate(self, term2: Term):
        """
        设置语句的谓语

        参数:
            term2: 新的谓语
        """
        self.term[1] = term2
        self.set_term_name(self.make_name())

    def ret_by_side(self, side: EnumStatementSide) -> Term:
        """
        根据侧边返回主语(0)或谓语(1)

        参数:
            side: 主语(0)或谓语(1)

        返回:
            Term: 对应侧的项
        """
        return self.get_subject() if side == EnumStatementSide.SUBJECT else self.get_predicate()

    @staticmethod
    def ret_opposite_side(side: EnumStatementSide) -> EnumStatementSide:
        """
        返回相反的侧边

        参数:
            side: 当前侧边

        返回:
            EnumStatementSide: 相反的侧边
        """
        return EnumStatementSide.PREDICATE if side == EnumStatementSide.SUBJECT else EnumStatementSide.SUBJECT

    @staticmethod
    def equal_sub_terms_in_respect_to_image_and_product(subject: Term, predicate: Term) -> bool:
        """
        检查项在图像和乘积方面是否相等

        参数:
            subject: 主语项
            predicate: 谓语项

        返回:
            bool: 如果相等则为True，如果发生异常则返回False
        """
        try:
            # 参数验证
            if subject is None or predicate is None:
                return False

            # 检查是否为图像项
            try:
                if (hasattr(subject, 'is_image') and callable(getattr(subject, 'is_image')) and
                    hasattr(predicate, 'is_image') and callable(getattr(predicate, 'is_image'))):

                    # 安全调用is_image方法
                    try:
                        subject_is_image = subject.is_image()
                        predicate_is_image = predicate.is_image()
                    except Exception as e:
                        print(f"警告: 调用is_image方法时发生异常: {e}")
                        return False

                    if subject_is_image and predicate_is_image:
                        # 检查是否为相同类型的图像
                        try:
                            subject_is_image_ext = hasattr(subject, 'is_image_ext') and callable(getattr(subject, 'is_image_ext')) and subject.is_image_ext()
                            predicate_is_image_ext = hasattr(predicate, 'is_image_ext') and callable(getattr(predicate, 'is_image_ext')) and predicate.is_image_ext()
                            subject_is_image_int = hasattr(subject, 'is_image_int') and callable(getattr(subject, 'is_image_int')) and subject.is_image_int()
                            predicate_is_image_int = hasattr(predicate, 'is_image_int') and callable(getattr(predicate, 'is_image_int')) and predicate.is_image_int()

                            if (subject_is_image_ext and predicate_is_image_ext) or (subject_is_image_int and predicate_is_image_int):
                                # 检查关系索引是否相同
                                if hasattr(subject, 'relation_index') and hasattr(predicate, 'relation_index'):
                                    try:
                                        if subject.relation_index == predicate.relation_index:
                                            # 检查关系项是否相同
                                            if (hasattr(subject, 'term') and hasattr(predicate, 'term') and
                                                isinstance(subject.term, (list, tuple)) and isinstance(predicate.term, (list, tuple))):

                                                try:
                                                    if (len(subject.term) > subject.relation_index and
                                                        len(predicate.term) > predicate.relation_index and
                                                        subject.term[subject.relation_index] == predicate.term[predicate.relation_index]):
                                                        return True
                                                except (IndexError, TypeError) as e:
                                                    print(f"警告: 访问term索引时发生异常: {e}")
                                                    return False
                                    except Exception as e:
                                        print(f"警告: 比较relation_index时发生异常: {e}")
                                        return False
                        except Exception as e:
                            print(f"警告: 检查图像类型时发生异常: {e}")
                            return False
            except Exception as e:
                print(f"警告: 检查图像项时发生异常: {e}")
                # 继续执行，不要因为这个检查失败而中断整个方法

            # 检查是否为乘积项
            try:
                if (hasattr(subject, 'is_product') and callable(getattr(subject, 'is_product')) and
                    hasattr(predicate, 'is_product') and callable(getattr(predicate, 'is_product'))):

                    # 安全调用is_product方法
                    try:
                        subject_is_product = subject.is_product()
                        predicate_is_product = predicate.is_product()
                    except Exception as e:
                        print(f"警告: 调用is_product方法时发生异常: {e}")
                        return False

                    if subject_is_product and predicate_is_product:
                        # 检查项数是否相同
                        if (hasattr(subject, 'term') and hasattr(predicate, 'term') and
                            isinstance(subject.term, (list, tuple)) and isinstance(predicate.term, (list, tuple))):

                            try:
                                if len(subject.term) == len(predicate.term):
                                    # 检查每个项是否相同
                                    all_same = True
                                    for i in range(len(subject.term)):
                                        try:
                                            if subject.term[i] != predicate.term[i]:
                                                all_same = False
                                                break
                                        except (IndexError, TypeError) as e:
                                            print(f"警告: 比较term元素时发生异常: {e}")
                                            all_same = False
                                            break
                                    if all_same:
                                        return True
                            except Exception as e:
                                print(f"警告: 比较term长度时发生异常: {e}")
                                return False
            except Exception as e:
                print(f"警告: 检查乘积项时发生异常: {e}")
                # 继续执行，不要因为这个检查失败而中断整个方法

            return False

        except Exception as e:
            print(f"错误: equal_sub_terms_in_respect_to_image_and_product方法发生未处理的异常: {e}")
            return False

    def clone(self) -> 'Statement':
        """
        克隆语句项

        返回:
            Statement: 克隆的语句项
        """
        # This is an abstract method that should be implemented by subclasses
        raise NotImplementedError("Subclasses must implement clone()")
