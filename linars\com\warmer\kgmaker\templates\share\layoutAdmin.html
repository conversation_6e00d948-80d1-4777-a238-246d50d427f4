<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
	xmlns:layout="http://www.ultraq.net.nz/web/thymeleaf/layout">

<head>
	<meta charset="utf-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<meta name="_csrf" th:content="${_csrf.token}" />
	<!-- default header name is X-CSRF-TOKEN spring security 避免ajax请求403-->
	<meta name="_csrf_header" th:content="${_csrf.headerName}" />
	<title>首页</title>
	<link href="https://cdn.bootcss.com/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcss.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://cdn.bootcss.com/animate.css/3.5.2/animate.min.css" rel="stylesheet">
    <link th:href="@{/css/style.css}" rel="stylesheet">
    <!-- import css -->
    <link href="#" rel="stylesheet">
    <link th:href="@{/css/element-style.css}" rel="stylesheet">
	<style type="text/css">
	[v-cloak] {
	    	display: none !important;
		}
	.txtCenter {
		text-align: center;
	}
	.app-container{
	  margin:20px;
	}
	</style>
	<script type="text/javascript">
	var contextRoot = "[[@{/}]]";
	</script>
</head>

<body class="fixed-sidebar full-height-layout white-bg ">
	<div id="wrapper">
            <!-- 侧边栏导航 -->
            <div id="navBar" th:include="share/sidebar::sidebar"></div>
            <!--右侧部分开始-->
            <div id="page-wrapper" class="gray-bg dashbard-1">
                <!-- 头部工具栏 -->
                <div th:include="share/headerAdmin::headerAdmin"></div>
                <div class="row J_mainContent" >
                    <!-- 正文内容 -->
                    <div id="app" style="min-height: 100%;background-color:#fff">
                       <div layout:fragment="content"></div>
                    </div>
                </div>
            </div>
            <!--右侧部分结束-->
        </div>
	<!-- 全局js -->
    <script src="https://cdn.bootcss.com/jquery/2.2.4/jquery.min.js"></script>
    <script src="https://cdn.bootcss.com/bootstrap/3.3.6/js/bootstrap.min.js"></script> 
	<script  src="https://cdn.bootcss.com/vue/2.5.17-beta.0/vue.js"></script>  
    <script src="https://cdn.bootcss.com/element-ui/2.4.0/index.js"></script>
    <script src="https://cdn.bootcss.com/moment.js/2.22.1/moment.min.js"></script>
    <script src="https://cdn.bootcss.com/jQuery-slimScroll/1.3.8/jquery.slimscroll.min.js"></script>
    <script src="https://cdn.bootcss.com/axios/0.18.0/axios.min.js"></script>

    <script th:inline="javascript" type="text/javascript" th:src="@{/js/chatC/res.layui.com/layui/src/layui.js}"></script>-->
    <script th:inline="javascript" type="text/javascript" th:src="@{/js/chatC/res.layui.com/mods/socket0.js}"></script>
    <script th:inline="javascript" type="text/javascript" th:src="@{/js/chatC/res.layui.com/mods/webim.config.js}"></script>
    <script th:inline="javascript" type="text/javascript" th:src="@{/js/chatC/res.layui.com/mods/websdk.js}"></script>

        <script th:src="@{/js/iconfont.js}"></script>
        <script th:src="@{/js/sidebarAdmin.js}"></script>


        <script type="text/javascript">
            var token = $("meta[name='_csrf']").attr("content");
            var header = $("meta[name='_csrf_header']").attr("content");
            axios.defaults.headers.common[header] = token;
            axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
            $(document).ajaxSend(function(e, xhr, options) {
                xhr.setRequestHeader(header, token);
            });
        </script>
        <script th:inline="javascript" type="text/javascript">
          new Vue({
            el: '#navBar',
            data:{
                currentUrl:[[${#request.getRequestURI()}]],
                menuList:[
                    {
                        title:'图谱管理',icon:'el-icon-document',linkUrl:'#',active:false,childrens:[
                            {title:'领域列表',icon:'',linkUrl:'/article',active:false,childrens:[]},
                        ]
                    }
                ]
              },
              created(){
                  this.initActiveItem();
              },
              methods: {
                  initActiveItem() {
                    var _this=this;
                    _this.menuList.forEach(function (item) {
                        for(var i=0;i<item.childrens.length;i++){
                            if(item.childrens[i].linkUrl==_this.currentUrl){
                                item.active=true;
                                item.childrens[i].active=true;
                                return;
                            }
                            if(item.active) return;
                        }
                    })
                  },
                  collapseClick(item){
                      //其他节点都折叠
                      this.menuList.forEach(function (it) {
                          it.active=false;
                        })
                        //操作当前节点
                      item.active=!item.active;
                  }
                }
            })
        </script>
        <!-- js内容 -->
	<div layout:fragment="jscontent"></div>
</body>
</html>