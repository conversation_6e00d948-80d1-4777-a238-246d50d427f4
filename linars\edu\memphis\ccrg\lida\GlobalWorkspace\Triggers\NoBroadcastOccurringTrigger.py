# LIDA Cognitive Framework
"""
A BroadcastTrigger that triggers a broadcast if no broadcast is currently occurring.
"""

import logging
from typing import Dict, Any, Collection
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.GlobalWorkspace import GlobalWorkspace
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.BroadcastTrigger import BroadcastTrigger
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.TriggerTask import TriggerTask

class NoBroadcastOccurringTrigger(BroadcastTrigger):
    """
    A BroadcastTrigger that triggers a broadcast if no broadcast is currently occurring.
    """
    
    # Default values
    DEFAULT_DELAY = 10
    DEFAULT_NAME = "NoBroadcastOccurringTrigger"
    
    def __init__(self):
        """
        Initialize a NoBroadcastOccurringTrigger.
        """
        self.delay = self.DEFAULT_DELAY
        self.name = self.DEFAULT_NAME
        self.gw = None
        self.task = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def init(self, params: Dict[str, Any], gw: GlobalWorkspace) -> None:
        """
        Initialize this trigger.
        
        Args:
            params: Parameters for initialization
            gw: The GlobalWorkspace
        """
        self.gw = gw
        
        delay = params.get("delay")
        if isinstance(delay, int) and delay > 0:
            self.delay = delay
        else:
            self.delay = self.DEFAULT_DELAY
            self.logger.warning(f"Failed to set delay parameter, using default at tick {TaskManager.get_current_tick()}")
        
        name = params.get("name")
        if isinstance(name, str):
            self.name = name
        else:
            self.name = self.DEFAULT_NAME
            self.logger.warning(f"Failed to set name parameter, using default at tick {TaskManager.get_current_tick()}")
    
    def start(self) -> None:
        """
        Start this trigger.
        """
        self.task = TriggerTask(self.delay, self.gw, self.name, self)
        self.gw.get_assisting_task_spawner().add_task(self.task)
    
    def check_for_trigger_condition(self, coalitions: Collection[Coalition]) -> None:
        """
        Check if the trigger condition is met.
        
        Args:
            coalitions: The coalitions in the GlobalWorkspace
        """
        # This trigger doesn't check coalitions, it just triggers periodically
        pass
    
    def reset(self) -> None:
        """
        Reset this trigger.
        """
        if self.task is not None:
            self.gw.get_assisting_task_spawner().cancel_task(self.task)
        self.start()
