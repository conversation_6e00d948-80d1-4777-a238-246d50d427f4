# LIDA Cognitive Framework
"""
A task that starts all BroadcastTriggers in the GlobalWorkspace.
"""

import logging
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.GlobalWorkspace import GlobalWorkspace

class StartTriggersTask(FrameworkTaskImpl):
    """
    A task that starts all BroadcastTriggers in the GlobalWorkspace.
    """

    def __init__(self):
        """
        Initialize a StartTriggersTask.
        """
        super().__init__()
        self.global_workspace = None
        self.logger = logging.getLogger(self.__class__.__name__)

    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module for this task.

        Args:
            module: The module to associate with this task
            module_usage: How this task will use the module
        """
        if isinstance(module, GlobalWorkspace):
            self.global_workspace = module

    def run_this_framework_task(self) -> None:
        """
        Run this task.
        """
        if self.global_workspace is not None:
            self.logger.info(f"Starting {len(self.global_workspace.broadcast_triggers)} broadcast triggers at tick {TaskManager.get_current_tick()}")
            for trigger in self.global_workspace.broadcast_triggers:
                try:
                    trigger.start()
                    self.logger.debug(f"Started trigger {trigger} at tick {TaskManager.get_current_tick()}")
                except Exception as e:
                    self.logger.error(f"Error starting trigger {trigger}: {e} at tick {TaskManager.get_current_tick()}")
                    import traceback
                    traceback.print_exc()
            self.cancel()
        else:
            self.logger.warning(f"GlobalWorkspace is null at tick {TaskManager.get_current_tick()}")
            self.cancel()
