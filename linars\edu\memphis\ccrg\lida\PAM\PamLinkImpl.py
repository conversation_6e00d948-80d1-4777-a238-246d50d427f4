# LIDA Cognitive Framework
"""
Default implementation of PamLink.
"""

import logging
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkImpl import LinkImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.LearnableImpl import LearnableImpl
from linars.edu.memphis.ccrg.lida.Framework.Strategies.ExciteStrategy import ExciteStrategy
from linars.edu.memphis.ccrg.lida.Framework.Strategies.DecayStrategy import DecayStrategy
from linars.edu.memphis.ccrg.lida.PAM.PamLink import PamLink

class PamLinkImpl(LinkImpl, PamLink):
    """
    Default implementation of PamLink.
    """

    def __init__(self):
        """
        Initialize a PamLinkImpl.
        """
        # print("PamLinkImpl---__init__--0---")
        super().__init__()
        self.grounding_pam_link = self
        self.learnable = LearnableImpl()
        # print("PamLinkImpl---__init__--1---")
        self.logger = logging.getLogger(self.__class__.__name__)

    def init(self, *args) -> None:
        """
        Initialize this PamLink.

        Args:
            *args: 可变参数，兼容Java版本可能传入的参数
        """
        # print("PamLinkImpl---init-----")
        # 忽略额外的参数，不使用参数初始化
        # 在Python版本中，我们不需要调用get_parameters()
        # self.learnable.init(self.get_parameters())
        # 直接初始化learnable
        if hasattr(self.learnable, 'init'):
            self.learnable.init({})
        else:
            # 如果Learnable没有init方法，则不进行初始化
            pass

    def update_link_values(self, link: Link) -> None:
        """
        Update this PamLink's values from another Link.

        Args:
            link: The Link to update from
        """
        if isinstance(link, PamLinkImpl):
            self.learnable.set_base_level_activation(link.get_base_level_activation())
        else:
            from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
            self.logger.debug(f"Cannot set PamLinkImpl-specific values. Required: {PamLinkImpl.__name__} \n Received: {link.__class__.__name__} at tick {TaskManager.get_current_tick()}")

    def __eq__(self, other) -> bool:
        """
        Check if this PamLink is equal to another.

        Args:
            other: The other PamLink to compare with

        Returns:
            True if the PamLinks have the same ID, False otherwise
        """
        if isinstance(other, PamLinkImpl):
            return self.get_id() == other.get_id()
        return False

    def __hash__(self) -> int:
        """
        Return the hash code of this PamLink.

        Returns:
            The hash code of the link ID
        """
        return self.get_id()

    def get_link_id(self) -> int:
        """
        Get the ID of this PamLink. This is an alias for get_id().

        Returns:
            The ID of this PamLink
        """
        return self.get_id()

    # LEARNABLE METHODS
    def get_activation(self) -> float:
        """
        Get the activation of this PamLink.

        Returns:
            The activation of this PamLink
        """
        if self.learnable is None:
            self.learnable = LearnableImpl()
        return self.learnable.get_activation()

    def set_activation(self, activation: float) -> None:
        """
        Set the activation of this PamLink.

        Args:
            activation: The activation to set
        """
        self.learnable.set_activation(activation)

    def get_total_activation(self) -> float:
        """
        Get the total activation of this PamLink.

        Returns:
            The total activation of this PamLink
        """
        return self.learnable.total_activation()

    def excite(self, amount: float) -> None:
        """
        Excite this PamLink by the given amount.

        Args:
            amount: The amount to excite by
        """
        self.excite_activation(amount)

    def excite_activation(self, amount: float) -> None:
        """
        Excite this PamLink's activation by the given amount.

        Args:
            amount: The amount to excite by
        """
        self.learnable.excite(amount)

    def decay(self, ticks: int) -> None:
        """
        Decay this PamLink by the given number of ticks.

        Args:
            ticks: The number of ticks to decay by
        """
        self.learnable.decay(ticks)

    def is_removable(self) -> bool:
        """
        Check if this PamLink is removable.

        Returns:
            True if this PamLink is removable, False otherwise
        """
        return self.get_activation() <= 0.0 and abs(self.get_incentive_salience()) <= 0.0

    def get_base_level_activation(self) -> float:
        """
        Get the base level activation of this PamLink.

        Returns:
            The base level activation of this PamLink
        """
        return self.learnable.get_base_level_activation()

    def set_base_level_activation(self, activation: float) -> None:
        """
        Set the base level activation of this PamLink.

        Args:
            activation: The base level activation to set
        """
        self.learnable.set_base_level_activation(activation)

    def reinforce_base_level_activation(self, amount: float) -> None:
        """
        Reinforce the base level activation of this PamLink by the given amount.

        Args:
            amount: The amount to reinforce by
        """
        self.learnable.set_base_level_activation(self.learnable.get_base_level_activation() + amount)

    def set_base_level_excite_strategy(self, strategy: ExciteStrategy) -> None:
        """
        Set the base level excite strategy of this PamLink.

        Args:
            strategy: The base level excite strategy to set
        """
        self.learnable.set_excite_strategy(strategy)

    def get_base_level_excite_strategy(self) -> ExciteStrategy:
        """
        Get the base level excite strategy of this PamLink.

        Returns:
            The base level excite strategy of this PamLink
        """
        return self.learnable.get_excite_strategy()

    def set_base_level_decay_strategy(self, strategy: DecayStrategy) -> None:
        """
        Set the base level decay strategy of this PamLink.

        Args:
            strategy: The base level decay strategy to set
        """
        self.learnable.set_decay_strategy(strategy)

    def get_base_level_decay_strategy(self) -> DecayStrategy:
        """
        Get the base level decay strategy of this PamLink.

        Returns:
            The base level decay strategy of this PamLink
        """
        return self.learnable.get_decay_strategy()

    def get_incentive_salience(self) -> float:
        """
        Get the incentive salience of this PamLink.

        Returns:
            The incentive salience of this PamLink
        """
        return self.learnable.get_incentive_salience()

    def set_incentive_salience(self, salience: float) -> None:
        """
        Set the incentive salience of this PamLink.

        Args:
            salience: The incentive salience to set
        """
        self.learnable.set_incentive_salience(salience)

    def reinforce(self, amount: float) -> None:
        """
        Reinforce this PamLink by the given amount.

        Args:
            amount: The amount to reinforce by
        """
        self.learnable.reinforce(amount)

    def get_activation_threshold(self) -> float:
        """
        Get the activation threshold of this PamLink.

        Returns:
            The activation threshold of this PamLink
        """
        return self.learnable.get_activation_threshold()

    def set_activation_threshold(self, threshold: float) -> None:
        """
        Set the activation threshold of this PamLink.

        Args:
            threshold: The activation threshold to set
        """
        self.learnable.set_activation_threshold(threshold)

    def get_removal_threshold(self) -> float:
        """
        Get the removal threshold of this PamLink.

        Returns:
            The removal threshold of this PamLink
        """
        return self.learnable.get_removal_threshold()

    def set_removal_threshold(self, threshold: float) -> None:
        """
        Set the removal threshold of this PamLink.

        Args:
            threshold: The removal threshold to set
        """
        self.learnable.set_removal_threshold(threshold)

    def is_above_activation_threshold(self) -> bool:
        """
        Check if this PamLink is above its activation threshold.

        Returns:
            True if this PamLink is above its activation threshold, False otherwise
        """
        return self.learnable.is_above_activation_threshold()

    def is_above_removal_threshold(self) -> bool:
        """
        Check if this PamLink is above its removal threshold.

        Returns:
            True if this PamLink is above its removal threshold, False otherwise
        """
        return self.learnable.is_above_removal_threshold()

    def get_excite_strategy(self) -> ExciteStrategy:
        """
        Get the excite strategy of this PamLink.

        Returns:
            The excite strategy of this PamLink
        """
        return self.learnable.get_excite_strategy()

    def set_excite_strategy(self, strategy: ExciteStrategy) -> None:
        """
        Set the excite strategy of this PamLink.

        Args:
            strategy: The excite strategy to set
        """
        self.learnable.set_excite_strategy(strategy)

    def get_decay_strategy(self) -> DecayStrategy:
        """
        Get the decay strategy of this PamLink.

        Returns:
            The decay strategy of this PamLink
        """
        return self.learnable.get_decay_strategy()

    def set_decay_strategy(self, strategy: DecayStrategy) -> None:
        """
        Set the decay strategy of this PamLink.

        Args:
            strategy: The decay strategy to set
        """
        self.learnable.set_decay_strategy(strategy)
