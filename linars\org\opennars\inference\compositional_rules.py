"""
复合术语的组合和分解规则，需要两个前提。

新的复合术语仅在前向推理中引入，
而分解规则也用于后向推理。
"""
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.control.derivation_context import DerivationContext
from linars.org.opennars.entity.budget_value import BudgetValue
from linars.org.opennars.entity.task import Task
from linars.org.opennars.entity.truth_value import TruthValue
from linars.org.opennars.inference.budget_functions import BudgetFunctions
from linars.org.opennars.inference.temporal_rules import TemporalRules
from linars.org.opennars.inference.truth_functions import TruthFunctions
from linars.org.opennars.language.conjunction import Conjunction
from linars.org.opennars.language.difference_ext import DifferenceExt
from linars.org.opennars.language.difference_int import DifferenceInt
from linars.org.opennars.language.disjunction import Disjunction
from linars.org.opennars.language.equivalence import Equivalence
from linars.org.opennars.language.image_ext import ImageExt
from linars.org.opennars.language.image_int import ImageInt
from linars.org.opennars.language.implication import Implication
from linars.org.opennars.language.inheritance import Inheritance
from linars.org.opennars.language.intersection_ext import IntersectionExt
from linars.org.opennars.language.intersection_int import IntersectionInt
from linars.org.opennars.language.set_ext import SetExt
from linars.org.opennars.language.set_int import SetInt
from linars.org.opennars.language.statement import Statement
from linars.org.opennars.language.terms import Terms
from linars.org.opennars.language.variable import Variable
from linars.org.opennars.io.symbols import VAR_DEPENDENT, VAR_INDEPENDENT, VAR_QUERY

import random
from typing import List, Set, Dict, Tuple, Optional
import collections

class CompositionalRules:
    """
    复合术语的组合和分解规则，需要两个前提。
    """

    @staticmethod
    def compose_compound(task_content: Statement, belief_content: Statement, index: int, nal: DerivationContext):
        """
        {<S ==> M>, <P ==> M>} |- {<(S|P) ==> M>, <(S&P) ==> M>, <(S-P) ==> M>, <(P-S) ==> M>}

        参数:
            task_content: 第一个前提
            belief_content: 第二个前提
            index: 共享术语的位置
            nal: 内存引用
        """
        if (not nal.get_current_task().sentence.is_judgment()) or (task_content.__class__ != belief_content.__class__):
            return

        component_t = task_content.term[1 - index]
        component_b = belief_content.term[1 - index]
        component_common = task_content.term[index]
        order1 = task_content.get_temporal_order()
        order2 = belief_content.get_temporal_order()
        order = TemporalRules.compose_order(order1, order2)

        if order == TemporalRules.ORDER_INVALID:
            return

        truth_t = nal.get_current_task().sentence.truth
        truth_b = nal.get_current_belief().truth
        truth_or = TruthFunctions.union(truth_t, truth_b, nal.nar.narParameters)
        truth_and = TruthFunctions.intersection(truth_t, truth_b, nal.nar.narParameters)
        truth_dif = None
        term_or = None
        term_and = None
        term_dif = None

        if index == 0:
            if isinstance(task_content, Inheritance):
                term_or = IntersectionInt.make([component_t, component_b])
                term_and = IntersectionExt.make([component_t, component_b])
                if truth_b.is_negative():
                    if not truth_t.is_negative():
                        term_dif = DifferenceExt.make([component_t, component_b])
                        truth_dif = TruthFunctions.intersection(truth_t, TruthFunctions.negation(truth_b, nal.nar.narParameters), nal.nar.narParameters)
                elif truth_t.is_negative():
                    term_dif = DifferenceExt.make([component_b, component_t])
                    truth_dif = TruthFunctions.intersection(truth_b, TruthFunctions.negation(truth_t, nal.nar.narParameters), nal.nar.narParameters)
            elif isinstance(task_content, Implication):
                term_or = Disjunction.make([component_t, component_b])
                term_and = Conjunction.make([component_t, component_b], order)

            if not component_t.clone_deep().equals(component_b.clone_deep()):
                CompositionalRules.process_composed(task_content, term_or, component_common, order, truth_or, nal)
                CompositionalRules.process_composed(task_content, term_and, component_common, order, truth_and, nal)

            CompositionalRules.process_composed(task_content, term_dif, component_common, order, truth_dif, nal)
        else:  # index == 1
            if isinstance(task_content, Inheritance):
                term_or = IntersectionExt.make([component_t, component_b])
                term_and = IntersectionInt.make([component_t, component_b])
                if truth_b.is_negative():
                    if not truth_t.is_negative():
                        term_dif = DifferenceInt.make([component_t, component_b])
                        truth_dif = TruthFunctions.intersection(truth_t, TruthFunctions.negation(truth_b, nal.nar.narParameters), nal.nar.narParameters)
                elif truth_t.is_negative():
                    term_dif = DifferenceInt.make([component_b, component_t])
                    truth_dif = TruthFunctions.intersection(truth_b, TruthFunctions.negation(truth_t, nal.nar.narParameters), nal.nar.narParameters)
            elif isinstance(task_content, Implication):
                term_or = Conjunction.make([component_t, component_b], order)
                term_and = Disjunction.make([component_t, component_b])

            if not component_t.clone_deep().equals(component_b.clone_deep()):
                CompositionalRules.process_composed(task_content, term_or, component_common, order, truth_or, nal)
                CompositionalRules.process_composed(task_content, term_and, component_common, order, truth_and, nal)

            CompositionalRules.process_composed(task_content, term_dif, component_common, order, truth_dif, nal)

    @staticmethod
    def process_composed(statement: Statement, subject: Term, predicate: Term, order: int, truth: TruthValue, nal: DerivationContext):
        """
        完成蕴含术语的组合。

        参数:
            statement: contentInd的类型
            subject: contentInd的主语
            predicate: contentInd的谓语
            order: 时间顺序
            truth: contentInd的真值
            nal: 内存引用
        """
        if subject is None or predicate is None:
            return

        content = Statement.make(statement.__class__, subject, predicate, order)
        if (content is None or statement is None or content.equals(statement) or
            content.equals(nal.get_current_belief().term)):
            return

        budget = BudgetFunctions.compound_forward(truth, content, nal)
        nal.double_premise_task(content, truth, budget, False, False)

    @staticmethod
    def decompose_compound(compound: CompoundTerm, component: Term, task_compound: bool, index: int, nal: DerivationContext):
        """
        {<(S|P) ==> M>, <S ==> M>} |- <P ==> M>

        参数:
            compound: 复合术语
            component: 组件术语
            task_compound: 复合术语是否来自任务
            index: 共享术语的位置
            nal: 内存引用
        """
        if not compound.is_commutative():
            return

        term1 = component
        term2 = None

        if compound.contains(term1):
            for i in range(compound.size()):
                if not compound.term[i].equals(term1):
                    term2 = compound.term[i]
                    break

        if term2 is None:
            return

        order = compound.get_temporal_order()

        # Handle temporal intervals
        delta = 0
        while isinstance(term2, Conjunction) and isinstance(term2.term[0], Interval):
            interval = term2.term[0]
            delta += interval.time
            term2 = term2.set_component(0, None, nal.memory)

        task = nal.get_current_task()
        sentence = task.sentence
        belief = nal.get_current_belief()
        old_content = task.get_term()

        v1 = sentence.truth if task_compound else belief.truth
        v2 = belief.truth if task_compound else sentence.truth

        content = Statement.make(old_content.__class__, term1 if index == 0 else term2, term2 if index == 0 else term1, order)
        if content is None:
            return

        truth = None
        if sentence.is_judgment():
            if isinstance(compound, Conjunction):
                if isinstance(old_content, Inheritance) or isinstance(old_content, Similarity):
                    truth = TruthFunctions.reduce_conjunction(v1, v2, nal.nar.narParameters)
                elif isinstance(old_content, Implication) or isinstance(old_content, Equivalence):
                    truth = TruthFunctions.reduce_conjunction(v1, v2, nal.nar.narParameters)
            elif isinstance(compound, Disjunction):
                if isinstance(old_content, Inheritance) or isinstance(old_content, Similarity):
                    truth = TruthFunctions.reduce_disjunction(v1, v2, nal.nar.narParameters)
                elif isinstance(old_content, Implication) or isinstance(old_content, Equivalence):
                    truth = TruthFunctions.reduce_disjunction(v1, v2, nal.nar.narParameters)

        if truth is not None:
            budget = BudgetFunctions.compound_forward(truth, content, nal)
            if delta != 0:
                base_time = task.sentence.get_occurrence_time()
                if base_time != Stamp.ETERNAL:
                    base_time += delta
                    nal.get_the_new_stamp().set_occurrence_time(base_time)
            nal.double_premise_task(content, truth, budget, False, True)  # (allow overlap), a form of detachment

    @staticmethod
    def decompose_statement(compound: CompoundTerm, component: Term, compound_task: bool, index: int, nal: DerivationContext):
        """
        {(||, S, P), P} |- S {(&&, S, P), P} |- S

        参数:
            compound: 复合术语
            component: 组件术语
            compound_task: 蕴含是否来自任务
            index: 组件的索引
            nal: 内存引用
        """
        is_temporal_conjunction = isinstance(compound, Conjunction) and not compound.is_spatial
        if is_temporal_conjunction and (compound.get_temporal_order() == TemporalRules.ORDER_FORWARD) and (index != 0):
            return

        occurrence_time = nal.get_current_task().sentence.get_occurrence_time()

        task = nal.get_current_task()
        task_sentence = task.sentence
        belief = nal.get_current_belief()
        content = Terms.reduce_components(compound, component, nal.memory)
        if content is None:
            return

        truth = None
        budget = None

        if task_sentence.is_question() or task_sentence.is_quest():
            budget = BudgetFunctions.compound_backward(content, nal)
            nal.get_the_new_stamp().set_occurrence_time(occurrence_time)
            nal.double_premise_task(content, truth, budget, False, False)

            # Special inference to answer conjunctive questions with query variables
            if task_sentence.term.has_var_query():
                content_concept = nal.memory.concept(content)
                if content_concept is None:
                    return
                content_belief = content_concept.get_belief(nal, task)
                if content_belief is None:
                    return
                from linars.org.opennars.entity.task import EnumType
                content_task = Task(content_belief, task.budget, EnumType.DERIVED)
                nal.set_current_task(content_task)
                conj = Conjunction.make([component, content])
                truth = TruthFunctions.intersection(content_belief.truth, belief.truth, nal.nar.narParameters)
                budget = BudgetFunctions.compound_forward(truth, conj, nal)
                nal.get_the_new_stamp().set_occurrence_time(occurrence_time)
                nal.double_premise_task(conj, truth, budget, False, False)
        else:
            v1 = task_sentence.truth if compound_task else belief.truth
            v2 = belief.truth if compound_task else task_sentence.truth

            if isinstance(compound, Conjunction) or isinstance(compound, Disjunction):
                if task_sentence.is_goal() and not compound_task:
                    return
            else:
                return

            if isinstance(compound, Conjunction):
                if task_sentence.is_goal():
                    truth = TruthFunctions.intersection(v1, v2, nal.nar.narParameters)
                else:  # isJudgment
                    truth = TruthFunctions.reduce_conjunction(v1, v2, nal.nar.narParameters)
            else:  # Disjunction
                if task_sentence.is_goal():
                    truth = TruthFunctions.reduce_conjunction(v2, v1, nal.nar.narParameters)
                else:  # isJudgment
                    truth = TruthFunctions.reduce_disjunction(v1, v2, nal.nar.narParameters)

            budget = BudgetFunctions.compound_forward(truth, content, nal)
            nal.get_the_new_stamp().set_occurrence_time(occurrence_time)
            nal.double_premise_task(content, truth, budget, False, False)

    @staticmethod
    def introduce_var_outer(statement1: Statement, statement2: Statement, index: int, nal: DerivationContext):
        """
        在外层合取中引入变量。

        参数:
            statement1: 第一个前提
            statement2: 第二个前提
            index: 共享术语的位置
            nal: 内存引用
        """
        if not nal.get_current_task().sentence.is_judgment():
            return

        task_sentence = nal.get_current_task().sentence
        belief = nal.get_current_belief()

        truth_t = task_sentence.truth
        truth_b = belief.truth

        state1 = statement1.term[1 - index]
        state2 = statement2.term[1 - index]

        # Introduce variables for subject and predicate
        for subject_introduction in [True, False]:
            # Implication: state1 ==> state2
            contents = CompositionalRules.introduce_variables(nal, Implication.make(state1, state2), subject_introduction)
            for content_penalty in contents:
                content = content_penalty[0]
                penalty = content_penalty[1]
                truth = TruthFunctions.induction(truth_t, truth_b, nal.nar.narParameters).mul_confidence(penalty)
                budget = BudgetFunctions.compound_forward(truth, content, nal)
                nal.double_premise_task(content, truth, budget.clone(), False, False)

            # Implication: state2 ==> state1
            contents = CompositionalRules.introduce_variables(nal, Implication.make(state2, state1), subject_introduction)
            for content_penalty in contents:
                content = content_penalty[0]
                penalty = content_penalty[1]
                truth = TruthFunctions.induction(truth_b, truth_t, nal.nar.narParameters).mul_confidence(penalty)
                budget = BudgetFunctions.compound_forward(truth, content, nal)
                nal.double_premise_task(content, truth, budget.clone(), False, False)

            # Equivalence: state1 <=> state2
            contents = CompositionalRules.introduce_variables(nal, Equivalence.make(state1, state2), subject_introduction)
            for content_penalty in contents:
                content = content_penalty[0]
                penalty = content_penalty[1]
                truth = TruthFunctions.comparison(truth_t, truth_b, nal.nar.narParameters).mul_confidence(penalty)
                budget = BudgetFunctions.compound_forward(truth, content, nal)
                nal.double_premise_task(content, truth, budget.clone(), False, False)

            # Conjunction: state1 && state2
            contents = CompositionalRules.introduce_variables(nal, Conjunction.make([state1, state2]), subject_introduction)
            for content_penalty in contents:
                content = content_penalty[0]
                penalty = content_penalty[1]
                truth = TruthFunctions.intersection(truth_t, truth_b, nal.nar.narParameters).mul_confidence(penalty)
                budget = BudgetFunctions.compound_forward(truth, content, nal)
                nal.double_premise_task(content, truth, budget.clone(), False, False)

    @staticmethod
    def introduce_variables(nal: DerivationContext, term: Term, subject: bool) -> Set[Tuple[Term, float]]:
        """
        向术语中引入变量。

        参数:
            nal: 内存引用
            term: 要引入变量的术语
            subject: 是否在主语中引入变量

        返回:
            术语和惩罚值的对集合
        """
        if term is None or not isinstance(term, Statement):
            return set()

        statement = term
        side = statement.get_subject() if subject else statement.get_predicate()
        side_term = side

        # Collect variable candidates
        candidates = set()
        CompositionalRules.add_variable_candidates(candidates, side_term, subject)

        # If no candidates, return empty set
        if not candidates:
            return set()

        # Limit the number of combinations
        max_combinations = nal.nar.narParameters.VARIABLE_INTRODUCTION_COMBINATIONS_MAX
        if len(candidates) > max_combinations:
            candidates_list = list(candidates)
            random.shuffle(candidates_list)
            candidates = set(candidates_list[:max_combinations])

        # Generate variable substitutions
        results = set()
        for candidate in candidates:
            # Skip if candidate is a variable
            if isinstance(candidate, Variable):
                continue

            # Create a copy of the term
            term_copy = term.clone_deep()

            # Create a variable
            variable = Variable.make(VAR_INDEPENDENT, candidate.name())

            # Replace the candidate with the variable
            if isinstance(term_copy, CompoundTerm):
                term_copy.substitute(candidate, variable)

            # Add the result with a penalty
            confidence_penalty = nal.nar.narParameters.VARIABLE_INTRODUCTION_CONFIDENCE_MUL
            results.add((term_copy, confidence_penalty))

        return results

    @staticmethod
    def add_variable_candidates(candidates: Set[Term], side: Term, subject: bool):
        """
        添加作为主语和谓语出现的变量候选。

        参数:
            candidates: 被操作的候选集合
            side: 从中提取候选的一侧
            subject: 是否从主语提取
        """
        junction = isinstance(side, Conjunction) or isinstance(side, Disjunction) or isinstance(side, Negation)
        n = side.size() if junction else 1

        for i in range(n):
            # Find an Inheritance
            t = None
            if i < n:
                if junction:
                    t = side.term[i]
                else:
                    t = side

            if isinstance(t, Conjunction) or isinstance(t, Disjunction) or isinstance(t, Negation):
                # Component itself is a conjunction/disjunction
                CompositionalRules.add_variable_candidates(candidates, t, subject)

            if isinstance(t, Inheritance):
                inh = t
                subj_t = inh.get_subject()
                pred_t = inh.get_predicate()
                add_subject = subject or isinstance(subj_t, ImageInt)  # Also allow for images due to equivalence transform

                removals = set()
                if add_subject and not subj_t.has_var():
                    ret = CompoundTerm.add_components_recursively(subj_t, None)
                    for ct in ret:
                        if isinstance(ct, Image):
                            removals.add(ct.term[ct.relation_index])
                        candidates.add(ct)

                for r in removals:
                    candidates.remove(r)

                if not subject and not pred_t.has_var():
                    ret = CompoundTerm.add_components_recursively(pred_t, None)
                    for ct in ret:
                        candidates.add(ct)

    @staticmethod
    def eliminate_variable_of_condition_abductive(figure: int, sentence: Statement, belief: Statement, nal: DerivationContext):
        """
        消除条件中的变量。

        参数:
            figure: 三段论的图形
            sentence: 第一个前提
            belief: 第二个前提
            nal: 内存引用
        """
        T1 = sentence.term
        T2 = belief.term

        S1 = T2.get_subject()
        S2 = T1.get_subject()
        P1 = T2.get_predicate()
        P2 = T1.get_predicate()

        res1 = {}
        res2 = {}

        # Find substitutions based on figure
        if figure == 21:
            Variable.find_substitute(nal.memory.random_number, VAR_INDEPENDENT, P1, S2, res1, res2)
        elif figure == 12:
            Variable.find_substitute(nal.memory.random_number, VAR_INDEPENDENT, S1, P2, res1, res2)
        elif figure == 11:
            Variable.find_substitute(nal.memory.random_number, VAR_INDEPENDENT, S1, S2, res1, res2)
        elif figure == 22:
            Variable.find_substitute(nal.memory.random_number, VAR_INDEPENDENT, P1, P2, res1, res2)

        # Apply substitutions
        T1 = T1.apply_substitute(res2)
        if T1 is None:
            return

        # Check if the substitution is valid
        if not (isinstance(T1, Statement) and isinstance(T2, Statement)):
            return

        # Get the terms after substitution
        s1 = T1.get_subject()
        s2 = T2.get_subject()
        p1 = T1.get_predicate()
        p2 = T2.get_predicate()

        # Check if the terms are valid
        if s1 is None or s2 is None or p1 is None or p2 is None:
            return

        # Check if the terms are equal
        if figure == 11:
            if s1.equals(s2):
                if p1.equals(p2):
                    return  # Identical statements
                if sentence.truth is not None and belief.truth is not None:
                    truth = TruthFunctions.abduction(sentence.truth, belief.truth, nal.nar.narParameters)
                    budget = BudgetFunctions.compound_forward(truth, p2, nal)
                    nal.double_premise_task(p2, truth, budget, False, False)
        elif figure == 12:
            if s1.equals(p2):
                if p1.equals(s2):
                    return  # Identical statements
                if sentence.truth is not None and belief.truth is not None:
                    truth = TruthFunctions.abduction(sentence.truth, belief.truth, nal.nar.narParameters)
                    budget = BudgetFunctions.compound_forward(truth, p2, nal)
                    nal.double_premise_task(p2, truth, budget, False, False)
        elif figure == 21:
            if p1.equals(s2):
                if s1.equals(p2):
                    return  # Identical statements
                if sentence.truth is not None and belief.truth is not None:
                    truth = TruthFunctions.abduction(sentence.truth, belief.truth, nal.nar.narParameters)
                    budget = BudgetFunctions.compound_forward(truth, s2, nal)
                    nal.double_premise_task(s2, truth, budget, False, False)
        elif figure == 22:
            if p1.equals(p2):
                if s1.equals(s2):
                    return  # Identical statements
                if sentence.truth is not None and belief.truth is not None:
                    truth = TruthFunctions.abduction(sentence.truth, belief.truth, nal.nar.narParameters)
                    budget = BudgetFunctions.compound_forward(truth, s2, nal)
                    nal.double_premise_task(s2, truth, budget, False, False)
