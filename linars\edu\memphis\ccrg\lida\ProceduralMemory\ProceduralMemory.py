# LIDA Cognitive Framework
"""
FrameworkModule containing Schemes activated by each conscious broadcast.
Activated schemes are instantiated, becoming Behaviors which are sent to
ActionSelection.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action
from linars.edu.memphis.ccrg.lida.ActionSelection.Behavior import Behavior
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Scheme import Scheme

class ProceduralMemory(FrameworkModule):
    """
    FrameworkModule containing Schemes activated by each conscious broadcast.
    Activated schemes are instantiated, becoming Behaviors which are sent to
    ActionSelection.
    """
    
    @abstractmethod
    def get_new_scheme(self, action: Action) -> Scheme:
        """
        Gets a new Scheme having specified Action.
        
        Args:
            action: An Action
            
        Returns:
            A new Scheme with the specified Action
        """
        pass
    
    @abstractmethod
    def contains_scheme(self, scheme: Scheme) -> bool:
        """
        Returns whether this procedural memory contains specified scheme.
        
        Args:
            scheme: A Scheme
            
        Returns:
            True if it contains an equal scheme, False otherwise
        """
        pass
    
    @abstractmethod
    def add_scheme(self, scheme: Scheme) -> bool:
        """
        Adds specified scheme to this procedural memory.
        
        Args:
            scheme: A Scheme to add
            
        Returns:
            True if the scheme was added, False otherwise
        """
        pass
    
    @abstractmethod
    def remove_scheme(self, scheme: Scheme) -> None:
        """
        Removes specified scheme from this procedural memory.
        
        Args:
            scheme: A Scheme to remove
        """
        pass
    
    @abstractmethod
    def activate_schemes(self) -> None:
        """
        Activates schemes based on the current broadcast buffer.
        """
        pass
    
    @abstractmethod
    def should_instantiate(self, scheme: Scheme, broadcast_buffer: NodeStructure) -> bool:
        """
        A call-back method to determine if the Scheme should be instantiated.
        This method can be overridden by subclasses to provide custom functionality.
        
        Args:
            scheme: The Scheme to be checked
            broadcast_buffer: The NodeStructure in ProceduralMemory containing recent broadcast
            
        Returns:
            True if the Scheme should be instantiated, False otherwise
        """
        pass
    
    @abstractmethod
    def create_instantiation(self, scheme: Scheme) -> Behavior:
        """
        Instantiates specified Scheme.
        
        Args:
            scheme: A Scheme over instantiation threshold
            
        Returns:
            A Behavior, an instantiation of the specified Scheme
        """
        pass
