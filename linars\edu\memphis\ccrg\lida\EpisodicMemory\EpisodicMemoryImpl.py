# LIDA Cognitive Framework
"""
Default implementation of EpisodicMemory.
"""

import logging
from typing import List, Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener import BroadcastListener
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.EpisodicMemory.EpisodicMemory import EpisodicMemory
from linars.edu.memphis.ccrg.lida.EpisodicMemory.CueListener import CueListener
from linars.edu.memphis.ccrg.lida.EpisodicMemory.LocalAssociationListener import LocalAssociationListener

class EpisodicMemoryImpl(FrameworkModuleImpl, EpisodicMemory, CueListener, BroadcastListener):
    """
    Default implementation of EpisodicMemory.
    """
    
    def __init__(self):
        """
        Initialize an EpisodicMemoryImpl.
        """
        super().__init__()
        self.local_association_listeners: List[LocalAssociationListener] = []
        self.episodic_memory = NodeStructureImpl()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def add_listener(self, listener: ModuleListener) -> None:
        """
        Add a listener to this EpisodicMemory.
        
        Args:
            listener: The listener to add
        """
        if isinstance(listener, LocalAssociationListener):
            self.add_local_association_listener(listener)
        else:
            self.logger.warning(f"Cannot add listener {listener} at tick {TaskManager.get_current_tick()}")
    
    def add_local_association_listener(self, listener: LocalAssociationListener) -> None:
        """
        Add a local association listener to this EpisodicMemory.
        
        Args:
            listener: The listener to add
        """
        self.local_association_listeners.append(listener)
    
    def receive_cue(self, cue: NodeStructure) -> None:
        """
        Receive a cue from the Workspace.
        
        Args:
            cue: The cue to receive
        """
        if cue is None or cue.get_node_count() == 0:
            self.logger.warning(f"Received empty cue at tick {TaskManager.get_current_tick()}")
            return
        
        self.logger.debug(f"Received cue with {cue.get_node_count()} nodes at tick {TaskManager.get_current_tick()}")
        
        # In a real implementation, this would search the episodic memory for associations
        # For now, we'll just return the cue as the association
        association = NodeStructureImpl()
        association.merge_with(cue)
        
        for listener in self.local_association_listeners:
            listener.receive_local_association(association)
    
    def receive_broadcast(self, coalition: Coalition) -> None:
        """
        Receive a broadcast from the GlobalWorkspace.
        
        Args:
            coalition: The coalition that won the competition for consciousness
        """
        content = coalition.get_content()
        if content is None:
            self.logger.warning(f"Received empty broadcast at tick {TaskManager.get_current_tick()}")
            return
        
        self.logger.debug(f"Received broadcast at tick {TaskManager.get_current_tick()}")
        
        # In a real implementation, this would store the broadcast content in episodic memory
        # For now, we'll just merge it with the episodic memory
        self.episodic_memory.merge_with(content)
    
    def learn(self, coalition: Coalition) -> None:
        """
        Learn from a coalition.
        
        Args:
            coalition: The coalition to learn from
        """
        # This method is a placeholder for learning from broadcasts
        # It should be implemented by the receiving module
        pass
    
    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.
        
        Args:
            ticks: The number of ticks to decay by
        """
        self.episodic_memory.decay_node_structure(ticks)
    
    def get_module_content(self, *params: Any) -> Any:
        """
        Get the content of this module.
        
        Args:
            params: Parameters specifying what content to return
            
        Returns:
            The content of this module
        """
        return self.episodic_memory
