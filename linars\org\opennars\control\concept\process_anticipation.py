"""
概念中的预期处理逻辑。
负责管理概念的预期、确认和维护失望预期。
"""
from typing import List, Dict, Optional, Set
import time

from linars.edu.memphis.ccrg.linars.term import Term
from linars.edu.memphis.ccrg.linars.concept import Concept
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.org.opennars.entity.task import Task
from linars.org.opennars.entity.sentence import Sentence
from linars.org.opennars.entity.stamp import Stamp
from linars.org.opennars.entity.budget_value import BudgetValue
from linars.org.opennars.entity.truth_value import TruthValue
from linars.org.opennars.control.derivation_context import DerivationContext
from linars.org.opennars.inference.rule_tables import RuleTables

class ProcessAnticipation:
    """
    概念预期处理类。
    负责管理概念的预期、确认和维护失望预期。
    """

    @staticmethod
    def anticipate(nal: DerivationContext, main_sentence: Sentence, budget: BudgetValue,
                  min_time: int, max_time: int, urgency: float, substitution: Dict[Term, Term]):
        """
        预期未来事件。

        参数:
            nal: 派生上下文
            main_sentence: 主句子
            budget: 预算值
            min_time: 最小时间
            max_time: 最大时间
            urgency: 紧急度
            substitution: 替换映射
        """
        # Derivation was successful and it was a judgment event
        stamp = Stamp(nal.time, nal.memory)
        stamp.set_occurrence_time(Stamp.ETERNAL)
        eternalized_induction_confidence = nal.narParameters.ANTICIPATION_CONFIDENCE

        s = Sentence(
            main_sentence.term,
            main_sentence.punctuation,
            TruthValue(0.0, eternalized_induction_confidence, nal.narParameters),
            stamp
        )

        # Budget for one-time processing
        budget_for_task = BudgetValue(
            budget.get_priority() * nal.narParameters.ANTICIPATION_PRIORITY_MUL,
            budget.get_durability() * nal.narParameters.ANTICIPATION_DURABILITY_MUL,
            budget.get_quality(),
            nal.narParameters
        )
        from linars.org.opennars.entity.task import EnumType
        # Create anticipation task
        task = Task(s, budget_for_task, EnumType.DERIVED)

        # Add anticipation to concept
        concept = nal.memory.concept(main_sentence.term)
        if concept is not None:
            concept.anticipate(task, nal.memory, min_time, max_time, substitution)

    @staticmethod
    def confirm_anticipation(task: Task, concept: Concept, nal: DerivationContext):
        """
        检查处理后的判断任务是否满足概念中的预期。

        参数:
            task: 待检查的判断任务
            concept: 处理中的概念
            nal: 派生上下文
        """
        # 检查nal和nal.memory是否为None
        if nal is None or not hasattr(nal, 'memory') or nal.memory is None or not hasattr(nal, 'narParameters'):
            return
        # 检查task和task.sentence是否为None
        if task is None or task.sentence is None:
            return

        satisfies_anticipation = task.is_input() and not task.sentence.is_eternal()

        # 检查task.sentence.truth是否为None
        if task.sentence.truth is None:
            is_expectation_above_threshold = False
        else:
            is_expectation_above_threshold = task.sentence.truth.get_expectation() > nal.narParameters.DEFAULT_CONFIRMATION_EXPECTATION

        # 检查concept和concept.anticipations是否为None
        if concept is None or not hasattr(concept, 'anticipations') or concept.anticipations is None:
            return

        confirmed = []

        # Check each anticipation
        for entry in concept.anticipations:
            # Check if the anticipation is confirmed
            # 检查entry和entry.target是否为None
            if entry is None or not hasattr(entry, 'target') or entry.target is None:
                continue

            if (satisfies_anticipation and is_expectation_above_threshold):
                # 安全地调用replace_intervals方法
                try:
                    task_term_replaced = CompoundTerm.replace_intervals(task.sentence.term) if hasattr(CompoundTerm, 'replace_intervals') else task.sentence.term
                    entry_target_replaced = CompoundTerm.replace_intervals(entry.target) if hasattr(CompoundTerm, 'replace_intervals') else entry.target
                    terms_match = task_term_replaced == entry_target_replaced
                except Exception as e:
                    print(f"错误: 在比较术语时出错: {e}")
                    terms_match = False

                if terms_match:
                    # Confirmed anticipation
                    confirmed.append(entry)

                    # Emit anticipation confirm event
                    if hasattr(nal.memory, 'emit'):
                        nal.memory.emit("AnticipationConfirm", task, entry.target, concept)

                    # Adjust happiness
                    if hasattr(nal.memory, 'emotion') and nal.memory.emotion is not None:
                        nal.memory.emotion.happy(task, entry.target, concept, nal)

                # Process anticipation
                if hasattr(entry, 'substitution') and entry.substitution is not None:
                    # Process substitution
                    pass

        # Remove confirmed anticipations
        for entry in confirmed:
            concept.anticipations.remove(entry)

    @staticmethod
    def maintain_disappointed_anticipations(narParameters, concept: Concept, nar, mem):
        """
        维护失望的预期。

        参数:
            narParameters: NAR参数配置
            concept: 处理中的概念
            nar: NAR推理机实例
            mem: 内存对象
        """
        # 检查参数是否为None
        if narParameters is None or concept is None or nar is None or mem is None:
            return

        # 检查concept.anticipations是否为None
        if not hasattr(concept, 'anticipations') or concept.anticipations is None:
            return

        # 安全地获取当前时间
        try:
            current_time = nar.time() if hasattr(nar, 'time') else int(time.time() * 1000)
        except Exception as e:
            print(f"错误: 获取当前时间时出错: {e}")
            return

        disappointed = []

        # Check each anticipation
        for entry in concept.anticipations:
            # 检查entry和entry.occurrence_time是否为None
            if entry is None or not hasattr(entry, 'occurrence_time') or entry.occurrence_time is None:
                continue

            if current_time > entry.occurrence_time:
                # 检查entry.target是否为None
                if not hasattr(entry, 'target') or entry.target is None:
                    continue

                # Anticipation is disappointed
                disappointed.append(entry)

                # Emit anticipation fail event
                if hasattr(mem, 'emit'):
                    mem.emit("AnticipationFail", entry.target, concept)

                # Adjust happiness
                if hasattr(mem, 'emotion') and mem.emotion is not None:
                    mem.emotion.unhappy(entry.target, concept, nar)

                # Process anticipation
                if hasattr(entry, 'substitution') and entry.substitution is not None:
                    # Process substitution
                    pass

        # Remove disappointed anticipations
        for entry in disappointed:
            concept.anticipations.remove(entry)

    @staticmethod
    def fire_predictions(judgement_task: Task, concept: Concept, nal: DerivationContext, time_obj, tasklink):
        """
        基于概念邻居的已知信念触发预测推理。

        参数:
            judgement_task: 判断任务
            concept: 处理中的概念
            nal: 派生上下文
            time_obj: 用于获取当前时间
            tasklink: 对应的任务链接
        """
        if not judgement_task.sentence.is_eternal() and judgement_task.is_input() and judgement_task.sentence.is_judgment():
            # Check each term link
            for tl in concept.term_links:
                term = tl.get_target()
                tc = nal.memory.concept(term)

                if tc is not None:
                    # Check each belief
                    for belief in tc.beliefs:
                        if belief.term.is_higher_order_statement():
                            # Get the subject and predicate
                            component = belief.term.get_subject()

                            if CompoundTerm.replace_intervals(concept.get_term()) == CompoundTerm.replace_intervals(component):
                                # Trigger inference of the task with the belief
                                cont = DerivationContext(nal.memory, nal.narParameters, time_obj)
                                cont.set_current_task(judgement_task)     # a
                                cont.set_current_belief_link(tl)          # a =/> b
                                cont.set_current_task_link(tasklink)      # a
                                cont.set_current_concept(concept)         # a
                                cont.set_current_term(concept.get_term()) # a
                                RuleTables.reason(tasklink, tl, cont)     # generate b
