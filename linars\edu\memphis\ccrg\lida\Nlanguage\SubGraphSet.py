#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
SubGraphSet class for the LIDA framework.
"""

from linars.edu.memphis.ccrg.linars.memory import Memory
from linars.edu.memphis.ccrg.lida.Nlanguage.Tree_nest import Tree_nest
from linars.edu.memphis.ccrg.lida.Nlanguage.TreeNode import TreeNode

from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
from linars.org.opennars.storage.bag1 import Bag1
from linars.org.opennars.storage.buffer import Buffer

import logging

class SubGraphSet(Memory):
    """
    A subgraph set in the LIDA framework.
    """


    def __init__(self):
        nar = AgentStarter.nar
        """
        Initialize a SubGraphSet.
        """
        super().__init__(
            nar.narParameters,
            Bag1(nar.narParameters.CONCEPT_BAG_SIZE),
            <PERSON><PERSON><PERSON>(nar, nar.narParameters.GLOBAL_BUFFER_LEVELS, nar.narParameters.GLOBAL_BUFFER_SIZE, nar.narParameters),
            Buffer(nar, nar.narParameters.SEQUENCE_BAG_LEVELS, nar.narParameters.SEQUENCE_BAG_SIZE, nar.narParameters),
            Bag1(nar.narParameters.OPERATION_BAG_SIZE)
        )
        # 初始化NodeStructureImpl所需的属性
        # self.nodes = {}
        # self.links = {}
        # self.links_from = {}
        # self.links_to = {}

        # 同一个点边集，可能构建出多个普通树结构，特定的树结构就是一个特定的点边子集，NodeStructureImpl是总集
        # 目标树子集需特定构建，确定的点边才加入，竞争性构建，分短中长目标，限量，点边过多，则将暂不执行的放一边
        # 可复用某些结构，如果只是平面平等罗列，则只有一个实例。语句arg结构本身有数量标记，纯构式可能无标记。这里边无标记
        self.goal_tree = Tree_nest(TreeNode("<SELF --> [happy]>"))
        # self.concepts = {}  # 存储概念
        # self.logger = logging.getLogger(self.__class__.__name__)

        # Tree_nest类是树结构基本方法，这里是特定树结构构建方案，目标线程是操作方案

        # 202405之前，结合nars方案：场景顺推，直到本能，途中路径加入nars概念前提条件，再从本能反推，提升途中路径为目标，进而执行
        # 05之后，不能只顺推或反推，中间路径加入目标buffer，各自交互竞争，结果整合到goaltree中，最终执行
        # todo 本质是一个自底向上构建多叉树，可直接用arg结构算法，只是算的是顺承和相似，不是arg组件



    def merge_with(self, content):
        """
        将内容合并到当前子图集

        参数:
            content: 要合并的内容
        """
        try:
            # 如果内容是另一个SubGraphSet，尝试合并其树和概念
            if isinstance(content, SubGraphSet):
                # 合并概念
                if hasattr(content, 'concepts') and content.concepts:
                    for key, value in content.concepts.items():
                        if key not in self.concepts:
                            self.concepts[key] = value
                        # 如果已存在，可以添加合并逻辑

                # 这里可以添加对树结构的合并逻辑
            else:
                # 如果内容是其他类型，可以添加相应的处理逻辑
                self.logger.warning(f"Cannot merge with content of type {type(content)}")
        except Exception as e:
            self.logger.warning(f"Error in merge_with: {e}")
