"""
继承关系(Inheritance)是NARS理论中的核心关系，表示"A是B的子类"或"A继承B的属性"

继承关系定义:
- 语法结构: <A --> B>
- 表示A是B的子类或实例
- 是NARS中最重要的基础关系之一
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar

from linars.edu.memphis.ccrg.linars.term import Term
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.org.opennars.language.statement import Statement, NativeOperator

class Inheritance(Statement):
    """
    继承关系类，表示NARS中的"A --> B"关系

    主要功能:
    1. 表示类与类之间的继承关系
    2. 处理特殊情况的继承关系(如操作符等)
    3. 提供克隆和创建方法
    """

    def __init__(self, arg: Union[List[Term], Term] = None, pred: Term = None):
        """
        Constructor with partial values, called by make

        Args:
            arg: The component list of the term or the subject term
            pred: The predicate term (when arg is the subject)
        """
        # 处理两种构造函数形式：
        # 1. Inheritance([subject, predicate]) - 传入项列表
        # 2. Inheritance(subject, predicate) - 传入主语和谓语
        if pred is not None and not isinstance(arg, list):
            # 如果是第二种形式，转换为第一种形式
            arg = [arg, pred]

        super().__init__(arg)

    @classmethod
    def create(cls, subj: Term, pred: Term) -> 'Inheritance':
        """
        Create an Inheritance statement

        Args:
            subj: The subject term
            pred: The predicate term

        Returns:
            Inheritance: The created statement
        """
        return cls(subj, pred)

    def clone(self, replaced: List[Term] = None) -> 'Inheritance':
        """
        Clone an object

        Args:
            replaced: The replacement terms, if None use original terms

        Returns:
            Inheritance: A new object
        """
        if replaced is not None:
            if len(replaced) != 2:
                raise ValueError(f"Invalid terms for {self.__class__.__name__}: {replaced}")
            return self.make(replaced[0], replaced[1])
        return self.make(self.get_subject(), self.get_predicate())

    def clone_with_terms(self, t: List[Term]) -> Optional['Inheritance']:
        """
        Clone with new terms

        Args:
            t: The new terms

        Returns:
            Inheritance: The cloned object
        """
        if t is None:
            return None

        if len(t) != 2:
            raise ValueError(f"Invalid terms for {self.__class__.__name__}: {t}")

        return self.make(t[0], t[1])

    @staticmethod
    def make_term(subject: Term, predicate: Term) -> Term:
        """
        Alternate version of Inheritance.make that allows equivalent subject and predicate
        to be reduced to the common term

        Args:
            subject: The subject term
            predicate: The predicate term

        Returns:
            Term: The created term
        """
        return Inheritance.make(subject, predicate)

    @staticmethod
    def make(subject: Term, predicate: Term) -> Optional['Inheritance']:
        """
        尝试创建新的继承关系(由推理规则调用)

        参数:
            subject: 主语项
            predicate: 谓语项

        返回:
            Inheritance: 创建的继承关系，如果无效返回None

        注意:
        1. 会检查无效的继承关系组合
        2. 处理特殊情况的继承关系(如操作符)
        3. 如果subject是乘积项且predicate是操作符，会创建Operation
        """
        try:
            # 参数验证
            if subject is None:
                print(f"警告: Inheritance.make 收到了空的主语")
                return None

            if predicate is None:
                print(f"警告: Inheritance.make 收到了空的谓语")
                return None

            # 检查语句有效性
            try:
                if Statement.invalid_statement(subject, predicate):
                    return None
            except Exception as e:
                print(f"警告: 在检查语句有效性时发生异常: {e}")
                # 继续执行，不要因为这个检查失败而中断整个方法

            # 检查是否为乘积项和操作符
            subject_product = False
            predicate_operator = False

            # 检查subject是否为乘积项
            try:
                if isinstance(subject, CompoundTerm):
                    if hasattr(subject, 'is_product') and callable(getattr(subject, 'is_product')):
                        try:
                            subject_product = subject.is_product()
                        except Exception as e:
                            print(f"警告: 调用is_product方法时发生异常: {e}")
                    elif hasattr(subject, 'operator') and callable(getattr(subject, 'operator')):
                        try:
                            subject_product = subject.operator() == NativeOperator.PRODUCT
                        except Exception as e:
                            print(f"警告: 调用operator方法时发生异常: {e}")
            except Exception as e:
                print(f"警告: 在检查subject是否为乘积项时发生异常: {e}")

            # 检查predicate是否为操作符
            try:
                if hasattr(predicate, 'is_operator') and callable(getattr(predicate, 'is_operator')):
                    try:
                        predicate_operator = predicate.is_operator()
                    except Exception as e:
                        print(f"警告: 调用is_operator方法时发生异常: {e}")
            except Exception as e:
                print(f"警告: 在检查predicate是否为操作符时发生异常: {e}")

            # 如果subject是乘积项且predicate是操作符，创建Operation
            if subject_product and predicate_operator:
                try:
                    from linars.org.opennars.operator.operation import Operation
                    if hasattr(subject, 'term') and isinstance(subject.term, (list, tuple)):
                        return Operation.make(predicate, subject.term, True)
                    else:
                        print(f"警告: subject.term不是列表或元组类型")
                        return Inheritance(subject, predicate)
                except Exception as e:
                    print(f"错误: 在创建Operation时发生异常: {e}")
                    # 如果创建Operation失败，尝试创建普通的继承关系
                    try:
                        return Inheritance(subject, predicate)
                    except Exception as inner_e:
                        print(f"错误: 在创建普通的继承关系时发生异常: {inner_e}")
                        return None
            else:
                # 创建普通的继承关系
                try:
                    return Inheritance(subject, predicate)
                except Exception as e:
                    print(f"错误: 在创建普通的继承关系时发生异常: {e}")
                    return None

        except Exception as e:
            print(f"错误: Inheritance.make方法发生未处理的异常: {e}")
            return None

    def operator(self) -> NativeOperator:
        """
        Get the operator of the term

        Returns:
            NativeOperator: The operator of the term
        """
        return NativeOperator.INHERITANCE
