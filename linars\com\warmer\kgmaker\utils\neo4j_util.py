"""
Neo4j Utility

This module provides utility functions for interacting with Neo4j.
"""
import logging
from py2neo import Graph, Node, Relationship, NodeMatcher

class Neo4jUtil:
    """Neo4j Utility class for interacting with Neo4j."""

    def __init__(self, graph):
        """
        Initialize the Neo4j Utility.

        Args:
            graph: The Neo4j Graph instance
        """
        self.graph = graph
        self.logger = logging.getLogger(__name__)
        self.parameters = {}
        self.logger.debug("Neo4jUtil initialized")

    def execute_cypher_sql(self, cypher_sql, method="GetGraphNodeAndShip"):
        """
        Execute a Cypher SQL query.

        Args:
            cypher_sql: The Cypher SQL query to execute
            method: The method to use for processing the result

        Returns:
            A list of dictionaries containing the result
        """
        result_list = []

        try:
            # 处理特殊查询
            if cypher_sql.strip().lower() == "call db.labels()":
                self.logger.debug("Executing query to get label list")
                result_list = self._get_graph_item(cypher_sql)
            elif cypher_sql.strip().lower() == "call db.relationshiptypes()":
                self.logger.debug("Executing query to get relationship type list")
                result_list = self._get_graph_item(cypher_sql)
            # 处理常规查询
            elif method == "GetGraphNode":
                result_list = self._get_graph_node(cypher_sql)
            elif method == "GetGraphRelationShip":
                result_list = self._get_graph_relationship(cypher_sql)
            elif method == "GetGraphItem":
                result_list = self._get_graph_item(cypher_sql)
            elif method == "GetEntityList":
                result_list = self._get_entity_list(cypher_sql)
            elif method == "GetGraphNodeAndShip":
                result_list = self._get_graph_node_and_ship(cypher_sql)
            elif method == "excuteCypherSql":
                result_list = self._get_graph_node_and_ship(cypher_sql)
            else:
                self.logger.error(f"Invalid method: {method}")
                print(f"无效的方法: {method}")
        except Exception as e:
            self.logger.error(f"Error executing Cypher SQL: {e}")
            print(f"执行Cypher SQL错误: {e}")

        return result_list

    def _get_graph_node(self, cypher_sql):
        """
        Get graph nodes from a Cypher SQL query.

        Args:
            cypher_sql: The Cypher SQL query to execute

        Returns:
            A list of dictionaries containing the nodes
        """
        nodes = []
        try:
            result = self.graph.run(cypher_sql, self.parameters)
            for record in result:
                for key in record.keys():
                    node = record[key]
                    if isinstance(node, Node):
                        node_dict = dict(node)
                        node_dict['id'] = node.identity
                        node_dict['labels'] = list(node.labels)
                        nodes.append(node_dict)
        except Exception as e:
            self.logger.error(f"Error getting graph nodes: {e}")

        return nodes

    def _get_graph_relationship(self, cypher_sql):
        """
        Get graph relationships from a Cypher SQL query.

        Args:
            cypher_sql: The Cypher SQL query to execute

        Returns:
            A list of dictionaries containing the relationships
        """
        relationships = []
        try:
            result = self.graph.run(cypher_sql, self.parameters)
            for record in result:
                for key in record.keys():
                    rel = record[key]
                    if isinstance(rel, Relationship):
                        rel_dict = dict(rel)
                        rel_dict['id'] = rel.identity
                        rel_dict['type'] = rel.type
                        rel_dict['startNode'] = rel.start_node.identity
                        rel_dict['endNode'] = rel.end_node.identity
                        relationships.append(rel_dict)
        except Exception as e:
            self.logger.error(f"Error getting graph relationships: {e}")

        return relationships

    def _get_graph_item(self, cypher_sql):
        """
        Get graph items from a Cypher SQL query.

        Args:
            cypher_sql: The Cypher SQL query to execute

        Returns:
            A list of dictionaries containing the items
        """
        items = []
        try:
            self.logger.debug(f"Executing Cypher query: {cypher_sql}")
            result = self.graph.run(cypher_sql, self.parameters)
            for record in result:
                item = {}
                for key in record.keys():
                    # 处理特殊情况：db.labels()查询
                    if cypher_sql.strip().lower() == "call db.labels()" and key == "label":
                        item[key] = record[key]
                    # 处理特殊情况：db.relationshipTypes()查询
                    elif cypher_sql.strip().lower() == "call db.relationshiptypes()" and key == "relationshipType":
                        item[key] = record[key]
                    # 处理一般情况
                    else:
                        item[key] = record[key]
                items.append(item)
            self.logger.debug(f"Query result: {items}")
        except Exception as e:
            self.logger.error(f"Error getting graph items: {e}")
            self.logger.error(f"Query error: {e}")

        return items

    def _get_entity_list(self, cypher_sql):
        """
        Get entity list from a Cypher SQL query.

        Args:
            cypher_sql: The Cypher SQL query to execute

        Returns:
            A list of dictionaries containing the entities
        """
        entities = []
        try:
            result = self.graph.run(cypher_sql, self.parameters)
            for record in result:
                entity = {}
                for key in record.keys():
                    entity[key] = record[key]
                entities.append(entity)
        except Exception as e:
            self.logger.error(f"Error getting entity list: {e}")

        return entities

    def _get_graph_node_and_ship(self, cypher_sql):
        """
        Get graph nodes and relationships from a Cypher SQL query.

        Args:
            cypher_sql: The Cypher SQL query to execute

        Returns:
            A list containing a dictionary with nodes and relationships
        """
        result_dict = {"nodes": [], "relationships": []}
        try:
            result = self.graph.run(cypher_sql, self.parameters)

            # 处理节点
            nodes = set()
            for record in result:
                for key in record.keys():
                    item = record[key]
                    if isinstance(item, Node):
                        if item.identity not in nodes:
                            node_dict = dict(item)
                            node_dict['id'] = item.identity
                            node_dict['labels'] = list(item.labels)
                            result_dict["nodes"].append(node_dict)
                            nodes.add(item.identity)

            # 处理关系
            relationships = set()
            for record in result:
                for key in record.keys():
                    item = record[key]
                    if isinstance(item, Relationship):
                        if item.identity not in relationships:
                            rel_dict = dict(item)
                            rel_dict['id'] = item.identity
                            rel_dict['type'] = item.type
                            rel_dict['startNode'] = item.start_node.identity
                            rel_dict['endNode'] = item.end_node.identity
                            result_dict["relationships"].append(rel_dict)
                            relationships.add(item.identity)
        except Exception as e:
            self.logger.error(f"Error getting graph nodes and relationships: {e}")

        return [result_dict]

    def get_all_labels(self):
        """
        Get all labels from the database.

        Returns:
            A list of labels
        """
        labels = []
        try:
            cypher_sql = "CALL db.labels()"
            result = self.graph.run(cypher_sql)
            for record in result:
                if 'label' in record:
                    labels.append(record['label'])
            self.logger.debug(f"Retrieved label list: {labels}")
        except Exception as e:
            self.logger.error(f"Error getting labels: {e}")
            self.logger.error(f"Error retrieving label list: {e}")

        return labels

    def get_all_relationship_types(self):
        """
        Get all relationship types from the database.

        Returns:
            A list of relationship types
        """
        relationship_types = []
        try:
            cypher_sql = "CALL db.relationshipTypes()"
            result = self.graph.run(cypher_sql)
            for record in result:
                if 'relationshipType' in record:
                    relationship_types.append(record['relationshipType'])
            self.logger.debug(f"Retrieved relationship type list: {relationship_types}")
        except Exception as e:
            self.logger.error(f"Error getting relationship types: {e}")
            self.logger.error(f"Error retrieving relationship type list: {e}")

        return relationship_types
