"""
Response Utility

This module provides a response class for API responses.
"""

class R:
    """Response class for API responses."""
    
    def __init__(self, code=200, msg="success", data=None):
        """
        Initialize a response.
        
        Args:
            code: The response code
            msg: The response message
            data: The response data
        """
        self.code = code
        self.msg = msg
        self.data = data
    
    def set_msg(self, msg):
        """
        Set the response message.
        
        Args:
            msg: The response message
        """
        self.msg = msg
        return self
    
    def set_data(self, data):
        """
        Set the response data.
        
        Args:
            data: The response data
        """
        self.data = data
        return self
    
    def to_dict(self):
        """
        Convert the response to a dictionary.
        
        Returns:
            A dictionary representation of the response
        """
        return {
            'code': self.code,
            'msg': self.msg,
            'data': self.data
        }
