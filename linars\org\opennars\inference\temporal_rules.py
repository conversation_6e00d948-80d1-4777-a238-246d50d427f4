"""
NARS的时间推理规则。
"""
# print("TemporalRules----------Imported -----")
import collections
from typing import List, Set, Dict, Tuple, Optional

from linars.org.opennars.entity.sentence import Sentence
from linars.org.opennars.entity.stamp import Stamp
# print("TemporalRules----------Imported Stamp")
from linars.org.opennars.inference.budget_functions import BudgetFunctions
# print("TemporalRules----------Imported BudgetFunctions")
# 避免循环导入
# from linars.org.opennars.inference.compositional_rules import CompositionalRules
from linars.org.opennars.inference.truth_functions import TruthFunctions
from linars.org.opennars.io.symbols import JUDGMENT_MARK
from linars.org.opennars.language.conjunction import Conjunction
from linars.org.opennars.language.equivalence import Equivalence
from linars.org.opennars.language.implication import Implication
from linars.org.opennars.language.interval import Interval
from linars.edu.memphis.ccrg.linars.term import Term

class TemporalRules:
    """
    NARS的时间推理规则。
    """

    # Temporal order constants
    ORDER_NONE = 2
    ORDER_FORWARD = 1
    ORDER_CONCURRENT = 0
    ORDER_BACKWARD = -1
    ORDER_INVALID = -2

    @staticmethod
    def reverse_order(order: int) -> int:
        """
        反转时间顺序。
        参数:
            order: 要反转的时间顺序
        返回:
            反转后的时间顺序
        """
        if order == TemporalRules.ORDER_NONE:
            return TemporalRules.ORDER_NONE
        else:
            return -order

    @staticmethod
    def matching_order(a: Sentence, b: Sentence) -> bool:
        """
        检查两个句子的时间顺序是否匹配。
        参数:
            a: 第一个句子
            b: 第二个句子
        返回:
            如果时间顺序匹配返回True
        """
        return TemporalRules.matching_order_int(a.get_temporal_order(), b.get_temporal_order())

    @staticmethod
    def matching_order_int(order1: int, order2: int) -> bool:
        """
        检查两个时间顺序是否匹配。
        参数:
            order1: 第一个时间顺序
            order2: 第二个时间顺序
        返回:
            如果时间顺序匹配返回True
        """
        return (order1 == order2) or (order1 == TemporalRules.ORDER_NONE) or (order2 == TemporalRules.ORDER_NONE)

    @staticmethod
    def ded_exe_order(order1: int, order2: int) -> int:
        """
        确定演绎和例证的时间顺序。
        参数:
            order1: 第一个时间顺序
            order2: 第二个时间顺序
        返回:
            结果时间顺序
        """
        order = TemporalRules.ORDER_INVALID
        if (order1 == order2) or (order2 == TemporalRules.ORDER_NONE):
            order = order1
        elif (order1 == TemporalRules.ORDER_NONE) or (order1 == TemporalRules.ORDER_CONCURRENT):
            order = order2
        elif order2 == TemporalRules.ORDER_CONCURRENT:
            order = order1
        return order

    @staticmethod
    def abd_ind_com_order(order1: int, order2: int) -> int:
        """
        确定溯因、归纳和比较的时间顺序。

        参数:
            order1: 第一个时间顺序
            order2: 第二个时间顺序

        返回:
            结果时间顺序
        """
        order = TemporalRules.ORDER_INVALID
        if order2 == TemporalRules.ORDER_NONE:
            order = order1
        elif (order1 == TemporalRules.ORDER_NONE) or (order1 == TemporalRules.ORDER_CONCURRENT):
            order = TemporalRules.reverse_order(order2)
        elif (order2 == TemporalRules.ORDER_CONCURRENT) or (order1 == -order2):
            order = order1
        return order

    @staticmethod
    def analogy_order(order1: int, order2: int, figure: int) -> int:
        """
        确定类比的时间顺序。

        参数:
            order1: 第一个时间顺序
            order2: 第二个时间顺序
            figure: 三段论的图形

        返回:
            结果时间顺序
        """
        order = TemporalRules.ORDER_INVALID
        if (order2 == TemporalRules.ORDER_NONE) or (order2 == TemporalRules.ORDER_CONCURRENT):
            order = order1
        elif (order1 == TemporalRules.ORDER_NONE) or (order1 == TemporalRules.ORDER_CONCURRENT):
            order = (order2 if figure < 20 else TemporalRules.reverse_order(order2))
        elif order1 == order2:
            if (figure == 12) or (figure == 21):
                order = order1
        elif order1 == -order2:
            if (figure == 11) or (figure == 22):
                order = order1
        return order

    @staticmethod
    def resemblance_order(order1: int, order2: int, figure: int) -> int:
        """
        确定相似的时间顺序。

        参数:
            order1: 第一个时间顺序
            order2: 第二个时间顺序
            figure: 三段论的图形

        返回:
            结果时间顺序
        """
        order = TemporalRules.ORDER_INVALID
        order1_reverse = TemporalRules.reverse_order(order1)

        if order2 == TemporalRules.ORDER_NONE:
            order = order1 if figure > 20 else order1_reverse  # switch when 11 or 12
        elif (order1 == TemporalRules.ORDER_NONE) or (order1 == TemporalRules.ORDER_CONCURRENT):
            order = order2 if figure % 10 == 1 else TemporalRules.reverse_order(order2)  # switch when 12 or 22
        elif order2 == TemporalRules.ORDER_CONCURRENT:
            order = order1 if figure > 20 else order1_reverse  # switch when 11 or 12
        elif order1 == order2:
            order = order1 if figure == 21 else -order1
        return order

    @staticmethod
    def compose_order(order1: int, order2: int) -> int:
        """
        确定组合的时间顺序。

        参数:
            order1: 第一个时间顺序
            order2: 第二个时间顺序

        返回:
            结果时间顺序
        """
        order = TemporalRules.ORDER_INVALID
        if order2 == TemporalRules.ORDER_NONE:
            order = order1
        elif order1 == TemporalRules.ORDER_NONE:
            order = order2
        elif order1 == order2:
            order = order1
        return order

    @staticmethod
    def too_much_temporal_statements(t: Term) -> bool:
        """
        检查术语是否有太多时间语句。

        参数:
            t: 要检查的术语

        返回:
            如果术语有太多时间语句返回True
        """
        return (t is None) or (t.contained_temporal_relations() > 1)

    @staticmethod
    def term_for_temporal_induction(t: Term) -> bool:
        """
        检查术语是否可以用于时间归纳。

        参数:
            t: 要检查的术语

        返回:
            如果术语可以用于时间归纳返回True
        """
        from linars.org.opennars.language.inheritance import Inheritance
        from linars.org.opennars.language.similarity import Similarity
        return isinstance(t, Inheritance) or isinstance(t, Similarity)

    @staticmethod
    def temporal_induction(s1: Sentence, s2: Sentence, nal, succeeding_events_induction: bool,
                          add_to_memory: bool, allow_sequence: bool, buffer_induction: bool) -> List:
        """
        两个句子之间的时间归纳。

        参数:
            s1: 第一个句子
            s2: 第二个句子
            nal: 派生上下文
            succeeding_events_induction: 是否执行后续事件归纳
            add_to_memory: 是否将派生任务添加到内存
            allow_sequence: 是否允许序列派生
            buffer_induction: 是否缓冲归纳

        返回:
            派生任务列表
        """
        from linars.org.opennars.language.statement import Statement

        if (s1.truth is None or s2.truth is None or
            s1.punctuation != JUDGMENT_MARK or
            s2.punctuation != JUDGMENT_MARK or
            s1.is_eternal() or s2.is_eternal()):
            return []

        t1 = s1.term
        t2 = s2.term

        derive_sequence_only = (not add_to_memory and not buffer_induction) or Statement.invalid_statement(t1, t2, True)
        if Statement.invalid_statement(t1, t2, False):
            return []

        duration_cycles = nal.nar.narParameters.DURATION
        time1 = s1.get_occurrence_time()
        time2 = s2.get_occurrence_time()
        time_diff = time2 - time1
        interval = None

        if not TemporalRules.concurrent(time1, time2, duration_cycles):
            interval = Interval(abs(time_diff))
            if time_diff > 0:
                t1 = Conjunction.make(t1, interval, TemporalRules.ORDER_FORWARD)
            else:
                t2 = Conjunction.make(t2, interval, TemporalRules.ORDER_FORWARD)

        order = TemporalRules.order(time_diff, duration_cycles)
        given_truth1 = s1.truth
        given_truth2 = s2.truth

        # This code adds a penalty for large time distance
        s3 = s2.projection(s1.get_occurrence_time(), nal.time.time(), nal.memory)
        given_truth2 = s3.truth

        # Truth and priority calculations
        truth1 = TruthFunctions.induction(given_truth1, given_truth2, nal.nar.narParameters)
        truth2 = TruthFunctions.induction(given_truth2, given_truth1, nal.nar.narParameters)
        truth3 = TruthFunctions.comparison(given_truth1, given_truth2, nal.nar.narParameters)
        truth4 = TruthFunctions.intersection(given_truth1, given_truth2, nal.nar.narParameters)
        budget1 = BudgetFunctions.forward(truth1, nal)
        budget2 = BudgetFunctions.forward(truth2, nal)
        budget3 = BudgetFunctions.forward(truth3, nal)
        budget4 = BudgetFunctions.forward(truth4, nal)

        statement1 = Implication.make(t1, t2, order)
        statement2 = Implication.make(t2, t1, TemporalRules.reverse_order(order))
        statement3 = Equivalence.make(t1, t2, order)
        statement4 = None

        if order == TemporalRules.ORDER_FORWARD:
            statement4 = Conjunction.make(t1, interval, s2.term, order)
        elif order == TemporalRules.ORDER_BACKWARD:
            statement4 = Conjunction.make(s2.term, interval, t1, TemporalRules.reverse_order(order))
        else:
            statement4 = Conjunction.make(t1, s2.term, order)

        t11s = []
        t22s = []
        penalties = []

        # "Perception Variable Introduction Rule"
        if not derive_sequence_only and statement2 is not None:
            for subject_intro in [True, False]:
                results = CompositionalRules.introduce_variables(nal, statement2, subject_intro)
                for content_penalty in results:
                    content = content_penalty[0]
                    penalty = content_penalty[1]
                    t11s.append(content.get_predicate())
                    t22s.append(content.get_subject())
                    penalties.append(penalty)

        derivations = []
        if not derive_sequence_only:
            for i in range(len(t11s)):
                t11 = t11s[i]
                t22 = t22s[i]
                penalty = penalties[i]
                statement11 = Implication.make(t11, t22, order)
                statement22 = Implication.make(t22, t11, TemporalRules.reverse_order(order))
                statement33 = Equivalence.make(t11, t22, order)
                TemporalRules.append_conclusion(nal, truth1.clone().mul_confidence(penalty), budget1.clone(), statement11, derivations, add_to_memory)
                TemporalRules.append_conclusion(nal, truth2.clone().mul_confidence(penalty), budget2.clone(), statement22, derivations, add_to_memory)
                TemporalRules.append_conclusion(nal, truth3.clone().mul_confidence(penalty), budget3.clone(), statement33, derivations, add_to_memory)

            TemporalRules.append_conclusion(nal, truth1, budget1, statement1, derivations, add_to_memory)
            TemporalRules.append_conclusion(nal, truth2, budget2, statement2, derivations, add_to_memory)
            TemporalRules.append_conclusion(nal, truth3, budget3, statement3, derivations, add_to_memory)

        if not TemporalRules.too_much_temporal_statements(statement4):
            if not allow_sequence:
                return derivations

            tl = nal.double_premise_task(statement4, truth4, budget4, True, False, add_to_memory)
            if tl is not None:
                for t in tl:
                    # fill sequenceTask buffer due to the new derived sequence
                    if (add_to_memory and
                        t.sentence.is_judgment() and
                        not t.sentence.is_eternal() and
                        isinstance(t.sentence.term, Conjunction) and
                        t.sentence.term.get_temporal_order() != TemporalRules.ORDER_NONE and
                        t.sentence.term.get_temporal_order() != TemporalRules.ORDER_INVALID):
                        t.sequence_task = True
                        nal.nar.memory.globalBuffer.put_in(t)
                    derivations.append(t)

        return derivations

    @staticmethod
    def append_conclusion(nal, truth1, budget1, statement1, success, add_to_memory):
        """
        将结论附加到派生任务列表中。

        参数:
            nal: 派生上下文
            truth1: 真值
            budget1: 预算值
            statement1: 语句
            success: 要附加到的列表
            add_to_memory: 是否添加到内存
        """
        if not TemporalRules.too_much_temporal_statements(statement1):
            t = nal.double_premise_task(statement1, truth1, budget1, True, False, add_to_memory)
            if t is not None:
                success.extend(t)

    @staticmethod
    def order(time_diff: int, duration_cycles: int) -> int:
        """
        根据时间差确定时间顺序。

        参数:
            time_diff: 时间差
            duration_cycles: 持续时间周期

        返回:
            时间顺序
        """
        half_duration = duration_cycles // 2
        if time_diff > half_duration:
            return TemporalRules.ORDER_FORWARD
        elif time_diff < -half_duration:
            return TemporalRules.ORDER_BACKWARD
        else:
            return TemporalRules.ORDER_CONCURRENT

    @staticmethod
    def order_times(a: int, b: int, duration_cycles: int) -> int:
        """
        确定两个时间之间的时间顺序。

        参数:
            a: 第一个时间
            b: 第二个时间
            duration_cycles: 持续时间周期

        返回:
            时间顺序
        """
        if a == Stamp.ETERNAL or b == Stamp.ETERNAL:
            raise ValueError("order() does not compare ETERNAL times")

        return TemporalRules.order(b - a, duration_cycles)

    @staticmethod
    def concurrent(a: int, b: int, duration_cycles: int) -> bool:
        """
        检查两个时间是否并发。

        参数:
            a: 第一个时间
            b: 第二个时间
            duration_cycles: 持续时间周期

        返回:
            如果时间并发返回True
        """
        if a == Stamp.ETERNAL:
            return b == Stamp.ETERNAL
        elif b == Stamp.ETERNAL:
            return False
        else:
            return TemporalRules.order_times(a, b, duration_cycles) == TemporalRules.ORDER_CONCURRENT
