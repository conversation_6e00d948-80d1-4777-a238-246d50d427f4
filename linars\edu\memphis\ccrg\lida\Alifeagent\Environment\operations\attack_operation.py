"""
攻击操作实现

本模块提供ALife环境中攻击操作的实现
"""
from typing import List, Optional, Any

from linars.edu.memphis.ccrg.alife.elements.alife_object import ALifeObject
from linars.edu.memphis.ccrg.alife.elements.cell import Cell
from linars.edu.memphis.ccrg.alife.opreations.world_operation import WorldOperation

class AttackOperation(WorldOperation):
    """
    攻击操作实现

    该类实现ALife环境中的攻击操作
    """

    def __init__(self):
        """初始化攻击操作"""
        super().__init__("attack")

    def execute(self, actor: ALifeObject, target: Optional[ALifeObject], *params) -> bool:
        """
        执行攻击操作

        参数:
            actor: 执行攻击的对象
            target: 攻击目标(未使用)
            params: 附加参数

        返回:
            操作成功返回True，否则返回False
        """
        # Get the actor's cell
        cell = actor.get_container()
        if not isinstance(cell, Cell):
            return False

        # Get the actor's direction
        direction = actor.get_attribute("direction")
        if direction is None:
            return False

        # Determine the target cell based on direction
        x = cell.get_x_coordinate()
        y = cell.get_y_coordinate()

        if direction == 'N':
            y -= 1
        elif direction == 'S':
            y += 1
        elif direction == 'E':
            x += 1
        elif direction == 'W':
            x -= 1

        # Get the world and check if the target cell is valid
        world = cell.get_world()
        if not (0 <= x < world.get_width() and 0 <= y < world.get_height()):
            return False

        # Get the target cell and its objects
        target_cell = world.get_cell(x, y)
        objects = target_cell.get_objects()

        # Attack all objects in the target cell
        for obj in objects:
            # Reduce health of the target
            obj.decrease_health(0.3)

        return True
