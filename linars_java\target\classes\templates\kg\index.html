 <!DOCTYPE html>
 <html lang = "en">
     <head>
     <meta charset = "utf-8">
     <title>clinars - GraphXR</title>
     <meta http-equiv = "X-UA-Compatible" content = "IE=edge">
     <meta name = "viewport" content = "width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
     <meta name = "description" content = "">
     <meta name = "author" content = "Kineviz inc.">

     <link href = "index999.css" async = "true" rel = "stylesheet">

     <link href="static/hopscotch.min.css" async="true" rel="stylesheet">

     <link href="static/jquery-ui.min.css" async="true" rel="stylesheet">

     <link href="static/dc.css" async="true" rel="stylesheet">

     <link href="static/mapbox-gl.css" async="true" rel="stylesheet">

     <link href="static/mathbox.css" async="true" rel="stylesheet">

     <link href="static/Common.css" async="true" rel="stylesheet">

     <link href="static/IconStylesheet.css" async="true" rel="stylesheet">

     <link href="static/Animation.css" async="true" rel="stylesheet">

     <link href="static/app.css" async="true" rel="stylesheet">

     <link href="static/gui.css" async="true" rel="stylesheet">

     <style>
    /*=====start tip-container style=====*/
     #tip-container {
    display: flex;
    position: fixed;
    justify-content:center;
    align-items: center;
    width: 100%;
    height: 100%;
    top: 0;
    z-index: 9999999;
    text-align: center;
}

 #loading-tip {
    max-width: 600px;
    padding: 10px 12px;
    text-align: center;
    line-height: 32px;
    display: flex;
    justify-content: space-around;
    color:  #fff;
    font-size: 16px;
    font-family: Georgia,
    'Times New Roman',
    Times,
    serif;
    background-color:  #222;
    opacity: 0.7;
    z-index: 99999;
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    border-radius: 8px;
    filter: progid:DXImageTransform.Microsoft.Alpha(opacity=70);
    border: 1px #666 solid;
}

 #loading-tip> img {
    padding-right: 6px;
    width: 32px;
    height: 32px;
    box-sizing: initial;
    -webkit-box-sizing: initial;
    -moz-box-sizing: initial;
    user-select: none;
    -webkit-user-select: none;
}

 #loading-tip .loading-text {
    padding: 0 10px;
    max-width: 100%;
    /* word-break: break-all; */
    max-height: 420px;
    text-overflow: ellipsis;
    overflow: hidden;
}

 #loading-tip.has-type {
    padding-right: 20px;
    padding-left: 12px;
    min-width: 80px;
    text-align: left;
}

 #loading-tip .loading-close {
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
}

 #loading-tip.warn,
 #loading-tip.warning {
    color:  #856404;
    background-color:  #fff3cd;
    border-color:  #ffeeba;
    opacity: 1.0;
}

 #loading-tip.danger {
    color:  #a94442;
    background-color:  #f2dede;
    border-color:  #ebccd1;
    opacity: 1.0;
}

 #loading-tip.info {
    color:  #31708f;
    background-color:  #d9edf7;
    border-color:  #bce8f1;
    opacity: 1.0;
}

 #loading-tip.success {
    color:  #3c763d;
    background-color:  #dff0d8;
    border-color:  #d6e9c6;
    opacity: 1.0;
}

/*=====end tip-container style=====*/

 #renderContainer {
    width: 100% !important;
    height: 100% !important;
    position: fixed;
    margin: 0;
    padding: 0;
    top: 0;
    left: 0;
}

 #renderContainer {
    display: none;
}

.confirm-modal .modal-content {
    margin-top: 78px!important;
}

body {
    background-color: black;
}
 </style>

 <script>
function addJSScript(jsURL) {
    return new Promise((resolve, reject) => {

        let jsDom = Array.from(document.getElementsByTagName('script')).find(e => e.src == jsURL);
        if (jsDom) {
            return resolve('done');
        } else {

            let jsDom = document.createElement('script');
            jsDom.type = 'text/javascript';
            jsDom.async = true;
            jsDom.src = jsURL;
            jsDom.onload = function () {
                resolve('done');
            };
            let s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(jsDom, s);
        }

    })
}

//for electron
if (typeof module === 'object') {
    window.module = module;
}

window.isElectron = (/Electron/ig).test(navigator.userAgent) && !((/Neo4jDesktop/ig).test(navigator.userAgent)) ? true : false;
if (window.isElectron) {
    addJSScript('/static/javascripts/RecordingUtil.js');
    addJSScript('/static/javascripts/ElectronSocket.js');
    // addJSScript('/static/javascripts/ElectronPerformance.js');
}
 </script>

 <script>
window.globalVariable = (function () {
    return {
        "_id": "6340eb99c19ffe00575cf4a5",
        "name": "graphxr",
        "version": "2.14.2",
        "enableSurvey": false,
        "time": *************,
        "firstName": "kkk",
        "lastName": "zzz",
        "email": "@qq.com",
        "cityInfo": "china",
        "planType": 0,
        "planDays": 0,
        "planPrice": {
            "0": {
                "name": "Free Account",
                "unit": "Free",
                "price": 0
            },
            "1": {
                "name": "Pro Account",
                "unit": "Month",
                "price": 120,
                "stripePriceId": "plan_F1ZkXb6UhJLAVU"
            },
            "2": {
                "name": "Pro Account",
                "unit": "Year",
                "price": 1320,
                "stripePriceId": "plan_F1ZkfEDQZhAPMN"
            }
        },
        "connectorAPIAuth": false,
        "isPlanPriceOpen": true,
        "isManager": false,
        "isCustomDB": false,
        "timeZone": -8,
        "isPostgresql": 0,
        "isDemo": 0,
        "requestMode": "server",
        "path": "",
        "adminEmail": "com",
        "title": "GraphXR",
        "graphDatabaseType": "neo4j",
        "stripePublishableKey": "pk_live_B2CmxFhSNkKvv9iJ1N4OjNH3",
        "license": {
            "projectName": "GraphXR",
            "company": "Kineviz inc."
        },
        "blankProject": true,
        "query": {},
        "features": {
            "dbclickExpandAll": false,
            "aboutToQuickStart": false
        },
        "maxReturnLimit": 10000,
        "auth": {
            "ldap": false,
            "azureAD": false,
            "google": false,
            "googleClientID": "",
            "saml": false,
            "oauth2": false
        },
        "NODE_ENV": "production",
        "disableExport": false,
        "project": {
            "forceLayoutSettings": {
                "linkStrength": 1,
                "linkDistance": 0.5,
                "charge": -0.01,
                "gravity": 0.15,
                "friction": 0.3,
                "heightCompress": 1,
                "collision": 0.02
            },
            "projectSettings": {
                "loadInnerRelationship": true,
                "autoShowImage": false,
                "disableInfoPanel": true,
                "quickInfo": false,
                "hideArrow": false,
                "showRelationshipName": false,
                "useDash": false,
                "nodeCaptionPosition": "right",
                "iconMode": 1,
                "useCurve": true,
                "hidePin": false,
                "edgeWidthScale": 1,
                "nodeSizeScale": 1,
                "captionSizeScale": 1,
                "captionMaxLength": 100,
                "fogDensity": 0,
                "showSnapshot": false,
                "captionMultiLangua": false,
                "blendEdges": false,
                "theme": "dark",
                "mode": "3d"
            },
            "currentNeo4jDB": "neo4j",
            "isDemo": true,
            "isLocal": false,
            "isShare": false,
            "queryCollections": [],
            "sqlCollections": [],
            "mysqlCollections": [],
            "searchIndex": "graphxrIndex",
            "lastActiveTime": 1665225359549,
            "_id": "6340eed2c19ffe00575cf7d4",
            "user": "6340eb99c19ffe00575cf4a5",
            "hostname": "vc-investment.windmill-1.kinevizlabs.com",
            "port": 0,
            "boltPort": 7687,
            "username": "b9f03f27ee95a6a7d4d519b8dc479b9b23fc7d04d5",
            "password": "",
            "projectName": "VC investment 2004-2013",
            "labels": [],
            "relationships": [],
            "createTime": "2022-10-08T03:30:26.717Z",
            "__v": 0
        },
        "theme": "dark"
    };
})();
 </script>

     <script src = "/static/catchreport.kineviz.com.js">  </script>

     <script src = "/static/jquery.min.js">  </script>

     <script src = "/static/hopscotch.js">  </script>

     <script src = "/static/jquery-ui.min.js">  </script>

     <script src = "/static/d3.js">  </script>

     <script src = "/static/md5.min.js">  </script>

     <script src = "/static/crossfilter.js">  </script>

     <script src = "/static/dc.js">  </script>

     <script src = "/static/three.js">  </script>

     <script src = "/static/mapbox-gl.js">  </script>

     <script src = "/static/mathbox.min.js">  </script>

     <script src = "/static/cypher-editor-support.min.js">  </script>

     <script src = "/static/jquery.fullscreen-min.js">  </script>

     <script src = "/static/html2canvas.min.js">  </script>

     <script src = "/static/socket.io.js">  </script>

     <script src = "/static/vendor.bundle.js?v=2.14.2-082a552d">  </script>

     <script src = "/static/app.reactdatagrid.js?v=2.14.2-082a552d">  </script>

     <script src = "/static/app.nodeModules.js?v=2.14.2-082a552d">  </script>

     <script>
    // Use webpack handle the issue.
    //disable console.log when window.globalVariable.NODE_ENV !== "development"
    // if( window.globalVariable.NODE_ENV !== "development"){
    //   console.log = () =>{ };
    //   console.time = () =>{ };
    //   console.timeEnd = () =>{ };
    // }
     </script>

     </head>

     <body>

     <div id = "map">  </div>

     <!--start tip container-->
     <div id = "tip-container" style = "display: flex;">
     <div id = "loading-tip">
     <span class = "loading-text"> Loading... </span>
     <span class = "loading-close" style = "display: none;">  & times;
     </span>  </span>
     </div>
     </div>
 <!--end tip container-->

 <!--start confirm Modal-->
 <div id = "confirm-modal" class = "modal small show" tabindex = "-1" role = "dialog" aria-labelledby = "confirm-title"
    aria-hidden = "true" style = "display: none;">
     <div class = "modal-dialog">
     <div class = "modal-content ">

     <div class = "modal-header">
     <h5 id = "confirm-title" class = "text-center"> Title </h5>
     <button type = "button" onclick = "hideModal()" class = "close" data-dismiss = "modal" aria-hidden = "true"> × </button>
     </div>
     <div class = "modal-body ">
     <p class = "error-text text-center" style = "font-size:14px;">
     <i class = "fa fa-exclamation-triangle">  </i>
     <span id = "confirm-content"> Content </span>
     </p>
     </div>
     <div class = "modal-footer">
     <button class = "btn btn-info" id = "confirm-cancel-button"
    onclick = "hideModal()" aria-hidden = "true" data-dismiss = "modal">
    Cancel
     </button>
     <button class = "btn btn-danger" id = "confirm-ok-button">
    OK
     </button>
     </div>
     </div>
     </div>
     </div>
     <!--end confirm Modal-->

     <!--react-dom-->
     <div id = "react-dom">  </div>
     <!--three-vrbutton-->
     <div id = "vr-actions">  </div>

     <!--three-canvas-->
     <div id = "renderContainer">
     <div id = "canvas-renderContainer">  </div>
     </div>

     <!--Dynamic reactjs page-->

     <script src = "/static/Stats.js">  </script>

     <script src = "/static/OrbitControls.js">  </script>

     <script src = "/static/TrackballControls.js">  </script>

     <script src = "/static/threex.keyboardstate.js">  </script>

     <script src = "/static/bootstrap.bundle.min.js">  </script>

     <script src = "/static/jszip.min.js">  </script>

     <script src = "static/app.js">  </script>

     <script>
        $(document).ready(() => {
            console.log("document ready");

            $("#tip-container").hide(0);
            $("#tip-container").on("click", () => {
                $("#tip-container").hide(0);
            })
        })

        function hideModal() {
            $('#confirm-modal').modal('hide');
        }

        /*********************************
        ---- start analytics.kineviz------
         **********************************/
        //ignore admin page
        if (!(/\/(admin|login)/ig).test(window.location.href)) {
            var Countly = Countly || {};
            Countly.q = Countly.q || [];
            Countly.app_key = "Graph";
            Countly.host = "";
            Countly.url = Countly.host + "/api/track/track";
            Countly.q.push(['track_sessions']);
            // Countly.q.push(['track_pageview']);
            (function () {
                var cly = document.createElement('script');
                cly.type = 'text/javascript';
                cly.async = true;
                cly.src = '/static/analytics.kineviz.countly.js';
                cly.onload = function () {
                    Countly.init()
                };
                var s = document.body.getElementsByTagName('script')[0];
                s.parentNode.insertBefore(cly, s);
            })();
        }
    /*********************************
    ---- end analytics.kineviz------
     **********************************/
    </script>
 </body>
</html>
