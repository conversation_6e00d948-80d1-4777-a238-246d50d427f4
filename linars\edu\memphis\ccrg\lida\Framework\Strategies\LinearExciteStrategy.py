# LIDA认知框架
"""
线性激励策略实现
"""

from typing import Dict, Any
from linars.edu.memphis.ccrg.lida.Framework.Strategies.ExciteStrategy import ExciteStrategy

class LinearExciteStrategy(ExciteStrategy):
    """
    线性激励策略实现
    """

    def __init__(self):
        """
        初始化线性激励策略
        """
        self.factor = 1.0

    def excite(self, current_activation: float, base_level_activation: float, amount: float) -> float:
        """
        使用给定参数激励对象

        参数:
            current_activation: 对象的当前激活值
            base_level_activation: 对象的基础激活水平
            amount: 激励值

        返回:
            新的激活值
        """
        result = current_activation + amount * self.factor
        if result > 1.0:
            result = 1.0
        return result

    def init(self, params: Dict[str, Any]) -> None:
        """
        使用给定参数初始化此策略

        参数:
            params: 初始化参数
        """
        if "factor" in params:
            self.factor = float(params["factor"])
