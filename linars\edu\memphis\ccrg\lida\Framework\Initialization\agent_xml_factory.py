"""
基于XML的代理工厂实现

本模块提供了基于XML的代理工厂实现。
它根据XML文件创建并返回Agent对象。

每个创建的模块都会被实例化和初始化。在所有模块创建完成后，
为每个模块添加所有关联模块。最后，如果模块有初始化器，则运行该初始化器。
"""
import importlib
import logging
import os
import xml.etree.ElementTree as ET
from collections import defaultdict
from typing import Dict, Any, Optional, List, Tuple, Set

# 延迟导入Concept类以避免循环导入
try:
    from linars.edu.memphis.ccrg.linars.concept import Concept
except ImportError:
    # 如果无法导入，创建一个空的Concept类
    class Concept:
        def __init__(self, *args, **kwargs):
            pass

# 延迟导入Memory类以避免循环导入
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.linars.memory import Memory

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Shared.ElementFactory import ElementFactory
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawner import TaskSpawner
from linars.edu.memphis.ccrg.lida.Framework.agent import Agent
from linars.edu.memphis.ccrg.lida.Framework.agent_impl import AgentImpl
from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_factory import AgentFactory
from linars.edu.memphis.ccrg.lida.Framework.Initialization.config_utils import ConfigUtils
from linars.edu.memphis.ccrg.lida.Framework.Initialization.factories_data_xml_loader import FactoriesDataXmlLoader
from linars.edu.memphis.ccrg.lida.Framework.Initialization.global_initializer import GlobalInitializer
from linars.edu.memphis.ccrg.lida.Framework.Initialization.xml_utils import XmlUtils


class TaskData:
    """
    用于表示FrameworkTask定义的嵌套类
    """

    def __init__(self, name: str, tasktype: str, ticks_per_run: int, params: Dict[str, Any]):
        """
        初始化TaskData对象

        参数:
            name: 任务名称
            tasktype: 任务类型
            ticks_per_run: 任务每次运行的ticks数
            params: 任务参数
        """
        self.task_spawner = None
        self.name = name
        self.tasktype = tasktype
        self.ticks_per_run = ticks_per_run
        self.params = params


class AgentXmlFactory(AgentFactory):
    """
    基于XML的代理工厂实现

    该类提供了基于XML的代理工厂实现。

    每个创建的模块都使用Class.forName(String)实例化。
    然后运行其FrameworkModule.init()方法。所有模块都以这种方式创建后，
    为每个模块添加所有关联模块。最后，如果模块有初始化器，则运行该初始化器。
    """

    # Default file paths and property names
    DEFAULT_XML_FILE_PATH = "configs/alifeAgent.xml"
    DEFAULT_SCHEMA_FILE_PATH = "edu/memphis/ccrg/lida/framework/initialization/config/LidaXMLSchema.xsd"
    AGENT_DATA_PROPERTY_NAME = "lida.agentdata"

    def __init__(self, factory_file: str = None):
        """
        初始化基于XML的代理工厂

        参数:
            factory_file: XML工厂文件路径。如果为None，则使用默认路径
        """
        self.logger = logging.getLogger(__name__)
        self.factory_file = factory_file if factory_file else self.DEFAULT_XML_FILE_PATH
        self.properties = {}
        self.initializer = GlobalInitializer.get_instance()
        self.loader = FactoriesDataXmlLoader()

    def get_agent(self, properties: Dict[str, Any] = None) -> Optional[Agent]:
        """
        根据属性创建并返回Agent对象
        参数:
            properties: 代理的属性
        返回:
            创建的Agent对象，如果创建失败则返回None
        """
        file_name = self.DEFAULT_XML_FILE_PATH
        if properties is not None:
            file_name = properties.get(self.AGENT_DATA_PROPERTY_NAME, self.DEFAULT_XML_FILE_PATH)
        else:
            self.logger.warning("Properties was null using default agent XML file path")

        dom = XmlUtils.parse_xml_file(file_name, self.DEFAULT_SCHEMA_FILE_PATH)
        print(f"Parsing XML file--------: {file_name}")
        agent = self.parse_document(dom)
        return agent

    def parse_document(self, dom: ET.Element) -> Optional[Agent]:
        """
        解析XML文档，创建TaskManager、TaskSpawners、Modules和子模块。
        设置监听器并关联模块，创建一个新的Agent。
        参数:
            dom: XML DOM文档
        返回:
            创建的Agent对象，如果解析失败则返回None
        """
        if dom is None:
            self.logger.error("Document dom was null. Cannot parse it")
            return None

        agent = None
        to_initialize = []
        to_associate = []
        to_run = []

        # Get the root element
        doc_ele = dom

        # Debug the XML structure
        self.logger.debug(f"XML document structure:\n{XmlUtils.debug_element(doc_ele)}")

        # Get global parameters
        self.logger.debug("Getting global parameters...")
        global_parameters = self.get_global_parameters(doc_ele)
        self.logger.debug(f"Global parameters: {global_parameters}")

        g = GlobalInitializer.get_instance()
        for key, value in global_parameters.items():
            self.logger.debug(f"Setting global attribute: {key}={value}")
            g.set_attribute(key, value)

        # Get task manager
        tm = self.get_task_manager(doc_ele)
        self.logger.debug("Finished obtaining TaskManager")

        # Create the agent with the task manager
        try:
            agent = AgentImpl(tm)
            return agent
            self.logger.debug(f"Created agent: {agent}")

            # Set the agent in the task manager
            if tm:
                tm.set_agent(agent)
                self.logger.debug("Set agent in TaskManager")
        except Exception as e:
            self.logger.error(f"Error creating agent: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return None

        # Get task spawners
        task_spawners = self.get_task_spawners(doc_ele, tm)
        self.logger.debug("Finished creating TaskSpawners")

        # Get modules
        modules = self.get_modules(doc_ele, to_associate, to_initialize, task_spawners, to_run)
        for framework_module in modules:
            agent.add_submodule(framework_module)
        self.logger.debug("Finished creating modules and submodules")

        # Get listeners
        self.get_listeners(doc_ele, agent)
        self.logger.debug("Finished setting up listeners")

        # Associate modules
        self.associate_modules(to_associate, agent)
        self.logger.debug("Finished associating modules")

        # Initialize modules
        self.initialize_modules(agent, to_initialize)
        self.logger.debug("Finished initializing modules")

        # Initialize tasks
        modules_map = {}
        self.get_module_map(agent, modules_map)
        self.initialize_tasks(modules_map, to_run)
        self.logger.debug("Finished initializing tasks")

        # Initialize agent
        agent.init()

        return agent

    def get_module_map(self, module: FrameworkModule, all_modules: Dict[ModuleName, FrameworkModule]) -> None:
        """
        递归构建系统中所有模块的映射

        参数:
            module: 当前要处理的模块
            all_modules: 用于存储模块的映射
        """
        subm = module.get_submodules()
        if subm and len(subm) > 0:
            all_modules.update(subm)
            for m in module.get_submodules().values():
                self.get_module_map(m, all_modules)

    @staticmethod
    def get_global_parameters(element: ET.Element) -> Dict[str, Any]:
        """
        从XML元素获取全局参数

        参数:
            element: 包含全局参数的XML元素

        返回:
            全局参数字典
        """
        # Try to get globalparams children
        nl = XmlUtils.get_children(element, "globalparams")

        # Log the result for debugging
        logging.debug(f"Found {len(nl) if nl else 0} globalparams elements")

        if nl and len(nl) > 0:
            global_params_elem = nl[0]
            # Log the content of the first globalparams element
            logging.debug(f"Global params element: {ET.tostring(global_params_elem, encoding='unicode') if global_params_elem is not None else 'None'}")

            # Get the parameters
            params = XmlUtils.get_typed_params(global_params_elem)
            logging.debug(f"Extracted parameters: {params}")
            return params
        else:
            # If no globalparams element found, try to find param elements directly
            params = XmlUtils.get_typed_params(element)
            if params:
                logging.debug(f"Found parameters directly in element: {params}")
                return params

        logging.warning("No global parameters found in the XML")
        return {}

    def get_task_manager(self, element: ET.Element) -> TaskManager:
        """
        从XML元素获取TaskManager
        参数:
            element: 包含TaskManager配置的XML元素
        返回:
            TaskManager对象
        """
        from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
        # Get TaskManager parameters
        nl = XmlUtils.get_children(element, "taskmanager")
        params = {}
        if nl and len(nl) > 0:
            task_manager_element = nl[0]
            params = XmlUtils.get_typed_params(task_manager_element)
        # Get tick duration
        tick_duration = None
        td = params.get("taskManager.tickDuration")
        if isinstance(td, str):
            try:
                tick_duration = int(td)
            except (ValueError, TypeError):
                self.logger.warning("Could not load tickDuration, using default")
        elif isinstance(td, int):
            tick_duration = td
        else:
            self.logger.warning("Could not load tickDuration, using default")
        # Get max number of threads
        max_threads = None
        mt = params.get("taskManager.maxNumberOfThreads")
        if isinstance(mt, str):
            try:
                max_threads = int(mt)
            except (ValueError, TypeError):
                self.logger.warning("Could not load maxNumberOfThreads, using default")
        elif isinstance(mt, int):
            max_threads = mt
        else:
            self.logger.warning("Could not load maxNumberOfThreads, using default")
        # Get shutdown tick
        shutdown_tick = None
        st = params.get("taskManager.shutdownTick")
        if isinstance(st, str):
            try:
                shutdown_tick = int(st)
            except (ValueError, TypeError):
                self.logger.warning("Could not load shutdownTick, using default")
        elif isinstance(st, int):
            shutdown_tick = st
        else:
            self.logger.warning("Could not load shutdownTick, using default")
        if shutdown_tick is None:
            shutdown_tick = TaskManager.DEFAULT_SHUTDOWN_TICK
        # Get post execution class
        post_execution_class = None
        pec = params.get("taskManager.postExecutionClass")
        if isinstance(pec, str):
            post_execution_class = pec
        # Create TaskManager
        task_manager = TaskManager(tick_duration, max_threads, shutdown_tick, post_execution_class)
        return task_manager

    def load_factory_file(self) -> None:
        """
        加载工厂文件及其属性
        """
        try:
            # Load the XML file
            root = XmlUtils.load_xml_file(self.factory_file)
            if root is None:
                self.logger.error(f"Could not load factory file: {self.factory_file}")
                return

            # Get the properties file path
            properties_file = XmlUtils.get_attribute(root, "properties")
            if properties_file:
                # Load the properties
                props = ConfigUtils.load_properties(properties_file)
                if props:
                    self.properties.update(props)

            # Load logging configuration
            logging_file = XmlUtils.get_attribute(root, "logging")
            if logging_file:
                ConfigUtils.config_loggers(logging_file)

            # Load factories data
            self.loader.load_factories_data(root)
        except Exception as e:
            self.logger.error(f"Error loading factory file: {e}")

    def get_task_spawners(self, element: ET.Element, tm: TaskManager) -> Dict[str, TaskSpawner]:
        """
        读取并创建XML元素中指定的所有TaskSpawners

        参数:
            element: 包含task spawners的XML元素
            tm: TaskManager对象

        返回:
            按名称索引的TaskSpawners字典
        """
        import traceback
        spawners = {}

        try:
            # 获取taskspawners元素
            self.logger.debug("正在查找taskspawners元素")
            element_list = XmlUtils.get_children(element, "taskspawners")

            if not element_list or len(element_list) == 0:
                self.logger.warning("未找到taskspawners元素")
                return spawners

            task_spawners_element = element_list[0]
            self.logger.debug(f"找到taskspawners元素: {task_spawners_element}")

            # 获取taskspawner元素列表
            spawner_list = XmlUtils.get_children(task_spawners_element, "taskspawner")
            if not spawner_list or len(spawner_list) == 0:
                self.logger.warning("未找到taskspawner元素")
                return spawners

            self.logger.debug(f"找到 {len(spawner_list)} 个taskspawner元素")

            # 处理每个taskspawner元素
            for i, task_spawner_element in enumerate(spawner_list):
                try:
                    self.logger.debug(f"处理第 {i+1} 个taskspawner元素")
                    name = task_spawner_element.get("name", "未命名").strip()
                    self.logger.debug(f"正在创建名为 '{name}' 的TaskSpawner")
                    self.get_task_spawner(task_spawner_element, tm, spawners)
                except Exception as e:
                    self.logger.error(f"处理taskspawner元素 '{name if 'name' in locals() else i+1}' 时出错: {e}")
                    self.logger.error(f"错误类型: {type(e).__name__}")
                    self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                    # 继续处理下一个元素
        except Exception as e:
            self.logger.error(f"获取任务生成器时出错: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")

        self.logger.debug(f"成功创建 {len(spawners)} 个TaskSpawner")
        return spawners

    def get_task_spawner(self, element: ET.Element, tm: TaskManager, spawners: Dict[str, TaskSpawner]) -> None:
        """
        从XML元素创建TaskSpawner并添加到spawners字典中

        参数:
            element: 包含task spawner配置的XML元素
            tm: TaskManager对象
            spawners: 要添加到的TaskSpawners字典
        """
        ts = None
        class_name = XmlUtils.get_text_value(element, "class")
        name = element.get("name", "").strip()

        # print(f"Creating TaskSpawner of class {class_name} with name {name}")

        try:
            # Check if the class name starts with 'edu.memphis.ccrg.lida'
            if class_name.startswith('edu.memphis.ccrg.lida'):
                # Convert Java package path to Python package path
                class_name = 'linars.' + class_name

                # Fix case issues in package names
                if 'Framework.tasks.' in class_name:
                    class_name = class_name.replace('Framework.tasks.', 'Framework.Tasks.')

                self.logger.debug(f"Converted Java package path to Python package path: {class_name}")

            # Import the class dynamically
            module_name, class_name_only = class_name.rsplit(".", 1)
            self.logger.debug(f"Importing module {module_name} to get class {class_name_only}")
            # print(f"Module path: {module_name}, Class name only: {class_name_only}")
            try:
                # Try to import the class directly
                if class_name == 'linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawnerImpl':
                    from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawnerImpl import TaskSpawnerImpl
                    ts = TaskSpawnerImpl()
                else:
                    # Use the standard importlib approach
                    module = importlib.import_module(module_name + "." + class_name_only)
                    ts_class = getattr(module, class_name_only)
                    ts = ts_class()
                self.logger.debug(f"Successfully created TaskSpawner instance of class {class_name}")
            except (ImportError, AttributeError) as e:
                self.logger.error(f"0Module class name: {class_name} is not found0. Check TaskSpawner class name. Error: {e}")
                return
        except Exception as e:
            self.logger.error(f"Exception '{str(e)}' occurred during creation of object of class {class_name}")
            return

        if ts is None:
            self.logger.error(f"Failed to create TaskSpawner of class {class_name}")
            return

        # Set task manager and initialize
        self.logger.debug(f"Setting TaskManager for TaskSpawner {ts}")
        ts.set_task_manager(tm)

        # Get parameters and initialize
        params = XmlUtils.get_typed_params(element)
        self.logger.debug(f"Initializing TaskSpawner with parameters: {params}")
        try:
            ts.init(params)
            self.logger.debug(f"Successfully initialized TaskSpawner {ts}")

            # Add to spawners dictionary
            if name:
                self.logger.debug(f"Adding TaskSpawner {ts} with name {name} to spawners dictionary")
                spawners[name] = ts
                self.logger.debug(f"TaskSpawner: {name} added.")

            # Set the TaskSpawner instance
            from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawner import TaskSpawner
            TaskSpawner.set_instance(ts)
        except Exception as e:
            self.logger.error(f"Error initializing task spawner: {ts}. Error: {e}")

    def create_agent(self) -> Optional[Agent]:
        """
        创建新代理

        返回:
            新创建的代理，如果创建失败则返回None
        """
        try:
            # Load the factory file first
            self.logger.debug("Loading factory file...")
            self.load_factory_file()
            self.logger.debug("Factory file loaded successfully.")

            # Create a new agent
            try:
                # Create the task manager first
                self.logger.debug("Creating TaskManager...")
                from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
                task_manager = TaskManager()
                self.logger.debug(f"TaskManager created: {task_manager}")

                # Create the agent with the task manager
                self.logger.debug("Creating AgentImpl...")
                agent = AgentImpl(task_manager)
                self.logger.debug(f"AgentImpl created: {agent}")

                # Set the agent in the task manager
                self.logger.debug("Setting agent in TaskManager...")
                task_manager.set_agent(agent)
                self.logger.debug("Agent set in TaskManager.")

                # Initialize the agent
                self.logger.debug("Initializing agent...")
                self.initializer.init_object(agent, self.properties)
                self.logger.debug("Agent initialized.")

                return agent
            except Exception as e:
                self.logger.error(f"Error creating AgentImpl: {e}")
                import traceback
                self.logger.error(f"Traceback: {traceback.format_exc()}")
                return None
        except Exception as e:
            self.logger.error(f"Error creating agent: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def get_modules(self, element: ET.Element, to_assoc: List, to_init: List, spawners: Dict[str, TaskSpawner], to_run: List[TaskData]) -> List[FrameworkModule]:
        """
        读取并创建指定元素中的所有FrameworkModules
        参数:
            element: 包含modules的XML元素
            to_assoc: 待关联列表
            to_init: 待初始化列表
            spawners: 按名称索引的TaskSpawners字典
            to_run: 待运行任务列表
        返回:
            创建的FrameworkModules列表
        """
        import traceback
        modules = []

        try:
            # 获取submodules元素
            self.logger.debug("正在查找submodules元素")
            nl = XmlUtils.get_children(element, "submodules")

            if not nl or len(nl) == 0:
                # self.logger.warning("未找到submodules元素", nl, "----", element)
                return modules

            submodule_element = nl[0]
            self.logger.debug(f"找到submodules元素: {submodule_element}")

            # 获取module元素列表
            module_list = XmlUtils.get_children(submodule_element, "module")
            if not module_list or len(module_list) == 0:
                self.logger.warning("未找到module元素")
                return modules

            self.logger.debug(f"找到 {len(module_list)} 个module元素")

            # 处理每个module元素
            for i, module_element in enumerate(module_list):
                try:
                    name = module_element.get("name", "未命名").strip()
                    self.logger.debug(f"正在创建第 {i+1} 个模块: '{name}'")

                    module = self.get_module(module_element, to_assoc, to_init, spawners, to_run)
                    if module:
                        self.logger.debug(f"成功创建模块: '{name}'")
                        modules.append(module)
                    else:
                        self.logger.warning(f"无法创建模块: '{name}'")
                except Exception as e:
                    self.logger.error(f"创建模块 '{name if 'name' in locals() else i+1}' 时出错: {e}")
                    self.logger.error(f"错误类型: {type(e).__name__}")
                    self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                    # 继续处理下一个模块
        except Exception as e:
            self.logger.error(f"获取模块时出错: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")

        self.logger.debug(f"成功创建 {len(modules)} 个模块")
        return modules

    def get_module(self, module_element: ET.Element, to_assoc: List, to_init: List, spawners: Dict[str, TaskSpawner], to_run: List[TaskData]) -> Optional[FrameworkModule]:
        """
        从指定元素读取并创建FrameworkModule
        参数:
            module_element: 包含module配置的XML元素
            to_assoc: 待关联列表
            to_init: 待初始化列表
            spawners: 按名称索引的TaskSpawners字典
            to_run: 待运行任务列表
        返回:
            创建的FrameworkModule，如果创建失败则返回None
        """
        # Get module name and class name
        module = None
        class_name = XmlUtils.get_text_value(module_element, "class")
        name = module_element.get("name", "").strip()

        # 创建或获取模块名称
        try:
            module_name = ModuleName.add_module_name(name)
        except Exception as e:
            self.logger.error(f"Error creating module name for {name}: {e}")
            module_name = ModuleName.UnnamedModule

        # Import the class dynamically
        module_path, class_name_only = class_name.rsplit(".", 1)
        self.logger.debug(f"Importing module {module_path} to get class {class_name_only}")

        # print(f"Module path: {module_path}, Class name only: {class_name_only}")
        try:
            # Use the standard importlib approach
            module_import = importlib.import_module(module_path + "." + class_name_only)
            module_class = getattr(module_import, class_name_only)
            module = module_class()
            self.logger.debug(f"Successfully created module instance of class {class_name}")
        except (ImportError, AttributeError) as e:
            self.logger.error(f"1Module class name: {class_name} is not found1. Check module class name. Error: {e}")
            return None
        # except Exception as e:
        #     self.logger.error(f"Exception '{str(e)}' occurred during creation of object of class {class_name}")
        #     return None
        if module is None:
            return None
        # Set module name
        try:
            if name and name.strip():
                # 先尝试使用模块名称
                module.set_module_name(module_name)
                self.logger.debug(f"Set module name to {module_name} for module {module}")
            else:
                # 如果没有名称，使用类名作为模块名称
                module_name = ModuleName.add_module_name(class_name_only)
                module.set_module_name(module_name)
                self.logger.debug(f"Set module name to {module_name} (from class name) for module {module}")
        except Exception as e:
            self.logger.warning(f"Error setting module name for {module}: {e}")
            module.set_module_name(ModuleName.UnnamedModule)

        # Set up module's TaskSpawner and initial tasks
        taskspawner = XmlUtils.get_text_value(module_element, "taskspawner")
        ts = spawners.get(taskspawner)
        if ts is not None:
            module.set_assisting_task_spawner(ts)
            initial_tasks = self.get_tasks(module_element, ts)
            to_run.extend(initial_tasks)
        else:
            self.logger.warning(f"Illegal TaskSpawner definition for module: {name}")

        # Get and add all submodules
        for lm in self.get_modules(module_element, to_assoc, to_init, spawners, to_run):
            module.add_submodule(lm)

        # Get parameters specified for this module
        params = XmlUtils.get_typed_params(module_element)

        # Initialize module's parameters
        try:
            module.init(params)
        except Exception as e:
            self.logger.warning(f"Module: {name} threw exception {e} during call to init()")

        # Setup the user-specified Initializer that will run later
        class_init = XmlUtils.get_text_value(module_element, "initializerclass")
        if class_init is not None:
            to_init.append([module, class_init, params])

        self.get_associated_modules(module, module_element, to_assoc)

        self.logger.debug(f"Module: {name} added.")
        return module

    def get_associated_modules(self, module, module_element: ET.Element, to_assoc: List) -> None:
        """
        获取指定Initializable的关联模块
        参数:
            ele: dom元素
            ini: Initializable
            to_assoc: 待关联列表
        """
        # Setup the associated modules that will be set later
        assoc_list = XmlUtils.get_children(module_element, "associatedmodule")
        if assoc_list and len(assoc_list) > 0:
            for assoc_element in assoc_list:
                # 直接获取associatedmodule元素的文本内容
                module_name = XmlUtils.get_element_text(assoc_element)
                # 使用模块类型作为类型，如果没有指定则使用默认值
                module_type = assoc_element.get("function", "NOT_SPECIFIED").strip()

                # 如果没有指定类型，尝试使用模块名称作为类型
                if module_type == "NOT_SPECIFIED" and module_name:
                    module_type = module_name

                self.logger.debug(f"Found associated module: {module_name} with type: {module_type}")
                to_assoc.append([module, module_type, module_name])

        # module_name = XmlUtils.get_text_value(module_element, "associatedmodule")
        # module_name0 = module_element.text.strip()
        # classname = XmlUtils.get_children(module_element, "class")
        # to_assoc.append([module, module_name, module_name])

    def get_tasks(self, element: ET.Element, ts: TaskSpawner) -> List[TaskData]:
        """
        读取并创建元素中指定的FrameworkTasks

        参数:
            element: 包含tasks的XML元素
            ts: 将运行任务的TaskSpawner

        返回:
            TaskData对象列表
        """
        tasks = []
        nl = XmlUtils.get_children(element, "initialTasks")
        if nl and len(nl) > 0:
            initial_tasks_element = nl[0]
            default_ticks = self.get_tasks_default_ticks_per_run(initial_tasks_element)
            task_elements = XmlUtils.get_children(initial_tasks_element, "task")
            if task_elements and len(task_elements) > 0:
                for task_element in task_elements:
                    task_data = self.get_task(task_element, default_ticks)
                    if task_data is not None:
                        task_data.task_spawner = ts
                        tasks.append(task_data)
        return tasks

    def get_task(self, module_element: ET.Element, default_ticks: int) -> Optional[TaskData]:
        """
        读取并创建元素中指定的FrameworkTask

        参数:
            module_element: 包含task配置的XML元素
            default_ticks: 这些初始任务的默认每次运行ticks数

        返回:
            TaskData对象，如果创建失败则返回None
        """
        task_type = XmlUtils.get_text_value(module_element, "tasktype")
        name = module_element.get("name", "").strip()

        ticks = default_ticks
        if XmlUtils.contains_tag(module_element, "ticksperrun"):
            ticks = XmlUtils.get_integer_value(module_element, "ticksperrun")

        if ticks < 0:
            ticks = default_ticks
            self.logger.warning(f"Task: {name} has an invalid ticksperrun value. Default used.")

        params = XmlUtils.get_typed_params(module_element)
        task_data = TaskData(name, task_type, ticks, params)

        self.logger.debug(f"Task: {name} added.")
        return task_data

    @staticmethod
    def get_tasks_default_ticks_per_run(element: ET.Element) -> int:
        """
        读取初始任务的默认每次运行ticks数

        参数:
            element: 包含默认ticks per run的XML元素

        返回:
            默认ticks per run，如果无效则返回0
        """
        tpr = 0
        if XmlUtils.contains_tag(element, "defaultticksperrun"):
            tpr_value = XmlUtils.get_integer_value(element, "defaultticksperrun")
            if tpr_value is not None and tpr_value >= 0:
                tpr = tpr_value
        return tpr

    def get_listeners(self, element: ET.Element, top_module: FrameworkModule) -> None:
        """
        读取并创建元素中指定的所有监听器

        参数:
            element: 包含listeners的XML元素
            top_module: FrameworkModules层次结构的根
        """
        import traceback

        try:
            # 获取listeners元素
            self.logger.debug("正在查找listeners元素")
            children_list = XmlUtils.get_children(element, "listeners")

            if not children_list or len(children_list) == 0:
                self.logger.warning("未找到listeners元素")
                return

            listeners_element = children_list[0]
            self.logger.debug(f"找到listeners元素: {listeners_element}")

            # 获取listener元素列表
            listener_elements = XmlUtils.get_children(listeners_element, "listener")
            if not listener_elements or len(listener_elements) == 0:
                self.logger.warning("未找到listener元素")
                return

            self.logger.debug(f"找到 {len(listener_elements)} 个listener元素")

            # 处理每个listener元素
            for i, listener_element in enumerate(listener_elements):
                try:
                    self.logger.debug(f"处理第 {i+1} 个监听器元素")
                    self.get_listener(listener_element, top_module)
                except Exception as e:
                    self.logger.error(f"处理第 {i+1} 个监听器元素时出错: {e}")
                    self.logger.error(f"错误类型: {type(e).__name__}")
                    self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                    # 继续处理下一个元素
        except Exception as e:
            self.logger.error(f"获取监听器时出错: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")

    def get_listener(self, element: ET.Element, top_module: FrameworkModule) -> None:
        """
        读取并创建元素中指定的监听器

        参数:
            element: 包含listener配置的XML元素
            top_module: FrameworkModules层次结构的根
        """
        import traceback

        try:
            # 获取源模块名称
            source_name = XmlUtils.get_text_value(element, "modulename")
            if not source_name:
                self.logger.warning("监听器元素缺少modulename")
                return

            # 获取监听器模块名称
            listener_name = XmlUtils.get_text_value(element, "listenername")
            if not listener_name:
                self.logger.warning("监听器元素缺少listenername")
                return

            self.logger.debug(f"处理监听器: 源模块={source_name}, 监听器模块={listener_name}")

            # 获取模块名称对象
            try:
                source_name0 = ModuleName.get_module_name(source_name)
                self.logger.debug(f"源模块名称对象: {source_name0}")
            except Exception as e:
                self.logger.error(f"创建源模块名称对象失败: {e}")
                self.logger.error(f"错误类型: {type(e).__name__}")
                self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                return

            try:
                listener_name0 = ModuleName.get_module_name(listener_name)
                self.logger.debug(f"监听器模块名称对象: {listener_name0}")
            except Exception as e:
                self.logger.error(f"创建监听器模块名称对象失败: {e}")
                self.logger.error(f"错误类型: {type(e).__name__}")
                self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                return

            # 获取模块实例
            try:
                source = top_module.get_submodule(source_name0)
                if not source:
                    self.logger.warning(f"源模块: {source_name} 未找到")
                    return
                self.logger.debug(f"找到源模块: {source}")
            except Exception as e:
                self.logger.error(f"获取源模块失败: {e}")
                self.logger.error(f"错误类型: {type(e).__name__}")
                self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                return

            try:
                listener = top_module.get_submodule(listener_name0)
                if not listener:
                    self.logger.warning(f"监听器模块: {listener_name} 未找到")
                    return
                self.logger.debug(f"找到监听器模块: {listener}")
            except Exception as e:
                self.logger.error(f"获取监听器模块失败: {e}")
                self.logger.error(f"错误类型: {type(e).__name__}")
                self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                return

            # 添加监听器
            try:
                source.add_listener(listener)
                self.logger.debug(f"监听器: {listener_name} 添加到源: {source_name} 成功")
            except Exception as e:
                self.logger.error(f"添加监听器失败: {e}")
                self.logger.error(f"错误类型: {type(e).__name__}")
                self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
        except Exception as e:
            self.logger.error(f"处理监听器元素时出错: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")

    def associate_modules(self, to_assoc: List, top_module: FrameworkModule) -> None:
        """
        按照to_assoc列表中的指定关联模块

        参数:
            to_assoc: 待关联列表
            top_module: FrameworkModules层次结构的根
        """
        import traceback

        # 首先输出所有可用的模块
        try:
            self.logger.debug("获取可用模块列表")
            module_names = []
            for m in top_module.get_submodules():
                try:
                    if hasattr(m, 'get_module_name'):
                        if callable(m.get_module_name):
                            # 检查方法是否需要参数
                            import inspect
                            sig = inspect.signature(m.get_module_name)
                            if len(sig.parameters) == 0:
                                module_names.append(m.get_module_name())
                            else:
                                module_names.append(str(m))
                        else:
                            module_names.append(str(m))
                    else:
                        module_names.append(str(m))
                except Exception as e:
                    module_names.append(f"<Error: {e}>")
            self.logger.debug(f"可用模块: {module_names}")
        except Exception as e:
            self.logger.warning(f"获取模块名称时出错: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")

        for vals in to_assoc:
            module = vals[0]
            module_type = vals[1]
            module_name = vals[2]

            # 尝试使用模块名称获取模块
            associated_module = top_module.get_submodule(module_name)

            # 如果没有找到模块，尝试使用模块类型获取模块
            if not associated_module and module_type:
                # 尝试使用模块类型作为模块名称
                module_name_obj = ModuleName.get_module_name(module_type)
                if module_name_obj:
                    associated_module = top_module.get_submodule(module_name_obj)
                    if associated_module:
                        self.logger.debug(f"Found module using module_type: {module_type}")

                # 特殊情况：如果是PAM模块，尝试直接获取PAMemory模块
                if not associated_module and (module_type == "PAM" or module_type == "PAMemory"):
                    # 尝试获取PAMemory模块
                    pam_module_name = ModuleName.get_module_name("PAMemory")
                    if pam_module_name:
                        associated_module = top_module.get_submodule(pam_module_name)
                        if associated_module:
                            self.logger.debug(f"Found PAMemory module directly")

            # 如果仍然没有找到模块，尝试使用其他方法
            if not associated_module:
                # 尝试使用常见的模块名称映射
                module_name_mapping = {
                    "PAMemory": ["PAMemory", "PerceptualAssociativeMemory", "PAM"],
                    "SensoryMemory": ["SensoryMemory"],
                    "Environment": ["Environment", "ALifeEnvironment"],
                    "Workspace": ["Workspace"],
                    "CurrentSM": ["CurrentSM", "CurrentSituationalModel"],
                    "GlobalWorkspace": ["GlobalWorkspace"],
                    "ProceduralMemory": ["ProceduralMemory"],
                    "SensoryMotorMemory": ["SensoryMotorMemory"],
                    "StructureBuildingCodeletModule": ["StructureBuildingCodeletModule"],
                    "LanGen": ["LanGen", "LanguageGeneration"]
                }

                # 检查模块类型是否在映射中
                if module_type in module_name_mapping:
                    for alt_name in module_name_mapping[module_type]:
                        # 尝试使用替代名称获取模块
                        alt_module = top_module.get_submodule(alt_name)
                        if alt_module:
                            associated_module = alt_module
                            self.logger.debug(f"Found module {alt_name} using mapping for {module_type}")
                            break

                # 如果仍然没有找到模块，尝试遍历所有子模块
                if not associated_module:
                    for submodule in top_module.get_submodules().values():
                        try:
                            # 安全地获取模块名称
                            submodule_name = None
                            if hasattr(submodule, 'get_module_name'):
                                if callable(submodule.get_module_name):
                                    # 检查方法是否需要参数
                                    import inspect
                                    sig = inspect.signature(submodule.get_module_name)
                                    if len(sig.parameters) == 0:
                                        submodule_name = submodule.get_module_name()

                            # 如果无法获取模块名称，尝试使用字符串表示
                            if submodule_name is None:
                                submodule_str = str(submodule)
                                if module_name is not None and module_name in submodule_str:
                                    submodule_name = module_name

                            # 检查模块名称是否匹配
                            if submodule_name is not None and module_name is not None and str(submodule_name) == module_name:
                                associated_module = submodule
                                self.logger.debug(f"Found module {module_name} by exact name match")
                                break
                            elif module_type == "PAMemory" and (str(submodule_name) == "PAMemory" or str(submodule_name) == "PerceptualAssociativeMemory" or
                                 (isinstance(submodule_str, str) and ("PAMemory" in submodule_str or "PerceptualAssociativeMemory" in submodule_str))):
                                associated_module = submodule
                                self.logger.debug(f"Found PAMemory module by special case")
                                break
                        except Exception as e:
                            self.logger.warning(f"Error checking module name: {e}")

            if associated_module:
                module.set_associated_module(associated_module, module_type)
                self.logger.debug(f"Associated module: {module_name} with module: {module.get_module_name()} using type: {module_type}")
            else:
                # 如果找不到模块，创建一个空模块
                try:
                    # 尝试导入模块类
                    # 根据模块类型构建可能的模块类名称
                    possible_module_paths = [
                        f"linars.edu.memphis.ccrg.lida.{module_type}.{module_type}Impl",
                        f"linars.edu.memphis.ccrg.lida.{module_type}Impl",
                        f"linars.edu.memphis.ccrg.lida.{module_type}.{module_type}Impl0",
                        f"linars.edu.memphis.ccrg.lida.{module_type}Impl0"
                    ]

                    module_class = None
                    for module_class_name in possible_module_paths:
                        try:
                            module_class = self.get_class(module_class_name)
                            if module_class:
                                self.logger.debug(f"Found module class: {module_class_name}")
                                break
                        except Exception as e:
                            self.logger.debug(f"0Could not import class {module_class_name}: {e}")

                    if module_class:
                        # 创建模块实例
                        new_module = module_class()
                        # 设置模块名称
                        new_module.set_module_name(ModuleName.get_module_name(module_type))
                        # 添加到顶层模块
                        top_module.add_submodule(new_module)
                        # 关联模块
                        module.set_associated_module(new_module, module_type)
                        self.logger.debug(f"Created and associated new module: {module_type} with module: {module.get_module_name()}")
                        continue
                except Exception as e:
                    self.logger.warning(f"Error creating module {module_type}: {e}")

                self.logger.warning(f"Could not find module: {module_name} to associate with module: {module.get_module_name()}")

    def initialize_modules(self, top_module: Agent, to_init: List) -> None:
        """
        对所有有初始化器的模块，运行初始化器并传入特定模块

        参数:
            top_module: FrameworkModules层次结构的根
            to_init: 待初始化列表
        """
        import traceback

        self.logger.debug(f"开始初始化 {len(to_init)} 个模块")

        for i, vals in enumerate(to_init):
            try:
                self.logger.debug(f"处理第 {i+1} 个初始化项")

                # 检查vals是否为元组或列表
                if isinstance(vals, (tuple, list)):
                    if len(vals) < 3:
                        self.logger.warning(f"初始化项格式不正确: {vals}")
                        continue

                    module_to_initialize = vals[0]
                    initializer_class_name = vals[1]
                    params = vals[2]

                    self.logger.debug(f"初始化项信息: 模块={module_to_initialize}, 初始化器={initializer_class_name}")
                else:
                    # 如果vals不是元组或列表，它可能是模块本身
                    self.logger.debug(f"初始化项不是列表或元组: {vals}")
                    module_to_initialize = vals
                    initializer_class_name = None
                    params = None
                    continue  # 跳过这个迭代，因为我们没有初始化器

                # 创建初始化器
                initializer = None
                try:
                    self.logger.debug(f"尝试创建初始化器: {initializer_class_name}")

                    # 使用延迟导入避免循环导入
                    # 先检查类是否已经存在
                    initializer_class = None
                    try:
                        # 尝试直接获取类
                        module_path, class_name = initializer_class_name.rsplit(".", 1)
                        self.logger.debug(f"尝试导入模块: {module_path}, 类: {class_name}")
                        module_import = importlib.import_module(module_path)
                        initializer_class = getattr(module_import, class_name)
                        self.logger.debug(f"成功获取类: {initializer_class}")
                    except (ImportError, AttributeError) as e:
                        self.logger.warning(f"直接导入失败: {e}, 尝试使用exec动态导入")
                        # 如果失败，尝试使用exec动态导入
                        try:
                            exec(f"from {module_path} import {class_name}")
                            initializer_class = eval(class_name)
                            self.logger.debug(f"使用exec成功获取类: {initializer_class}")
                        except Exception as e2:
                            self.logger.error(f"exec导入失败: {e2}")
                            self.logger.error(f"错误类型: {type(e2).__name__}")
                            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                            raise

                    # 创建初始化器实例
                    if initializer_class:
                        initializer = initializer_class()
                        self.logger.debug(f"成功创建初始化器实例: {initializer}")
                except (ImportError, AttributeError, ValueError) as e:
                    self.logger.error(f"初始化器类: {initializer_class_name} 未找到: {e}")
                    self.logger.error(f"错误类型: {type(e).__name__}")
                    self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                except Exception as e:
                    self.logger.error(f"创建初始化器类 {initializer_class_name} 时出错: {e}")
                    self.logger.error(f"错误类型: {type(e).__name__}")
                    self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")

                # 使用初始化器初始化模块
                if initializer:
                    try:
                        self.logger.debug(f"开始初始化模块: {module_to_initialize.get_module_name() if hasattr(module_to_initialize, 'get_module_name') else module_to_initialize}")
                        initializer.initModule(module_to_initialize, top_module, params)
                        self.logger.debug(f"成功初始化模块: {module_to_initialize.get_module_name() if hasattr(module_to_initialize, 'get_module_name') else module_to_initialize}")
                    except Exception as e:
                        self.logger.error(f"使用初始化器 {initializer_class_name} 初始化模块时出错: {e}")
                        self.logger.error(f"错误类型: {type(e).__name__}")
                        self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
            except Exception as e:
                self.logger.error(f"处理初始化项时出错: {e}")
                self.logger.error(f"错误类型: {type(e).__name__}")
                self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                # 继续处理下一项

        self.logger.debug("模块初始化完成")

    def initialize_tasks(self, module_map: Dict[ModuleName, FrameworkModule], to_run: List[TaskData]) -> None:
        """
        创建指定的任务并将其添加到关联的TaskSpawner

        参数:
            module_map: 按ModuleName索引的所有FrameworkModules字典
            to_run: 要创建和运行的TaskData对象列表
        """
        import traceback

        self.logger.debug(f"开始初始化 {len(to_run)} 个任务")

        try:
            # 获取元素工厂实例
            factory = ElementFactory.get_instance()
            self.logger.debug(f"获取到元素工厂实例: {factory}")

            # 输出模块映射信息，便于调试
            if self.logger.isEnabledFor(logging.DEBUG):
                self.logger.debug(f"模块映射键: {[str(k) for k in module_map.keys()]}")

            # 处理每个任务
            for i, td in enumerate(to_run):
                try:
                    self.logger.debug(f"处理第 {i+1} 个任务: {td.name}, 类型: {td.tasktype}")

                    # 创建任务
                    try:
                        task = factory.get_framework_task(td.tasktype, td.params, module_map)
                        if task is not None:
                            self.logger.debug(f"成功创建任务: {task}")
                        else:
                            self.logger.warning(f"无法创建任务: {td.name}, 类型: {td.tasktype}")
                            continue
                    except Exception as e:
                        self.logger.error(f"创建任务 {td.name} 时出错: {e}")
                        self.logger.error(f"错误类型: {type(e).__name__}")
                        self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                        continue

                    # 设置任务的执行频率
                    try:
                        if td.ticks_per_run > 0:
                            task.set_ticks_per_run(td.ticks_per_run)
                            self.logger.debug(f"设置任务 {td.name} 的执行频率为 {td.ticks_per_run}")
                    except Exception as e:
                        self.logger.error(f"设置任务 {td.name} 的执行频率时出错: {e}")
                        self.logger.error(f"错误类型: {type(e).__name__}")
                        self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")

                    # 检查任务的关联模块
                    try:
                        if hasattr(task, 'pam') and task.pam is None and 'PAMemory' in module_map:
                            pam_module = module_map.get('PAMemory')
                            if pam_module:
                                task.set_associated_module(pam_module, 'PAM')
                                self.logger.debug(f"手动设置任务 {td.name} 的PAM模块")
                    except Exception as e:
                        self.logger.error(f"设置任务 {td.name} 的PAM模块时出错: {e}")
                        self.logger.error(f"错误类型: {type(e).__name__}")
                        self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")

                    # 添加任务到任务生成器
                    try:
                        if td.task_spawner:
                            td.task_spawner.add_task(task)
                            self.logger.debug(f"任务 {td.name} 添加到任务生成器成功")
                        else:
                            self.logger.warning(f"任务 {td.name} 的任务生成器为空")
                    except Exception as e:
                        self.logger.error(f"添加任务 {td.name} 到任务生成器时出错: {e}")
                        self.logger.error(f"错误类型: {type(e).__name__}")
                        self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                except Exception as e:
                    self.logger.error(f"处理任务 {td.name if hasattr(td, 'name') else i+1} 时出错: {e}")
                    self.logger.error(f"错误类型: {type(e).__name__}")
                    self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
                    # 继续处理下一个任务
        except Exception as e:
            self.logger.error(f"初始化任务时出错: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")

        self.logger.debug("任务初始化完成")

    def configure_agent(self, agent: Agent) -> None:
        """
        配置代理
        参数:
            agent: 要配置的代理
        """
        import traceback

        # 步骤1: 加载工厂文件
        try:
            self.logger.debug("步骤1: 开始加载工厂文件")
            self.load_factory_file()
            self.logger.debug("步骤1: 工厂文件加载完成")
        except Exception as e:
            self.logger.error(f"步骤1错误 - 加载工厂文件失败: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
            return

        # 步骤2: 解析XML文档
        try:
            self.logger.debug("步骤2: 开始解析XML文档")
            root = XmlUtils.load_xml_file(self.factory_file)
            if root is None:
                self.logger.error(f"步骤2错误 - 无法加载工厂文件: {self.factory_file}")
                return
            self.logger.debug("步骤2: XML文档解析完成")
        except Exception as e:
            self.logger.error(f"步骤2错误 - 解析XML文档失败: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
            return

        # 初始化数据结构
        to_initialize = []
        to_associate = []
        to_run = []

        # 步骤3: 获取任务生成器
        try:
            self.logger.debug("步骤3: 开始获取任务生成器")
            task_spawners = self.get_task_spawners(root, agent.get_task_manager())
            self.logger.debug(f"步骤3: 获取到 {len(task_spawners)} 个任务生成器")
        except Exception as e:
            self.logger.error(f"步骤3错误 - 获取任务生成器失败: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
            return

        # 步骤4: 获取模块并添加到代理
        try:
            self.logger.debug("步骤4: 开始获取模块")
            modules = self.get_modules(root, to_associate, to_initialize, task_spawners, to_run)
            self.logger.debug(f"步骤4: 获取到 {len(modules)} 个模块")

            for framework_module in modules:
                try:
                    self.logger.debug(f"添加模块: {framework_module}")
                    agent.add_submodule(framework_module)
                except Exception as e:
                    self.logger.error(f"添加模块 {framework_module} 失败: {e}")
                    self.logger.error(f"错误类型: {type(e).__name__}")
                    self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
            self.logger.debug("步骤4: 模块添加完成")
        except Exception as e:
            self.logger.error(f"步骤4错误 - 获取或添加模块失败: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
            return

        # 步骤5: 获取监听器
        try:
            self.logger.debug("步骤5: 开始获取监听器")
            self.get_listeners(root, agent)
            self.logger.debug("步骤5: 监听器设置完成")
        except Exception as e:
            self.logger.error(f"步骤5错误 - 获取监听器失败: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
            # 继续执行，不返回

        # 步骤6: 关联模块
        try:
            self.logger.debug("步骤6: 开始关联模块")
            self.associate_modules(to_associate, agent)
            self.logger.debug("步骤6: 模块关联完成")
        except Exception as e:
            self.logger.error(f"步骤6错误 - 关联模块失败: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
            # 继续执行，不返回

        # 步骤7: 初始化模块
        try:
            self.logger.debug("步骤7: 开始初始化模块")
            self.initialize_modules(agent, to_initialize)
            self.logger.debug("步骤7: 模块初始化完成")
        except Exception as e:
            self.logger.error(f"步骤7错误 - 初始化模块失败: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
            # 继续执行，不返回

        # 步骤8: 初始化任务
        try:
            self.logger.debug("步骤8: 开始初始化任务")
            modules_map = {}
            self.get_module_map(agent, modules_map)
            self.initialize_tasks(modules_map, to_run)
            self.logger.debug("步骤8: 任务初始化完成")
        except Exception as e:
            self.logger.error(f"步骤8错误 - 初始化任务失败: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")
            # 继续执行，不返回

        # 步骤9: 初始化代理
        try:
            self.logger.debug("步骤9: 开始初始化代理")
            agent.init()
            self.logger.debug("步骤9: 代理初始化完成")
        except Exception as e:
            self.logger.error(f"步骤9错误 - 初始化代理失败: {e}")
            self.logger.error(f"错误类型: {type(e).__name__}")
            self.logger.error(f"堆栈跟踪: {traceback.format_exc()}")

    def add_tasks(self, agent: Agent) -> None:
        """
        使用加载器的任务定义向代理添加任务

        参数:
            agent: 要添加任务的代理
        """
        # Get task definitions from the loader
        task_defs = self.loader.get_task_defs()

        # Add each task to the agent
        for task_def in task_defs:
            try:
                # Create the task
                task_class = task_def.get_task_class()
                if task_class:
                    task = self.initializer.get_object(task_class, self.properties)
                    if task:
                        # Add the task to the agent
                        agent.add_task(task)
            except Exception as e:
                self.logger.error(f"Error adding task: {e}")

    def add_submodules(self, agent: Agent) -> None:
        """
        使用加载器的子模块定义向代理添加子模块

        参数:
            agent: 要添加子模块的代理
        """
        # Get submodule definitions from the loader
        submodule_defs = self.loader.get_submodule_defs()

        # Add each submodule to the agent
        for submodule_def in submodule_defs:
            try:
                # Create the submodule
                submodule_class = submodule_def.get_module_class()
                if submodule_class:
                    submodule = self.initializer.get_object(submodule_class, self.properties)
                    if submodule:
                        # Add the submodule to the agent
                        agent.add_submodule(submodule)
            except Exception as e:
                self.logger.error(f"Error adding submodule: {e}")

    def add_listeners(self, agent: Agent) -> None:
        """
        使用加载器的监听器定义向代理添加监听器

        参数:
            agent: 要添加监听器的代理
        """
        # Get listener definitions from the loader
        listener_defs = self.loader.get_listener_defs()

        # Add each listener to the agent
        for listener_def in listener_defs:
            try:
                # Get the source and listener modules
                source_module = agent.get_submodule(listener_def.get_source_name())
                listener_module = agent.get_submodule(listener_def.get_listener_name())

                if source_module and listener_module:
                    # Add the listener to the source module
                    source_module.add_listener(listener_module)
            except Exception as e:
                self.logger.error(f"Error adding listener: {e}")

    def get_class(self, class_name: str) -> Any:
        """
        动态导入类

        参数:
            class_name: 类名称

        返回:
            类对象，如果导入失败则返回None
        """
        try:
            # 分割类名称
            parts = class_name.split('.')
            # 获取模块名称
            module_name = '.'.join(parts[:-1])
            # 获取类名称
            class_name = parts[-1]
            # 导入模块
            module = importlib.import_module(module_name)
            # 获取类
            return getattr(module, class_name)
        except (ImportError, AttributeError, ValueError) as e:
            self.logger.warning(f"1Could not import class {class_name}: {e}")
            return None

    def get_default_properties(self) -> Dict[str, Any]:
        """
        获取代理的默认属性

        返回:
            默认属性字典
        """
        return self.properties.copy()
