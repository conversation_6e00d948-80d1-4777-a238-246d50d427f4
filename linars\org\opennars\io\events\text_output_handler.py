"""
文本输出处理器
将任务流作为经验读写
"""
from typing import List, Any, Optional, Type, Callable, TextIO
import traceback
import io

from linars.org.opennars.io.events.output_handler import OutputHandler
from linars.org.opennars.io.events.events import Answer
from linars.org.opennars.entity.task import Task
from linars.org.opennars.entity.sentence import Sentence

class TextOutputHandler(OutputHandler):
    """
    文本输出处理器
    继承自输出处理器，实现任务流作为文本经验的读写功能
    """

    class LineOutput:
        """行输出接口"""

        def println(self, s: str):
            """
            打印一行文本

            参数:
                s: 要打印的文本行
            """
            pass

    def __init__(self, n, out_exp2=None, out_exp=None, min_priority=0.0):
        """
        构造函数

        参数:
            n: NARS系统实例
            out_exp2: 行输出接口
            out_exp: 打印写入器
            min_priority: 最低优先级阈值
        """
        super().__init__(n.memory.event, True)
        self.nar = n
        self.prefix = ""
        self.out_exp2 = out_exp2
        self.out_exp = out_exp
        self.show_errors = True
        self.show_stack_trace = False
        self.show_stamp = True
        self.show_input = True
        self.min_priority = min_priority
        self.result = ""

    def open_save_file(self, path: str):
        """
        打开输出经验文件

        参数:
            path: 文件路径

        异常:
            ValueError: 当文件无法打开时抛出
        """
        try:
            self.out_exp = open(path, 'w')
        except Exception as ex:
            raise ValueError("Could not open save file.") from ex

    def close_save_file(self):
        """关闭输出经验文件"""
        if self.out_exp:
            self.out_exp.close()
        self.set_active(False)

    def event(self, channel, *oo):
        """
        处理输出数据块

        参数:
            channel: 输出通道
            *oo: 输出数据
        """
        if not self.show_errors and channel == self.ERR:
            return

        if not self.show_input and channel == self.IN:
            return

        if self.out_exp or self.out_exp2:
            o = oo[0]
            s = self.process(channel, o)
            if s:
                if self.out_exp:
                    self.out_exp.write(self.prefix + s + "\n")
                    self.out_exp.flush()
                if self.out_exp2:
                    self.out_exp2.println(self.prefix + s)

    def process(self, c, o) -> str:
        """
        处理输出事件

        参数:
            c: 输出通道
            o: 输出数据

        返回:
            str: 处理后的输出字符串
        """
        return self.get_output_string(c, o, True, self.show_stamp, self.nar, self.min_priority)

    def set_errors(self, errors: bool) -> 'TextOutputHandler':
        """
        设置是否显示错误信息

        参数:
            errors: 是否显示错误信息

        返回:
            TextOutputHandler: 当前实例(支持链式调用)
        """
        self.show_errors = errors
        return self

    def set_show_input(self, show_input: bool) -> 'TextOutputHandler':
        """
        设置是否显示输入内容

        参数:
            show_input: 是否显示输入内容

        返回:
            TextOutputHandler: 当前实例(支持链式调用)
        """
        self.show_input = show_input
        return self

    def set_error_stack_trace(self, b: bool) -> 'TextOutputHandler':
        """
        设置是否显示错误堆栈跟踪

        参数:
            b: 是否显示错误堆栈跟踪

        返回:
            TextOutputHandler: 当前实例(支持链式调用)
        """
        self.show_stack_trace = b
        return self

    def set_line_prefix(self, prefix: str) -> 'TextOutputHandler':
        """
        设置行前缀

        参数:
            prefix: 行前缀字符串

        返回:
            TextOutputHandler: 当前实例(支持链式调用)
        """
        self.prefix = prefix
        return self

    @staticmethod
    def get_output_string(channel, signal, show_channel: bool, show_stamp: bool, nar, min_priority: float = 0.0) -> str:
        """
        生成人类可读的输出字符串

        参数:
            channel: 输出通道
            signal: 输出信号
            show_channel: 是否显示通道名称
            show_stamp: 是否显示时间戳
            nar: NARS系统实例
            min_priority: 最低优先级阈值

        返回:
            str: 格式化后的输出字符串
        """
        buffer = ""

        if show_channel:
            buffer += f"{channel.__name__}: "

        if channel == OutputHandler.ERR:
            if isinstance(signal, Exception):
                e = signal
                buffer += str(e)

                if True:  # show_stack_trace
                    buffer += f" {traceback.format_exc()}"
            else:
                buffer += str(signal)

        elif (channel == OutputHandler.OUT or
              channel == OutputHandler.IN or
              channel == OutputHandler.ECHO or
              channel == OutputHandler.EXE or
              channel == Answer or
              channel == OutputHandler.ANTICIPATE or
              channel == OutputHandler.DISAPPOINT or
              channel == OutputHandler.CONFIRM or
              channel == OutputHandler.DEBUG):

            if channel == OutputHandler.CONFIRM:
                buffer += str(signal)

            if isinstance(signal, Task):
                t = signal
                if t.get_priority() < min_priority:
                    return None

                if (channel == OutputHandler.ANTICIPATE or
                    channel == OutputHandler.DISAPPOINT):
                    buffer += t.sentence.to_string(nar, show_stamp)

                elif channel == Answer:
                    task = t
                    answer = task.get_best_solution()
                    if answer:
                        buffer += answer.to_string(nar, show_stamp)
                    else:
                        buffer += t.sentence.to_string(nar, show_stamp)

                else:
                    buffer += t.sentence.to_string(nar, show_stamp)

            else:
                buffer += str(signal)

        else:
            buffer += str(signal)

        return buffer
