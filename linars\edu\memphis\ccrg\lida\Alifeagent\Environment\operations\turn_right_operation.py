"""
右转操作实现

本模块提供ALife环境中右转操作的实现
"""
from typing import List, Optional, Any

from linars.edu.memphis.ccrg.alife.elements.alife_object import ALifeObject
from linars.edu.memphis.ccrg.alife.opreations.world_operation import WorldOperation

class TurnRightOperation(WorldOperation):
    """
    右转操作实现

    该类实现ALife环境中的右转操作
    """

    def __init__(self):
        """初始化右转操作"""
        super().__init__("right")

    def execute(self, actor: ALifeObject, target: Optional[ALifeObject], *params) -> bool:
        """
        执行右转操作

        参数:
            actor: 执行右转行为的对象
            target: 右转目标(未使用)
            params: 附加参数

        返回:
            操作成功返回True，否则返回False
        """
        # Get the actor's current direction
        direction = actor.get_attribute("direction")
        if direction is None:
            return False

        # Turn right (90 degrees clockwise)
        new_direction = None
        if direction == 'N':
            new_direction = 'E'
        elif direction == 'S':
            new_direction = 'W'
        elif direction == 'E':
            new_direction = 'S'
        elif direction == 'W':
            new_direction = 'N'

        # Set the new direction
        actor.set_attribute("direction", new_direction)

        return True
