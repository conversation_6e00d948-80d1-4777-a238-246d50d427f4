# LIDA Cognitive Framework
"""
Default implementation of Codelet.
"""

import logging
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.Codelet import Codelet

class CodeletImpl(FrameworkTaskImpl, Codelet):
    """
    Default implementation of Codelet.
    """
    
    def __init__(self, ticks_per_run: int = 1):
        """
        Initialize a CodeletImpl.
        
        Args:
            ticks_per_run: The number of ticks between runs of this codelet
        """
        super().__init__(ticks_per_run)
        self.sought_content = NodeStructureImpl()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get_sought_content(self) -> NodeStructure:
        """
        Get the content this codelet is looking for.
        
        Returns:
            The content this codelet is looking for
        """
        return self.sought_content
    
    def set_sought_content(self, content: NodeStructure) -> None:
        """
        Set the content this codelet is looking for.
        
        Args:
            content: The content this codelet is looking for
        """
        if content is not None:
            self.sought_content = content
    
    def init(self, params: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize this codelet with the given parameters.
        
        Args:
            params: Parameters for initialization
        """
        super().init(params)
