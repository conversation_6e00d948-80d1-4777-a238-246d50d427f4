#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to run a NARS cycle.
"""

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory

class NarCycleTask(FrameworkTaskImpl):
    """
    A task to run a NARS cycle.
    """

    def __init__(self):
        """
        Initialize a NarCycleTask.
        """
        super().__init__(1, "tact")

    def run_this_framework_task(self):
        """
        Run a NARS cycle.
        """
        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        AgentStarter.nar.cycles(1)
        self.cancel()
