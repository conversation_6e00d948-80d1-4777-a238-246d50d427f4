# LIDA认知框架
"""
可学习对象的接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.Activatible import Activatible

class Learnable(Activatible, ABC):
    """
    可学习对象的接口
    """

    @abstractmethod
    def get_incentive_salience(self) -> float:
        """
        获取此对象的激励显著性

        返回:
            此对象的激励显著性
        """
        pass

    @abstractmethod
    def set_incentive_salience(self, salience: float) -> None:
        """
        设置此对象的激励显著性

        参数:
            salience: 要设置的激励显著性
        """
        pass

    @abstractmethod
    def reinforce(self, amount: float) -> None:
        """
        按给定值强化此对象

        参数:
            amount: 强化值
        """
        pass
