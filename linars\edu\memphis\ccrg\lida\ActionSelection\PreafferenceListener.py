# LIDA认知框架
"""
行为选择模块的预偏好信号监听器
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure

class PreafferenceListener(ModuleListener):
    """
    行为选择模块的预偏好信号监听器
    """

    @abstractmethod
    def receive_preafference(self, add_set: NodeStructure, delete_set: NodeStructure) -> None:
        """
        接收来自行为选择模块的预偏好信号

        参数:
            add_set: 预期在未来感知中添加的节点集合
            delete_set: 预期在未来感知中删除的节点集合
        """
        pass
