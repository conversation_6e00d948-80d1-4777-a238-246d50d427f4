"""
语法任务 - 用于处理语法分析和生成
"""

import logging
from typing import Dict, List, Set, Optional, Any, Collection, Tuple, Union

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter

# 尝试导入Neo4j相关组件
try:
    from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False

class GrammarTask(FrameworkTaskImpl):
    """
    语法任务类 - 用于处理语法分析和生成
    """
    
    def __init__(self, yufa_ns: NodeStructure, scene_ns: NodeStructure, ticks_per_run: int, pam: PAMemory):
        """
        初始化语法任务
        
        Args:
            yufa_ns: 语法节点结构
            scene_ns: 场景节点结构
            ticks_per_run: 每次运行的tick数
            pam: PAM内存
        """
        super().__init__(ticks_per_run, "tact")
        self.logger = logging.getLogger(self.__class__.__name__)
        
        self.yufa_ns = yufa_ns
        self.scene_ns = scene_ns
        self.pam = pam
        
        self.words = {}  # 词汇映射
        self.word_num = 0  # 词汇数量
        self.is_done = False  # 是否完成
        self.node = None  # 当前节点
        
        # 场景和语法映射
        self.scene_map = {}  # 场景映射
        self.yufa_map = {}  # 语法映射
    
    def run_this_framework_task(self):
        """
        运行语法任务
        """
        try:
            # 初始化变量
            self.words = {}
            
            # 检查Neo4j是否可用
            if not NEO4J_AVAILABLE:
                self.logger.warning("Neo4j不可用，无法执行语法任务")
                self.cancel()
                return
                
            # 获取主场景链接
            main_links = None
            if hasattr(self.scene_ns, "get_links_of_sink") and hasattr(self.scene_ns, "get_main_node_id"):
                main_links = self.scene_ns.get_links_of_sink(self.scene_ns.get_main_node_id())
            elif hasattr(self.scene_ns, "getLinksOfSink") and hasattr(self.scene_ns, "getMainNodeId"):
                main_links = self.scene_ns.getLinksOfSink(self.scene_ns.getMainNodeId())
                
            if not main_links:
                self.logger.warning("无法获取主场景链接")
                self.cancel()
                return
                
            # 构建场景树
            level = 0
            main_node_id = None
            if hasattr(self.scene_ns, "get_main_node_id"):
                main_node_id = self.scene_ns.get_main_node_id()
            elif hasattr(self.scene_ns, "getMainNodeId"):
                main_node_id = self.scene_ns.getMainNodeId()
                
            if main_node_id is None:
                self.logger.warning("无法获取主节点ID")
                self.cancel()
                return
                
            self.scene_map[f"{level}_{main_node_id}"] = main_links
            
            # 递归构建场景树
            self.get_tree(main_links, self.scene_map, level + 1)
            
            # 设置场景映射
            if self.scene_map and hasattr(self.scene_ns, "set_main_map"):
                self.scene_ns.set_main_map(self.scene_map)
            elif self.scene_map and hasattr(self.scene_ns, "setMainMap"):
                self.scene_ns.setMainMap(self.scene_map)
                
            # 获取语法节点
            yufa_nodes = []
            if hasattr(self.yufa_ns, "get_nodes"):
                for node in self.yufa_ns.get_nodes():
                    if hasattr(AgentStarter, "yufamap") and hasattr(node, "get_node_name"):
                        if node.get_node_name() in AgentStarter.yufamap:
                            yufa_nodes.append(node)
                    elif hasattr(AgentStarter, "yufamap") and hasattr(node, "getTNname"):
                        if node.getTNname() in AgentStarter.yufamap:
                            yufa_nodes.append(node)
            elif hasattr(self.yufa_ns, "getNodes"):
                for node in self.yufa_ns.getNodes():
                    if hasattr(AgentStarter, "yufamap") and hasattr(node, "get_node_name"):
                        if node.get_node_name() in AgentStarter.yufamap:
                            yufa_nodes.append(node)
                    elif hasattr(AgentStarter, "yufamap") and hasattr(node, "getTNname"):
                        if node.getTNname() in AgentStarter.yufamap:
                            yufa_nodes.append(node)
            
            # 遍历场景，匹配语法
            for scene_key in self.scene_map:
                scene_links = self.scene_map[scene_key]
                
                for node in yufa_nodes:
                    # 获取语法链接
                    yufa_links = None
                    if hasattr(self.yufa_ns, "get_links_of_sink") and hasattr(node, "get_node_id"):
                        yufa_links = self.yufa_ns.get_links_of_sink(node.get_node_id())
                    elif hasattr(self.yufa_ns, "getLinksOfSink") and hasattr(node, "getNodeId"):
                        yufa_links = self.yufa_ns.getLinksOfSink(node.getNodeId())
                        
                    if not yufa_links:
                        continue
                        
                    # 获取核心数量
                    yufa_core = 0
                    if hasattr(node, "get_property") and node.get_property("core"):
                        yufa_core = int(node.get_property("core"))
                    elif hasattr(node, "getProperty") and node.getProperty("core"):
                        prop = node.getProperty("core")
                        if isinstance(prop, str):
                            yufa_core = int(prop)
                        else:
                            yufa_core = prop
                            
                    # 筛选语法槽
                    yufa_list = []
                    for link in yufa_links:
                        source = None
                        if hasattr(link, "get_source"):
                            source = link.get_source()
                        elif hasattr(link, "getSource"):
                            source = link.getSource()
                            
                        if source:
                            source_name = None
                            if hasattr(source, "get_node_name"):
                                source_name = source.get_node_name()
                            elif hasattr(source, "getTNname"):
                                source_name = source.getTNname()
                                
                            if source_name and hasattr(AgentStarter, "facaomap") and source_name in AgentStarter.facaomap:
                                yufa_list.append(link)
                    
                    # 如果语法槽数对应
                    if len(yufa_list) == yufa_core:
                        # 获取预设语序
                        list_yufa = None
                        if hasattr(node, "get_property") and node.get_property("listyufa"):
                            list_yufa = node.get_property("listyufa").split("_")
                        elif hasattr(node, "getProperty") and node.getProperty("listyufa"):
                            list_yufa = node.getProperty("listyufa").split("_")
                            
                        if not list_yufa:
                            continue
                            
                        # 如果语法槽数与某层某个子场景元素数一样，判断内容是否一致
                        if len(yufa_list) == len(scene_links):
                            fit_size = 0
                            for scene_link in scene_links:
                                for yufa_link in yufa_list:
                                    # 获取类别名称
                                    category_name = None
                                    if hasattr(scene_link, "get_category") and hasattr(scene_link.get_category(), "get_node_name"):
                                        category_name = scene_link.get_category().get_node_name()
                                    elif hasattr(scene_link, "getCategory") and hasattr(scene_link.getCategory(), "getName"):
                                        category_name = scene_link.getCategory().getName()
                                        
                                    # 获取源节点名称
                                    source_name = None
                                    if hasattr(yufa_link, "get_source") and hasattr(yufa_link.get_source(), "get_node_name"):
                                        source_name = yufa_link.get_source().get_node_name()
                                    elif hasattr(yufa_link, "getSource") and hasattr(yufa_link.getSource(), "getTNname"):
                                        source_name = yufa_link.getSource().getTNname()
                                        
                                    if category_name and source_name and category_name == source_name:
                                        fit_size += 1
                            
                            # 找到全匹配，按场景树结构建立语法树结构
                            if fit_size == len(scene_links):
                                yufa_total = 0
                                if hasattr(node, "get_property") and node.get_property("total"):
                                    yufa_total = int(node.get_property("total"))
                                elif hasattr(node, "getProperty") and node.getProperty("total"):
                                    prop = node.getProperty("total")
                                    if isinstance(prop, str):
                                        yufa_total = int(prop)
                                    else:
                                        yufa_total = prop
                                        
                                final_yufa = []
                                
                                # 如果还有其他非场景元素语法连接词没取到
                                if len(yufa_links) != yufa_total:
                                    self.get_total(node, final_yufa, list_yufa)
                                else:
                                    # 按既存的语法序列整理list
                                    for i in range(len(list_yufa)):
                                        for link in yufa_links:
                                            node_id = None
                                            if hasattr(link, "get_source") and hasattr(link.get_source(), "get_node_id"):
                                                node_id = str(link.get_source().get_node_id())
                                            elif hasattr(link, "getSource") and hasattr(link.getSource(), "getNodeId"):
                                                node_id = str(link.getSource().getNodeId())
                                                
                                            if node_id and node_id == list_yufa[i]:
                                                final_yufa.append(link)
                                
                                self.yufa_map[scene_key] = final_yufa
                                break
            
            # 如果场景和语法映射数量相同，处理语法生成
            if len(self.scene_map) == len(self.yufa_map):
                self.word_num = 0
                
                # 获取第一层父语法
                main_node_id = None
                if hasattr(self.scene_ns, "get_main_node_id"):
                    main_node_id = self.scene_ns.get_main_node_id()
                elif hasattr(self.scene_ns, "getMainNodeId"):
                    main_node_id = self.scene_ns.getMainNodeId()
                    
                if main_node_id is None:
                    self.logger.warning("无法获取主节点ID")
                    self.cancel()
                    return
                    
                main_links = self.yufa_map.get(f"0_{main_node_id}")
                if not main_links:
                    self.logger.warning("无法获取主语法链接")
                    self.cancel()
                    return
                
                # 同层的都顺次解决，子层的继续深挖
                for i in range(len(main_links)):
                    self.node = None
                    if hasattr(main_links[i], "get_source"):
                        self.node = main_links[i].get_source()
                    elif hasattr(main_links[i], "getSource"):
                        self.node = main_links[i].getSource()
                        
                    if self.node:
                        self.invoke(1, f"0_{main_node_id}")
                
                # 更新场景主节点
                if hasattr(AgentStarter, "scenelist") and AgentStarter.scenelist:
                    if hasattr(self.scene_ns, "set_main_node_id"):
                        self.scene_ns.set_main_node_id(int(AgentStarter.scenelist[0]))
                    elif hasattr(self.scene_ns, "setMainNodeId"):
                        self.scene_ns.setMainNodeId(int(AgentStarter.scenelist[0]))
                    AgentStarter.scenelist.pop(0)
                else:
                    if hasattr(self.scene_ns, "set_main_node_id"):
                        self.scene_ns.set_main_node_id(0)
                    elif hasattr(self.scene_ns, "setMainNodeId"):
                        self.scene_ns.setMainNodeId(0)
                
                # 清除映射
                if hasattr(self.yufa_ns, "set_main_map"):
                    self.yufa_ns.set_main_map(None)
                elif hasattr(self.yufa_ns, "setMainMap"):
                    self.yufa_ns.setMainMap(None)
                    
                if hasattr(self.scene_ns, "set_main_map"):
                    self.scene_ns.set_main_map(None)
                elif hasattr(self.scene_ns, "setMainMap"):
                    self.scene_ns.setMainMap(None)
            
            # 取消任务
            self.cancel()
            
        except Exception as e:
            self.logger.error(f"运行语法任务时出错: {e}")
            import traceback
            traceback.print_exc()
            self.cancel()
    
    def invoke(self, level0: int, up_map_key: str):
        """
        递归调用处理语法节点
        
        Args:
            level0: 当前层级
            up_map_key: 上层映射键
        """
        try:
            name = ""
            node_name = None
            if hasattr(self.node, "get_node_name"):
                node_name = self.node.get_node_name()
            elif hasattr(self.node, "getTNname"):
                node_name = self.node.getTNname()
                
            if not node_name:
                return
                
            map_key = f"{level0}_{node_name}"
            main_links = self.yufa_map.get(map_key)
            self.is_done = False
            
            if main_links is None:
                # 不是子场景，则直接在前一层查场景元素，匹配则替换
                scene_links = self.scene_map.get(up_map_key)
                if scene_links:
                    for scene_link in scene_links:
                        scene_link_name = None
                        if hasattr(scene_link, "get_node_name"):
                            scene_link_name = scene_link.get_node_name()
                        elif hasattr(scene_link, "getTNname"):
                            scene_link_name = scene_link.getTNname()
                            
                        if scene_link_name and scene_link_name == node_name:
                            source = None
                            if hasattr(scene_link, "get_source"):
                                source = scene_link.get_source()
                            elif hasattr(scene_link, "getSource"):
                                source = scene_link.getSource()
                                
                            if source and hasattr(self.pam, "get_listener") and self.pam.get_listener():
                                self.pam.get_listener().receive_percept(source, ModuleName.WordGraph)
                                
                                source_name = None
                                if hasattr(source, "get_node_name"):
                                    source_name = source.get_node_name()
                                elif hasattr(source, "getTNname"):
                                    source_name = source.getTNname()
                                    
                                if source_name:
                                    name = source_name
                                    self.words[self.word_num] = name
                                    self.is_done = True
                
                # 没有场景元素匹配，直接拼接当前词
                if not self.is_done:
                    name = node_name
                    self.words[self.word_num] = name
                    if hasattr(self.pam, "get_listener") and self.pam.get_listener():
                        self.pam.get_listener().receive_percept(self.node, ModuleName.WordGraph)
                
                # 内部语言重理解
                print(f"新词-----------{name}")
                self.word_num += 1
            else:
                # 处理子场景
                for i in range(len(main_links)):
                    self.node = None
                    if hasattr(main_links[i], "get_source"):
                        self.node = main_links[i].get_source()
                    elif hasattr(main_links[i], "getSource"):
                        self.node = main_links[i].getSource()
                        
                    if self.node:
                        # 只能一个地方加加，深度担当，其他都按同层处理
                        self.invoke(level0 + 1, map_key)
        except Exception as e:
            self.logger.error(f"调用语法处理时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def get_total(self, node: Node, yufa_links: List[Link], list_yufa: List[str]):
        """
        获取总语法链接
        
        Args:
            node: 节点
            yufa_links: 语法链接列表
            list_yufa: 语法列表
        """
        try:
            if not NEO4J_AVAILABLE:
                return
                
            node_name = None
            if hasattr(node, "get_node_name"):
                node_name = node.get_node_name()
            elif hasattr(node, "getTNname"):
                node_name = node.getTNname()
                
            if not node_name:
                return
                
            # 构建查询
            query = f"match (n{{name:'{node_name}'}})<-[r:语序]-() return r"
            list_yf = []
            
            # 执行查询
            result = NeoUtil.execute_query(query)
            if result and hasattr(result, "has_next"):
                while result.has_next():
                    row = result.next()
                    for key in result.columns():
                        rel = row.get(key)
                        if rel:
                            link = None
                            if hasattr(NeoUtil, "cast_neo_to_lida_link"):
                                link = NeoUtil.cast_neo_to_lida_link(rel)
                            elif hasattr(NeoUtil, "CastNeoToLidaLink"):
                                link = NeoUtil.CastNeoToLidaLink(rel, None)
                                
                            if link:
                                list_yf.append(link)
            
            # 按预设语序整理链接
            for i in range(len(list_yufa)):
                for link in list_yf:
                    source_id = None
                    if hasattr(link, "get_source") and hasattr(link.get_source(), "get_node_id"):
                        source_id = str(link.get_source().get_node_id())
                    elif hasattr(link, "getSource") and hasattr(link.getSource(), "getNodeId"):
                        source_id = str(link.getSource().getNodeId())
                        
                    if source_id and source_id == list_yufa[i]:
                        yufa_links.append(link)
        except Exception as e:
            self.logger.error(f"获取总语法链接时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def get_tree(self, main_links: Collection[Link], links_map: Dict[str, Collection[Link]], level: int):
        """
        递归构建场景树
        
        Args:
            main_links: 主链接集合
            links_map: 链接映射
            level: 当前层级
        """
        try:
            for link in main_links:
                source = None
                if hasattr(link, "get_source"):
                    source = link.get_source()
                elif hasattr(link, "getSource"):
                    source = link.getSource()
                    
                if not source:
                    continue
                    
                source_name = None
                if hasattr(source, "get_node_name"):
                    source_name = source.get_node_name()
                elif hasattr(source, "getTNname"):
                    source_name = source.getTNname()
                    
                if source_name and hasattr(AgentStarter, "scenemap") and source_name in AgentStarter.scenemap:
                    # 获取子场景链接
                    sub_links = None
                    if hasattr(self.scene_ns, "get_links_of_sink") and hasattr(source, "get_node_id"):
                        sub_links = self.scene_ns.get_links_of_sink(source.get_node_id())
                    elif hasattr(self.scene_ns, "getLinksOfSink") and hasattr(source, "getNodeId"):
                        sub_links = self.scene_ns.getLinksOfSink(source.getNodeId())
                        
                    if sub_links:
                        link_name = None
                        if hasattr(link, "get_node_name"):
                            link_name = link.get_node_name()
                        elif hasattr(link, "getTNname"):
                            link_name = link.getTNname()
                            
                        if link_name:
                            links_map[f"{level}_{link_name}"] = sub_links
                            # 递归处理子场景
                            self.get_tree(sub_links, links_map, level + 1)
        except Exception as e:
            self.logger.error(f"构建场景树时出错: {e}")
            import traceback
            traceback.print_exc()
