"""
TLink接口定义了链接的基本功能
"""
from abc import ABC, abstractmethod
from typing import TypeVar, Generic

T = TypeVar('T')

class TLink(Generic[T], ABC):
    """
    链接接口
    """
    
    @abstractmethod
    def get_index(self, i: int) -> int:
        """
        按级别获取一个索引
        
        参数:
            i: 索引级别
            
        返回:
            int: 索引值
        """
        pass
    
    @abstractmethod
    def get_target(self) -> T:
        """
        获取目标
        
        返回:
            T: 目标对象
        """
        pass
    
    @abstractmethod
    def get_priority(self) -> float:
        """
        获取优先级
        
        返回:
            float: 优先级
        """
        pass
