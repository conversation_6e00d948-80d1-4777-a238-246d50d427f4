# LIDA Cognitive Framework
"""
Task which starts the broadcast triggers.
"""

import logging
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl

class StartTriggersTask(FrameworkTaskImpl):
    """
    Task which starts the broadcast triggers.
    """
    
    def __init__(self, ticks_per_run=1):
        """
        Initialize the task.
        
        Args:
            ticks_per_run: Number of ticks per run
        """
        super().__init__(ticks_per_run)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def run_this_framework_task(self) -> None:
        """
        Run the task, starting the broadcast triggers.
        """
        # In a real implementation, you would start the broadcast triggers here
        pass
