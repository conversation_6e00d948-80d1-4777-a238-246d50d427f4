"""
Base class for Term nodes

This module provides the base implementation for term nodes in the system.
"""
import logging
from typing import Dict, List, Optional, Any
import decimal

from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.ActivatibleImpl import ActivatibleImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node


class ExtendedId:
    """Extended ID for nodes and links"""

    def __init__(self, source_id: int, target_id: int = None, category_id: int = None):
        """
        Constructor

        Args:
            source_id: Source node ID
            target_id: Target node ID (for links)
            category_id: Category ID (for links)
        """
        self.source_node_id = source_id
        self.target_node_id = target_id
        self.category_id = category_id

    def is_node_id(self) -> bool:
        """
        Check if this is a node ID

        Returns:
            bool: True if this is a node ID
        """
        return self.target_node_id is None and self.category_id is None

    def get_source_node_id(self) -> int:
        """
        Get the source node ID

        Returns:
            int: The source node ID
        """
        return self.source_node_id

class TermNodeImpl(ActivatibleImpl, Node):
    """Base implementation for term nodes"""

    def is_node(self) -> bool:
        pass

    logger = logging.getLogger("TermNodeImpl")

    def __init__(self):
        """Default constructor"""
        # print("TermNodeImpl---__init__-----")
        super().__init__()
        self.nodeId = 0
        self.extendedId = None
        self.TNname = None  # Term name
        self.label = "word0"
        self.factoryName = None
        self.toStringName = None

        self.bcastid = "0"
        self.lastAct = "0"  # Last activation round

        self.ptwath = "see"  # Information source, perception channel
        self.fromActor = "agent"  # Actor, action or content
        self.toActor = "user"  # Recipient, action or content

        self.nodeProxy = None
        self.truth = 0  # Virtual/real distinction, 0 is virtual

        self.fromsceneid = 0
        self.fromnodeid = 0
        self.fromLinkType = ""

        self.doneNum = 0

        self.properties = {}
        self.labels = []

        self.location = None
        self.groundingPamNode = None

        # Activation related fields
        self.activation = 0.0
        self.incentive_salience = 0.0

    def get_node_id(self) -> int:
        """Get the node ID"""
        return self.nodeId

    def set_node_id(self, node_id: int):
        """
        Set the node ID

        Args:
            node_id: The node ID
        """
        self.nodeId = node_id
        self.extendedId = ExtendedId(node_id)
        self.update_name()

    def set_extended_id(self, eid: ExtendedId):
        """
        Set the extended ID

        Args:
            eid: The extended ID
        """
        if eid is None:
            self.logger.warning("Supplied ExtendedId was null. ExtendedId not set.")
        elif eid.is_node_id():
            self.extendedId = eid
            self.nodeId = eid.get_source_node_id()
            self.update_name()
        else:
            self.logger.warning("Cannot give a Node a Link's ExtendedId")

    def update_name(self):
        """Update the node's name"""
        self.toStringName = f"{self.TNname}[{self.nodeId}] {self.get_activation()}"

    def get_tn_name(self) -> str:
        """
        Get the term name
        Returns:
            str: The term name
        """
        return self.TNname
        # return self.get_node_name()

    def getTNname(self):
        # return self.TNname
        return self.get_node_name()

    def get_node_name(self) -> str:
        """
        Get the node name

        Returns:
            str: The node name
        """
        return self.TNname

    def set_node_name(self, name: str):
        """
        Set the node name

        Args:
            name: The node name
        """
        self.TNname = name
        self.update_name()

    def get_extended_id(self) -> ExtendedId:
        """
        Get the extended ID

        Returns:
            ExtendedId: The extended ID
        """
        return self.extendedId

    def get_label(self) -> str:
        """
        Get the label

        Returns:
            str: The label
        """
        return self.label

    def set_label(self, label: str):
        """
        Set the label

        Args:
            label: The label
        """
        self.label = label
        self.update_name()

    def get_factory_type(self) -> str:
        """
        Get the factory type

        Returns:
            str: The factory type
        """
        return self.factoryName

    def set_factory_type(self, factory_type: str):
        """
        Set the factory type

        Args:
            factory_type: The factory type
        """
        self.factoryName = factory_type

    def get_location(self) -> str:
        """
        Get the location

        Returns:
            str: The location
        """
        return self.location

    def set_location(self, location: str):
        """
        Set the location

        Args:
            location: The location
        """
        self.location = location

    def get_bcastid(self) -> str:
        """
        Get the broadcast ID

        Returns:
            str: The broadcast ID
        """
        return self.bcastid

    def set_bcastid(self, bcastid: str):
        """
        Set the broadcast ID

        Args:
            bcastid: The broadcast ID
        """
        self.bcastid = bcastid

    def get_grounding_pam_node(self):
        """
        Get the grounding PAM node

        Returns:
            Any: The grounding PAM node
        """
        return self.groundingPamNode

    def set_grounding_pam_node(self, node):
        """
        Set the grounding PAM node

        Args:
            node: The grounding PAM node
        """
        self.groundingPamNode = node

    def __eq__(self, other: Any) -> bool:
        """
        Check equality

        Args:
            other: Object to compare with

        Returns:
            bool: True if equal
        """
        if hasattr(other, 'get_node_id'):
            return other.get_node_id() == self.nodeId
        return False

    def __hash__(self) -> int:
        """
        Hash code

        Returns:
            int: Hash code
        """
        return self.nodeId

    def __str__(self) -> str:
        """
        String representation

        Returns:
            str: String representation
        """
        df = decimal.Decimal("0.00")
        return f"{self.TNname} [{self.nodeId}] {df.quantize(decimal.Decimal(str(self.get_activation())))}"

    def update_node_values(self, node):
        """
        Update node values from another node

        Args:
            node: The source node
        """
        if hasattr(node, 'get_total_incentive_salience'):
            self.set_incentive_salience(node.get_total_incentive_salience())

    def get_condition_id(self) -> ExtendedId:
        """
        Get the condition ID

        Returns:
            ExtendedId: The condition ID
        """
        return self.extendedId

    def get_string_name(self) -> str:
        """
        Get the string name

        Returns:
            str: The string name
        """
        return self.toStringName

    def get_from(self) -> str:
        """
        Get the from actor

        Returns:
            str: The from actor
        """
        return self.fromActor

    def set_from(self, from_actor: str):
        """
        Set the from actor

        Args:
            from_actor: The from actor
        """
        self.fromActor = from_actor

    def get_to(self) -> str:
        """
        Get the to actor

        Returns:
            str: The to actor
        """
        return self.toActor

    def set_to(self, to_actor: str):
        """
        Set the to actor

        Args:
            to_actor: The to actor
        """
        self.toActor = to_actor

    def get_truth(self) -> int:
        """
        Get the truth value

        Returns:
            int: The truth value
        """
        return self.truth

    def set_truth(self, truth: int):
        """
        Set the truth value

        Args:
            truth: The truth value
        """
        self.truth = truth

    def get_last_act(self) -> str:
        """
        Get the last activation

        Returns:
            str: The last activation
        """
        return self.lastAct

    def set_last_act(self, last_act: str):
        """
        Set the last activation

        Args:
            last_act: The last activation
        """
        self.lastAct = last_act

    def get_fromsceneid(self) -> int:
        """
        Get the from scene ID

        Returns:
            int: The from scene ID
        """
        return self.fromsceneid

    def set_fromsceneid(self, fromsceneid: int):
        """
        Set the from scene ID

        Args:
            fromsceneid: The from scene ID
        """
        self.fromsceneid = fromsceneid

    def get_fromnodeid(self) -> int:
        """
        Get the from node ID
        Returns:
            int: The from node ID
        """
        return self.fromnodeid

    def set_fromnodeid(self, fromnodeid: int):
        """
        Set the from node ID

        Args:
            fromnodeid: The from node ID
        """
        self.fromnodeid = fromnodeid

    def get_from_link_type(self) -> str:
        """
        Get the from link type

        Returns:
            str: The from link type
        """
        return self.fromLinkType

    def set_from_link_type(self, from_link_type: str):
        """
        Set the from link type

        Args:
            from_link_type: The from link type
        """
        self.fromLinkType = from_link_type

    def get_done_num(self) -> int:
        """
        Get the done number

        Returns:
            int: The done number
        """
        return self.doneNum

    def set_done_num(self, done_num: int):
        """
        Set the done number

        Args:
            done_num: The done number
        """
        self.doneNum = done_num

    def get_properties(self) -> Dict[str, Any]:
        """
        Get the properties

        Returns:
            Dict: The properties
        """
        return self.properties

    def set_properties(self, properties: Dict[str, Any]):
        """
        Set the properties

        Args:
            properties: The properties
        """
        self.properties = properties

    def get_property(self, key: str) -> Any:
        """
        Get a property

        Args:
            key: The property key

        Returns:
            Any: The property value
        """
        return self.properties.get(key)

    def to_term(self):
        """
        Convert to a Term

        Returns:
            Term: The term
        """
        from linars.edu.memphis.ccrg.linars.term import Term
        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter

        narsese = AgentStarter.narsese

        term = Term()
        term.set_term_name(self.get_tn_name())
        term.set_term_id(self.get_node_id())

        # try {
        #     term = narsese.parseTerm(getTNname());
        # } catch (Parser.InvalidInputException e) {
        #     throw new RuntimeException(e);
        # }

        try:
            term = narsese.parse_term(self.get_tn_name())
        except Exception as e:
            raise RuntimeError(e)

        # In a real implementation, we would parse the term name
        # but for now we'll just return the basic term
        return term

    def get_strs(self) -> str:
        """
        Get the string representation of components

        Returns:
            str: The string representation
        """
        strs = ""
        term = self.to_term()

        if hasattr(term, 'get_components_strs'):
            strs = term.get_components_strs()
        else:
            strs = self.get_tn_name()

        return strs

    # Activation related methods
    def get_activation(self) -> float:
        """
        Get the activation

        Returns:
            float: The activation
        """
        return self.activation

    def getActivation(self) -> float:
        return self.get_activation()

    def set_activation(self, activation: float):
        """
        Set the activation

        Args:
            activation: The activation
        """
        self.activation = max(0.0, min(1.0, activation))

    def get_incentive_salience(self) -> float:
        """
        Get the incentive salience

        Returns:
            float: The incentive salience
        """
        return self.incentive_salience

    def set_incentive_salience(self, salience: float):
        """
        Set the incentive salience

        Args:
            salience: The incentive salience
        """
        self.incentive_salience = max(0.0, min(1.0, salience))

    def get_total_incentive_salience(self) -> float:
        """
        Get the total incentive salience

        Returns:
            float: The total incentive salience
        """
        return self.incentive_salience
