#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to select a concept for inference.
"""

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl

# Import required components
from linars.edu.memphis.ccrg.linars.memory import Memory
from linars.org.opennars.control.inference_control import InferenceControl
from linars.org.opennars.main.nar import Nar

class SelectConceptTask(FrameworkTaskImpl):
    """
    A task to select a concept for inference.
    """

    def __init__(self, mem, nar):
        """
        Initialize a SelectConceptTask.

        Args:
            mem: The memory
            nar: The NARS instance
        """
        super().__init__(1, "tact")
        self.mem = mem
        self.nar = nar

    def run_this_framework_task(self):
        """
        Run the task.
        """
        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        InferenceControl.select_concept_for_inference(self.mem, AgentStarter.nar.narParameters, self.nar)
        self.cancel()

        # 添加任务到TaskSpawner
        from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
        if FrameworkModuleImpl.task_spawner:
            FrameworkModuleImpl.task_spawner.receive_finished_task(self)
