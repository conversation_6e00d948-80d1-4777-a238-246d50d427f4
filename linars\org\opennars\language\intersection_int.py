"""
内涵交集项定义
按照NARS理论定义的项的内涵交集操作
"""
from typing import List
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.io.symbols import NativeOperator
from linars.org.opennars.language.set_ext import SetExt
from linars.org.opennars.language.set_int import SetInt

class IntersectionInt(CompoundTerm):
    """
    内涵交集项类
    按照NARS理论定义的项的内涵交集操作
    """

    def __init__(self, arg):
        """
        构造函数

        参数:
            arg: 项的组成部分列表
        """
        super().__init__(arg)
        # In Python implementation, we'll verify sorted and unique in the make method
        self.init(arg)

    def clone(self, replaced: List[Term] = None):
        """
        克隆对象

        参数:
            replaced: 替换的组件项列表，如果为None则使用原始组件项

        返回:
            IntersectionInt: 新的内涵交集对象
        """
        if replaced is not None:
            return IntersectionInt(replaced)
        return IntersectionInt(self.term)

    def clone_with_terms(self, replaced):
        """
        使用替换项克隆对象

        参数:
            replaced: 被替换的项列表

        返回:
            Term: 包含替换组件的新项
        """
        if replaced is None:
            return None
        return IntersectionInt.make(replaced)

    @staticmethod
    def make_from_terms(term1, term2):
        """
        尝试从两个项创建新的内涵交集项

        参数:
            term1: 第一个组件
            term2: 第二个组件

        返回:
            Term: 生成的复合项或简化后的项
        """
        if isinstance(term1, SetExt) and isinstance(term2, SetExt):
            # Set union
            both = term1.term + term2.term
            return SetExt.make(both)

        if isinstance(term1, SetInt) and isinstance(term2, SetInt):
            # Set intersection
            set1 = set(term1.as_term_list())
            set2 = set(term2.as_term_list())
            intersection = set1.intersection(set2)
            return SetInt.make(list(intersection))

        terms = []
        if isinstance(term1, IntersectionInt):
            terms.extend(term1.term)
            if isinstance(term2, IntersectionInt):
                # (&,(&,P,Q),(&,R,S)) = (&,P,Q,R,S)
                terms.extend(term2.term)
            else:
                # (&,(&,P,Q),R) = (&,P,Q,R)
                terms.append(term2)
        elif isinstance(term2, IntersectionInt):
            # (&,R,(&,P,Q)) = (&,P,Q,R)
            terms.extend(term2.term)
            terms.append(term1)
        else:
            terms.append(term1)
            terms.append(term2)

        return IntersectionInt.make(terms)

    @staticmethod
    def make(t):
        """
        尝试创建新的内涵交集项

        参数:
            t: 项列表

        返回:
            Term: 从参数生成的项
        """
        # Sort and remove duplicates
        t = Term.to_sorted_set_array(t)

        if len(t) == 0:
            return None
        elif len(t) == 1:
            return t[0]
        else:
            return IntersectionInt(t)

    def operator(self):
        """
        获取项的操作符

        返回:
            str: 项的操作符
        """
        return NativeOperator.INTERSECTION_INT
