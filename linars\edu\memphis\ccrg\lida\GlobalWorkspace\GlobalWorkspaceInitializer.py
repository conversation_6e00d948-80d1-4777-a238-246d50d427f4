# LIDA Cognitive Framework
"""
Initializer for the GlobalWorkspace module.
"""

import logging
from typing import Dict, Any, Optional, TypeVar, Generic, Type

from linars.edu.memphis.ccrg.lida.Framework.Initialization.initializer import Initializer
from linars.edu.memphis.ccrg.lida.Framework.Initialization.FullyInitializable import FullyInitializable
from linars.edu.memphis.ccrg.lida.Framework.agent import Agent
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.GlobalWorkspaceImpl import GlobalWorkspaceImpl
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.BroadcastTrigger import BroadcastTrigger

T = TypeVar('T')

class GlobalWorkspaceInitializer(Initializer):
    """
    Initializer for the GlobalWorkspace module.
    """

    def __init__(self):
        """
        Initialize a GlobalWorkspaceInitializer.
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.global_workspace = None

    def initModule(self, module: FullyInitializable, agent: Agent, params: Dict[str, Any]) -> None:
        """
        Initialize the module.

        Args:
            module: The module to initialize
            agent: The agent
            params: The parameters for initialization
        """
        # print(f"GlobalWorkspaceInitializer.initModule() called with module: {module}, agent: {agent}, params: {params}")
        # print(f"GlobalWorkspaceInitializer.initModule() called with module: {module}")
        self.global_workspace = module

        if not isinstance(self.global_workspace, GlobalWorkspaceImpl):
            self.logger.warning(f"Module is not a GlobalWorkspaceImpl at tick {TaskManager.get_current_tick()}")
            return

        # Add broadcast triggers
        delay_no_broadcast = params.get("globalWorkspace.delayNoBroadcast", 100)
        delay_no_new_coalition = params.get("globalWorkspace.delayNoNewCoalition", 50)
        aggregate_activation_threshold = params.get("globalWorkspace.aggregateActivationThreshold", 0.8)
        individual_activation_threshold = params.get("globalWorkspace.individualActivationThreshold", 0.5)

        # Create and add broadcast triggers
        self.logger.info(f"GlobalWorkspace initialized with delayNoBroadcast: {delay_no_broadcast}, delayNoNewCoalition: {delay_no_new_coalition}, aggregateActivationThreshold: {aggregate_activation_threshold}, individualActivationThreshold: {individual_activation_threshold} at tick {TaskManager.get_current_tick()}")

        # Import trigger classes
        from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.NoBroadcastOccurringTrigger import NoBroadcastOccurringTrigger
        from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.AggregateCoalitionActivationTrigger import AggregateCoalitionActivationTrigger
        from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.NoCoalitionArrivingTrigger import NoCoalitionArrivingTrigger
        from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.IndividualCoalitionActivationTrigger import IndividualCoalitionActivationTrigger

        # Create and add NoBroadcastOccurringTrigger
        trigger = NoBroadcastOccurringTrigger()
        parameters = {"name": "NoBroadcastOccurringTrigger", "delay": delay_no_broadcast}
        trigger.init(parameters, self.global_workspace)
        self.global_workspace.add_broadcast_trigger(trigger)

        # Create and add AggregateCoalitionActivationTrigger
        trigger = AggregateCoalitionActivationTrigger()
        parameters = {"threshold": aggregate_activation_threshold}
        trigger.init(parameters, self.global_workspace)
        self.global_workspace.add_broadcast_trigger(trigger)

        # Create and add NoCoalitionArrivingTrigger
        trigger = NoCoalitionArrivingTrigger()
        parameters = {"name": "NoCoalitionArrivingTrigger", "delay": delay_no_new_coalition}
        trigger.init(parameters, self.global_workspace)
        self.global_workspace.add_broadcast_trigger(trigger)

        # Create and add IndividualCoalitionActivationTrigger
        trigger = IndividualCoalitionActivationTrigger()
        parameters = {"threshold": individual_activation_threshold}
        trigger.init(parameters, self.global_workspace)
        self.global_workspace.add_broadcast_trigger(trigger)

    # 删除不需要的方法，因为我们已经有了initModule方法
