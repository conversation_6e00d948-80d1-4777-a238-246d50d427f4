# LIDA Cognitive Framework
"""
Default implementation of PamNode.
A Learnable Node that overrides hashCode and equals.
Has a private LearnableImpl to help implement all Learnable methods.
"""

import logging
from typing import Dict, Any, Optional, List, Set
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.LearnableImpl import LearnableImpl
from linars.edu.memphis.ccrg.lida.Framework.Strategies.ExciteStrategy import ExciteStrategy
from linars.edu.memphis.ccrg.lida.Framework.Strategies.DecayStrategy import DecayStrategy
from linars.edu.memphis.ccrg.lida.PAM.PamNode import PamNode

class PamNodeImpl(NodeImpl, PamNode):
    """
    Default implementation of PamNode.
    A Learnable Node that overrides hashCode and equals.
    Has a private LearnableImpl to help implement all Learnable methods.
    """

    def __init__(self, name: str = None):
        """
        Initialize a PamNodeImpl.

        Args:
            name: The name of this PamNode
        """
        super().__init__(name)
        self.grounding_pam_node = self
        self.learnable = LearnableImpl()
        self.labels = []
        self.logger = logging.getLogger(self.__class__.__name__)

    def init(self) -> None:
        """
        Initialize this PamNode.
        """
        self.learnable.init(self.get_parameters())

    def update_node_values(self, node: Node) -> None:
        """
        Update this PamNode's values from another Node.

        Args:
            node: The Node to update from
        """
        if isinstance(node, PamNodeImpl):
            self.learnable.set_base_level_activation(node.get_base_level_activation())
        else:
            from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
            self.logger.debug(f"Cannot set PamNodeImpl-specific values. Required: {PamNodeImpl.__name__} \n Received: {node.__class__.__name__} at tick {TaskManager.get_current_tick()}")

    def __eq__(self, other) -> bool:
        """
        Check if this PamNode is equal to another.

        Args:
            other: The other PamNode to compare with

        Returns:
            True if the PamNodes have the same ID, False otherwise
        """
        if isinstance(other, PamNodeImpl):
            return self.get_node_id() == other.get_node_id()
        return False

    def __hash__(self) -> int:
        """
        Return the hash code of this PamNode.

        Returns:
            The hash code of the node ID
        """
        return self.get_node_id()

    def get_id(self) -> int:
        """
        Get the ID of this PamNode.

        Returns:
            The ID of this PamNode
        """
        return self.get_node_id()

    # LEARNABLE METHODS
    def get_activation(self) -> float:
        """
        Get the activation of this PamNode.

        Returns:
            The activation of this PamNode
        """
        if not hasattr(self, "learnable") or self.learnable is None:
            self.learnable = LearnableImpl()
        return self.learnable.get_activation()

    def set_activation(self, activation: float) -> None:
        """
        Set the activation of this PamNode.

        Args:
            activation: The activation to set
        """
        self.learnable.set_activation(activation)

    def get_total_activation(self) -> float:
        """
        Get the total activation of this PamNode.

        Returns:
            The total activation of this PamNode
        """
        return self.learnable.total_activation()

    def excite(self, amount: float) -> None:
        """
        Excite this PamNode by the given amount.

        Args:
            amount: The amount to excite by
        """
        self.excite_activation(amount)

    def excite_activation(self, amount: float) -> None:
        """
        Excite this PamNode's activation by the given amount.

        Args:
            amount: The amount to excite by
        """
        self.learnable.excite(amount)

    def decay(self, ticks: int) -> None:
        """
        Decay this PamNode by the given number of ticks.

        Args:
            ticks: The number of ticks to decay by
        """
        self.learnable.decay(ticks)

    def is_removable(self) -> bool:
        """
        Check if this PamNode is removable.

        Returns:
            True if this PamNode is removable, False otherwise
        """
        return self.get_activation() <= 0.0 and abs(self.get_incentive_salience()) <= 0.0

    def get_base_level_activation(self) -> float:
        """
        Get the base level activation of this PamNode.

        Returns:
            The base level activation of this PamNode
        """
        return self.learnable.get_base_level_activation()

    def set_base_level_activation(self, activation: float) -> None:
        """
        Set the base level activation of this PamNode.

        Args:
            activation: The base level activation to set
        """
        self.learnable.set_base_level_activation(activation)

    def reinforce_base_level_activation(self, amount: float) -> None:
        """
        Reinforce the base level activation of this PamNode by the given amount.

        Args:
            amount: The amount to reinforce by
        """
        self.learnable.set_base_level_activation(self.learnable.get_base_level_activation() + amount)

    def get_labels(self) -> list:
        """
        Get the labels of this PamNode.

        Returns:
            The labels of this PamNode
        """
        return self.labels

    def set_labels(self, labels: list) -> None:
        """
        Set the labels of this PamNode.

        Args:
            labels: The labels to set
        """
        self.labels = labels

    def set_base_level_excite_strategy(self, strategy: ExciteStrategy) -> None:
        """
        Set the base level excite strategy of this PamNode.

        Args:
            strategy: The base level excite strategy to set
        """
        self.learnable.set_excite_strategy(strategy)

    def get_base_level_excite_strategy(self) -> ExciteStrategy:
        """
        Get the base level excite strategy of this PamNode.

        Returns:
            The base level excite strategy of this PamNode
        """
        return self.learnable.get_excite_strategy()

    def set_base_level_decay_strategy(self, strategy: DecayStrategy) -> None:
        """
        Set the base level decay strategy of this PamNode.

        Args:
            strategy: The base level decay strategy to set
        """
        self.learnable.set_decay_strategy(strategy)

    def get_base_level_decay_strategy(self) -> DecayStrategy:
        """
        Get the base level decay strategy of this PamNode.

        Returns:
            The base level decay strategy of this PamNode
        """
        return self.learnable.get_decay_strategy()

    def get_incentive_salience(self) -> float:
        """
        Get the incentive salience of this PamNode.

        Returns:
            The incentive salience of this PamNode
        """
        return self.learnable.get_incentive_salience()

    def set_incentive_salience(self, salience: float) -> None:
        """
        Set the incentive salience of this PamNode.

        Args:
            salience: The incentive salience to set
        """
        self.learnable.set_incentive_salience(salience)

    def reinforce(self, amount: float) -> None:
        """
        Reinforce this PamNode by the given amount.

        Args:
            amount: The amount to reinforce by
        """
        self.learnable.reinforce(amount)

    def get_activation_threshold(self) -> float:
        """
        Get the activation threshold of this PamNode.

        Returns:
            The activation threshold of this PamNode
        """
        return self.learnable.get_activation_threshold()

    def set_activation_threshold(self, threshold: float) -> None:
        """
        Set the activation threshold of this PamNode.

        Args:
            threshold: The activation threshold to set
        """
        self.learnable.set_activation_threshold(threshold)

    def get_removal_threshold(self) -> float:
        """
        Get the removal threshold of this PamNode.

        Returns:
            The removal threshold of this PamNode
        """
        return self.learnable.get_removal_threshold()

    def set_removal_threshold(self, threshold: float) -> None:
        """
        Set the removal threshold of this PamNode.

        Args:
            threshold: The removal threshold to set
        """
        self.learnable.set_removal_threshold(threshold)

    def is_above_activation_threshold(self) -> bool:
        """
        Check if this PamNode is above its activation threshold.

        Returns:
            True if this PamNode is above its activation threshold, False otherwise
        """
        return self.learnable.is_above_activation_threshold()

    def is_above_removal_threshold(self) -> bool:
        """
        Check if this PamNode is above its removal threshold.

        Returns:
            True if this PamNode is above its removal threshold, False otherwise
        """
        return self.learnable.is_above_removal_threshold()

    def get_excite_strategy(self) -> ExciteStrategy:
        """
        Get the excite strategy of this PamNode.

        Returns:
            The excite strategy of this PamNode
        """
        return self.learnable.get_excite_strategy()

    def set_excite_strategy(self, strategy: ExciteStrategy) -> None:
        """
        Set the excite strategy of this PamNode.

        Args:
            strategy: The excite strategy to set
        """
        self.learnable.set_excite_strategy(strategy)

    def get_decay_strategy(self) -> DecayStrategy:
        """
        Get the decay strategy of this PamNode.

        Returns:
            The decay strategy of this PamNode
        """
        return self.learnable.get_decay_strategy()

    def set_decay_strategy(self, strategy: DecayStrategy) -> None:
        """
        Set the decay strategy of this PamNode.

        Args:
            strategy: The decay strategy to set
        """
        self.learnable.set_decay_strategy(strategy)
