# LIDA Cognitive Framework
"""
Default implementation of PAMemory. Module essentially concerned with PamNode and PamLinks,
source of meaning in LIDA, how they are activated and how they pass activation among themselves.
"""

import logging
from typing import Dict, Any, List, Set, Collection, Optional

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkImpl import LinkImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener import BroadcastListener
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.GlobalWorkspaceImpl import GlobalWorkspaceImpl
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemoryImpl import SensoryMemoryImpl
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceImpl import WorkspaceImpl

from linars.edu.memphis.ccrg.lida.PAM.Tasks.ExcitationTask import ExcitationTask
from linars.edu.memphis.ccrg.lida.PAM.PamListener import PamListener

class PropagationTask(FrameworkTaskImpl):
    """Task for handling activation propagation in PAM"""
    def __init__(self, ticks_per_run, link, amount, pam, depth=0, from0="pam"):
        super().__init__(ticks_per_run)
        self.link = link
        self.amount = amount
        self.pam = pam
        self.depth = depth
        self.from0 = from0

    def run_this_framework_task(self):
        """Run the propagation task"""
        # Get the sink node
        sink = self.link.get_sink()  # 使用Python版本的方法名

        # Apply activation to the sink
        if sink:
            sink.set_activation(self.amount)  # 使用Python版本的方法名

            # Propagate to parents if needed
            self.pam.propagate_activation_to_parents(sink, self.depth, self.from0)

        # Task is complete
        self.cancel()

class UpscalePropagationStrategy:
    """Strategy for propagating activation upwards"""
    def get_activation_to_propagate(self, params):
        """Calculate the amount of activation to propagate"""
        upscale = params.get("upscale", 0.6)
        total_activation = params.get("totalActivation", 0.0)
        return upscale * total_activation

class PamNodeStructure(NodeStructureImpl):
    """Internal implementation of NodeStructureImpl for PAM"""
    def __init__(self, node_type="PamNodeImpl", link_type="PamLinkImpl"):
        # super().__init__(node_type, link_type)
        super().__init__()
        self.default_node_type = node_type
        self.default_link_type = link_type

    def addNode(self, node, copy=True):
        """Add a node without copying it"""
        return super().addNode(node, self.default_node_type)

    def getNeoLinks(self, node):
        """
        从Neo4j数据库获取节点的链接 (Java风格命名)

        Args:
            node: 节点

        Returns:
            链接集合或None
        """
        return super().getNeoLinks(node)

class PAMemoryImpl(FrameworkModuleImpl, PAMemory, BroadcastListener):
    """Default implementation of PAMemory"""

    def __init__(self):
        """Initialize a PAMemoryImpl."""
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)

        # Constants
        self.DEFAULT_NONDECAYING_PAMNODE = "NoDecayPamNode"
        self.DEFAULT_PERCEPT_THRESHOLD = 0.7
        self.DEFAULT_UPSCALE_FACTOR = 0.6
        self.DEFAULT_DOWNSCALE_FACTOR = 0.5
        self.DEFAULT_PROPAGATION_THRESHOLD = 0.05
        self.DEFAULT_EXCITATION_TASK_TICKS = 1
        self.DEFAULT_PROPAGATION_TASK_TICKS = 1

        # Configuration parameters
        self.percept_threshold = self.DEFAULT_PERCEPT_THRESHOLD
        self.upscale_factor = self.DEFAULT_UPSCALE_FACTOR
        self.downscale_factor = self.DEFAULT_DOWNSCALE_FACTOR
        self.propagate_activation_threshold = self.DEFAULT_PROPAGATION_THRESHOLD
        self.excitation_task_ticks_per_run = self.DEFAULT_EXCITATION_TASK_TICKS
        self.propagation_task_ticks_per_run = self.DEFAULT_PROPAGATION_TASK_TICKS

        # Initialize collections
        self.pam_listeners = []
        self.nodes_by_label = {}

        # Initialize node structure
        self.pam_node_structure = PamNodeStructure()
        self.memory = NodeStructureImpl()
        self.current_cell = None
        self.position = None

        # Initialize propagation strategy
        self.propagation_strategy = UpscalePropagationStrategy()  # Strategy for propagating activation
        self.type_conversion_map = {}  # Map for converting node types
        self.propagate_params = {}  # Parameters for propagation

        # Initialize workspace buffers
        self.csm = None
        self.non_graph = None
        self.feel_graph = None
        self.goal_graph = None
        self.seq_graph = None
        self.concent_graph = None
        self.scene_graph = None
        self.grammar_graph = None
        self.understand_graph = None
        self.listen_graph = None

        # NodeStructures for buffers
        self.csm_ns = None
        self.non_ns = None
        self.feel_ns = None
        self.goal_ns = None
        self.seq_ns = None
        self.con_ns = None
        self.scene_ns = None
        self.yufa_ns = None
        self.yuyi_ns = None
        self.listen_ns = None

        # Environment and global workspace
        self.environment = None
        self.global_workspace = None

        # NARS integration
        self.nar = None

        self.nars_memory = None  # Will be initialized when needed

        """Create node for each cell the agent could visit"""
        for cell in range(16):
            node = NodeImpl()
            """Set the cell identifier to the corresponding state"""
            node.set_node_id(cell)
            """Store the node in memory"""
            self.memory.addNode_(node)

    def init(self, params=None):
        """Initialize the PAM module with parameters"""
        # Set parameters from configuration
        self.upscale_factor = self.get_param("pam.upscale", self.DEFAULT_UPSCALE_FACTOR)
        self.downscale_factor = self.get_param("pam.downscale", self.DEFAULT_DOWNSCALE_FACTOR)
        self.percept_threshold = self.get_param("pam.perceptThreshold", self.DEFAULT_PERCEPT_THRESHOLD)
        self.excitation_task_ticks_per_run = self.get_param("pam.excitationTicksPerRun", self.DEFAULT_EXCITATION_TASK_TICKS)
        self.propagation_task_ticks_per_run = self.get_param("pam.propagationTicksPerRun", self.DEFAULT_PROPAGATION_TASK_TICKS)
        self.propagate_activation_threshold = self.get_param("pam.propagateActivationThreshold", self.DEFAULT_PROPAGATION_THRESHOLD)

        # Initialize type conversion map
        self._init_type_conversion()

        # Initialize maps for node types
        self.act_map = {}
        self.mind_act_map = {}
        self.scene_map = {}
        self.self_scene_map = {}
        self.feel_map = {}
        self.seq_map = {}
        self.state_map = {}
        self.var_map = {}
        self.yufa_map = {}
        self.facao_map = {}
        self.ifelse_map = {}
        self.for_map = {}
        self.var_scene_map = {}

    def _init_type_conversion(self):
        """Initialize type conversion map"""
        parameters = self.get_parameters()
        if not parameters:
            return

        for key, value in parameters.items():
            if key.startswith("pam.perceptMapping.") and isinstance(value, str):
                mapping_params = value.strip().split(",")
                if len(mapping_params) == 3:
                    mapping_type, original_type, mapped_type = mapping_params

                    if mapping_type.lower() == "node":
                        self.type_conversion_map[original_type] = mapped_type
                    elif mapping_type.lower() == "link":
                        self.type_conversion_map[original_type] = mapped_type
                    else:
                        self.logger.warning(f"Bad mapping type: {mapping_type}. Must be 'node' or 'link'.")
                else:
                    self.logger.warning("Mapping parameters must have 3 parts: mappingType:originalType:mappedType separated by ','.")

    def get_param(self, name, default_value):
        """Get a parameter value with a default"""
        parameters = self.get_parameters()
        if parameters and name in parameters:
            return parameters[name]
        return default_value

    def get_parameters(self):
        """Get all parameters"""
        return getattr(self, "parameters", {})

    def set_associated_module(self, module, usage):
        """Associate a module with this PAM"""
        if hasattr(module, "get_environment"):
            self.environment = module.get_environment()
        elif hasattr(module, "is_global_workspace") and module.is_global_workspace():
            self.global_workspace = module
        elif hasattr(module, "is_workspace") and module.is_workspace():
            # Get workspace buffers
            self.csm = module.get_submodule(ModuleName.CurrentSM)
            self.non_graph = module.get_submodule(ModuleName.NonGraph)
            self.feel_graph = module.get_submodule(ModuleName.FeelGraph)
            self.goal_graph = module.get_submodule(ModuleName.GoalGraph)
            self.seq_graph = module.get_submodule(ModuleName.SeqGraph)
            self.concent_graph = module.get_submodule(ModuleName.ConcentGraph)
            self.scene_graph = module.get_submodule(ModuleName.SceneGraph)
            self.grammar_graph = module.get_submodule(ModuleName.GrammarGraph)
            self.understand_graph = module.get_submodule(ModuleName.UnderstandGraph)
            self.listen_graph = module.get_submodule(ModuleName.ListenGraph)

            # Get buffer contents
            if self.csm:
                self.csm_ns = self.csm.get_buffer_content(None)
            if self.non_graph:
                self.non_ns = self.non_graph.get_buffer_content(None)
            if self.feel_graph:
                self.feel_ns = self.feel_graph.get_buffer_content(None)
            if self.goal_graph:
                self.goal_ns = self.goal_graph.get_buffer_content(None)
            if self.seq_graph:
                self.seq_ns = self.seq_graph.get_buffer_content(None)
            if self.concent_graph:
                self.con_ns = self.concent_graph.get_buffer_content(None)
            if self.scene_graph:
                self.scene_ns = self.scene_graph.get_buffer_content(None)
            if self.grammar_graph:
                self.yufa_ns = self.grammar_graph.get_buffer_content(None)
            if self.understand_graph:
                self.yuyi_ns = self.understand_graph.get_buffer_content(None)
            if self.listen_graph:
                self.listen_ns = self.listen_graph.get_buffer_content(None)
        else:
            self.logger.error(f"Cannot add module {module}")

    def run(self):
        """Run the PAM module"""
        # Process any pending tasks
        if self.task_spawner:
            self.task_spawner.process_tasks()

        # Decay node activations
        self.memory.decayNodeStructure(1)  # Decay by 1 tick
        self.pam_node_structure.decayNodeStructure(1)  # Decay PAM node structure

    def get_state(self):
        return self.current_cell

    def get_stored_nodes(self):
        return self.memory.getNodes()

    def add_listener(self, listener):
        """Add a listener to this PAM module"""
        if isinstance(listener, PamListener):
            self.add_pam_listener(listener)
        else:
            self.logger.warning(f"Cannot add listener type {listener} to this module at tick {TaskManager.get_current_tick()}")

    def add_pam_listener(self, listener):
        """Add a PAM listener"""
        if listener not in self.pam_listeners:
            self.pam_listeners.append(listener)

    def get_pam_node_structure(self):
        """Get the PAM node structure"""
        return self.pam_node_structure

    def add_default_node(self, label_or_node):
        """Add a default node to PAM."""
        if isinstance(label_or_node, str):
            label = label_or_node
            if label is None:
                self.logger.warning(f"Cannot add a Node to Pam with a null label at tick {TaskManager.get_current_tick()}")
                return None

            node = self.nodes_by_label.get(label)
            if node is not None:
                self.logger.warning(f"A Node with the label {label} already exists in PAM at tick {TaskManager.get_current_tick()}")
            else:
                # 创建一个新节点
                from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
                new_node = NodeImpl(label)
                new_node.set_activation(0.0)
                new_node.set_removal_threshold(0.0)  # 使用正确的方法名
                # 使用addNode_方法添加节点（不复制）
                node = self.pam_node_structure.addNode_(new_node)
                if node is not None:
                    self.nodes_by_label[node.get_node_name()] = node  # 使用正确的方法名
            return node
        elif isinstance(label_or_node, Node):
            node = label_or_node
            if node is None:
                return None

            # 使用Python风格的方法名
            if hasattr(self.pam_node_structure, 'get_node'):
                existing_node = self.pam_node_structure.get_node(node.get_node_id())
            # 兼容Java风格的方法名
            elif hasattr(self.pam_node_structure, 'getNodeByName'):
                existing_node = self.pam_node_structure.getNodeByName(node.get_node_id())
            else:
                self.logger.error("PamNodeStructure has neither get_node nor getNode method")
                return None

            if existing_node is not None:
                return existing_node

            # 使用Python风格的方法名
            if hasattr(self.pam_node_structure, 'add_default_node'):
                added_node = self.pam_node_structure.add_default_node(node)
            # 兼容Java风格的方法名
            elif hasattr(self.pam_node_structure, 'addDefaultNode'):
                added_node = self.pam_node_structure.addDefaultNode(node)
            else:
                self.logger.error("PamNodeStructure has neither add_default_node nor addDefaultNode method")
                return None
            if added_node is not None and hasattr(added_node, "get_node_name") and added_node.get_node_name():
                self.nodes_by_label[added_node.get_node_name()] = added_node
            return added_node
        return None

    def add_node(self, type_name, label):
        """Add a node of the specified type with the specified label."""
        if label is None:
            self.logger.warning(f"Cannot add a Node to Pam with a null label at tick {TaskManager.get_current_tick()}")
            return None

        node = self.nodes_by_label.get(label)
        if node is not None:
            self.logger.warning(f"A Node with the label {label} already exists in PAM at tick {TaskManager.get_current_tick()}")
        else:
            node = self.pam_node_structure.addNode(type_name, label, 0.0, 0.0)
            if node is not None:
                self.nodes_by_label[node.get_node_name()] = node
        return node

    def add_default_link(self, src, snk, cat):
        """Add a default link between the source and sink with the specified category."""
        if cat is None:
            self.logger.warning(f"Cannot add new Link. Category is null at tick {TaskManager.get_current_tick()}")
            return None

        # 创建一个临时Link对象
        from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkImpl import LinkImpl
        temp_link = LinkImpl()
        temp_link.set_source(src)
        temp_link.set_sink(snk)
        temp_link.set_category(cat)

        # 使用Python风格的方法名
        if hasattr(self.pam_node_structure, 'add_default_link'):
            return self.pam_node_structure.add_default_link(temp_link)
        # 兼容Java风格的方法名
        elif hasattr(self.pam_node_structure, 'addDefaultLink'):
            return self.pam_node_structure.addDefaultLink(src, snk, cat, 1.0, 0.0)
        else:
            self.logger.error("PamNodeStructure has neither add_default_link nor addDefaultLink method")
            return None

    def add_to_percept(self, node_structure):
        """Add a node structure to the percept"""
        converted_ns = self._convert_node_structure(node_structure)

        for listener in self.pam_listeners:
            listener.receive_percept(converted_ns)

    def _convert_node_structure(self, node_structure):
        """Convert a node structure to a format suitable for PAM"""
        converted_ns = NodeStructureImpl()

        # Convert nodes
        for node in node_structure.getNodes():
            converted_type = self.type_conversion_map.get(node.getType())
            if converted_type is None:
                converted_type = "PamNodeImpl"
            node.set_activation(node.getActivation())
            converted_ns.addNode(node, converted_type)

        # Convert links
        for link in node_structure.getLinks():
            converted_type = self.type_conversion_map.get(link.getType())
            if converted_type is None:
                converted_type = "PamLinkImpl"
            link.set_activation(link.getActivation())
            converted_ns.add_link(link, converted_type)

        return converted_ns

    def propagate_activation_to_parents0(self, pn, deep=0, from0="pam"):
        """Propagate activation from a node to its parents"""
        # Implementation of PamImpl0.propagateActivationToParents
        pname = pn.get_node_name()
        sname = ""
        sink = None
        source = None
        deep += 1
        deep_threshold = 6
        self.put_map(pn, pname)
        tense = None

        # Handle different sources of activation
        if from0 in ["listen", "see", "touch", "smell", "taste", "feel", "get"]:
            tense = Tense.Present if NARS_AVAILABLE else None
            if self.nar:
                self.nar.add_input(f"{pname}. :|:")
                self.nar.cycles(1)

        # Check depth threshold
        if deep > deep_threshold:
            return

        # Get parent links
        parent_link_set = self.pam_node_structure.getConnectedSinks(pn)

        # Process each parent link
        for parent in parent_link_set:
            sink = parent.getSink()
            source = parent.getSource()
            sname = sink.get_node_name() if hasattr(sink, "get_node_name") else ""

            # Avoid circular activation
            if pn.get_fromnodeid() == sink.get_node_id():
                continue

            self.put_map(sink, sname)
            link_type = parent.getCategory()["label"] if isinstance(parent.getCategory(), dict) else parent.getCategory()

            # Calculate propagation amount
            self.propagate_params["upscale"] = self.upscale_factor
            self.propagate_params["totalActivation"] = pn.getActivation()
            amount_to_propagate = self.propagation_strategy.get_activation_to_propagate(self.propagate_params)

            # Set source node ID to avoid circular activation
            parent.getSource().set_fromnodeid(pn.get_node_id())

            # Add to PAM node structure
            self.pam_node_structure.addNode(sink, "PamNodeImpl")
            self.pam_node_structure.add_link(parent, "PamLinkImpl")

            # Notify listeners
            for listener in self.pam_listeners:
                listener.receive_percept(pn, ModuleName.CurrentSM)
                listener.receive_percept(sink, ModuleName.CurrentSM)
                listener.receive_percept(parent, ModuleName.CurrentSM)

                # Add to NARS memory if available
                if self.nar and hasattr(self.nar, "memory"):
                    self.nar.memory.add_default_node(pn)
                    self.nar.memory.add_default_node(sink)
                    self.nar.memory.add_default_link(parent)

            # Set source information
            sink.set_fromsceneid(pn.get_fromnodeid())
            sink.set_fromnodeid(pn.get_node_id())
            sink.set_from_link_type(link_type)

            # Check if term is in memory for compound terms
            all_in_mem = True
            term = None
            if NARS_AVAILABLE:
                try:
                    # Parse term using Narsese
                    if hasattr(self.nar, "narsese") and hasattr(self.nar.narsese, "parse_term"):
                        term = self.nar.narsese.parse_term(sname)

                        # Check if compound term components are in memory
                        if isinstance(term, CompoundTerm):
                            all_in_mem = self.is_all_in_mem(term, all_in_mem, [])
                        elif self.nar.memory.get_node(str(term)) is None:
                            all_in_mem = False
                except Exception as e:
                    self.logger.error(f"Error parsing term: {e}")

            # Propagate activation if all components are in memory
            if all_in_mem:
                self.propagate_activation(sink, parent, amount_to_propagate, deep, "pam")
            elif deep != deep_threshold:
                # Propagate one more level
                self.propagate_activation(sink, parent, amount_to_propagate, deep_threshold - 2, "pam")

    def is_all_in_mem(self, ct, all_in_mem, term_name_list):
        """Check if all components of a compound term are in memory"""
        if not NARS_AVAILABLE:
            return all_in_mem

        term_names = set()
        # Get all term names from the compound term
        if hasattr(ct, "get_term_names"):
            term_names = ct.get_term_names(term_names)
        elif hasattr(ct, "term"):
            # Extract term names manually
            for sub_term in ct.term:
                if hasattr(sub_term, "name"):
                    term_names.add(sub_term.name)
                elif hasattr(sub_term, "__str__"):
                    term_names.add(str(sub_term))

        # Check if each term is in memory
        for term_name in term_names:
            if not term_name.startswith("$") and not term_name.startswith("#") and term_name != "SELF" and term_name != "happy":
                if self.nar and hasattr(self.nar, "memory") and self.nar.memory.get_node(term_name) is None and term_name != "听到" and not "^" in term_name and term_name != "听到1" and term_name != "回应" and term_name != "不开心" and term_name != "不说":
                    all_in_mem = False
                    break
                else:
                    term_name_list.append(term_name)

        return all_in_mem

    def propagate_activation(self, linkable, link, amount, deep=0, from0=None):
        """Propagate activation from a linkable to its connections"""
        # Create a propagation task
        task = PropagationTask(self.propagation_task_ticks_per_run, link, amount, self, deep, from0)
        if self.task_spawner:
            self.task_spawner.add_task(task)

    def put_map(self, node, name):
        """Add node to appropriate maps based on its labels"""
        if not hasattr(node, "getLabels"):
            return

        for label in node.getLabels():
            # Process different node types
            if label == "具身动作":
                core = 1
                if hasattr(node, "getProperty") and node.getProperty("core"):
                    core_val = node.getProperty("core")
                    if isinstance(core_val, str):
                        core = int(core_val)
                    else:
                        core = int(core_val)
                # Store in appropriate map if available
                if hasattr(self, "act_map"):
                    self.act_map[name] = core
            elif label == "心理动作" and hasattr(self, "mind_act_map"):
                self.mind_act_map[name] = 1
            elif label == "场景" and hasattr(self, "scene_map"):
                self.scene_map[name] = 1
            # Add more label types as needed

    def add_link(self, type_name, src, snk, cat):
        """Add a link of the specified type between the source and sink with the specified category."""
        if cat is None:
            self.logger.warning(f"Cannot add new Link. Category is null at tick {TaskManager.get_current_tick()}")
            return None

        return self.pam_node_structure.add_link(type_name, src, snk, cat, 0.0, 0.0)

    def excite(self, object_name, amount, from0="pam"):
        """Excite a node by its name."""
        linkable = self.get_node(object_name)

        if linkable is None:
            # Try to get node from Neo4j
            linkable = self.pam_node_structure.getNeoNode(object_name)

            if linkable is not None:
                # First time execution
                self.add_default_node(linkable)

        self.receive_excitation(linkable, amount, from0)

    def receive_excitation(self, linkable, amount, from0="pam"):
        """Receive excitation for a linkable object"""
        if linkable is None:
            self.logger.warning("Received null Linkable in receiveExcitation")
            return

        if hasattr(linkable, "isPamLink") and linkable.isPamLink():
            self.logger.warning("Does not support pam links yet")
            return

        # Get the node from pam node structure
        pam_node = self.pam_node_structure.get_node(linkable.getId()) if hasattr(linkable, "getId") else None

        if pam_node is not None:
            self.active(amount, from0, pam_node)
        else:
            # Add node to PAM node structure
            self.add_default_node(linkable)
            self.active(amount, from0, linkable)

    def receive_excitation_set(self, linkables, amount, from0="pam"):
        """Receive excitation for a set of linkable objects

        Args:
            linkables: Set of linkable objects
            amount: Amount of activation
            source: Source of excitation
        """
        if linkables is None:
            self.logger.warning("Received null linkables set in receiveExcitation")
            return

        for linkable in linkables:
            self.receive_excitation(linkable, amount, from0)

    def active(self, amount, from0, linkable):
        """Activate a node"""
        if linkable is None:
            self.logger.warning("linkable is None")
            return

        # Add site information if environment is available
        self._add_site(linkable)

        # Get weight property
        weight = 1.0
        weight_prop = linkable.getProperty("weight") if hasattr(linkable, "getProperty") else None

        if weight_prop is not None:
            if isinstance(weight_prop, float):
                weight = weight_prop
            elif isinstance(weight_prop, int):
                weight = float(weight_prop)
            elif isinstance(weight_prop, str):
                try:
                    weight = float(weight_prop)
                except ValueError:
                    pass

        # Set activation
        linkable.set_activation(weight * amount)

        # Create excitation task
        task = ExcitationTask(self.excitation_task_ticks_per_run, linkable, amount, self, from0)
        if self.task_spawner:
            self.task_spawner.add_task(task)

    def _add_site(self, linkable):
        """Add site information to a node"""
        if not self.environment or not hasattr(linkable, "setLocation"):
            return

        try:
            # Get agent position from Environment
            world = self.environment.get_module_content()
            agent = world.get_object("agent")
            direction = agent.get_attribute("direction")

            cell = agent.get_container()
            x = cell.get_x_coordinate()
            y = cell.get_y_coordinate()
            x0, y0 = x, y

            # Adjust coordinates based on direction
            if direction == 'N':
                y -= 1
            elif direction == 'S':
                y += 1
            elif direction == 'E':
                x += 1
            elif direction == 'W':
                x -= 1

            # Set location based on node type
            name = linkable.get_node_name() if hasattr(linkable, "get_node_name") else ""

            if name == "rockFront":
                if 0 <= x < world.get_width() and 0 <= y < world.get_height():
                    linkable.setLocation(f"{x}_{y}")
                else:
                    linkable.setLocation(f"{x0}_{y0}")
            else:
                linkable.setLocation(f"{x0}_{y0}")
        except Exception as e:
            self.logger.error(f"Error adding site information: {e}")

    def get_node(self, label):
        """Get a node by its label."""
        return self.nodes_by_label.get(label)

    def get_link(self, id_obj):
        """Get a link by its ID."""
        return self.pam_node_structure.getLink(id_obj)

    def get_nodes(self):
        """Get all nodes in PAM."""
        return self.pam_node_structure.getNodes()

    def get_links(self):
        """Get all links in PAM."""
        return self.pam_node_structure.getLinks()

    def get_environment(self):
        """Get the environment."""
        return self.environment

    def get_global_workspace(self):
        """Get the global workspace."""
        return self.global_workspace

    def get_workspace_buffer(self, name):
        """Get a workspace buffer by name."""
        if name == "csm":
            return self.csm
        elif name == "goal":
            return self.goal_graph
        elif name == "non":
            return self.non_graph
        elif name == "seq":
            return self.seq_graph
        elif name == "scene":
            return self.scene_graph
        elif name == "con":
            return self.concent_graph
        elif name == "feel":
            return self.feel_graph
        elif name == "yufa":
            return self.grammar_graph #包括理解语法树图和生成语法树图
        elif name == "yuyi":
            return self.understand_graph # 包括理解语义树图和生成语义树图
        elif name == "listen":
            return self.listen_graph
        else:
            return self.non_graph

    def add_default_link_obj(self, link):
        """
        Add a default link to PAM.

        Args:
            link: The link to add

        Returns:
            The new PamLink, the existing PamLink, or None
        """
        if link is None:
            return None

        # 使用链接自带的源、汇和类别
        return self.add_default_link(link.get_source(), link.get_sink(), link.get_category())

    def add_to_percept(self, node_structure):
        """
        Add a node structure to PAM as a percept.

        Args:
            node_structure: The node structure to add
        """
        if node_structure is None:
            self.logger.warning("Cannot add null NodeStructure to PAM")
            return

        # 如果传入的是一个Node对象，而不是NodeStructure对象
        from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
        from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl

        if isinstance(node_structure, Node):
            # 直接添加该节点
            self.add_default_node(node_structure)
            self.logger.info(f"Added node {node_structure} to PAM")
            return self.csm

        # 如果传入的是NodeStructure对象
        try:
            # 检查是否有get_nodes方法
            if hasattr(node_structure, 'get_nodes'):
                # 添加节点
                for node in node_structure.get_nodes():
                    self.add_default_node(node)

                # 检查是否有get_links方法
                if hasattr(node_structure, 'get_links'):
                    # 添加连接
                    for link in node_structure.get_links():
                        self.add_default_link_obj(link)

                self.logger.info(f"Added {len(node_structure.get_nodes())} nodes and {len(node_structure.get_links()) if hasattr(node_structure, 'get_links') else 0} links to PAM")
            else:
                # 如果没有get_nodes方法，尝试其他方法
                if hasattr(node_structure, 'getNodes'):
                    # 添加节点
                    for node in node_structure.getNodes():
                        self.add_default_node(node)

                    # 检查是否有getLinks方法
                    if hasattr(node_structure, 'getLinks'):
                        # 添加连接
                        for link in node_structure.getLinks():
                            self.add_default_link_obj(link)

                    self.logger.info(f"Added {len(node_structure.getNodes())} nodes and {len(node_structure.getLinks()) if hasattr(node_structure, 'getLinks') else 0} links to PAM")
                else:
                    # 如果既没有get_nodes也没有getNodes方法，尝试将其作为单个节点处理
                    self.add_default_node(node_structure)
                    self.logger.info(f"Added object {node_structure} as a node to PAM")
        except Exception as e:
            self.logger.error(f"Error adding percept to PAM: {e}")
            import traceback
            traceback.print_exc()
            return self.csm

        # 返回当前感知图
        return self.csm

    def set_propagation_strategy(self, strategy):
        """Set the propagation strategy."""
        self.propagation_strategy = strategy

    def get_propagation_strategy(self):
        """Get the propagation strategy."""
        return self.propagation_strategy

    def is_over_percept_threshold(self, linkable):
        """Returns whether Linkable is above percept threshold.

        Args:
            linkable: A Linkable

        Returns:
            True if Linkable's total activation is above percept threshold
        """
        if linkable is None:
            return False

        if hasattr(linkable, "get_total_activation"):
            return linkable.get_total_activation() > self.percept_threshold
        elif hasattr(linkable, "getTotalActivation"):
            return linkable.getTotalActivation() > self.percept_threshold
        else:
            # Fallback to activation if total_activation is not available
            if hasattr(linkable, "get_activation"):
                return linkable.get_activation() > self.percept_threshold
            elif hasattr(linkable, "getActivation"):
                return linkable.getActivation() > self.percept_threshold
            else:
                return False

    def set_percept_threshold(self, threshold):
        """Sets the percept threshold.

        Args:
            threshold: Threshold for a Linkable to become part of the percept
        """
        if 0.0 <= threshold <= 1.0:
            self.percept_threshold = threshold
        else:
            self.logger.warning(f"Percept threshold must be in range [0.0, 1.0]. Threshold will not be modified. Tick: {TaskManager.get_current_tick()}")

    def get_percept_threshold(self):
        """Gets the percept threshold.

        Returns:
            The percept threshold
        """
        return self.percept_threshold

    def set_upscale_factor(self, factor):
        """Sets the upscale factor.

        Args:
            factor: Scale factor for feed-forward activation propagation
        """
        if factor < 0.0:
            self.upscale_factor = 0.0
        elif factor > 1.0:
            self.upscale_factor = 1.0
        else:
            self.upscale_factor = factor

    def get_upscale_factor(self):
        """Gets the upscale factor.

        Returns:
            Scale factor for feed-forward activation propagation
        """
        return self.upscale_factor

    def set_downscale_factor(self, factor):
        """Sets the downscale factor.

        Args:
            factor: Scale factor for top-down activation propagation
        """
        if factor < 0.0:
            self.downscale_factor = 0.0
        elif factor > 1.0:
            self.downscale_factor = 1.0
        else:
            self.downscale_factor = factor

    def get_downscale_factor(self):
        """Gets the downscale factor.

        Returns:
            Scale factor for top-down activation propagation
        """
        return self.downscale_factor

    def get_listener(self):
        """Get the first PAM listener."""
        if self.pam_listeners:
            return self.pam_listeners[0]
        return None

    def get_act_root(self, link, is_var=False, is_loop=False, act_stamp=""):
        """Get the action root for a link.

        Args:
            link: The link to process
            is_var: Whether this is a variable
            is_loop: Whether this is a loop
            act_stamp: The action stamp
        """
        # This is a placeholder implementation
        self.logger.debug(f"getActRoot called with link: {link}, isVar: {is_var}, isLoop: {is_loop}, actStamp: {act_stamp}")
        pass

    def get_act_root0(self, link, is_var=False):
        """Get the action root for a link (simplified version).

        Args:
            link: The link to process
            is_var: Whether this is a variable
        """
        # This is a placeholder implementation
        self.logger.debug(f"getActRoot0 called with link: {link}, isVar: {is_var}")
        pass

    def put_map(self, node, name):
        """Add node to appropriate maps based on its labels"""
        if not hasattr(node, "getLabels"):
            return

        for label in node.getLabels():
            # Process different node types
            if label == "具身动作":
                core = 1
                if hasattr(node, "getProperty") and node.getProperty("core"):
                    core_val = node.getProperty("core")
                    if isinstance(core_val, str):
                        core = int(core_val)
                    else:
                        core = int(core_val)
                # Store in appropriate map if available
                if hasattr(self, "act_map"):
                    self.act_map[name] = core
            elif label == "心理动作" and hasattr(self, "mind_act_map"):
                self.mind_act_map[name] = 1
            elif label == "场景" and hasattr(self, "scene_map"):
                self.scene_map[name] = 1
            # Add more label types as needed

    def set_scene_main_node(self, node):
        """Set the main node for a scene.

        Args:
            node: The node to set as main scene node
        """
        # This is a placeholder implementation
        self.logger.debug(f"setSceneMainNode called with node: {node}")
        pass

    def get_isa_link(self, node, to_node, category, pam):
        """Get an 'is-a' link between nodes.

        Args:
            node: The source node
            to_node: The target node
            category: The link category
            pam: The PAM instance

        Returns:
            A map containing the link information
        """
        # This is a placeholder implementation
        self.logger.debug(f"getIsaLink called with node: {node}, toNode: {to_node}, category: {category}")
        return {}

    def activ_grammar_link(self, link, retype):
        """Activate a grammar link.

        Args:
            link: The link to activate
            retype: The retype information
        """
        # This is a placeholder implementation
        self.logger.debug(f"activGrammarLink called with link: {link}, retype: {retype}")
        pass

    def get_scene_node(self, scene, name, b):
        """Get a scene node.

        Args:
            scene: The scene node
            name: The name of the node
            b: A boolean flag
        """
        # This is a placeholder implementation
        self.logger.debug(f"getSceneNode called with scene: {scene}, name: {name}, b: {b}")
        pass

    def receive_broadcast(self, coalition):
        """Receive a broadcast from the Global Workspace.

        Args:
            coalition: The coalition that was broadcast
        """
        self.learn(coalition)

    def notify(self, module):
        if isinstance(module, SensoryMemoryImpl):
            cue = module.get_sensory_content(module)
            self.position = cue["params"]["position"]
            self.learn(cue)
        elif isinstance(module, WorkspaceImpl):
            # Get buffer content from workspace
            cue = module.get_buffer_content(ModuleName.CurrentSM)
            if cue is not None:
                # Check if cue has links
                if hasattr(cue, 'getLinks') and callable(getattr(cue, 'getLinks')):
                    links = cue.getLinks()
                    if links:
                        self.logger.debug(f"Cue received from Workspace, forming associations with {len(links)} links")
                        self.learn(links)
                # Check if cue has nodes
                if hasattr(cue, 'getNodes') and callable(getattr(cue, 'getNodes')):
                    nodes = cue.getNodes()
                    if nodes:
                        self.logger.debug(f"Cue received from Workspace, forming associations with {len(nodes)} nodes")
                        self.learn(nodes)
        elif isinstance(module, GlobalWorkspaceImpl):
            winning_coalition = module.get_broadcast()
            broadcast = winning_coalition.getContent()
            self.logger.debug(
                f"Received conscious broadcast: {broadcast}")

            """Get the nodes that have been previously visited and update
                        the connected sink links"""
            links = []
            for link in broadcast.getLinks():
                source = link.getSource()
                if isinstance(source, NodeImpl):
                    if link.getSource().getActivation() < 1:
                        links.append(link)
                else:
                    source_node = broadcast.containsNode(source)
                    if isinstance(source_node, NodeImpl):
                        if link.getSource().getActivation() < 1:
                            links.append(link)
            self.learn(links)

    def learn(self, cue):
        #Check all cells for the corresponding node
        for node in self.memory.getNodes():
            if (node.getActivation() is not None and
                                            node.getActivation() >= 0.01):
                node.decay(0.01)
                if node.isRemovable():
                    self.associations.remove(node)
            """If the result of the function to obtain the cell state
            equals the node id, activate the corresponding node"""
            if self.position["row"] * 4 + self.position["col"] == node.getId():
                if node.getActivation() is None:
                    node.set_activation(1.0)
                    node.setLabel(str(self.position["row"]) +
                                  str(self.position["col"]))

                """Considering the current cell node as the percept
                i.e agent recognizing position within environment"""
                self.current_cell = node
                self.add_association(self.current_cell)
        if isinstance(cue, list):
            self.form_associations(cue)
        else:
            self.form_associations(cue["cue"])

    def form_associations(self, cue):
        # Set links to surrounding cell nodes if none exist
        for link in cue:
            # Priming data for scheme instantiation
            if link.getCategory("label") == 'S':
                link.setCategory(link.getCategory("id"), "start")
                link.set_activation(0.5)
                link.setIncentiveSalience(0.3)
            elif link.getCategory("label") == 'G':
                link.setCategory(link.getCategory("id"), "goal")
                link.set_activation(1.0)
                link.setIncentiveSalience(1.0)
            elif link.getCategory("label") == 'F':
                link.setCategory(link.getCategory("id"), "safe")
                link.set_activation(0.75)
                link.setIncentiveSalience(0.5)
            elif link.getCategory("label") == 'H':
                link.setCategory(link.getCategory("id"), "hole")
                link.set_activation(0.1)
            else:
                if (link.getActivation() == 0.0 and
                        link.getIncentiveSalience() == 0.0):
                    link.set_activation(0.1)

            link.setSource(self.current_cell)

            # Add links to surrounding cells
            if link not in self.associations.getLinks():
                self.associations.addDefaultLink(link.getSource(), link,
                            category = {"id": link.getCategory("id"),
                                        "label": link.getCategory("label")},
                                            activation=link.getActivation(),
                                            removal_threshold=0.0)

        self.notify_observers()
