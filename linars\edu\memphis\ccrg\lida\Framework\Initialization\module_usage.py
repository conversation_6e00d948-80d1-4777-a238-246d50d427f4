"""
Module usage definition.

This module provides a class for defining module usage.
"""
from typing import Dict, Any, Optional

class ModuleUsage:
    """
    Module usage definition.
    
    This class defines how a module is used by another module.
    """
    
    # Default module usage
    DEFAULT_USAGE = "default"
    
    def __init__(self, source_name: str, listener_name: str, module_usage: str = DEFAULT_USAGE):
        """
        Initialize the module usage.
        
        Args:
            source_name: The name of the source module
            listener_name: The name of the listener module
            module_usage: How the listener uses the source
        """
        self.source_name = source_name
        self.listener_name = listener_name
        self.module_usage = module_usage
    
    def get_source_name(self) -> str:
        """
        Get the name of the source module.
        
        Returns:
            The name of the source module
        """
        return self.source_name
    
    def get_listener_name(self) -> str:
        """
        Get the name of the listener module.
        
        Returns:
            The name of the listener module
        """
        return self.listener_name
    
    def get_module_usage(self) -> str:
        """
        Get how the listener uses the source.
        
        Returns:
            How the listener uses the source
        """
        return self.module_usage
