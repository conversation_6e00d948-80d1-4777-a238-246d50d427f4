"""
Move operator implementation.

This module provides the implementation of the move operator for the ALife agent.
"""
from typing import List, Optional, TYPE_CHECKING

from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.entity.task import Task
from linars.org.opennars.interfaces.timable import Timable
from linars.org.opennars.operator.operation import Operation
from linars.org.opennars.operator.operator import Operator

if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.linars.memory import Memory

class Move(Operator):
    """
    Move operator implementation.

    This class implements the move operator, which makes the agent move forward.
    """

    def __init__(self):
        """Initialize the move operator."""
        super().__init__("^move")

    def execute(self, operation: Operation, args: List[Term], memory, time: Timable) -> Optional[List[Task]]:
        """
        Execute the move operator.

        Args:
            operation: The operation to execute
            args: Arguments for the operation
            memory: The memory in which the operation is executed
            time: The time

        Returns:
            None, as this operator doesn't produce tasks
        """
        # Execute the move operation in the environment
        memory.emit_operator("move")

        return None
