"""
NARS通道
实现输入输出数据的缓冲和处理
"""
from typing import Dict, Optional

from linars.org.opennars.storage.bag1 import Bag1
from linars.org.opennars.main.parameters import Parameters
from linars.org.opennars.operator.operation import Operation

class Channel(Bag1):
    """
    NARS通道
    继承自Bag1，提供输入输出数据的缓冲和处理功能
    """

    def __init__(self, nar=None, levels=1, capacity=100):
        """
        构造函数

        参数:
            nar: NARS系统实例(可选)
            levels: 通道层级数
            capacity: 通道容量
        """
        parameters = Parameters() if nar is None else nar.narParameters
        super().__init__(capacity)
        self.nar = nar
        self.operations = {}  # Dictionary of operations
