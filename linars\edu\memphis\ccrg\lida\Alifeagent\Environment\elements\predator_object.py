"""
ALife环境中的捕食者对象实现

本模块提供ALife环境中捕食者对象的实现
"""
from linars.edu.memphis.ccrg.alife.elements.alife_object_impl import ALifeObjectImpl

class PredatorObject(ALifeObjectImpl):
    """
    捕食者对象实现

    该类表示ALife环境中的捕食者
    """

    def __init__(self):
        """初始化捕食者对象"""
        super().__init__()
        self.set_name("predator")

    def get_icon_id(self) -> int:
        """
        获取捕食者的图标ID

        返回:
            图标ID
        """
        return 2  # 捕食者图标ID
