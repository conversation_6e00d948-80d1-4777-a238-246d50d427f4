"""
真值是由NARS理论定义的频率和置信度的元组
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar
import math

from linars.edu.memphis.ccrg.linars.term import Term

# Symbols
TRUTH_VALUE_MARK = '%'
VALUE_SEPARATOR = ';'

class TruthValue:
    """
    真值是由NARS理论定义的频率和置信度的元组
    """

    # Static terms
    Truth_TRUE = Term("TRUE")
    Truth_FALSE = Term("FALSE")
    Truth_UNSURE = Term("UNSURE")

    def __init__(self, f: float = 0.0, c: float = 0.0, is_analytic: bool = False, narParameters = None):
        """
        构造函数

        参数:
            f: 频率值
            c: 置信度值
            is_analytic: 是否为分析性真值?
            narParameters: 推理机参数
        """
        self.narParameters = narParameters
        self.frequency = 0.0
        self.confidence = 0.0
        self.analytic = False

        self.set_frequency(f)
        self.set_confidence(c)
        self.set_analytic(is_analytic)

    @classmethod
    def from_truth(cls, v: 'TruthValue') -> 'TruthValue':
        """
        使用要克隆的真值构造

        参数:
            v: 要克隆的真值

        返回:
            TruthValue: 克隆后的真值
        """
        truth = cls()
        truth.narParameters = v.narParameters
        truth.frequency = v.get_frequency()
        truth.confidence = v.get_confidence()
        truth.analytic = v.get_analytic()
        return truth

    def get_frequency(self) -> float:
        """
        返回频率值

        返回:
            float: 频率值
        """
        return self.frequency

    def get_confidence(self) -> float:
        """
        返回置信度值

        返回:
            float: 置信度值
        """
        return self.confidence

    def set_frequency(self, f: float) -> 'TruthValue':
        """
        设置频率值

        参数:
            f: 频率值

        返回:
            TruthValue: 自身
        """
        self.frequency = f
        return self

    def set_confidence(self, c: float) -> 'TruthValue':
        """
        设置置信度值

        参数:
            c: 置信度值

        返回:
            TruthValue: 自身
        """
        if self.narParameters:
            max_confidence = 1.0 - self.narParameters.TRUTH_EPSILON
            self.confidence = min(c, max_confidence)
        else:
            self.confidence = min(c, 0.99)
        return self

    def mul_confidence(self, mul: float) -> 'TruthValue':
        """
        乘以置信度值

        参数:
            mul: 乘数

        返回:
            TruthValue: 自身
        """
        if self.narParameters:
            max_confidence = 1.0 - self.narParameters.TRUTH_EPSILON
            c = self.confidence * mul
            self.confidence = min(c, max_confidence)
        else:
            c = self.confidence * mul
            self.confidence = min(c, 0.99)
        return self

    def get_analytic(self) -> bool:
        """
        检查是否为分析性真值

        返回:
            bool: 如果是分析性则为True
        """
        return self.analytic

    def set_analytic(self, a: bool = True) -> 'TruthValue':
        """
        设置为分析性真值

        参数:
            a: 是否为分析性

        返回:
            TruthValue: 自身
        """
        self.analytic = a
        return self

    def get_expectation(self) -> float:
        """
        计算真值的期望值

        返回:
            float: 期望值
        """
        return self.confidence * (self.frequency - 0.5) + 0.5

    def get_exp_dif_abs(self, t: 'TruthValue') -> float:
        """
        计算期望值与给定真值的绝对差

        参数:
            t: 给定值

        返回:
            float: 绝对差
        """
        return abs(self.get_expectation() - t.get_expectation())

    def is_negative(self) -> bool:
        """
        检查真值是否为负

        返回:
            bool: 如果频率小于1/2则为True
        """
        return self.get_frequency() < 0.5

    @staticmethod
    def is_equal(a: float, b: float, epsilon: float) -> bool:
        """
        检查两个浮点值在epsilon范围内是否相等

        参数:
            a: 第一个值
            b: 第二个值
            epsilon: epsilon值

        返回:
            bool: 如果在epsilon范围内相等则为True
        """
        d = abs(a - b)
        return d < epsilon

    def __eq__(self, other: Any) -> bool:
        """
        比较两个真值

        参数:
            other: 其他对象

        返回:
            bool: 两者是否等价
        """
        if isinstance(other, TruthValue):
            epsilon = self.narParameters.TRUTH_EPSILON if self.narParameters else 0.01
            return (self.is_equal(self.get_frequency(), other.get_frequency(), epsilon) and
                   self.is_equal(self.get_confidence(), other.get_confidence(), epsilon))
        return False

    def __hash__(self) -> int:
        """
        真值的哈希码

        返回:
            int: 哈希码
        """
        return ((int(0xFFFF * self.frequency) << 16) | int(0xFFFF * self.confidence))

    def clone(self) -> 'TruthValue':
        """
        克隆真值

        返回:
            TruthValue: 克隆后的真值
        """
        return TruthValue(self.frequency, self.confidence, self.get_analytic(), self.narParameters)

    def append_string(self, sb: str, external: bool) -> str:
        """
        将字符串表示附加到字符串构建器

        参数:
            sb: 字符串构建器
            external: 是否用于外部表示

        返回:
            str: 附加后的字符串
        """
        def n2(x: float) -> str:
            return f"{x:.2f}"

        return f"{sb}{TRUTH_VALUE_MARK}{n2(self.frequency)}{VALUE_SEPARATOR}{n2(self.confidence)}{TRUTH_VALUE_MARK}"

    def name(self) -> str:
        """
        获取真值的名称

        返回:
            str: 名称
        """
        return self.append_string("", False)

    def to_string_external(self) -> str:
        """
        获取外部字符串表示

        返回:
            str: 外部字符串表示
        """
        return self.append_string("", True)

    def __str__(self) -> str:
        """
        返回系统内部使用的真值字符串表示

        返回:
            str: 字符串表示
        """
        return self.name()

    def to_word_term(self) -> Term:
        """
        转换为词项

        返回:
            Term: 词项
        """
        e = self.get_expectation()

        if self.narParameters:
            t = self.narParameters.DEFAULT_CREATION_EXPECTATION
        else:
            t = 0.66

        if e > t:
            return self.Truth_TRUE
        if e < 1 - t:
            return self.Truth_FALSE

        return self.Truth_UNSURE

    @staticmethod
    def from_word_term(narParameters, term: Term) -> Optional['TruthValue']:
        """
        从词项创建真值

        参数:
            narParameters: 推理机参数
            term: 词项

        返回:
            TruthValue: 创建的真值
        """
        if term == TruthValue.Truth_TRUE:
            return TruthValue(1.0, narParameters.DEFAULT_JUDGMENT_CONFIDENCE, False, narParameters)
        elif term == TruthValue.Truth_FALSE:
            return TruthValue(0.0, narParameters.DEFAULT_JUDGMENT_CONFIDENCE, False, narParameters)
        elif term == TruthValue.Truth_UNSURE:
            return TruthValue(0.5, narParameters.DEFAULT_JUDGMENT_CONFIDENCE / 2.0, False, narParameters)
        else:
            return None

    def set(self, frequency: float, confidence: float) -> 'TruthValue':
        """
        设置频率和置信度

        参数:
            frequency: 频率值
            confidence: 置信度值

        返回:
            TruthValue: 自身
        """
        self.set_frequency(frequency)
        self.set_confidence(confidence)
        return self
