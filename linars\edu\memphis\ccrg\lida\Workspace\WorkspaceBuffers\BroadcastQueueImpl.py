# LIDA Cognitive Framework
"""
The BroadcastQueue is the data structure storing the recent contents of
consciousness. It is a submodule of the Workspace. There is a limit on the
queue's capacity and on the amount of activation Linkables must have
to remain in the queue.
"""

import logging
from typing import List, Dict, Any, Optional, Iterator
from collections import deque
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.UnmodifiableNodeStructureImpl import UnmodifiableNodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceContent import WorkspaceContent
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.BroadcastQueue import BroadcastQueue

class BroadcastQueueImpl(FrameworkModuleImpl, BroadcastQueue):
    """
    The BroadcastQueue is the data structure storing the recent contents of
    consciousness. It is a submodule of the Workspace. There is a limit on the
    queue's capacity and on the amount of activation Linkables must have
    to remain in the queue.
    """

    DEFAULT_QUEUE_CAPACITY = 30

    def __init__(self):
        """
        Initialize a BroadcastQueueImpl.
        """
        super().__init__()
        self.broadcast_queue_capacity = self.DEFAULT_QUEUE_CAPACITY
        self.is_decay = False
        self.broadcast_queue: deque[WorkspaceContent] = deque(maxlen=self.broadcast_queue_capacity)
        self.big_scene_queue: deque[WorkspaceContent] = deque()
        self.logger = logging.getLogger(self.__class__.__name__)

    def init(self, params=None) -> None:
        """
        Initialize this BroadcastQueueImpl.
        Will set parameters with the following names:
        workspace.broadcastQueueCapacity - the number of recent broadcast maintained in this BroadcastQueue
        """
        self.is_decay = self.get_param("workspace.broadcastQueueDecay", False)
        requested_capacity = self.get_param("workspace.broadcastQueueCapacity", self.DEFAULT_QUEUE_CAPACITY)
        if requested_capacity > 0:
            self.broadcast_queue_capacity = requested_capacity
            self.broadcast_queue = deque(maxlen=self.broadcast_queue_capacity)
        else:
            self.logger.warning(f"Capacity must be greater than 0 at tick {TaskManager.get_current_tick()}")

    def get_param(self, name: str, default_value: Any) -> Any:
        """
        Get a parameter value with a default.

        Args:
            name: The name of the parameter
            default_value: The default value

        Returns:
            The parameter value or the default value
        """
        parameters = getattr(self, "parameters", {})
        if parameters and name in parameters:
            return parameters[name]
        return default_value

    def receive_broadcast(self, coalition: Coalition) -> None:
        """
        Receive a broadcast from the GlobalWorkspace.

        Args:
            coalition: The Coalition that won the competition for consciousness
        """
        content = coalition.get_content()
        if isinstance(content, UnmodifiableNodeStructureImpl):
            # Since content is not modifiable, a copy must be made. In this class
            # the copy (of the content) will be decayed (modified).
            content_copy = NodeStructureImpl()
            content_copy.merge_with(content)
            content_copy.save_scene(content)
            self.add_buffer_content(content_copy)

    def add_buffer_content(self, content: WorkspaceContent) -> None:
        """
        Adds specified content to this workspace buffer.

        Args:
            content: The WorkspaceContent to add
        """
        self.broadcast_queue.appendleft(content)

    def get_buffer_content(self, params: Optional[Dict[str, Any]] = None) -> Optional[WorkspaceContent]:
        """
        Gets buffer content based on specified parameters.

        Args:
            params: Optional parameters to specify what content is returned

        Returns:
            The WorkspaceContent
        """
        if params is not None:
            index = params.get("position")
            if isinstance(index, int):
                return self.get_position_content(index)
        return None

    def get_position_content(self, position: int) -> Optional[WorkspaceContent]:
        """
        Get the content at the specified position in the queue.

        Args:
            position: The position in the queue

        Returns:
            The content at the specified position
        """
        if position > -1 and position < len(self.broadcast_queue):
            if self.broadcast_queue:
                return self.broadcast_queue[position]
        return None

    def get_module_content(self, *params: Any) -> List[WorkspaceContent]:
        """
        Get the content of this module.

        Args:
            params: Parameters specifying what content to return

        Returns:
            The content of this module
        """
        return list(self.broadcast_queue)

    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.

        Args:
            ticks: The number of ticks to decay by
        """
        if self.is_decay:
            self.logger.debug(f"Decaying Broadcast Queue at tick {TaskManager.get_current_tick()}")
            to_remove = []
            for ns in self.broadcast_queue:
                ns.decay_node_structure(ticks)
                if ns.get_node_count() == 0:
                    to_remove.append(ns)
            for ns in to_remove:
                self.broadcast_queue.remove(ns)

    def learn(self, coalition: Coalition) -> None:
        """
        Learn from a coalition.

        Args:
            coalition: The coalition to learn from
        """
        # Not applicable
        pass
