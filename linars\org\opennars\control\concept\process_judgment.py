from typing import Optional

from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.concept import Concept
from linars.edu.memphis.ccrg.linars.memory import Memory
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.control.derivation_context import DerivationContext
from linars.org.opennars.entity.task import Task
from linars.org.opennars.inference.local_rules import LocalRules
from linars.org.opennars.inference.temporal_rules import TemporalRules
from linars.org.opennars.io.events.events import Events
from linars.org.opennars.operator.operation import Operation
from linars.org.opennars.storage.internal_experience_buffer import InternalExperienceBuffer
from linars.org.opennars.language.statement import Statement
from linars.org.opennars.language.conjunction import Conjunction
from linars.org.opennars.language.interval import Interval
from linars.org.opennars.language.implication import Implication

class ProcessJudgment:
    """
    处理判断任务的类
    """

    @staticmethod
    def processJudgment(concept: Concept, nal: DerivationContext, task: Task) -> None:
        """
        接受一个新的判断作为信念，并检查修订和解决方案。
        修订将作为判断任务本身处理。
        由于它们的置信度更高，总结了更多的证据，
        它们将成为信念表中的顶部条目。
        此外，判断本身可以是现有问题和目标的解决方案，这也在这里处理。

        Args:
            concept: 判断任务的概念
            nal: 推导上下文
            task: 要接受的判断任务
        """
        InternalExperienceBuffer.handleOperationFeedback(task, nal)
        judg = task.sentence
        from linars.org.opennars.control.concept.process_anticipation import ProcessAnticipation
        ProcessAnticipation.confirm_anticipation(task, concept, nal)
        oldBeliefT = concept.selectCandidate(task, concept.beliefs, nal.time)  # 只与最强的修订 -- 投影呢？
        oldBelief = None
        if oldBeliefT is not None:
            oldBelief = oldBeliefT.sentence
            newStamp = judg.stamp
            oldStamp = oldBelief.stamp  # 当表格已满时，后面的检查尤为重要
            if newStamp.equals(oldStamp, False, False, True):
                concept.memory.removeTask(task, "Duplicated")
                return
            elif LocalRules.revisible(judg, oldBelief, AgentStarter.nar.narParameters):
                nal.set_the_new_stamp_builder(newStamp, oldStamp, nal.time.time())
                projectedBelief = oldBelief.projection(nal.time.time(), newStamp.get_occurrence_time(), concept.memory)
                if projectedBelief is not None:
                    nal.set_current_belief(projectedBelief)
                    LocalRules.revision(judg, projectedBelief, concept, False, nal)
                    task.set_achievement(LocalRules.calc_task_achievement(task.sentence.truth, projectedBelief.truth))

        if not task.aboveThreshold():
            return

        nnq = len(concept.questions)
        for i in range(nnq):
            LocalRules.try_solution(judg, concept.questions[i], nal, True)

        nng = len(concept.desires)
        for i in range(nng):
            LocalRules.try_solution(judg, concept.desires[i], nal, True)

        concept.add_to_table(task, False, concept.beliefs,
                          AgentStarter.nar.narParameters.CONCEPT_BELIEFS_MAX,
                          Events.ConceptBeliefAdd, Events.ConceptBeliefRemove)

    @staticmethod
    def isExecutableHypothesis(task: Task, nal: DerivationContext) -> bool:
        """
        检查任务是否是可执行假设，形式为 <(&/,a,op()) =/> b>。

        Args:
            task: 要检查的判断任务
            nal: 推导上下文

        Returns:
            任务是否是可执行前提
        """
        subj = ProcessJudgment.getTerm(task)
        if subj is None:
            return False

        # 它也必须是可执行的，这意味着间隔之前序列的最后一个条目是一个操作
        isInExecutableFormat = False
        if isinstance(subj, Conjunction):
            conj = subj
            # 条件很多限制，非空间，但前向，长度大于4，偶数，最后一个是间隔，倒数第二个是操作
            isInExecutableFormat = (not conj.isSpatial and
                                   conj.getTemporalOrder() == TemporalRules.ORDER_FORWARD and
                                   len(conj.term) >= 4 and len(conj.term) % 2 == 0 and
                                   isinstance(conj.term[len(conj.term)-1], Interval) and
                                   isinstance(conj.term[len(conj.term)-2], Operation))
        elif not isinstance(subj, CompoundTerm):
            isInExecutableFormat = "^" in str(subj)

        return isInExecutableFormat

    @staticmethod
    def isBeliefsHypothesis(task: Task, nal: DerivationContext) -> bool:
        """
        检查任务是否是信念假设

        Args:
            task: 要检查的任务
            nal: 推导上下文

        Returns:
            任务是否是信念假设
        """
        subj = ProcessJudgment.getTerm(task)
        if subj is None:
            return False

        # 只要是顺承，不管是可执行还是状态，都加入到目标概念的信念中
        return True

    @staticmethod
    def getTerm(task: Task) -> Optional[Term]:
        """
        获取任务的术语

        Args:
            task: 要获取术语的任务

        Returns:
            术语，如果不符合条件则为None
        """
        term = task.get_term()

        if not isinstance(term, Statement):
            return None

        if isinstance(term, Implication):
            imp = term
            if imp.get_temporal_order() != TemporalRules.ORDER_FORWARD:
                return None

        subj = term.get_subject()
        return subj

    @staticmethod
    def addToTargetConceptsPreconditions(task: Task, memory: Memory) -> None:
        """
        将任务添加到目标概念的前提条件中

        Args:
            task: 要添加的任务
            memory: 内存
        """
        try:
            # 创建目标集合
            targets = set()

            # 获取谓词和主语
            term = task.get_term()
            if not isinstance(term, Statement):
                return

            pred = term.get_predicate()
            subj = term.get_subject()

            # 添加到所有组件，除非它没有变量
            if not pred.has_var():
                # 反推条件，不含变量，是时序路径溯源，结果反推条件。变量语句是部分反推整体
                targets.add(pred)
            else:
                # 非变量组件，关联变量语句，在动机bestReactionForGoal中，根据变量语句的非变量组件，汇总找到可代换的变量语句
                ret = pred.count_term_recursively(None)
                if ret:
                    targets.update(ret.keys())
            term = task.get_term()
            # 后面的这个task.getTerm()是一个Implication，它的subject是一个Conjunction，它的term是一个Operation
            origin_concept = memory.concept(term)
            if origin_concept is None:
                return

            # 获取第一个永恒的。最有信心的一个（由于排序顺序）：词项相同，但数值不同
            strongest_target = None
            with origin_concept:
                for iTask in origin_concept.beliefs:
                    if iTask.sentence.isEternal():
                        strongest_target = iTask
                        break

            if strongest_target is None:
                return

            iii = strongest_target.get_term()
            if isinstance(iii.get_subject(), Conjunction):
                prec0 = iii.get_subject().term
                for i in range(len(prec0) - 2):
                    if isinstance(prec0[i], Operation):
                        # 不要对最后一个之前的具有操作的前提做出反应
                        # 现在，这些可以被分解成更小的语句
                        return

            # 这里天然拆分组件并关联到目标（结论），pam对变量实例化，还需拆分关联。
            for t in targets:
                # 它需要匹配的目标子概念，也就是非变量组件。包括整体结论本身
                target_concept = memory.concept(t)
                if target_concept is None:  # 目标概念不存在
                    continue

                # 我们不添加目标，用目标概念的最强信念代替。词项相同，但数值不同
                with target_concept:
                    hasVar = strongest_target.sentence.term.has_var()
                    table = None
                    if hasVar:
                        # 这里就在追踪溯源，含变量语句每个非变量部分，都与变量语句关联，根据部分来查找判断是否可代换
                        table = target_concept.general_executable_preconditions
                    else:
                        # 不含变量，是时序路径溯源，结果反推条件。变量语句是部分反推整体
                        table = target_concept.executable_preconditions

                    # 首先，我们必须从表中删除具有相同内容的最后一个
                    i_delete = -1
                    for i in range(len(table)):
                        if hasattr(CompoundTerm, "replace_intervals") and CompoundTerm.replace_intervals(table[i].get_term()) == CompoundTerm.replace_intervals(strongest_target.get_term()):
                            # 即使这些术语相同但间隔不同也会在此处删除
                            i_delete = i
                            break

                    if i_delete != -1:
                        table.pop(i_delete)

                    # 这样，这个内容的最强有信心的结果就被放入表中，但表按照真实的期望排名
                    # 这里只增加，顺推激活，可能只占该概念全部组件的一部分，后期需按情况衰减删除，动态调整
                    target_concept.add_to_table(strongest_target, True, table,
                                             AgentStarter.nar.narParameters.CONCEPT_BELIEFS_MAX,
                                             Events.EnactableExplainationAdd, Events.EnactableExplainationRemove)
        except Exception as e:
            print(f"Error in addToTargetConceptsPreconditions: {e}")
            import traceback
            traceback.print_exc()
