# LIDA认知框架
"""
LIDA行为选择模块的规范定义
"""

from abc import abstractmethod
from typing import Collection
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.ActionSelection.ActionSelectionListener import ActionSelectionListener
from linars.edu.memphis.ccrg.lida.ActionSelection.PreafferenceListener import PreafferenceListener
from linars.edu.memphis.ccrg.lida.ActionSelection.Behavior import Behavior

class ActionSelection(FrameworkModule):
    """
    LIDA行为选择模块的规范定义
    """

    @abstractmethod
    def add_action_selection_listener(self, listener: ActionSelectionListener) -> None:
        """
        添加指定的行为选择监听器

        参数:
            listener: 接收来自行为选择模块的选定行为的模块
        """
        pass

    @abstractmethod
    def add_preafference_listener(self, listener: PreafferenceListener) -> None:
        """
        添加指定的预偏好监听器

        参数:
            listener: 接收来自行为选择模块的预偏好的模块
        """
        pass

    @abstractmethod
    def select_behavior(self, behaviors: Collection[Behavior], candidate_threshold: float) -> Behavior:
        """
        选择要执行的行为(包含一个动作)

        参数:
            behaviors: 模块当前可用的行为集合
            candidate_threshold: 行为成为候选的阈值

        返回:
            获胜的行为，如果没有选择则返回None
        """
        pass

    @abstractmethod
    def get_behaviors(self) -> Collection[Behavior]:
        """
        返回当前行为选择模块中的行为视图

        返回:
            行为对象的集合
        """
        pass
