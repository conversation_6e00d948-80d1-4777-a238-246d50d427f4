# LIDA Cognitive Framework
"""
Demon-like process operating on the workspace searching for particular
content which, when found, triggers its action producing its result. Has
workspace buffers it can access.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.Tasks.Codelet import Codelet

class StructureBuildingCodelet(Codelet):
    """
    Demon-like process operating on the workspace searching for particular
    content which, when found, triggers its action producing its result. Has
    workspace buffers it can access.
    """
    
    @abstractmethod
    def get_codelet_run_result(self) -> object:
        """
        Returns result of codelet's run.
        
        Returns:
            Current information about the codelet's progress
        """
        pass
    
    @abstractmethod
    def reset(self) -> None:
        """
        Clears this codelet's fields in preparation for reuse. Idea is that the
        same codelet object is reconfigured at runtime after it finishes to be
        run as a different altogether codelet.
        """
        pass
