"""
Agent starter for starting agents.

This module provides a class for starting agents.
"""
import logging
import os
import sys
import time
from typing import Dict, Any, Optional, List

from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_factory import AgentFactory
from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_xml_factory import AgentXmlFactory
from linars.edu.memphis.ccrg.lida.Framework.agent import Agent

class AgentStarter:
    """
    Agent starter for starting agents.
    This class provides methods for starting agents.
    """

    logger = logging.getLogger(__name__)
    agent = None  # 静态变量，用于存储当前的Agent实例
    pam = None   # 静态变量，用于存储PAM模块
    nar = None   # 静态变量，用于存储NARS实例

    # 添加VarManagerTask需要的静态变量
    is_do_var = False
    do_start_tick = 0
    inputQueueStr = ""

    @staticmethod
    def init_nars():
        """
        Initialize NARS.
        """
        try:
            # 尝试导入真实NAR
            # 先导入Concept类以避免循环导入
            try:
                # from linars.edu.memphis.ccrg.linars.concept import Concept
                print("Importing nar class----------")
                from linars.org.opennars.main.nar import Nar
                from linars.org.opennars.io.narsese import Narsese
                AgentStarter.nar = Nar()
                AgentStarter.narsese = Narsese(AgentStarter.nar)
                print("Imported nar class-------success---")
            except Exception as e:
                print("Failed to import nar class----------", e)
                AgentStarter.logger.error(f"Traceback: {traceback.format_exc()}")
        except Exception as e:
            AgentStarter.logger.error(f"Error initializing NARS: {e}")
            import traceback
            AgentStarter.logger.error(f"Traceback: {traceback.format_exc()}")

    @staticmethod
    def main(args=None):
        """
        Main entry point for starting an agent.

        Args:
            args: Command line arguments
        """
        # Set up logging
        logging.basicConfig(level=logging.INFO)

        # Initialize NARS
        AgentStarter.init_nars()

        # Get the factory file path
        factory_file = AgentStarter.get_factory_file(args)

        # Create the agent factory
        factory = AgentXmlFactory(factory_file)

        # Create and start the agent
        agent = AgentStarter.create_agent(factory)
        if agent is not None:
            # 将agent实例保存到静态变量中
            AgentStarter.agent = agent
            AgentStarter.start_agent(agent)

    @staticmethod
    def get_factory_file(args) -> str:
        """
        Get the factory file path from command line arguments or default.

        Args:
            args: Command line arguments

        Returns:
            The factory file path
        """
        # Default factory file
        factory_file = "configs/agent.xml"

        # Check command line arguments
        if args is not None and len(args) > 0:
            factory_file = args[0]

        # Check if the file exists
        if not os.path.exists(factory_file):
            # Try to find the file in various locations
            AgentStarter.logger.info(f"Factory file not found at {factory_file}, trying to find it in other locations")

            # Get the project root directory
            current_dir = os.path.abspath(__file__)
            for _ in range(7):  # Go up 7 levels to reach the project root
                current_dir = os.path.dirname(current_dir)
            project_root = current_dir

            # Try different paths
            possible_paths = [
                os.path.join(os.getcwd(), factory_file),  # Relative to current working directory
                os.path.join(project_root, factory_file),  # Relative to project root
                os.path.join(os.path.dirname(project_root), factory_file),  # One level up from project root
            ]

            # If factory_file is just a filename (e.g., "agent.xml"), also check in configs directory
            if os.path.basename(factory_file) == factory_file:
                possible_paths.append(os.path.join(project_root, "configs", factory_file))

            # Try each path
            for path in possible_paths:
                if os.path.exists(path):
                    AgentStarter.logger.info(f"Found factory file at {path}")
                    return path

        return factory_file

    @staticmethod
    def create_agent(factory: AgentFactory) -> Optional[Agent]:
        """
        Create an agent using the specified factory.

        Args:
            factory: The agent factory

        Returns:
            The created agent or None if creation fails
        """
        try:
            # Create the agent
            agent = factory.get_agent()

            # Configure the agent
            if agent is not None:
                factory.configure_agent(agent)

                # 获取PAM模块并设置为全局变量
                try:
                    # 尝试获取PAM模块
                    from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
                    pam_module_name = ModuleName.get_module_name("PAMemory")
                    if pam_module_name:
                        pam_module = agent.get_submodule(pam_module_name)
                        if pam_module:
                            AgentStarter.pam = pam_module
                            AgentStarter.logger.info(f"Set global PAM module: {pam_module}")
                except Exception as e:
                    AgentStarter.logger.warning(f"Error getting PAM module: {e}")

            return agent
        except Exception as e:
            AgentStarter.logger.error(f"Error creating agent: {e}")
            import traceback
            AgentStarter.logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    @staticmethod
    def start_agent(agent: Agent) -> None:
        """
        Start an agent.

        Args:
            agent: The agent to start
        """
        try:
            # Start the agent
            agent.start()

            # 启动回应触发器 - 暂时禁用以避免干扰运算测试
            # try:
            #     from linars.edu.memphis.ccrg.lida.Framework.Initialization.respond_trigger import start_respond_trigger
            #     start_respond_trigger()
            #     print("回应触发器已启动")
            # except Exception as e:
            #     print(f"启动回应触发器时发生错误: {e}")
            print("回应触发器已禁用（运算测试模式）")

            # Keep the main thread alive
            while agent.is_running():
                time.sleep(0.1)
        except KeyboardInterrupt:
            # Handle Ctrl+C
            AgentStarter.logger.info("Agent interrupted by user")
            agent.stop()
        except Exception as e:
            AgentStarter.logger.error(f"Error starting agent: {e}")
            agent.stop()
