# LIDA Cognitive Framework
"""
Implementation of the TaskSpawner interface.
"""

import logging
from typing import Dict, List, Set, Collection, Optional
from collections import defaultdict

from linars.edu.memphis.ccrg.lida.Framework.Initialization.InitializableImpl import InitializableImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawner import TaskSpawner
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTask import FrameworkTask, TaskStatus
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager

class TaskSpawnerImpl(InitializableImpl, TaskSpawner):
    """
    Maintains a queue of running tasks and their task status. Methods to add and
    cancel tasks. This implementation actually uses TaskManager to execute the tasks.
    """

    def __init__(self, task_manager=None):
        """
        Initialize a TaskSpawnerImpl.

        This constructor is used by the AgentXmlFactory.
        If task_manager is None, it must be set using set_task_manager(TaskManager).

        Args:
            task_manager: The TaskManager to use (optional)
        """
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.controlled_tasks = set()
        self.task_manager = task_manager

    def set_task_manager(self, tm: TaskManager) -> None:
        """
        Set the TaskManager this TaskSpawner will use to actually run the tasks.

        Args:
            tm: The TaskManager of the system
        """
        self.task_manager = tm

    def add_tasks(self, tasks: Collection[FrameworkTask]) -> None:
        """
        Adds and runs supplied FrameworkTasks.

        Args:
            tasks: A collection of tasks to be run
        """
        if tasks is not None:
            for task in tasks:
                self.add_task(task)

    def add_task(self, task: FrameworkTask) -> None:
        """
        Adds and runs supplied FrameworkTask.

        Args:
            task: The task to add
        """
        if task is None:
            self.logger.warning(f"Cannot add a null task at tick {TaskManager.get_current_tick()}")
        elif task.get_task_status() == TaskStatus.CANCELED:
            self.logger.warning(f"Cannot add task {task} because its TaskStatus is CANCELED at tick {TaskManager.get_current_tick()}")
        else:
            task.set_controlling_task_spawner(self)
            self.controlled_tasks.add(task)
            self._run_task(task)
            self.logger.debug(f"Task {task} added at tick {TaskManager.get_current_tick()}")

    def _run_task(self, task: FrameworkTask) -> None:
        """
        Schedule the FrameworkTask to be executed. Sets task status to RUNNING.

        Args:
            task: The task to run
        """
        self.logger.debug(f"Running task {task} at tick {TaskManager.get_current_tick()}")
        task.set_task_status(TaskStatus.RUNNING)
        self.task_manager.schedule_task(task, task.get_next_ticks_per_run())

    def receive_finished_task(self, task: FrameworkTask) -> None:
        """
        This method receives a task that has finished. TaskSpawners can choose what to do
        with the FrameworkTask each time it finishes running. Generally the FrameworkTask's
        TaskStatus determines this action.

        Args:
            task: The finished FrameworkTask
        """
        status = task.get_task_status()
        if status == TaskStatus.FINISHED_WITH_RESULTS:
            self.process_results(task)
            self._remove_task(task)
            self.logger.debug(f"FINISHED_WITH_RESULTS {task} at tick {TaskManager.get_current_tick()}")
        elif status == TaskStatus.FINISHED:
            self._remove_task(task)
            self.logger.debug(f"FINISHED {task} at tick {TaskManager.get_current_tick()}")
        elif status == TaskStatus.CANCELED:
            self._remove_task(task)
            self.logger.debug(f"CANCELLED {task} at tick {TaskManager.get_current_tick()}")
        elif status == TaskStatus.RUNNING:
            self.logger.debug(f"RUNNING {task} at tick {TaskManager.get_current_tick()}")
            self._run_task(task)

    def _remove_task(self, task: FrameworkTask) -> None:
        """
        When a finished task is received and its status is FINISHED_WITH_RESULTS
        or FINISHED or CANCELLED This method is called to remove the task from this TaskSpawner.

        Args:
            task: The task to remove
        """
        self.logger.debug(f"Removing task {task} at tick {TaskManager.get_current_tick()}")
        if task in self.controlled_tasks:
            self.controlled_tasks.remove(task)

    def process_results(self, task: FrameworkTask) -> None:
        """
        When a FrameworkTask has completed one execution and its status
        is TaskStatus.FINISHED_WITH_RESULTS this method is called to
        handle the results.

        Args:
            task: The task to be processed
        """
        pass

    def cancel_task(self, task: FrameworkTask) -> bool:
        """
        Cancels specified task if it exists in this TaskSpawner.
        Task is removed from TaskSpawner and canceled in the TaskManager.
        This is only possible if the tick for which the task
        is scheduled has not been reached.

        Args:
            task: The task to cancel

        Returns:
            True if the task was canceled, False otherwise
        """
        if self.contains_task(task):
            self._remove_task(task)
            return self.task_manager.cancel_task(task)
        return False

    def contains_task(self, task: FrameworkTask) -> bool:
        """
        Returns whether this TaskSpawner manages this task.

        Args:
            task: A FrameworkTask

        Returns:
            True if this TaskSpawner contains the task, False otherwise
        """
        return task is not None and task in self.controlled_tasks

    def contains_task_by_name(self, task_name: str) -> bool:
        """
        Returns whether this TaskSpawner manages a task with the given name.

        Args:
            task_name: The name of the task

        Returns:
            True if this TaskSpawner contains a task with the given name, False otherwise
        """
        for task in self.controlled_tasks:
            if task_name in str(task):
                return True
        return False

    def get_tasks(self) -> Collection[FrameworkTask]:
        """
        Returns the FrameworkTask objects controlled by this TaskSpawner.

        Returns:
            A collection of FrameworkTasks
        """
        return self.controlled_tasks.copy()

    def get_running_tasks(self) -> Collection[FrameworkTask]:
        """
        Returns a collection that contains the FrameworkTasks in this TaskSpawner.
        Tasks' TaskStatus may or may not be running.

        Deprecated: The returned tasks may not have TaskStatus.RUNNING. Use get_tasks() instead.

        Returns:
            A collection of running tasks
        """
        return self.get_tasks()
