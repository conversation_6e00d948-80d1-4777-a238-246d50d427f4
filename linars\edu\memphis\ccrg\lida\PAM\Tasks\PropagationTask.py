#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task that propagates activation from a link to its connected nodes.
"""

from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawner import Task

class PropagationTask(Task):
    """
    A task that propagates activation from a link to its connected nodes.
    """

    def __init__(self, ticks_per_run, link, activation, pam, depth=0, source="pam"):
        super().__init__(ticks_per_run)
        self.link = link
        self.activation = activation
        self.pam = pam
        self.depth = depth
        self.source = source

    def run(self):
        """Run the task"""
        if self.is_cancelled:
            return

        try:
            # Get the source and sink of the link
            source = self.link.getSource() if hasattr(self.link, 'getSource') else None
            sink = self.link.getSink() if hasattr(self.link, 'getSink') else None

            # Propagate activation to the source and sink
            if source:
                self.pam.receive_excitation(source, self.activation, self.source)

            if sink:
                self.pam.receive_excitation(sink, self.activation, self.source)
        except Exception as e:
            import logging
            import traceback
            logger = logging.getLogger(self.__class__.__name__)
            logger.warning(f"Exception encountered during the execution of task {self}. \n {str(e)}")
            logger.debug(f"报错0----》 {str(e)}")
            # traceback.print_exc()

        # Mark the task as finished
        self.is_finished = True
