# LIDA Cognitive Framework
"""
Interface for tasks in the LIDA framework.
"""

from abc import ABC, abstractmethod
from typing import Optional, Any
from enum import Enum
from linars.edu.memphis.ccrg.lida.Framework.Initialization.Initializable import Initializable
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.Activatible import Activatible

class TaskStatus(Enum):
    """
    Enum for the status of a task.
    """
    RUNNING = 0
    CANCELED = 1
    FINISHED = 2
    FINISHED_WITH_RESULTS = 3  # 添加与Java版本一致的状态值

class FrameworkTask(Activatible, Initializable, ABC):
    """
    This is the base interface for all task process in the LIDA framework.
    All parts of processes in the LIDA Framework have to implement this interface.
    A FrameworkTask is intended as a small fraction of a process.
    For example a Codelet or a Feature detector are examples of FrameworkTask.
    However, if the process includes a loop, one run of the FrameworkTask represents
    only one iteration of the loop.
    """

    @abstractmethod
    def get_task_id(self) -> int:
        """
        Get the ID of this task.

        Returns:
            The ID of this task
        """
        pass

    @abstractmethod
    def get_ticks_per_run(self) -> int:
        """
        Get the number of ticks between runs of this task.

        Returns:
            The number of ticks between runs
        """
        pass

    @abstractmethod
    def set_ticks_per_run(self, ticks: int) -> None:
        """
        Set the number of ticks between runs of this task.

        Args:
            ticks: The number of ticks between runs
        """
        pass

    @abstractmethod
    def get_task_status(self) -> TaskStatus:
        """
        Get the status of this task.

        Returns:
            The status of this task
        """
        pass

    @abstractmethod
    def set_next_ticks_per_run(self, ticks: int) -> None:
        """
        Set the number of ticks until the next run of this task.

        Args:
            ticks: The number of ticks until the next run
        """
        pass

    @abstractmethod
    def get_next_ticks_per_run(self) -> int:
        """
        Get the number of ticks until the next run of this task.

        Returns:
            The number of ticks until the next run
        """
        pass

    @abstractmethod
    def cancel(self) -> None:
        """
        Cancel this task.
        """
        pass

    @abstractmethod
    def set_controlling_task_spawner(self, ts: 'TaskSpawner') -> None:
        """
        Set the TaskSpawner that controls this task.

        Args:
            ts: The TaskSpawner that controls this task
        """
        pass

    @abstractmethod
    def get_controlling_task_spawner(self) -> 'TaskSpawner':
        """
        Get the TaskSpawner that controls this task.

        Returns:
            The TaskSpawner that controls this task
        """
        pass

    @abstractmethod
    def call(self) -> 'FrameworkTask':
        """
        Call this task.

        Returns:
            This task
        """
        pass
