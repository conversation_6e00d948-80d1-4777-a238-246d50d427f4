# LIDA Cognitive Framework
"""
A NodeStructure that cannot be modified.
"""

import logging
from typing import Dict, Any, Optional, Collection
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategory import LinkCategory

class UnmodifiableNodeStructureImpl(NodeStructure):
    """
    A NodeStructure that cannot be modified.
    """

    def __init__(self, ns: NodeStructure):
        """
        Initialize an UnmodifiableNodeStructureImpl.

        Args:
            ns: The NodeStructure to wrap
        """
        self.ns = ns
        self.logger = logging.getLogger(self.__class__.__name__)

    def add_node(self, node: Node, copy: bool = True) -> Node:
        """
        Add a node to this structure.

        Args:
            node: The node to add
            copy: Whether to copy the node

        Returns:
            The added node
        """
        self.logger.warning("Cannot modify an UnmodifiableNodeStructure")
        return None

    def add_default_node(self, node: Node) -> Node:
        """
        Add a node to this structure with default settings.

        Args:
            node: The node to add

        Returns:
            The added node
        """
        self.logger.warning("Cannot modify an UnmodifiableNodeStructure")
        return None

    def add_link(self, link_type: str, source: Node, sink: Linkable, category: LinkCategory, copy: bool = True) -> Link:
        """
        Add a link to this structure.

        Args:
            link_type: The type of the link
            source: The source of the link
            sink: The sink of the link
            category: The category of the link
            copy: Whether to copy the link

        Returns:
            The added link
        """
        self.logger.warning("Cannot modify an UnmodifiableNodeStructure")
        return None

    def add_default_link(self, *args) -> Link:
        """
        Add a link to this structure with default settings.

        Args:
            Can be one of the following two forms:
            1. link: The link to add
            2. source: The source of the link, sink: The sink of the link, category: The category of the link,
               activation: The activation of the link, removal_threshold: The removal threshold of the link

        Returns:
            The added link
        """
        self.logger.warning("Cannot modify an UnmodifiableNodeStructure")
        return None

    def get_node(self, id: int) -> Optional[Node]:
        """
        Get a node by its ID.

        Args:
            id: The ID of the node

        Returns:
            The node with the specified ID
        """
        return self.ns.get_node(id)

    def get_nodes(self) -> Collection[Node]:
        """
        Get all nodes in this structure.

        Returns:
            All nodes in this structure
        """
        return self.ns.get_nodes()

    def get_links(self) -> Collection[Link]:
        """
        Get all links in this structure.

        Returns:
            All links in this structure
        """
        return self.ns.get_links()

    def get_linkables(self) -> Collection[Linkable]:
        """
        Get all linkables in this structure.

        Returns:
            All linkables in this structure
        """
        return self.ns.get_linkables()

    def get_link(self, source: Node, sink: Linkable, category: LinkCategory) -> Optional[Link]:
        """
        Get a link by its source, sink, and category.

        Args:
            source: The source of the link
            sink: The sink of the link
            category: The category of the link

        Returns:
            The link with the specified source, sink, and category
        """
        return self.ns.get_link(source, sink, category)

    def get_links_of_source(self, source: Node) -> Collection[Link]:
        """
        Get all links from a source.

        Args:
            source: The source of the links

        Returns:
            All links from the specified source
        """
        return self.ns.get_links_of_source(source)

    def get_links_of_sink(self, sink: Linkable) -> Collection[Link]:
        """
        Get all links to a sink.

        Args:
            sink: The sink of the links

        Returns:
            All links to the specified sink
        """
        return self.ns.get_links_of_sink(sink)

    def get_attractor_links(self) -> Collection[Link]:
        """
        Get all attractor links in this structure.

        Returns:
            All attractor links in this structure
        """
        return self.ns.get_attractor_links()

    def get_node_count(self) -> int:
        """
        Get the number of nodes in this structure.

        Returns:
            The number of nodes in this structure
        """
        return self.ns.get_node_count()

    def get_link_count(self) -> int:
        """
        Get the number of links in this structure.

        Returns:
            The number of links in this structure
        """
        return self.ns.get_link_count()

    def get_linkable_count(self) -> int:
        """
        Get the number of linkables in this structure.

        Returns:
            The number of linkables in this structure
        """
        return self.ns.get_linkable_count()

    def contains_linkable(self, linkable: Linkable) -> bool:
        """
        Check if this structure contains a linkable.

        Args:
            linkable: The linkable to check

        Returns:
            True if this structure contains the linkable, False otherwise
        """
        return self.ns.contains_linkable(linkable)

    def get_linkable(self, linkable: Linkable) -> Optional[Linkable]:
        """
        Get a linkable from this structure.

        Args:
            linkable: The linkable to get

        Returns:
            The linkable from this structure
        """
        return self.ns.get_linkable(linkable)

    def merge_with(self, ns: NodeStructure) -> None:
        """
        Merge this structure with another.

        Args:
            ns: The structure to merge with
        """
        self.logger.warning("Cannot modify an UnmodifiableNodeStructure")

    def copy(self) -> NodeStructure:
        """
        Create a copy of this structure.

        Returns:
            A copy of this structure
        """
        return self.ns.copy()

    def clear_structure(self) -> None:
        """
        Clear this structure.
        """
        self.logger.warning("Cannot modify an UnmodifiableNodeStructure")

    def decay_node_structure(self, ticks: int) -> None:
        """
        Decay this structure.

        Args:
            ticks: The number of ticks to decay by
        """
        self.logger.warning("Cannot modify an UnmodifiableNodeStructure")

    def get_scene_time(self) -> str:
        """
        Get the scene time of this structure.

        Returns:
            The scene time of this structure
        """
        return self.ns.get_scene_time()

    def set_scene_time(self, time: str) -> None:
        """
        Set the scene time of this structure.

        Args:
            time: The scene time to set
        """
        self.logger.warning("Cannot modify an UnmodifiableNodeStructure")

    def get_scene_site(self) -> str:
        """
        Get the scene site of this structure.

        Returns:
            The scene site of this structure
        """
        return self.ns.get_scene_site()

    def set_scene_site(self, site: str) -> None:
        """
        Set the scene site of this structure.

        Args:
            site: The scene site to set
        """
        self.logger.warning("Cannot modify an UnmodifiableNodeStructure")

    def get_broad_scene_count(self) -> int:
        """
        Get the broadcast scene count of this structure.

        Returns:
            The broadcast scene count of this structure
        """
        return self.ns.get_broad_scene_count()

    def set_broad_scene_count(self, count: int) -> None:
        """
        Set the broadcast scene count of this structure.

        Args:
            count: The broadcast scene count to set
        """
        self.logger.warning("Cannot modify an UnmodifiableNodeStructure")

    def save_scene(self, ns: NodeStructure) -> None:
        """
        Save the scene of this structure.

        Args:
            ns: The structure to save the scene from
        """
        self.logger.warning("Cannot modify an UnmodifiableNodeStructure")

    def __str__(self) -> str:
        """
        Return the string representation of this structure.

        Returns:
            The string representation of this structure
        """
        return f"UnmodifiableNodeStructure[{self.ns}]"
