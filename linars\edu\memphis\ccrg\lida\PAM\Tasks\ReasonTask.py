#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to perform reasoning.
"""

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl

# 导入 linars 组件
try:
    from linars.edu.memphis.ccrg.linars.memory import Memory
    from linars.org.opennars.entity.task import Task
    from linars.org.opennars.entity.term_link import TermLink
    from linars.org.opennars.control.derivation_context import DerivationContext
    from linars.org.opennars.inference.rule_tables import RuleTables
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False

class ReasonTask(FrameworkTaskImpl):
    """
    A task to perform reasoning.
    """

    def __init__(self, term_link: TermLink, nal: DerivationContext):
        """
        Initialize a ReasonTask.

        Args:
            term_link: The term link
            nal: The derivation context
        """
        super().__init__(1, "tact")
        self.nal = nal
        self.term_link = term_link

    def run_this_framework_task(self):
        """
        Run the task.
        """
        RuleTables.reason(self.nal.current_task_link, self.term_link, self.nal)
        self.cancel()
