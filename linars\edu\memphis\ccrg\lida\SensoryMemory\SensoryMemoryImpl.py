# LIDA Cognitive Framework
"""
Default abstract (i.e. must be overridden to be used) implementation of the SensoryMemory module.
This module should sense the environment, store the sensed data and processing it.
It should expect access to its content from DetectionAlgorithms via method
SensoryMemory.get_sensory_content() and it may transmit content to SensoryMotorMemory.
"""

import logging
from typing import List, Dict, Any, Optional
from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Environment.Environment import Environment
from linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemory import SensoryMemory
from linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemoryListener import SensoryMemoryListener

class SensoryMemoryImpl(FrameworkModuleImpl, SensoryMemory):
    """
    Default abstract (i.e. must be overridden to be used) implementation of the SensoryMemory module.
    This module should sense the environment, store the sensed data and processing it.
    It should expect access to its content from DetectionAlgorithms via method
    SensoryMemory.get_sensory_content() and it may transmit content to SensoryMotorMemory.
    """
    
    def __init__(self):
        """
        Default Constructor.
        """
        super().__init__()
        self.sensory_memory_listeners: List[SensoryMemoryListener] = []
        self.environment = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def add_listener(self, listener: ModuleListener) -> None:
        """
        Add a listener to this SensoryMemory.
        
        Args:
            listener: The listener to add
        """
        if isinstance(listener, SensoryMemoryListener):
            self.add_sensory_memory_listener(listener)
        else:
            self.logger.warning(f"Cannot add listener {listener} at tick {TaskManager.get_current_tick()}")
    
    def add_sensory_memory_listener(self, listener: SensoryMemoryListener) -> None:
        """
        Adds a listener to this memory. This listener constantly checks for
        information being sent from this memory to other modules (Perceptual
        Associative Memory and Sensory Motor Memory).
        
        Args:
            listener: The listener added to this memory
        """
        self.sensory_memory_listeners.append(listener)
    
    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module for this SensoryMemory.
        
        Args:
            module: The module to associate with this SensoryMemory
            module_usage: How this SensoryMemory will use the module
        """
        if isinstance(module, Environment):
            self.environment = module
        else:
            self.logger.warning(f"Cannot add module {module} at tick {TaskManager.get_current_tick()}")
    
    @abstractmethod
    def run_sensors(self) -> None:
        """
        Runs all the sensors associated with this memory. The sensors get the
        information from the environment and store in this memory for later
        processing and passing to the perceptual memory module.
        """
        pass
    
    @abstractmethod
    def get_sensory_content(self, modality: str, params: Optional[Dict[str, Any]]) -> Any:
        """
        Returns content from this SensoryMemory.
        Intended to be used by feature detectors to get specific parts of the sensory memory.
        
        Args:
            modality: User may optionally use this parameter to specify modality
            params: Optional parameters
            
        Returns:
            Content from this SensoryMemory
        """
        pass
    
    @abstractmethod
    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.
        
        Args:
            ticks: The number of ticks to decay by
        """
        pass
