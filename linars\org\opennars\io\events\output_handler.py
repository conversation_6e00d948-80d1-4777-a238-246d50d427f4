"""
输出处理器
实现此接口并通过Nar.add_output(..)接收各种通道的输出信号
"""
from typing import List, Any, Optional, Type

from linars.org.opennars.io.events.event_handler import EventHandler
from linars.org.opennars.io.events.event_emitter import EventEmitter
from linars.org.opennars.io.events.events import Answer

class OutputHandler(EventHandler):
    """
    输出处理器
    继承自事件处理器，用于接收多种通道的输出信号
    """

    # 隐式重复输入(所有输入的重复)
    class IN:
        """隐式重复输入通道(所有输入的重复)"""
        pass

    # 对话式输出(判断、问题等)
    class OUT:
        """对话式输出通道(判断、问题等)"""
        pass

    # 警告、错误和异常
    class ERR:
        """警告/错误/异常输出通道"""
        pass

    # 显式重复输入(ECHO命令内容的重复)
    class ECHO:
        """显式重复输入通道(ECHO命令内容的重复)"""
        pass

    # 调试语句
    class DEBUG:
        """调试信息输出通道"""
        pass

    # 操作执行
    class EXE:
        """操作执行输出通道"""
        pass

    # 预期
    class ANTICIPATE:
        """预期输出通道"""
        pass

    # 确认
    class CONFIRM:
        """确认输出通道"""
        pass

    # 失望
    class DISAPPOINT:
        """失望输出通道"""
        pass

    # 默认输出事件列表
    DefaultOutputEvents = [IN, EXE, OUT, ERR, ECHO, Answer, ANTICIPATE, CONFIRM, DISAPPOINT, DEBUG]

    def __init__(self, source, active: bool = True):
        """
        构造函数

        参数:
            source: 事件源
            active: 是否激活处理器(默认为True)
        """
        super().__init__(source, active, *self.DefaultOutputEvents)
