# LIDA认知框架
"""
节点结构的默认实现
"""

import logging
from typing import Dict, Set, List, Any, Optional, Collection
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategory import LinkCategory
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkImpl import LinkImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager

class NodeStructureImpl(NodeStructure):
    """
    节点结构的默认实现
    """

    def __init__(self, node_type: str = None, link_type: str = None):
        """
        初始化节点结构实现

        参数:
            node_type: 结构中节点的类型
            link_type: 结构中链接的类型
        """
        self.nodes: Dict[int, Node] = {}
        self.links: Dict[int, Link] = {}
        self.links_from: Dict[int, Set[Link]] = {}
        self.links_to: Dict[int, Set[Link]] = {}
        self.node_type = node_type if node_type is not None else "NodeImpl"
        self.link_type = link_type if link_type is not None else "LinkImpl"
        self.scene_time = ""
        self.scene_site = ""
        self.broad_scene_count = 0
        self.logger = logging.getLogger(self.__class__.__name__)

    def add_node(self, node: Node, copy: bool = True) -> Node:
        """
        添加节点到此结构

        参数:
            node: 要添加的节点
            copy: 是否复制节点

        返回:
            添加的节点
        """
        if node is None:
            return None

        node_id = node.get_node_id()
        if node_id in self.nodes:
            return self.nodes[node_id]

        if copy:
            # Create a new node of the same type
            new_node = NodeImpl(node.get_node_name())
            new_node.set_node_id(node_id)
            new_node.set_activation(node.get_activation())
            new_node.set_activation_threshold(node.get_activation_threshold())
            new_node.set_removal_threshold(node.get_removal_threshold())
            new_node.set_base_level_activation(node.get_base_level_activation())
            new_node.set_grounding_pam_node(node.get_grounding_pam_node())
            new_node.set_location(node.get_location())
            new_node.set_truth(node.get_truth())
            new_node.set_bcastid(node.get_bcastid())
            new_node.set_last_act(node.get_last_act())

            # Add the new node to this structure
            self.nodes[node_id] = new_node
            self.links_from[node_id] = set()
            return new_node
        else:
            # Add the node to this structure
            self.nodes[node_id] = node
            self.links_from[node_id] = set()
            return node

    def addNode_(self, node: Node) -> Node:
        """
        添加节点到此结构(不复制，add_node的别名，copy=False)

        参数:
            node: 要添加的节点

        返回:
            添加的节点
        """
        return self.add_node(node, False)

    def add_default_node(self, node: Node) -> Node:
        """
        使用默认设置添加节点到此结构

        参数:
            node: 要添加的节点

        返回:
            添加的节点
        """
        return self.add_node(node, True)

    def addDefaultNode(self, node: Node) -> Node:
        """
        使用默认设置添加节点到此结构(add_default_node的别名)

        参数:
            node: 要添加的节点

        返回:
            添加的节点
        """
        return self.add_default_node(node)

    def add_link(self, link_type: str, source: Node, sink: Linkable, category: LinkCategory, copy: bool = True) -> Link:
        """
        添加链接到此结构

        参数:
            link_type: 链接类型
            source: 链接源端
            sink: 链接汇端
            category: 链接类别
            copy: 是否复制链接

        返回:
            添加的链接
        """
        if source is None or sink is None or category is None:
            return None

        # Check if the source and sink are in this structure
        source_id = source.get_node_id()
        if source_id not in self.nodes:
            source = self.add_node(source, copy)
        else:
            source = self.nodes[source_id]

        if sink.is_node():
            sink_id = sink.get_node_id()
            if sink_id not in self.nodes:
                sink = self.add_node(sink, copy)
            else:
                sink = self.nodes[sink_id]

        # Check if the link already exists
        for link in self.links.values():
            if (link.get_source().get_node_id() == source.get_node_id() and
                link.get_sink().get_extended_id() == sink.get_extended_id() and
                link.get_category().get_id() == category.get_id()):
                return link

        # Create a new link
        new_link = LinkImpl()
        new_link.set_source(source)
        new_link.set_sink(sink)
        new_link.set_category(category)

        # Add the link to this structure
        link_id = new_link.get_id()
        self.links[link_id] = new_link

        # Add the link to the source and sink
        source_id = source.get_node_id()
        if source_id not in self.links_from:
            self.links_from[source_id] = set()
        self.links_from[source_id].add(new_link)

        sink_id = sink.get_extended_id()
        if sink_id not in self.links_to:
            self.links_to[sink_id] = set()
        self.links_to[sink_id].add(new_link)

        return new_link

    def add_default_link(self, *args) -> Link:
        """
        使用默认设置添加链接到此结构

        参数:
            可以是以下两种形式之一:
            1. link: 要添加的链接
            2. source: 链接源端, sink: 链接汇端, category: 链接类别, activation: 激活值, removal_threshold: 移除阈值

        返回:
            添加的链接
        """
        # 如果只有一个参数，则它是一个Link对象
        if len(args) == 1:
            link = args[0]
            if link is None:
                return None
            return self.add_link(self.link_type, link.get_source(), link.get_sink(), link.get_category(), True)

        # 如果有五个参数，则它们是 source, sink, category, activation, removal_threshold
        elif len(args) == 5:
            source, sink, category, activation, removal_threshold = args
            if source is None or sink is None or category is None:
                return None

            # 创建新链接
            from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkImpl import LinkImpl
            new_link = LinkImpl()
            new_link.set_source(source)
            new_link.set_sink(sink)
            new_link.set_category(category)
            new_link.set_activation(activation)
            new_link.set_removal_threshold(removal_threshold)

            # 添加链接到结构中
            return self.add_default_link(new_link)
        else:
            self.logger.warning(f"Invalid number of arguments for add_default_link: {len(args)}")
            return None

    def get_node(self, id: int) -> Optional[Node]:
        """
        通过ID获取节点
        参数:
            id: 节点ID
        返回:
            指定ID的节点
        """
        return self.nodes.get(id)

    def getNodeByName(self, name: str) -> Optional[Node]:
        """
        通过ID获取节点(get_node的别名)
        参数:
            id: 节点ID
        返回:
            指定ID的节点
        """
        # 遍历所有节点，找到名称匹配的节点
        for node in self.nodes.values():
            if node.get_node_name() == name:
                return node

        return None

    def get_nodes(self) -> Collection[Node]:
        """
        获取结构中所有节点

        返回:
            结构中所有节点
        """
        return self.nodes.values()

    def get_links(self) -> Collection[Link]:
        """
        获取结构中所有链接

        返回:
            结构中所有链接
        """
        return self.links.values()

    def get_linkables(self) -> Collection[Linkable]:
        """
        获取结构中所有可链接对象

        返回:
            结构中所有可链接对象
        """
        linkables: List[Linkable] = []
        linkables.extend(self.nodes.values())
        linkables.extend(self.links.values())
        return linkables

    def get_link(self, source: Node, sink: Linkable, category: LinkCategory) -> Optional[Link]:
        """
        通过源端、汇端和类别获取链接

        参数:
            source: 链接源端
            sink: 链接汇端
            category: 链接类别

        返回:
            指定源端、汇端和类别的链接
        """
        if source is None or sink is None or category is None:
            return None

        source_id = source.get_node_id()
        if source_id not in self.links_from:
            return None

        for link in self.links_from[source_id]:
            if (link.get_sink().get_extended_id() == sink.get_extended_id() and
                link.get_category().get_id() == category.get_id()):
                return link

        return None

    def get_links_of_source(self, source) -> Collection[Link]:
        """
        获取来自源端的所有链接
        参数:
            source: 链接源端，可以是Node对象、节点ID或节点名称
        返回:
            来自指定源端的所有链接
        """
        if source is None:
            return []

        # 如果source是Node对象
        if isinstance(source, Node):
            source_id = source.get_node_id()
            if source_id not in self.links_from:
                return []
            return self.links_from[source_id]

        # 如果source是整数（节点ID）
        elif isinstance(source, int):
            return self.get_links_of_source_by_id(source)

        # 如果source是字符串（节点名称）
        elif isinstance(source, str):
            return self.get_links_of_source_by_name(source)

        # 不支持的类型
        else:
            self.logger.warning(f"Unsupported source type: {type(source)} at tick {TaskManager.get_current_tick()}")
            return []

    def get_links_of_source_by_id(self, node_id: int) -> Collection[Link]:
        """
        通过节点ID获取来自源端的所有链接
        参数:
            node_id: 节点ID
        返回:
            来自指定节点ID的所有链接
        """
        # 查找具有指定ID的节点
        node = self.get_node(node_id)
        if node is None:
            return []

        # 使用节点对象获取链接
        return self.get_links_of_source(node)

    def get_links_of_source_by_name(self, node_name: str) -> Collection[Link]:
        """
        通过节点名称获取来自源端的所有链接
        参数:
            node_name: 节点名称
        返回:
            来自指定节点名称的所有链接
        """
        # 查找具有指定名称的节点
        node = self.getNodeByName(node_name)
        if node is None:
            # 尝试从Neo4j获取节点
            node = self.get_neo_node(node_name)
            if node is None:
                return []

        # 使用节点对象获取链接
        return self.get_links_of_source(node)

    def get_links_of_sink(self, sink) -> Collection[Link]:
        """
        获取指向汇端的所有链接
        参数:
            sink: 链接汇端，可以是Linkable对象、节点ID或节点名称
        返回:
            指向指定汇端的所有链接
        """
        if sink is None:
            return []

        # 如果sink是Linkable对象
        if isinstance(sink, Linkable):
            sink_id = sink.get_extended_id()
            if sink_id not in self.links_to:
                return []
            return self.links_to[sink_id]

        # 如果sink是整数（节点ID）
        elif isinstance(sink, int):
            return self.get_links_of_sink_by_id(sink)

        # 如果sink是字符串（节点名称）
        elif isinstance(sink, str):
            return self.get_links_of_sink_by_name(sink)

        # 不支持的类型
        else:
            self.logger.warning(f"Unsupported sink type: {type(sink)} at tick {TaskManager.get_current_tick()}")
            return []

    def get_links_of_sink_by_id(self, node_id: int) -> Collection[Link]:
        """
        通过节点ID获取指向汇端的所有链接
        参数:
            node_id: 节点ID
        返回:
            指向指定节点ID的所有链接
        """
        # 查找具有指定ID的节点
        node = self.get_node(node_id)
        if node is None:
            return []

        # 使用节点对象获取链接
        return self.get_links_of_sink(node)

    def get_links_of_sink_by_name(self, node_name: str) -> Collection[Link]:
        """
        通过节点名称获取指向汇端的所有链接
        参数:
            node_name: 节点名称
        返回:
            指向指定节点名称的所有链接
        """
        # 查找具有指定名称的节点
        node = self.getNodeByName(node_name)
        if node is None:
            # 尝试从Neo4j获取节点
            node = self.get_neo_node(node_name)
            if node is None:
                return []

        # 使用节点对象获取链接
        return self.get_links_of_sink(node)

    def get_attractor_links(self) -> Collection[Link]:
        """
        获取结构中所有吸引子链接

        返回:
            结构中所有吸引子链接
        """
        # This method should be overridden by subclasses
        return []

    def get_node_count(self) -> int:
        """
        获取结构中节点数量

        返回:
            结构中节点数量
        """
        return len(self.nodes)

    def get_link_count(self) -> int:
        """
        获取结构中链接数量

        返回:
            结构中链接数量
        """
        return len(self.links)

    def get_linkable_count(self) -> int:
        """
        获取结构中可链接对象数量

        返回:
            结构中可链接对象数量
        """
        return len(self.nodes) + len(self.links)

    def contains_linkable(self, linkable: Linkable) -> bool:
        """
        检查结构中是否包含指定可链接对象

        参数:
            linkable: 要检查的可链接对象

        返回:
            如果包含返回True，否则返回False
        """
        if linkable is None:
            return False

        if linkable.is_node():
            return linkable.get_node_id() in self.nodes
        else:
            link = linkable
            source = link.get_source()
            sink = link.get_sink()
            category = link.get_category()

            if source is None or sink is None or category is None:
                return False

            return self.get_link(source, sink, category) is not None

    def containsLinkable(self, linkable: Linkable) -> bool:
        """
        检查结构中是否包含指定可链接对象(contains_linkable的别名)

        参数:
            linkable: 要检查的可链接对象

        返回:
            如果包含返回True，否则返回False
        """
        return self.contains_linkable(linkable)

    def contains_node(self, node: Node) -> bool:
        """
        检查结构中是否包含指定节点

        参数:
            node: 要检查的节点

        返回:
            如果包含返回True，否则返回False
        """
        if node is None:
            return False

        return node.get_node_id() in self.nodes

    def containsNode(self, node: Node) -> bool:
        """
        检查结构中是否包含指定节点(contains_node的别名)

        参数:
            node: 要检查的节点

        返回:
            如果包含返回True，否则返回False
        """
        return self.contains_node(node)

    def get_linkable(self, linkable: Linkable) -> Optional[Linkable]:
        """
        从结构中获取可链接对象

        参数:
            linkable: 要获取的可链接对象

        返回:
            结构中的可链接对象
        """
        if linkable is None:
            return None

        if linkable.is_node():
            return self.get_node(linkable.get_node_id())
        else:
            link = linkable
            source = link.get_source()
            sink = link.get_sink()
            category = link.get_category()

            if source is None or sink is None or category is None:
                return None

            return self.get_link(source, sink, category)

    def merge_with(self, ns: NodeStructure) -> None:
        """
        将此结构与另一个结构合并

        参数:
            ns: 要合并的结构
        """
        if ns is None:
            return

        # Add all nodes from the other structure
        for node in ns.get_nodes():
            self.add_default_node(node)

        # Add all links from the other structure
        for link in ns.get_links():
            self.add_default_link(link)

    def copy(self) -> NodeStructure:
        """
        创建此结构的副本

        返回:
            结构的副本
        """
        copy = NodeStructureImpl(self.node_type, self.link_type)
        copy.merge_with(self)
        copy.scene_time = self.scene_time
        copy.scene_site = self.scene_site
        copy.broad_scene_count = self.broad_scene_count
        return copy

    def clear_structure(self) -> None:
        """
        清空此结构
        """
        self.nodes.clear()
        self.links.clear()
        self.links_from.clear()
        self.links_to.clear()

    def decay_node_structure(self, ticks: int) -> None:
        """
        衰减此结构

        参数:
            ticks: 衰减的ticks数
        """
        # Decay all nodes
        nodes_to_remove = []
        for node in self.nodes.values():
            node.decay(ticks)
            if not node.is_above_removal_threshold():
                nodes_to_remove.append(node)

        # Remove nodes that are below the removal threshold
        for node in nodes_to_remove:
            self.remove_node(node)

        # Decay all links
        links_to_remove = []
        for link in self.links.values():
            link.decay(ticks)
            if not link.is_above_removal_threshold():
                links_to_remove.append(link)

        # Remove links that are below the removal threshold
        for link in links_to_remove:
            self.remove_link(link)

    def remove_node(self, node: Node) -> None:
        """
        从结构中移除节点

        参数:
            node: 要移除的节点
        """
        if node is None:
            return

        node_id = node.get_node_id()
        if node_id not in self.nodes:
            return

        # Remove all links to and from this node
        links_to_remove = []
        for link in self.links.values():
            if (link.get_source().get_node_id() == node_id or
                (link.get_sink().is_node() and link.get_sink().get_node_id() == node_id)):
                links_to_remove.append(link)

        for link in links_to_remove:
            self.remove_link(link)

        # Remove the node
        del self.nodes[node_id]
        if node_id in self.links_from:
            del self.links_from[node_id]

    def remove_link(self, link: Link) -> None:
        """
        从结构中移除链接

        参数:
            link: 要移除的链接
        """
        if link is None:
            return

        link_id = link.get_id()
        if link_id not in self.links:
            return

        # Remove the link from the source and sink
        source = link.get_source()
        sink = link.get_sink()

        if source is not None:
            source_id = source.get_node_id()
            if source_id in self.links_from:
                self.links_from[source_id].discard(link)

        if sink is not None:
            sink_id = sink.get_extended_id()
            if sink_id in self.links_to:
                self.links_to[sink_id].discard(link)

        # Remove the link
        del self.links[link_id]

    def get_scene_time(self) -> str:
        """
        获取结构的场景时间

        返回:
            结构的场景时间
        """
        return self.scene_time

    def set_scene_time(self, time: str) -> None:
        """
        设置结构的场景时间

        参数:
            time: 要设置的场景时间
        """
        self.scene_time = time

    def get_scene_site(self) -> str:
        """
        获取结构的场景位置

        返回:
            结构的场景位置
        """
        return self.scene_site

    def set_scene_site(self, site: str) -> None:
        """
        设置结构的场景位置

        参数:
            site: 要设置的场景位置
        """
        self.scene_site = site

    def get_broad_scene_count(self) -> int:
        """
        获取结构的广播场景计数

        返回:
            结构的广播场景计数
        """
        return self.broad_scene_count

    def set_broad_scene_count(self, count: int) -> None:
        """
        设置结构的广播场景计数

        参数:
            count: 要设置的广播场景计数
        """
        self.broad_scene_count = count

    def save_scene(self, ns: NodeStructure) -> None:
        """
        保存此结构的场景

        参数:
            ns: 从中保存场景的结构
        """
        if ns is None:
            return

        self.scene_time = ns.get_scene_time()
        self.scene_site = ns.get_scene_site()
        self.broad_scene_count = ns.get_broad_scene_count()

    def get_neo_node(self, name: str) -> Optional[Node]:
        """
        从Neo4j数据库获取节点
        参数:
            name: 节点名称
        返回:
            节点或None
        """
        # 延迟导入，避免循环导入
        from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
        return NeoUtil.get_node(name)

    def getNeoNode(self, name: str) -> Optional[Node]:
        return self.get_neo_node(name)

    def get_neo_links(self, node: Node) -> Optional[Set[Link]]:
        """
        从Neo4j数据库获取节点的链接

        参数:
            node: 节点

        返回:
            链接集合或None
        """
        # 延迟导入，避免循环导入
        from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
        return NeoUtil.get_links(node)

    def getNeoLinks(self, node: Node) -> Optional[Set[Link]]:
        """
        从Neo4j数据库获取节点的链接 (Java风格命名)

        参数:
            node: 节点

        返回:
            链接集合或None
        """
        return self.get_neo_links(node)

    def get_connected_sinks(self, node) -> Set[Link]:
        """
        获取与指定节点相连的所有汇端链接

        参数:
            node: 指定节点，可以是Node对象、节点ID或节点名称

        返回:
            与指定节点相连的所有汇端链接
        """
        if node is None:
            return set()

        # 如果node不是Node对象，尝试获取节点对象
        if not isinstance(node, Node):
            if isinstance(node, int):
                # 如果是整数（节点ID）
                node = self.get_node(node)
            elif isinstance(node, str):
                # 如果是字符串（节点名称）
                node = self.getNodeByName(node)
                if node is None:
                    # 尝试从Neo4j获取节点
                    node = self.get_neo_node(node)
            else:
                self.logger.warning(f"Unsupported node type: {type(node)} at tick {TaskManager.get_current_tick()}")
                return set()

            # 如果仍然无法获取节点对象
            if node is None:
                return set()

        # 获取节点的所有链接
        candidate_links = self.get_links_of_source(node)

        # 如果没有链接或链接为空，尝试从Neo4j获取
        if not candidate_links:
            candidate_links = self.get_neo_links(node)
            if candidate_links:
                # 将获取到的链接添加到结构中
                for link in candidate_links:
                    self.add_default_link(link)

        return set(candidate_links) if candidate_links else set()

    def getConnectedSinks(self, node) -> Set[Link]:
        """
        获取与指定节点相连的所有汇端链接(get_connected_sinks的别名)

        参数:
            node: 指定节点，可以是Node对象、节点ID或节点名称

        返回:
            与指定节点相连的所有汇端链接
        """
        return self.get_connected_sinks(node)

    def get_connected_sources(self, linkable) -> Dict[Node, Link]:
        """
        获取与指定可链接对象相连的所有源端节点

        参数:
            linkable: 指定可链接对象，可以是Linkable对象、节点ID或节点名称

        返回:
            与指定可链接对象相连的所有源端节点及其链接的映射
        """
        if linkable is None:
            return {}

        # 如果linkable不是Linkable对象，尝试获取节点对象
        if not isinstance(linkable, Linkable):
            if isinstance(linkable, int):
                # 如果是整数（节点ID）
                linkable = self.get_node(linkable)
            elif isinstance(linkable, str):
                # 如果是字符串（节点名称）
                linkable = self.getNodeByName(linkable)
                if linkable is None:
                    # 尝试从Neo4j获取节点
                    linkable = self.get_neo_node(linkable)
            else:
                self.logger.warning(f"Unsupported linkable type: {type(linkable)} at tick {TaskManager.get_current_tick()}")
                return {}

            # 如果仍然无法获取节点对象
            if linkable is None:
                return {}

        # 获取可链接对象的所有链接
        candidate_links = self.get_links_of_sink(linkable)

        # 创建源端节点到链接的映射
        source_link_map = {}
        if candidate_links:
            for link in candidate_links:
                source = link.get_source()
                if source != linkable:  # 避免自环
                    source_link_map[source] = link

        return source_link_map

    def getConnectedSources(self, linkable) -> Dict[Node, Link]:
        """
        获取与指定可链接对象相连的所有源端节点(get_connected_sources的别名)

        参数:
            linkable: 指定可链接对象，可以是Linkable对象、节点ID或节点名称

        返回:
            与指定可链接对象相连的所有源端节点及其链接的映射
        """
        return self.get_connected_sources(linkable)

    def get_subgraph(self, nodes: Collection[Node], d: int, threshold: float = 0.0) -> 'NodeStructure':
        """
        获取子图

        参数:
            nodes: 指定节点集合
            d: 指定节点与其邻居节点之间的最远距离
            threshold: 节点激活的下限

        返回:
            子图
        """
        if nodes is None or not nodes:
            self.logger.warning(f"Collection of specified nodes are not available or empty at tick {TaskManager.get_current_tick()}")
            return None

        if d < 0:
            self.logger.warning(f"Desired distance should not be negative at tick {TaskManager.get_current_tick()}")
            return None

        if threshold < 0:
            self.logger.warning(f"Desired threshold should not be negative at tick {TaskManager.get_current_tick()}")
            return None

        # 距离不应该大于所有链接的数量
        if d > self.get_link_count():
            d = self.get_link_count()

        # 创建子图
        sub_node_structure = NodeStructureImpl(self.node_type, self.link_type)

        # 对每个指定节点进行深度优先搜索
        for node in nodes:
            self._depth_first_search(node, d, sub_node_structure, threshold)

        # 添加所有简单链接到子图
        for sub_node in sub_node_structure.get_nodes():
            # 获取子图中已有节点的简单链接
            sources = self.get_connected_sources(sub_node)
            for n in sources.keys():
                # 仅当子图中存在其源时才添加简单链接
                if sub_node_structure.contains_node(n):
                    sub_node_structure.add_default_link(sources[n])

        return sub_node_structure

    def getSubgraph(self, nodes: Collection[Node], d: int, threshold: float = 0.0) -> 'NodeStructure':
        """
        获取子图(get_subgraph的别名)

        参数:
            nodes: 指定节点集合
            d: 指定节点与其邻居节点之间的最远距离
            threshold: 节点激活的下限

        返回:
            子图
        """
        return self.get_subgraph(nodes, d, threshold)

    def _depth_first_search(self, current_node: Node, distance_left_to_go: int, sub_node_structure: 'NodeStructure', threshold: float):
        """
        深度优先搜索

        参数:
            current_node: 当前节点
            distance_left_to_go: 剩余距离
            sub_node_structure: 子图
            threshold: 节点激活的下限
        """
        actual = self.get_node(current_node.get_node_id())
        if actual is not None and actual.get_activation() >= threshold:
            sub_node_structure.add_node(actual, self.node_type)

            # 获取所有连接的汇端
            sub_sinks = self.get_connected_sinks(actual)
            for link in sub_sinks:
                sink = link.get_sink()
                if isinstance(sink, Node) and distance_left_to_go > 0:
                    self._depth_first_search(sink, distance_left_to_go - 1, sub_node_structure, threshold)

            # 获取所有连接的源端
            sub_sources = self.get_connected_sources(actual)
            for n in sub_sources.keys():
                if distance_left_to_go > 0:
                    self._depth_first_search(n, distance_left_to_go - 1, sub_node_structure, threshold)

    def __str__(self) -> str:
        """
        返回结构的字符串表示

        返回:
            结构的字符串表示
        """
        return f"NodeStructure[nodes={len(self.nodes)}, links={len(self.links)}]"
