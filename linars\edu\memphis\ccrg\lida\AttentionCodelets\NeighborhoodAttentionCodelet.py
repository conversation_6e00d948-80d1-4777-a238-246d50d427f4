# LIDA Cognitive Framework
"""
An AttentionCodelet that seeks to create Coalitions from its sought content.
The resulting Coalition includes these nodes and possibly neighbors nodes.
"""

import logging
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WorkspaceBuffer import WorkspaceBuffer
from linars.edu.memphis.ccrg.lida.AttentionCodelets.DefaultAttentionCodelet import DefaultAttentionCodelet

class NeighborhoodAttentionCodelet(DefaultAttentionCodelet):
    """
    An AttentionCodelet that seeks to create Coalitions from its sought content.
    The resulting Coalition includes these nodes and possibly neighbors nodes.
    """
    
    def __init__(self, sought_content: str = None, ticks_per_run: int = 1):
        """
        Initialize a NeighborhoodAttentionCodelet.
        
        Args:
            sought_content: The content this codelet is looking for
            ticks_per_run: The number of ticks between runs of this codelet
        """
        super().__init__(ticks_per_run)
        self.node_structure = NodeStructureImpl()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        if sought_content is not None:
            self.bind_content(sought_content)
    
    def bind_content(self, content: str) -> None:
        """
        Bind content to this codelet.
        
        Args:
            content: The content to bind
        """
        # This method would parse the content string and create a NodeStructure
        # For now, we'll just log that we're binding content
        self.logger.debug(f"Binding content {content} at tick {TaskManager.get_current_tick()}")
    
    def buffer_contains_sought_content(self, buffer: WorkspaceBuffer) -> bool:
        """
        Returns true if specified WorkspaceBuffer contains this codelet's sought content.
        
        Args:
            buffer: The WorkspaceBuffer to be checked for content
            
        Returns:
            True if the buffer contains the sought content, False otherwise
        """
        model = buffer.get_buffer_content(None)
        
        for linkable in self.sought_content.get_linkables():
            if not model.contains_linkable(linkable):
                return False
        
        self.logger.debug(f"Attn codelet {self} found sought content at tick {TaskManager.get_current_tick()}")
        return True
    
    def retrieve_workspace_content(self, buffer: WorkspaceBuffer) -> NodeStructure:
        """
        Retrieves content from the specified WorkspaceBuffer.
        
        Args:
            buffer: The WorkspaceBuffer to retrieve content from Returns:
            The retrieved content
        """
        result = NodeStructureImpl()
        model = buffer.get_buffer_content(None)
        
        # Add all linkables from the sought content to the result
        for linkable in self.sought_content.get_linkables():
            if isinstance(linkable, Linkable):
                # Find the corresponding linkable in the model
                model_linkable = model.get_linkable(linkable)
                if model_linkable is not None:
                    # Add the linkable to the result
                    if model_linkable.is_node():
                        result.add_default_node(model_linkable)
                    else:
                        result.add_default_link(model_linkable)
        
        # If retrieval depth is greater than 0, add neighboring nodes
        if self.retrieval_depth > 0:
            # Add links connected to the nodes in the result
            for node in result.get_nodes():
                for link in model.get_links_of_source(node):
                    result.add_default_link(link)
                
                for link in model.get_links_of_sink(node):
                    result.add_default_link(link)
        
        return result
