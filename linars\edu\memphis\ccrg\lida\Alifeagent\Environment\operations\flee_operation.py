"""
逃跑操作实现

本模块提供ALife环境中逃跑操作的实现
"""
from typing import List, Optional, Any

from linars.edu.memphis.ccrg.alife.elements.alife_object import ALifeObject
from linars.edu.memphis.ccrg.alife.elements.cell import Cell
from linars.edu.memphis.ccrg.alife.opreations.world_operation import WorldOperation

class FleeOperation(WorldOperation):
    """
    逃跑操作实现

    该类实现ALife环境中的逃跑操作
    """

    def __init__(self):
        """初始化逃跑操作"""
        super().__init__("flee")

    def execute(self, actor: ALifeObject, target: Optional[ALifeObject], *params) -> bool:
        """
        执行逃跑操作

        参数:
            actor: 执行逃跑行为的对象
            target: 逃跑目标(未使用)
            params: 附加参数

        返回:
            操作成功返回True，否则返回False
        """
        # Get the actor's cell
        cell = actor.get_container()
        if not isinstance(cell, Cell):
            return False

        # Get the actor's direction
        direction = actor.get_attribute("direction")
        if direction is None:
            return False

        # Determine the opposite direction to flee
        opposite_direction = None
        if direction == 'N':
            opposite_direction = 'S'
        elif direction == 'S':
            opposite_direction = 'N'
        elif direction == 'E':
            opposite_direction = 'W'
        elif direction == 'W':
            opposite_direction = 'E'

        # Set the new direction
        actor.set_attribute("direction", opposite_direction)

        # Get coordinates for the new position
        x = cell.get_x_coordinate()
        y = cell.get_y_coordinate()

        if opposite_direction == 'N':
            y -= 1
        elif opposite_direction == 'S':
            y += 1
        elif opposite_direction == 'E':
            x += 1
        elif opposite_direction == 'W':
            x -= 1

        # Get the world and check if the new position is valid
        world = cell.get_world()
        if not (0 <= x < world.get_width() and 0 <= y < world.get_height()):
            return False

        # Move to the new cell
        new_cell = world.get_cell(x, y)
        cell.remove_object(actor)
        new_cell.add_object(actor)

        return True
