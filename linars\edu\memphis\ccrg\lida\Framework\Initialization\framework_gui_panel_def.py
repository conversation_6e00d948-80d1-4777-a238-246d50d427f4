"""
Framework GUI panel definition.

This module provides a class for defining framework GUI panels.
"""
import importlib
from typing import Dict, Any, Optional, Type

class FrameworkGuiPanelDef:
    """
    Framework GUI panel definition.
    
    This class defines a framework GUI panel.
    """
    
    def __init__(self, name: str, panel_class_name: str, position: str = None):
        """
        Initialize the framework GUI panel definition.
        
        Args:
            name: The name of the panel
            panel_class_name: The class name of the panel
            position: The position of the panel
        """
        self.name = name
        self.panel_class_name = panel_class_name
        self.position = position
    
    def get_name(self) -> str:
        """
        Get the name of the panel.
        
        Returns:
            The name of the panel
        """
        return self.name
    
    def get_panel_class_name(self) -> str:
        """
        Get the class name of the panel.
        
        Returns:
            The class name of the panel
        """
        return self.panel_class_name
    
    def get_position(self) -> Optional[str]:
        """
        Get the position of the panel.
        
        Returns:
            The position of the panel
        """
        return self.position
    
    def get_panel_class(self) -> Optional[Type]:
        """
        Get the panel class.
        
        Returns:
            The panel class or None if not found
        """
        try:
            # Split the class name into package and class
            parts = self.panel_class_name.split(".")
            class_name = parts[-1]
            package_name = ".".join(parts[:-1])
            
            # Import the module
            module = importlib.import_module(package_name)
            
            # Get the class
            class_obj = getattr(module, class_name)
            
            return class_obj
        except (ImportError, AttributeError, Exception):
            return None
