#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
SubGraph class for the LIDA framework.
"""

from typing import Dict, List, Set, Any, Optional, Collection, Tuple

from linars.edu.memphis.ccrg.lida.Nlanguage.TreeChart import <PERSON><PERSON><PERSON>
from linars.edu.memphis.ccrg.linars.term import Term


class SubGraph(TreeChart):
    """
    A subgraph in the LIDA framework.
    """
    
    def __init__(self, scene: str, budget: Budget, cond: Term, scene_term: List[Term], 
                 found_list: Collection[Term], finding_list: Collection[Term]):
        """
        Initialize a SubGraph.
        
        Args:
            scene: The scene
            budget: The budget
            cond: The condition
            scene_term: The scene terms
            found_list: The list of found terms
            finding_list: The list of terms to find
        """
        super().__init__(finding_list=finding_list, budget=budget, scene_root=cond, found_list=found_list)
        # 图比树多了环
        self.scene = scene
        self.scene_term = scene_term
