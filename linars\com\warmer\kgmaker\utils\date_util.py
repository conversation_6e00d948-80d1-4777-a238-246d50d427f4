"""
Date Utility

This module provides utility functions for working with dates.
"""
import datetime

class DateUtil:
    """Date Utility class for working with dates."""
    
    @staticmethod
    def get_date_now():
        """
        Get the current date and time as a string.
        
        Returns:
            A string representation of the current date and time
        """
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    @staticmethod
    def get_date_now_str():
        """
        Get the current date and time as a string without separators.
        
        Returns:
            A string representation of the current date and time without separators
        """
        return datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    
    @staticmethod
    def get_date_str():
        """
        Get the current date as a string.
        
        Returns:
            A string representation of the current date
        """
        return datetime.datetime.now().strftime("%Y-%m-%d")
    
    @staticmethod
    def parse_date(date_str, format_str="%Y-%m-%d %H:%M:%S"):
        """
        Parse a date string.
        
        Args:
            date_str: The date string to parse
            format_str: The format of the date string
            
        Returns:
            A datetime object
        """
        return datetime.datetime.strptime(date_str, format_str)
    
    @staticmethod
    def format_date(date, format_str="%Y-%m-%d %H:%M:%S"):
        """
        Format a date.
        
        Args:
            date: The date to format
            format_str: The format to use
            
        Returns:
            A string representation of the date
        """
        return date.strftime(format_str)
