"""
Narsese解析器抽象类
定义解析Narsese的接口规范
"""
from abc import ABC, abstractmethod

from linars.org.opennars.entity.task import Task

class Parser(ABC):
    """
    Narsese解析器抽象类
    定义解析Narsese的接口规范
    """

    @abstractmethod
    def parse_task(self, narsese: str) -> Task:
        """
        将Narsese字符串解析为任务对象

        参数:
            narsese: Narsese格式字符串

        返回:
            Task: 解析后的任务对象

        异常:
            InvalidInputException: 当输入无效时抛出
        """
        pass

    class InvalidInputException(Exception):
        """
        无效输入异常
        处理各种无效输入情况
        """

        def __init__(self, message: str):
            """
            无效输入异常构造函数

            参数:
                message: 错误描述信息
            """
            super().__init__(message)
