"""
时态枚举类，用于表示NARS中的时态
"""
from enum import Enum
from typing import Dict, Optional

class Tense(Enum):
    """
    时态枚举类

    表示NARS中的三种基本时态：过去、现在和未来
    """

    Past = ":\\:"
    Present = ":|:"
    Future = ":/:"

    # 静态属性，表示永恒时态（无时态）
    Eternal = None

    def __str__(self) -> str:
        """
        获取时态的字符串表示

        返回:
            str: 时态符号
        """
        return self.value

    @classmethod
    def tense(cls, s: str) -> Optional['Tense']:
        """
        根据字符串获取对应的时态

        参数:
            s: 时态字符串

        返回:
            Tense: 对应的时态，如果不存在则返回None
        """
        # 直接遍历枚举值并比较
        for t in cls:
            if t.value == s:
                return t

        # 如果没有找到匹配的时态，返回None
        return None
