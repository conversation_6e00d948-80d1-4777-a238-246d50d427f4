# LIDA Cognitive Framework
"""
A listener for local associations from episodic memory.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure

class LocalAssociationListener(ModuleListener):
    """
    A listener for local associations from episodic memory.
    """
    
    @abstractmethod
    def receive_local_association(self, association: NodeStructure) -> None:
        """
        Receive a local association from episodic memory.
        
        Args:
            association: The local association
        """
        pass
