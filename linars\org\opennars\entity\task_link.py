"""
TaskLink将任务(Task)连接到概念(Concept)

将任务与概念分开的原因是同一个任务可以从多个概念链接，具有不同的预算值。
根据它们引用的任务，TaskLink是唯一的。
"""
from typing import Optional, Any, List, Deque, Tuple, TypeVar
from collections import deque

from linars.org.opennars.entity.item import Item
from linars.org.opennars.entity.term_link import TermLink
from linars.org.opennars.entity.t_link import TLink

T = TypeVar('T')

class TaskLink(Item, TLink['Task']):
    """
    概念中对任务的引用
    """

    class Recording:
        """
        记录TermLink及其最近使用时间
        """

        def __init__(self, link, time):
            """
            构造函数

            参数:
                link: TermLink
                time: 时间
            """
            self.link = link
            self.time = time

        def get_time(self) -> int:
            """
            获取时间

            返回:
                int: 时间
            """
            return self.time

        def set_time(self, t: int) -> None:
            """
            设置时间

            参数:
                t: 时间
            """
            self.time = t

    def __init__(self, target=None, term_link=None, budget=None, record_length=10):
        """
        构造函数

        参数:
            target: 目标任务
            term_link: TermLink模板
            budget: 预算值
            record_length: 记录长度
        """
        super().__init__(budget)
        self.targetTask = target  # 目标任务

        # 设置类型和索引
        if term_link is None:
            self.type = TermLink.SELF
            self.index = None
        else:
            self.type = term_link.type
            self.index = term_link.index

        self.record_length = record_length  # 记录长度
        self.records = deque(maxlen=record_length)  # 已使用TermLink的记录

        # 计算哈希值
        target_hash = hash(self.targetTask) if self.targetTask else 0
        type_hash = hash(self.type) if hasattr(self, 'type') else 0
        index_hash = hash(tuple(self.index)) if hasattr(self, 'index') and self.index else 0
        self.hash = (((target_hash * 31) + type_hash) * 31) + index_hash

    def get_target(self):
        """
        获取目标任务

        返回:
            Task: 目标任务
        """
        return self.targetTask

    def get_index(self, i: int) -> int:
        """
        按级别获取一个索引

        参数:
            i: 索引级别

        返回:
            int: 索引值，如果不存在则返回-1
        """
        if hasattr(self, 'index') and self.index is not None and i < len(self.index):
            return self.index[i]
        return -1

    def get_priority(self) -> float:
        """
        获取优先级

        返回:
            float: 优先级
        """
        return self.budget.get_priority() if hasattr(self, 'budget') and self.budget else 0.0

    def get_term(self):
        """
        获取目标术语

        返回:
            Term: 目标术语
        """
        if self.targetTask and hasattr(self.targetTask, 'get_term'):
            return self.targetTask.get_term()
        elif self.targetTask and hasattr(self.targetTask, 'sentence') and hasattr(self.targetTask.sentence, 'term'):
            return self.targetTask.sentence.term
        return None

    def name(self):
        """
        获取链接名称

        返回:
            Task: 目标任务
        """
        return self.targetTask

    def novel(self, term_link, current_time, parameters, transform_task=False) -> bool:
        """
        检查TermLink相对于现有的是否新颖

        参数:
            term_link: 要检查的TermLink
            current_time: 当前时间
            parameters: 系统参数
            transform_task: 是否转换任务

        返回:
            bool: 如果TermLink新颖则为True
        """
        b_term = term_link.target
        if not transform_task and b_term == self.get_term():
            return False

        link_key = term_link.name()

        # 从最早（第一个）到最新（最后一个）遍历FIFO队列
        for i, r in enumerate(self.records):
            if link_key == r.link:
                if current_time < r.get_time() + parameters.NOVELTY_HORIZON:
                    # 太最近，不新颖
                    return False
                else:
                    # 发生得足够久，我们已经忘记了一些，使它看起来更新颖
                    r.set_time(current_time)
                    # 从当前位置移除并添加到队列末尾
                    self.records.remove(r)
                    self.records.append(r)
                    return True

        # 保持记录队列最大有限大小
        if len(self.records) + 1 >= self.record_length:
            self.records.popleft()

        # 将知识引用添加到记录中
        self.records.append(self.Recording(link_key, current_time))
        return True

    def __str__(self) -> str:
        """
        字符串表示

        返回:
            str: 字符串表示
        """
        return super().__str__()

    def to_string_brief(self) -> str:
        """
        简要字符串表示

        返回:
            str: 简要字符串表示
        """
        return super().__str__()

    def __eq__(self, other: Any) -> bool:
        """
        检查相等性

        参数:
            other: 要比较的对象

        返回:
            bool: 如果相等则为True
        """
        if other is self:
            return True

        if self.hash != hash(other):
            return False

        if not isinstance(other, TaskLink):
            return False

        if hasattr(self, 'type') and hasattr(other, 'type') and self.type != other.type:
            return False

        if hasattr(self, 'index') and hasattr(other, 'index') and self.index != other.index:
            return False

        return self.targetTask == other.targetTask

    def __hash__(self) -> int:
        """
        哈希码

        返回:
            int: 哈希码
        """
        return self.hash
