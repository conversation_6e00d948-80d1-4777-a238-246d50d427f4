# LIDA Cognitive Framework
"""
Basic implementation of SensoryMemory.
"""

import logging
from typing import Dict, Any, Optional, Set
from linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemoryImpl import SensoryMemoryImpl

class BasicSensoryMemory(SensoryMemoryImpl):
    """
    Basic implementation of SensoryMemory.
    """
    
    def __init__(self):
        """
        Initialize a BasicSensoryMemory.
        """
        super().__init__()
        self.origin_objects = set()
        self.next_cell_objects = set()
        self.health = 1.0
        self.message = None
        self.sensor_param: Dict[str, Any] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def init(self) -> None:
        """
        Initialize this BasicSensoryMemory.
        """
        pass
    
    def run_sensors(self) -> None:
        """
        Runs all the sensors associated with this memory. The sensors get the
        information from the environment and store in this memory for later
        processing and passing to the perceptual memory module.
        """
        if self.environment is None:
            self.logger.warning("Environment is None")
            return
        
        # Get origin objects
        self.sensor_param["mode"] = "seethis"
        self.origin_objects.clear()
        origin_objects = self.environment.get_state(self.sensor_param)
        if origin_objects is not None:
            self.origin_objects.update(origin_objects)
        
        # Get next cell objects
        self.sensor_param["mode"] = "seenext"
        self.next_cell_objects.clear()
        next_objects = self.environment.get_state(self.sensor_param)
        if next_objects is not None:
            self.next_cell_objects.update(next_objects)
        
        # Get health
        self.sensor_param["mode"] = "health"
        health = self.environment.get_state(self.sensor_param)
        if health is not None:
            self.health = health
        
        # Get message
        self.sensor_param["mode"] = "listen"
        message = self.environment.get_state(self.sensor_param)
        if message is not None:
            self.message = message
    
    def get_sensory_content(self, modality: str, params: Optional[Dict[str, Any]]) -> Any:
        """
        Returns content from this SensoryMemory.
        Intended to be used by feature detectors to get specific parts of the sensory memory.
        
        Args:
            modality: User may optionally use this parameter to specify modality
            params: Optional parameters
            
        Returns:
            Content from this SensoryMemory
        """
        if params is None:
            return None
        
        mode = params.get("mode")
        if mode == "health":
            return self.health
        
        if mode == "listen":
            return self.message
        
        location = params.get("position", 0)
        name = params.get("object", "")
        
        requested_objects = self.origin_objects if location == 0 else self.next_cell_objects
        
        if not name:
            return not requested_objects
        
        for obj in requested_objects:
            if obj.get_name() == name:
                return True
        
        return False
    
    def get_module_content(self, *params: Any) -> Any:
        """
        Get the content of this module.
        
        Args:
            params: Parameters specifying what content to return
            
        Returns:
            The content of this module
        """
        return None
    
    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.
        
        Args:
            ticks: The number of ticks to decay by
        """
        # Not applicable
        pass
