"""
Process goals in the system

This module handles goal processing, including goal revision, satisfaction checking,
and execution of operations to achieve goals.

<AUTHOR> (Java version)
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic
import random
import copy
import time

# Import only the necessary types for type hints
# All other imports will be done at runtime to avoid circular imports

class ProcessGoal:
    """
    Process goals in the system
    """

    # Forward declarations for type hints
    class Concept: pass
    class Term: pass
    class Memory: pass
    class BudgetValue: pass

    # Helper methods for truth and budget calculations
    @staticmethod
    def calc_task_achievement(t1, t2):
        """Calculate task achievement based on truth values"""
        if t1 is None or t2 is None:
            return 0.0
        return 1.0 - abs(t1.get_expectation() - t2.get_expectation())

    @staticmethod
    def solution_quality(rate_by_confidence, prob_t, solution, memory, time):
        """Evaluate the quality of the judgment as a solution to a problem"""
        try:
            from linars.org.opennars.inference.temporal_rules import TemporalRules

            problem = prob_t.sentence

            if ((prob_t.sentence.punctuation != solution.punctuation and solution.term.has_var_query()) or
                not TemporalRules.matching_order(problem.get_temporal_order(), solution.get_temporal_order())):
                return 0.0

            truth = solution.truth
            if problem.get_occurrence_time() != solution.get_occurrence_time():
                truth = solution.projection_truth(problem.get_occurrence_time(), time.time(), memory)

            if not rate_by_confidence:
                # Just some function that decreases quality of solution if it is complex,
                # and increases if it has a high truth expectation
                return truth.get_expectation() / (solution.term.get_complexity() * memory.narParameters.COMPLEXITY_UNIT) ** 0.125
            else:
                return truth.get_confidence()
        except Exception as e:
            print(f"Error in solution_quality: {e}")
            return 0.5  # Default quality

    @staticmethod
    def solution_eval(problem, solution, task, nal):
        """Evaluate the quality of a belief as a solution to a problem"""
        try:
            if problem.sentence.punctuation != solution.punctuation and solution.term.has_var_query():
                return None

            budget = None
            feedback_to_links = False

            if task is None:
                task = nal.get_current_task()
                feedback_to_links = True

            judgment_task = task.sentence.is_judgment()
            # Here it's whether it's a what or where question for budget adjustment
            rate_by_confidence = problem.get_term().has_var_query()
            quality = ProcessGoal.solution_quality(rate_by_confidence, problem, solution, nal.memory, nal.time)

            if problem.sentence.is_goal() and nal.memory.emotion is not None:
                nal.memory.emotion.adjust_satisfaction(quality, task.get_priority(), nal)

            if judgment_task:
                task.inc_priority(quality)
            else:
                task_priority = task.get_priority()
                budget = BudgetValue(
                    max(task_priority, quality),
                    task.get_durability(),
                    ProcessGoal.truth_to_quality(solution.truth),
                    nal.nar.narParameters
                )
                task.set_priority(min(1 - quality, task_priority))

            if feedback_to_links:
                t_link = nal.get_current_task_link()
                if t_link is not None:
                    t_link.set_priority(min(1 - quality, t_link.get_priority()))

                b_link = nal.get_current_belief_link()
                if b_link is not None:
                    b_link.inc_priority(quality)

            return budget
        except Exception as e:
            print(f"Error in solution_eval: {e}")
            return None

    @staticmethod
    def interval_projection(nal, new_belief_term, old_belief_term, recent_intervals, new_truth):
        """Apply interval projection to terms"""
        try:
            # This is a simplified version - in the Java code this is more complex
            # with extraction of intervals and calculations
            return True  # Default to using new belief term
        except Exception as e:
            print(f"Error in interval_projection: {e}")
            return True

    @staticmethod
    def truth_revision(new_truth, old_truth, parameters):
        """Perform truth revision"""
        try:
            from linars.org.opennars.inference.truth_functions import TruthFunctions
            return TruthFunctions.revision(new_truth, old_truth, parameters)
        except Exception as e:
            print(f"Error in truth_revision: {e}")
            return new_truth.clone()

    @staticmethod
    def revise_budget(new_truth, old_truth, truth, feedback_to_links, nal):
        """Calculate budget for revision"""
        try:
            from linars.org.opennars.inference.budget_functions import BudgetFunctions
            return BudgetFunctions.revise(new_truth, old_truth, truth, feedback_to_links, nal)
        except Exception as e:
            print(f"Error in revise_budget: {e}")
            return BudgetValue(0.8, 0.8, 0.8, nal.nar.narParameters)

    @staticmethod
    def truth_to_quality(truth):
        """Convert truth to quality"""
        try:
            from linars.org.opennars.inference.budget_functions import BudgetFunctions
            return BudgetFunctions.truth_to_quality(truth)
        except Exception as e:
            print(f"Error in truth_to_quality: {e}")
            return 0.8  # Default quality

    memory = None  # Static memory reference

    @staticmethod
    def get_mem():
        """Get the memory instance"""

        try:
            # Try to import AgentStarter and get memory from there
            try:
                from linars.edu.memphis.ccrg.lida.Nlanguage.SubGraphSet import SubGraphSet
                # In Python implementation, we need to get the memory from ModuleName.GoalGraph
                if ProcessGoal.memory is not None and isinstance(ProcessGoal.memory, SubGraphSet):
                    return  # Already initialized
                from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName

                if hasattr(AgentStarter, 'nar') and hasattr(AgentStarter.nar, 'memory'):
                    # First try to get memory from workspace using ModuleName.GoalGraph
                    if hasattr(AgentStarter, 'pam') and hasattr(AgentStarter.pam, 'get_listener'):
                        workspace = AgentStarter.pam.get_listener()

                        if hasattr(workspace, 'get_submodule'):
                            # Get the GoalGraph buffer
                            goal_graph_buffer = workspace.get_submodule(ModuleName.GoalGraph)

                            if goal_graph_buffer is not None and hasattr(goal_graph_buffer, 'get_buffer_content'):
                                # Get the buffer content
                                goal_graph = goal_graph_buffer.get_buffer_content(None)
                                if goal_graph is not None:
                                    ProcessGoal.memory = goal_graph
                                    print("Using memory from GoalGraph buffer")

                    # If not found, use regular memory
                    if ProcessGoal.memory is None:
                        ProcessGoal.memory = AgentStarter.nar.memory
                        print("Using memory from AgentStarter.nar")
            except ImportError:
                print("AgentStarter or ModuleName not available")

            # If memory is still not initialized, we'll initialize it when needed
            if ProcessGoal.memory is None:
                print("Memory will be initialized when needed")
        except Exception as e:
            print(f"Error in get_mem: {e}")



    @staticmethod
    def process_goal(concept, nal, task):
        """
        To accept a new goal, and check for revisions and realization, then
        decide whether to actively pursue it, potentially executing in case of an operation goal

        Args:
            concept: The concept of the goal
            nal: The derivation context
            task: The goal task to be processed
        """
        # Get memory reference
        ProcessGoal.get_mem()

        # If memory is not initialized, use the concept's memory
        if ProcessGoal.memory is None and hasattr(concept, 'memory'):
            ProcessGoal.memory = concept.memory
            print("Using concept's memory")

        # If memory is still not initialized, use nal's memory
        if ProcessGoal.memory is None and hasattr(nal, 'memory'):
            ProcessGoal.memory = nal.memory
            print("Using nal's memory")

        # Check if memory is initialized
        if ProcessGoal.memory is None:
            print("Warning: ProcessGoal.memory is not initialized")
            # Continue anyway, but some functionality may not work

        # Get the goal sentence
        goal = task.sentence

        # Check for existing goals to revise with
        old_goal_t = concept.selectCandidate(task, concept.desires, nal.time)
        old_goal = None
        new_stamp = goal.stamp

        if old_goal_t is not None:
            old_goal = old_goal_t.sentence
            old_stamp = old_goal.stamp

            # Check for duplicate
            if new_stamp.equals(old_stamp, False, False, True):
                return  # Duplicate goal

        belief_t = None
        if task.above_threshold():
            # Check if this goal answers any quests
            for i_quest in concept.quests:
                ProcessGoal.try_solution(task.sentence, i_quest, nal, True)

            # Check if the goal is already satisfied
            belief_t = concept.selectCandidate(task, concept.beliefs, nal.time)
            if belief_t is not None:
                ProcessGoal.try_solution(belief_t.sentence, task, nal, True)

        # Try to revise the goal with existing goals
        if old_goal_t is not None and not goal.is_revisied and ProcessGoal.revisible(goal, old_goal, nal.nar.narParameters):
            old_stamp = old_goal.stamp
            nal.set_the_new_stamp_builder(new_stamp, old_stamp, nal.time.time())

            try:
                # 使用正确的方法名 get_occurrence_time
                projected_goal = old_goal.projection(task.sentence.get_occurrence_time(),
                                                  new_stamp.get_occurrence_time(),
                                                  concept.memory)
            except AttributeError:
                # 如果方法不存在，尝试使用其他方式获取时间
                try:
                    projected_goal = old_goal.projection(task.sentence.stamp.get_occurrence_time(),
                                                      new_stamp.get_occurrence_time(),
                                                      concept.memory)
                except Exception as e:
                    print(f"Error projecting goal: {e}")
                    projected_goal = None

            if projected_goal is not None:
                nal.set_current_belief(projected_goal)
                was_revised = ProcessGoal.revision(task.sentence, projected_goal, concept, False, nal)

                if was_revised:
                    # It was revised, so there is a new task with higher/lower desire
                    goal.is_revisied = True
                    return

        # Project the goal to current time if needed
        s2 = goal.stamp.clone()
        s2.set_occurrence_time(nal.time.time())

        if s2.after(task.sentence.stamp, nal.nar.narParameters.DURATION):
            # This task is not up to date, we have to project it first
            proj_goal = task.sentence.projection(nal.time.time(),
                                               nal.nar.narParameters.DURATION,
                                               ProcessGoal.memory)

            if (proj_goal is not None and
                proj_goal.truth.get_expectation() > nal.nar.narParameters.DECISION_THRESHOLD):
                # Keep goal updated
                nal.single_premise_task_with_sentence(proj_goal, task.budget.clone())

        if not task.above_threshold():
            return

        # Calculate anti-satisfaction (how far the goal is from being satisfied)
        anti_satisfaction = 0.5
        if belief_t is not None:
            belief = belief_t.sentence
            try:
                # 使用正确的方法名 get_occurrence_time
                projected_belief = belief.projection(task.sentence.get_occurrence_time(),
                                                  nal.nar.narParameters.DURATION,
                                                  ProcessGoal.memory)
            except AttributeError:
                # 如果方法不存在，尝试使用其他方式获取时间
                try:
                    projected_belief = belief.projection(task.sentence.stamp.get_occurrence_time(),
                                                      nal.nar.narParameters.DURATION,
                                                      ProcessGoal.memory)
                except Exception as e:
                    print(f"Error projecting belief: {e}")
                    # 如果无法投影，使用原始信念
                    projected_belief = belief
            anti_satisfaction = task.sentence.truth.get_exp_dif_abs(projected_belief.truth)

        # Adjust priority based on anti-satisfaction
        task.set_priority(task.get_priority() * anti_satisfaction)

        if not task.above_threshold():
            return

        # Check if the goal is fulfilled
        is_fulfilled = anti_satisfaction < nal.nar.narParameters.SATISFACTION_TRESHOLD

        # Project the goal to current time
        projected_goal = goal.projection(nal.time.time(), nal.time.time(), ProcessGoal.memory)

        if not (projected_goal is not None and task.above_threshold() and not is_fulfilled):
            return

        # Check if babbling is inhibited for this goal
        inhibited_babbling_goal = task.is_input() and not concept.allow_babbling
        if inhibited_babbling_goal:
            return

        # Find the best reaction for this goal
        ProcessGoal.best_reaction_for_goal(concept, nal, projected_goal, task)

        # Add the goal to the concept's desire table
        concept.add_to_table(task, False, concept.desires,
                           nal.nar.narParameters.CONCEPT_GOALS_MAX,
                           "ConceptGoalAdd", "ConceptGoalRemove")

        # Generate internal experience from the task
        ProcessGoal.internal_experience_from_task(concept.memory, task, False, nal.time)

        # Process operation goal if needed
        if hasattr(task.sentence.term, 'is_operation') and task.sentence.term.is_operation():
            ProcessGoal.process_operation_goal(projected_goal, nal, concept, old_goal_t, task)

    @staticmethod
    def try_solution(belief, task, nal, report_success):
        """
        Try to use a belief to satisfy a goal or question

        Args:
            belief: The belief to use
            task: The task to satisfy
            nal: The derivation context
            report_success: Whether to report success

        Returns:
            bool: True if successful
        """
        try:
            problem = task.sentence
            memory = nal.memory
            old_best = task.get_best_solution()
            task.set_achievement(ProcessGoal.calc_task_achievement(task.sentence.truth, belief.truth))

            if old_best is not None:
                rate_by_confidence = old_best.get_term().equals(belief.get_term())
                new_q = ProcessGoal.solution_quality(rate_by_confidence, task, belief, memory, nal.time)
                old_q = ProcessGoal.solution_quality(rate_by_confidence, task, old_best, memory, nal.time)
                is_better_solution = new_q > old_q

                memory.emit("TrySolution", is_better_solution, task, belief)

                if not is_better_solution:
                    if problem.is_goal() and memory.emotion is not None:
                        memory.emotion.adjust_satisfaction(old_q, task.get_priority(), nal)

                    memory.emit("Unsolved", task, belief, "Lower quality")
                    return False

            task.set_best_solution(memory, belief, nal.time)
            budget = ProcessGoal.solution_eval(task, belief, task, nal)

            if budget is not None and budget.above_threshold():
                # Solution Activated
                if problem.punctuation == "?" or problem.punctuation == "?_":
                    if task.is_input() and report_success:  # only show input tasks as solutions
                        memory.emit("Answer", task, belief)
                    else:  # solution to quests and questions can be always showed
                        memory.emit("OutputHandler", task, belief)
                else:  # goal things only show silence related
                    memory.emit("OutputHandler", task, belief)

                print(f"ProcessGoal.try_solution---task: {task.sentence} ---- belief:{belief}")

                nal.add_task(nal.get_current_task(), budget, belief, task.get_parent_belief())
                return True
            else:
                memory.emit("Unsolved", task, belief, "Insufficient budget")

            return False
        except Exception as e:
            print(f"Error in try_solution: {e}")
            return False

    @staticmethod
    def revisible(a, b, parameters):
        """
        Check if two sentences can be revised

        Args:
            a: First sentence
            b: Second sentence
            parameters: NAR parameters

        Returns:
            bool: True if revisible
        """
        try:
            # 使用正确的方法名 get_occurrence_time
            if not a.is_eternal() and not b.is_eternal() and abs(a.get_occurrence_time() - b.get_occurrence_time()) > parameters.REVISION_MAX_OCCURRENCE_DISTANCE:
                return False
        except AttributeError:
            # 如果方法不存在，尝试使用其他方式获取时间
            try:
                if not a.is_eternal() and not b.is_eternal() and abs(a.stamp.get_occurrence_time() - b.stamp.get_occurrence_time()) > parameters.REVISION_MAX_OCCURRENCE_DISTANCE:
                    return False
            except Exception as e:
                print(f"Error checking occurrence time in revisible: {e}")
                # 如果无法获取时间，假设可以修订
                pass

        if a.term.term_indices is not None and b.term.term_indices is not None:
            for i in range(len(a.term.term_indices)):
                if a.term.term_indices[i] != b.term.term_indices[i]:
                    return False

        return True

    @staticmethod
    def revision(task_sentence, belief_sentence, concept, report_success, nal):
        """
        Revise two sentences

        Args:
            task_sentence: The task sentence
            belief_sentence: The belief sentence
            concept: The concept
            report_success: Whether to report success
            nal: The derivation context

        Returns:
            bool: True if revised
        """
        try:
            # Import here to avoid circular imports
            from linars.org.opennars.language.implication import Implication

            if task_sentence.term is None:
                return False

            task_sentence.stamp.already_anticipated_neg_confirmation = belief_sentence.stamp.already_anticipated_neg_confirmation
            new_truth = task_sentence.truth.clone()
            old_truth = belief_sentence.truth

            # Check for interval projection
            use_new_belief_term = ProcessGoal.interval_projection(nal, task_sentence.get_term(), belief_sentence.get_term(), concept.recent_intervals, new_truth)

            # Perform truth revision
            truth = ProcessGoal.truth_revision(new_truth, old_truth, nal.nar.narParameters)

            # Calculate budget
            budget = ProcessGoal.revise_budget(new_truth, old_truth, truth, report_success, nal)

            if budget.above_threshold():
                counter = -1  # -1 is invalid
                if isinstance(task_sentence.term, Implication) and isinstance(belief_sentence.term, Implication):
                    counter = task_sentence.term.counter + belief_sentence.term.counter  # add because the evidence adds up

                return nal.double_premise_task_revised(use_new_belief_term and task_sentence.term or belief_sentence.term, truth, budget, counter)

            return False
        except Exception as e:
            print(f"Error in revision: {e}")
            return False

    @staticmethod
    def internal_experience_from_task(memory, task, is_belief, time):
        """
        Generate internal experience from a task

        Args:
            memory: The memory
            task: The task
            is_belief: Whether the task is a belief
            time: The time
        """
        if memory.internalExperience is None:
            return

        if not memory.internalExperience.OLD_BELIEVE_WANT_EVALUATE_WONDER_STRATEGY:
            ProcessGoal.internal_experience_from_task_internal(memory, task, False, time)

    @staticmethod
    def internal_experience_from_task_internal(memory, task, full, time):
        """
        Create internal experience from task (internal implementation).

        Args:
            memory: The memory
            task: The task
            full: Whether to use full reflection
            time: The time

        Returns:
            bool: True if successful
        """
        try:
            # Import here to avoid circular imports
            from linars.org.opennars.entity.truth_value import TruthValue
            from linars.org.opennars.entity.sentence import Sentence
            from linars.org.opennars.entity.task import Task
            from linars.org.opennars.language.operation import Operation

            # Check if internal experience is enabled
            if not hasattr(memory, 'internalExperience') or not memory.internalExperience.enabled:
                return False

            # Check priority thresholds
            if task.sentence.punctuation in ["?", "?_"]:
                if task.get_priority() < memory.internalExperience.MINIMUM_PRIORITY_TO_CREATE_WONDER_EVALUATE:
                    return False
            elif task.get_priority() < memory.internalExperience.MINIMUM_PRIORITY_TO_CREATE_WANT_BELIEVE_ETC:
                return False

            content = task.get_term()
            # To prevent infinite recursions
            if isinstance(content, Operation):
                return True

            sentence = task.sentence
            truth = TruthValue(1.0, memory.narParameters.DEFAULT_JUDGMENT_CONFIDENCE, memory.narParameters)
            stamp = sentence.stamp.clone()
            stamp.set_occurrence_time(time.time())

            # Convert sentence to term
            ret = ProcessGoal.to_term(sentence, memory, time)
            if ret is None:
                return True

            # Create new sentence
            j = Sentence(
                ret,
                ".",  # JUDGMENT_MARK
                truth,
                stamp
            )

            # Create budget
            new_budget = BudgetValue(
                memory.narParameters.DEFAULT_JUDGMENT_CONFIDENCE * memory.internalExperience.INTERNAL_EXPERIENCE_PRIORITY_MUL,
                memory.narParameters.DEFAULT_JUDGMENT_PRIORITY * memory.internalExperience.INTERNAL_EXPERIENCE_DURABILITY_MUL,
                ProcessGoal.truth_to_quality(truth),
                memory.narParameters
            )

            if not memory.internalExperience.OLD_BELIEVE_WANT_EVALUATE_WONDER_STRATEGY:
                new_budget.set_priority(task.get_priority() * memory.internalExperience.INTERNAL_EXPERIENCE_PRIORITY_MUL)
                new_budget.set_durability(task.get_durability() * memory.internalExperience.INTERNAL_EXPERIENCE_DURABILITY_MUL)

            # Create new task
            new_task = Task(j, new_budget, Task.INPUT)
            print(f"ProcessGoal.internal_experience_from_task_internal: {new_task}")
            memory.add_new_task(new_task, "Internal")
            return False
        except Exception as e:
            print(f"Error in internal_experience_from_task_internal: {e}")
            return False

    @staticmethod
    def to_term(s, mem, time):
        """
        Convert a sentence to a term.

        Args:
            s: The sentence
            mem: The memory
            time: The time

        Returns:
            Term: The term or None
        """
        try:
            # Import here to avoid circular imports
            from linars.org.opennars.language.inheritance import Inheritance
            from linars.org.opennars.language.product import Product

            op_name = None

            if s.punctuation == ".":
                op_name = "^believe"
                if not mem.internalExperience.ALLOW_WANT_BELIEF:
                    return None
            elif s.punctuation == "!":
                op_name = "^want"
                if not mem.internalExperience.ALLOW_WANT_BELIEF:
                    return None
            elif s.punctuation == "?":
                op_name = "^wonder"
            elif s.punctuation == "?_":
                op_name = "^evaluate"
            else:
                return None

            op_term = mem.get_operator(op_name)
            arg = [Term.SELF, s.get_term()]

            if s.truth is not None:
                projected = s.projection(time.time(), time.time(), mem)
                if projected is not None and projected.truth is not None:
                    arg.append(projected.truth.to_word_term())

            # Create operation term
            operation = Inheritance.make(Product(arg), op_term)
            if operation is None:
                raise Exception(f"Unable to create Inheritance: {op_term}, {arg}")

            return operation
        except Exception as e:
            print(f"Error in to_term: {e}")
            return None

    @staticmethod
    def best_reaction_for_goal(concept, nal, projected_goal, task):
        """
        When a goal is processed, use the best memorized reaction
        that is applicable to the current context (recent events) in case that it exists.
        This is a special case of the choice rule and allows certain behaviors to be automated.

        Args:
            concept: The concept of the goal to realize
            nal: The derivation context
            projected_goal: The current goal
            task: The goal task
        """
        # Increment the concept's acquired quality
        concept.inc_acquired_quality()

        # Pull up variable based preconditions from component concepts
        ret = projected_goal.get_term().count_term_recursively(None)
        general_preconditions = []

        for t in ret.keys():
            # Get the concept to pull preconditions from
            get_concept = nal.nar.memory.concept(t)
            # Skip if target concept does not exist or is the same as the goal concept
            if get_concept is None or get_concept == concept:
                continue
            # Pull variable based preconditions from component concepts
            useful_component = False
            for precon2 in get_concept.general_executable_preconditions:
                # Check whether the conclusion matches
                # 处理precon2.sentence.term的不同类型
                precon_predicate = None
                precon_term = precon2.sentence.term

                # 如果precon_term是Statement类型
                if hasattr(precon_term, 'get_predicate'):
                    precon_predicate = precon_term.get_predicate()
                # 如果precon_term是列表类型
                elif isinstance(precon_term, list) and len(precon_term) >= 2:
                    precon_predicate = precon_term[1]  # 列表中第二个元素通常是谓词
                # 如果precon_term有term属性且是列表
                elif hasattr(precon_term, 'term') and isinstance(precon_term.term, list) and len(precon_term.term) >= 2:
                    precon_predicate = precon_term.term[1]

                if precon_predicate is not None and ProcessGoal.find_substitute(precon_predicate, projected_goal.term):
                    for prec in get_concept.general_executable_preconditions:
                        general_preconditions.append(prec)
                        useful_component = True
            if useful_component:
                # Useful as it contributed predictive hypotheses
                get_concept.inc_acquired_quality()
        # Accumulate all general preconditions of itself too
        general_preconditions.extend(concept.general_executable_preconditions)
        # Apply rule for general hypotheses
        ProcessGoal.apply_rule(concept, nal, projected_goal, task, general_preconditions)
        # Apply rule for specific hypotheses
        ProcessGoal.apply_rule(concept, nal, projected_goal, task, concept.executable_preconditions)

    @staticmethod
    def find_substitute(term1, term2):
        """
        Find substitution between terms

        Args:
            term1: First term
            term2: Second term

        Returns:
            bool: True if substitution found
        """
        try:
            from linars.org.opennars.language.variables import Variables
            from linars.org.opennars.io.symbols import VAR_INDEPENDENT
            from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
            # Get memory reference
            ProcessGoal.get_mem()
            # Replace intervals if needed
            term1_replaced = CompoundTerm.replace_intervals(term1) if hasattr(term1, 'replace_intervals') else term1
            term2_replaced = CompoundTerm.replace_intervals(term2) if hasattr(term2, 'replace_intervals') else term2
            # Find substitution
            return Variables.find_substitute(
                ProcessGoal.memory.random_number,
                VAR_INDEPENDENT,
                term1_replaced,
                term2_replaced,
                {},
                {}
            )
        except Exception as e:
            print(f"Error in find_substitute: {e}")
            return False

    @staticmethod
    def apply_rule(concept, nal, projected_goal, task, table):
        """
        Apply choice rule to a table of preconditions

        Args:
            concept: The concept
            nal: The derivation context
            projected_goal: The projected goal
            task: The task
            table: The table of preconditions
        """
        from linars.org.opennars.control.concept.process_anticipation import ProcessAnticipation
        anticipations_to_make = {}

        # Apply choice rule, using the highest truth expectation solution
        best_op_with_metas = ProcessGoal.calc_best_executable_precondition(
            nal, concept, projected_goal, table, anticipations_to_make, task)

        if best_op_with_metas is not None:
            if len(best_op_with_metas) > 1 and len(best_op_with_metas) < 4:
                ProcessGoal.memory.multi_action = True
                print(f"multiAction----------------- size: {len(best_op_with_metas)}")

            # Execute the preconditions
            for best_op_with_meta in best_op_with_metas:
                action_done = ProcessGoal.execute_precondition(
                    nal, best_op_with_meta, concept, projected_goal, task)

                if action_done:
                    ProcessGoal.memory.last2_action = ProcessGoal.memory.last_action
                    ProcessGoal.memory.last_action = str(best_op_with_meta.bestop.term[1])

                    op = ProcessGoal.memory.concept(best_op_with_meta.bestop)
                    if (op is not None and
                        best_op_with_meta.executable_precond.sentence.truth.get_confidence() >
                        nal.nar.narParameters.MOTOR_BABBLING_CONFIDENCE_THRESHOLD):
                        op.allow_babbling = False

                    print(f"Executed based on: {best_op_with_meta.executable_precond}")

                    # Create anticipations
                    for precon0 in anticipations_to_make.get(best_op_with_meta.bestop, []):
                        distance = precon0.time_offset - nal.time.time()
                        urgency = 2.0 + 1.0 / distance

                        ProcessAnticipation.anticipate(
                            nal, precon0.executable_precond.sentence,
                            precon0.executable_precond.budget,
                            precon0.mintime, precon0.maxtime,
                            urgency, precon0.substitution)

        ProcessGoal.memory.multi_action = False

    @staticmethod
    def anticipate(nal, sentence, budget, mintime, maxtime, urgency, substitution):
        """
        Create an anticipation

        Args:
            nal: The derivation context
            sentence: The sentence
            budget: The budget
            mintime: Minimum time
            maxtime: Maximum time
            urgency: Urgency
            substitution: Substitution map
        """
        try:
            from linars.org.opennars.entity.sentence import Sentence
            from linars.org.opennars.entity.task import Task
            from linars.org.opennars.entity.budget_value import BudgetValue
            from linars.org.opennars.entity.truth_value import TruthValue
            from linars.org.opennars.language.variables import Variables
            from linars.org.opennars.io.symbols import GOAL_MARK
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter

            # 获取内存引用
            ProcessGoal.get_mem()
            memory = ProcessGoal.memory
            nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None

            # 如果句子为空，则返回
            if sentence is None:
                return

            # 如果有替换映射，则实例化句子
            term = sentence.get_term()
            if substitution is not None and len(substitution) > 0:
                # 尝试实例化术语
                term_instance = Variables.instance(substitution, term, memory.random_number)
                if term_instance is not None:
                    term = term_instance

            # 创建新的句子
            new_sentence = Sentence(
                term,
                JUDGMENT_MARK,
                sentence.truth,
                sentence.stamp.clone()
            )

            # 设置发生时间
            new_sentence.stamp.set_occurrence_time(maxtime)

            # 创建新的预算
            new_budget = budget.clone()
            new_budget.set_durability(urgency)
            from linars.org.opennars.entity.task import EnumType
            # 创建新的任务
            task = Task(new_sentence, new_budget, EnumType.DERIVED)

            # 添加到内存
            if memory is not None:
                # 创建预期
                memory.create_anticipation(task, mintime, maxtime)

                # 添加任务
                memory.add_new_task(task, "Anticipation")

                # 如果nar不为空，则执行内存处理
                if nar is not None:
                    memory.do_all(nar)
        except Exception as e:
            print(f"Error in anticipate: {e}")

    # Placeholder for task0
    task0 = None

    @staticmethod
    def process_operation_goal(projected_goal, nal, concept, old_goal_t, task):
        """
        To process an operation for potential execution. Only called by process_goal

        Args:
            projected_goal: The current goal
            nal: The derivation context
            concept: The concept of the current goal
            old_goal_t: The best goal in the goal table
            task: The goal task
        """
        try:
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter

            # 获取内存引用
            ProcessGoal.get_mem()
            memory = ProcessGoal.memory
            nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None

            # 检查目标期望值是否超过决策阈值
            if projected_goal.truth.get_expectation() > nar.narParameters.DECISION_THRESHOLD:
                # 检查目标证据是否完全包含在旧目标中
                old_evidence = set()
                subset = False

                if old_goal_t is not None:
                    subset = True
                    for l in old_goal_t.sentence.stamp.evidential_base:
                        old_evidence.add(l)

                    for l in task.sentence.stamp.evidential_base:
                        if l not in old_evidence:
                            subset = False
                            break

                # 如果证据不是子集，则执行操作
                if not subset and not ProcessGoal.execute_operation(nal, task):
                    # 发送不可执行目标事件
                    if hasattr(concept, 'memory') and hasattr(concept.memory, 'emit'):
                        concept.memory.emit("UnexecutableGoal", task, concept, nal)
        except Exception as e:
            print(f"Error in process_operation_goal: {e}")

    class ExecutablePrecondition:
        """Class to hold executable precondition data"""

        def __init__(self):
            self.bestop = None
            self.bestop_truthexp = 0.0
            self.bestop_truth = None
            self.executable_precond = None
            self.mintime = -1
            self.maxtime = -1
            self.time_offset = 0.0
            self.substitution = {}

    @staticmethod
    def calc_best_executable_precondition(nal, concept0, projected_goal, exec_preconditions,
                                        anticipations_to_make, gtask):
        """
        Search for the best precondition that best matches recent events, and is most successful in leading to goal fulfillment

        Args:
            nal: The derivation context
            concept0: The goal concept
            projected_goal: The goal projected to the current time
            exec_preconditions: The procedural hypotheses with the executable preconditions
            anticipations_to_make: Map to store anticipations
            gtask: The goal task

        Returns:
            List: The procedural hypotheses with the highest result truth expectation
        """
        actions = []

        # Get memory reference
        ProcessGoal.get_mem()

        # Process each executable precondition
        for exec_precondition in exec_preconditions:
            should_continue = False
            task = None
            attention_task = None
            r = random.Random()

            ttd = exec_precondition.sentence.term

            # Skip if this is a circular goal reference
            if gtask.from_goal is not None:
                # 处理ttd的不同类型
                ttd_subject = None

                # 如果ttd是Statement类型
                if hasattr(ttd, 'get_subject'):
                    ttd_subject = ttd.get_subject()
                # 如果ttd是列表类型
                elif isinstance(ttd, list) and len(ttd) >= 1:
                    ttd_subject = ttd[0]  # 列表中第一个元素通常是主语
                # 如果ttd有term属性且是列表
                elif hasattr(ttd, 'term') and isinstance(ttd.term, list) and len(ttd.term) >= 1:
                    ttd_subject = ttd.term[0]

                if ttd_subject is not None and gtask.from_goal.term == ttd_subject:
                    continue

            ii = exec_precondition.get_term()

            # 处理ii是列表类型的情况
            if isinstance(ii, list) and len(ii) >= 2:
                subject = ii[0]  # 列表中第一个元素通常是主语
                predicate_term = ii[1]  # 列表中第二个元素通常是谓词
            # 处理ii有Statement类型的情况
            elif hasattr(ii, 'get_subject') and hasattr(ii, 'get_predicate'):
                subject = ii.get_subject()  # Premise subject
                predicate_term = ii.get_predicate()
            # 处理ii有term属性且是列表的情况
            elif hasattr(ii, 'term') and isinstance(ii.term, list) and len(ii.term) >= 2:
                subject = ii.term[0]
                predicate_term = ii.term[1]
            else:
                print(f"错误: 无法从执行前提条件中获取主语和谓词: {type(ii)}")
                if isinstance(ii, list):
                    print(f"ii列表内容: {ii}")
                elif hasattr(ii, 'term'):
                    print(f"ii.term内容: {ii.term}")
                continue

            # Check if terms are compound or variables
            sub_is_compound = hasattr(subject, 'term')
            pred_is_compound = hasattr(predicate_term, 'term')

            # Check if terms are variables
            from linars.org.opennars.language.variable import Variable
            sub_is_var = isinstance(subject, Variable)
            pred_is_var = isinstance(predicate_term, Variable)
            # sub_is_var = hasattr(subject, 'has_var_indep') and subject.has_var_indep()
            # pred_is_var = hasattr(predicate_term, 'has_var_indep') and predicate_term.has_var_indep()

            pred_has_var = pred_is_var or (pred_is_compound and predicate_term.has_var())
            sub_has_var = sub_is_var or (sub_is_compound and subject.has_var())

            # Check if conclusion matches
            subs_conc = {}
            conclusion_matches = ProcessGoal.find_substitute(predicate_term, projected_goal.get_term())

            if not conclusion_matches:
                continue

            # Handle variable terms
            if pred_has_var:
                re = ProcessGoal.do_var_task(projected_goal, subject, predicate_term, exec_precondition, nal)
                if re == "true":
                    continue
                elif re == "break":
                    return None

            if sub_has_var:
                ct = exec_precondition.get_term()
                re = ProcessGoal.do_var_unify(ct)

                if re == "true":
                    exec_precondition.sentence.set_key(str(ct))
                    continue
                elif re == "null":
                    return None

            # Get result for non-variable terms
            result = ProcessGoal.get_result(nal, projected_goal, exec_precondition, subject)
            if result is None:
                return None

            # Process operations and states
            ops = []
            prec_intervals = []
            is_single_ac = False
            is_action = False
            is_state = False
            pps = str(result.prec_term)

            if "^" not in pps:
                is_action = ProcessGoal.is_action(result.prec, is_action)
                if not is_action:
                    is_state = True

            # Extract operations from the precondition
            multi_act = ProcessGoal.get_multi_act(ops, attention_task, predicate_term,
                                                task, is_single_ac, result.prec_term,
                                                prec_intervals)

            # Find the best matching recent event
            seq_bag1 = ProcessGoal.memory.globalBuffer
            newest_time = -1
            best_sofar = None
            subs_best = {}

            best_sofar0 = ProcessGoal.get_best_sofar0(nal, seq_bag1, newest_time, subs_conc,
                                                    multi_act, prec_intervals, best_sofar,
                                                    subs_best)

            if best_sofar0.bestsofar is None:
                if is_state:
                    # Create default task for state goals
                    ProcessGoal.get_default_task(projected_goal, result.prec_term, "Present", "!")
                    continue
                continue

            if is_action:
                terms = result.prec_term.term
                ProcessGoal.get_default_task(projected_goal, terms[len(terms) - 1], "Present", "!")
                break

            if is_state:
                # Handle state preconditions
                if len(str(best_sofar0.bestsofar.get_term())) != len(str(exec_precondition.get_term())):
                    terms = result.prec_term.term
                    ProcessGoal.get_default_task(projected_goal, terms[len(terms) - 1], "Present", "!")
                else:
                    ProcessGoal.get_default_task(None, projected_goal.term, "Present", ".")
                continue

            # Create executable preconditions
            actions1 = ProcessGoal.get_executable_preconditions(
                nal, concept0, projected_goal, anticipations_to_make,
                exec_precondition, best_sofar0, multi_act, result,
                ops, actions, r)

            if actions1 is not None:
                return actions1

        return actions

    @staticmethod
    def get_default_task(projected_goal, prec_term, tense, symbol):
        """
        Create a default task
        Args:
            projected_goal: The projected goal
            prec_term: The precondition term
            tense: The tense
            symbol: The symbol
        Returns:
            Task: The created task
        """
        try:
            from linars.org.opennars.entity.sentence import Sentence
            from linars.org.opennars.entity.stamp import Stamp
            from linars.org.opennars.entity.task import Task
            from linars.org.opennars.entity.budget_value import BudgetValue
            from linars.org.opennars.entity.truth_value import TruthValue
            from linars.org.opennars.language.tense import Tense
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter

            # 获取内存引用
            ProcessGoal.get_mem()
            memory = ProcessGoal.memory
            nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None
            # 如果前提条件术语为空，则返回空
            if prec_term is None:
                return None
            # 创建时间戳
            stamp = None
            if nar is not None:
                # 获取时间戳
                if tense == "Present":
                    stamp = Stamp(nar.time(), Tense.Present, memory.new_stamp_serial(),
                                                nar.narParameters.DURATION)
                elif tense == "Past":
                    stamp = Stamp(nar.time(), Tense.Past, memory.new_stamp_serial(),
                                                nar.narParameters.DURATION)
                elif tense == "Future":
                    stamp = Stamp(nar.time(), Tense.Future, memory.new_stamp_serial(),
                                                nar.narParameters.DURATION)
                else:
                    stamp = Stamp(nar.time(), Tense.Present, memory.new_stamp_serial(),
                                                nar.narParameters.DURATION)
            else:
                # 创建一个默认的stamp
                stamp = Stamp(0, Tense.Present, 0, 1)

            # 创建真值
            truth = None
            if nar is not None:
                truth = TruthValue(1.0, 0.9, nar.narParameters)
            else:
                # 创建一个默认的truth
                truth = TruthValue(1.0, 0.9, None)
            # 创建句子
            sentence = Sentence(prec_term, symbol, truth, stamp)
            # 创建预算
            budget = None
            if nar is not None:
                budget = BudgetValue(0.8, 0.5, 1, nar.narParameters)
            else:
                # 创建一个默认的budget
                budget = BudgetValue(0.8, 0.5, 1, None)
            from linars.org.opennars.entity.task import EnumType
            # 创建任务
            task = Task(sentence, budget, EnumType.DERIVED)
            # 设置目标来源
            if projected_goal is not None:
                task.from_goal = projected_goal
            # 添加到内存
            if memory is not None:
                memory.add_new_task(task, "lida")
                if nar is not None:
                    memory.do_all(nar)
            return task
        except Exception as e:
            print(f"Error in get_default_task: {e}")
            return None

    @staticmethod
    def do_var_task(projected_goal, subject, predicate_term, exec_precondition, nal):
        """
        Handle variable tasks
        Args:
            projected_goal: The projected goal
            subject: The subject term
            predicate_term: The predicate term
            exec_precondition: The executable precondition
            nal: The derivation context
        Returns:
            str: Result status
        """
        try:
            from linars.org.opennars.language.variables import Variables
            from linars.org.opennars.io.symbols import JUDGMENT_MARK, GOAL_MARK, VAR_INDEPENDENT
            from linars.edu.memphis.ccrg.linars.term import Term
            from linars.org.opennars.language.statement import Statement
            from linars.org.opennars.language.conjunction import Conjunction
            from linars.org.opennars.language.operation import Operation
            from linars.org.opennars.entity.sentence import Sentence
            from linars.org.opennars.entity.task import Task
            from linars.org.opennars.entity.budget_value import BudgetValue
            from linars.org.opennars.entity.truth_value import TruthValue
            from linars.org.opennars.language.tense import Tense
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
            from linars.org.opennars.entity.stamp import Stamp

            # 获取内存引用
            ProcessGoal.get_mem()
            memory = ProcessGoal.memory
            nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None

            # 变量时序代换并执行
            seq_term = None
            subsconc = {}
            subsconc2 = {}
            subsconc3 = {}
            subsconc4 = {}

            # 检查主语是否匹配目标
            sub_matches = Variables.find_substitute(
                memory.random_number,
                VAR_INDEPENDENT,
                CompoundTerm.replace_intervals(subject) if hasattr(subject, 'replace_intervals') else subject,
                CompoundTerm.replace_intervals(projected_goal.get_term()) if hasattr(projected_goal.get_term(), 'replace_intervals') else projected_goal.get_term(),
                subsconc,
                subsconc2
            )

            # 检查谓语是否匹配目标
            pred_matches = Variables.find_substitute(
                memory.random_number,
                VAR_INDEPENDENT,
                CompoundTerm.replace_intervals(predicate_term) if hasattr(predicate_term, 'replace_intervals') else predicate_term,
                CompoundTerm.replace_intervals(projected_goal.get_term()) if hasattr(projected_goal.get_term(), 'replace_intervals') else projected_goal.get_term(),
                subsconc3,
                subsconc4
            )

            # 如果主语匹配，则实例化变量
            if sub_matches:
                # 将匹配到的实例化，makeIsa
                for term in subsconc.keys():
                    ProcessGoal.make_isa(str(term), 20001, str(subsconc[term]))

                # 当前一半变量在实例化，时序为另一半
                if isinstance(predicate_term, CompoundTerm):
                    seq_term = Variables.apply_substitute_and_rename_variables(predicate_term, subsconc)

            # 如果谓语匹配，则实例化变量
            if pred_matches:
                # 将匹配到的实例化，makeIsa
                for term in subsconc3.keys():
                    ProcessGoal.make_isa(str(term), 20001, str(subsconc3[term]))

                # 当前一半变量在实例化，时序为另一半
                if isinstance(subject, CompoundTerm):
                    seq_term = Variables.apply_substitute_and_rename_variables(subject, subsconc3)

            # 如果有实例化的时序，则创建任务
            if seq_term is not None:
                # 创建时间戳
                stamp = None
                if nar is not None:
                    stamp = Stamp(nar.time(), Tense.Present, memory.new_stamp_serial(),
                                                nar.narParameters.DURATION)
                else:
                    # 创建一个默认的stamp
                    stamp = Stamp(0, Tense.Present, 0, 1)

                # 创建真值
                truth = None
                if nar is not None:
                    truth = TruthValue(1.0, 0.9, nar.narParameters)
                else:
                    # 创建一个默认的truth
                    truth = TruthValue(1.0, 0.9, None)

                # 创建句子
                sentence = Sentence(seq_term, GOAL_MARK, truth, stamp)

                # 创建预算
                budget = None
                if nar is not None:
                    budget = BudgetValue(0.8, 0.5, 1, nar.narParameters)
                else:
                    # 创建一个默认的budget
                    budget = BudgetValue(0.8, 0.5, 1, None)
                from linars.org.opennars.entity.task import EnumType
                # 创建任务
                task = Task(sentence, budget, EnumType.DERIVED)

                # 设置目标来源
                task.from_goal = projected_goal

                # 添加到内存
                if memory is not None:
                    memory.add_new_task(task, "lida")
                    if nar is not None:
                        memory.do_all(nar)

                return "true"

            return "false"
        except Exception as e:
            print(f"Error in do_var_task: {e}")
            import traceback
            traceback.print_exc()
            return "false"

    @staticmethod
    def make_isa(term_str, id_val, subsconc_str):
        """
        Create an 'is-a' relationship between terms

        Args:
            term_str: The term string
            id_val: The ID value
            subsconc_str: The substitution string
        """
        try:
            from linars.edu.memphis.ccrg.lida.PAM.Tasks.IsaPamTask import IsaPamTask
            from linars.edu.memphis.ccrg.lida.Data.term_util import TermUtil
            from linars.edu.memphis.ccrg.lida.PAM.PamNodeImpl import PamNodeImpl

            # 如果term同时有开合括号，不去掉，只有一个括号，去掉
            if not ("(" in term_str and ")" in term_str):
                term_str = term_str.replace("(", "").replace(")", "")

            # 获取PAM节点
            type_node = TermUtil.get_pam_node(term_str, id_val)

            # 处理替换字符串
            if not ("(" in subsconc_str and ")" in subsconc_str):
                subsconc_str = subsconc_str.replace("(", "").replace(")", "")

            # 获取值节点
            value_node = TermUtil.get_pam_node(subsconc_str, id_val)

            # 创建nowisa关系
            IsaPamTask.make_nowisa(type_node, value_node)
        except Exception as e:
            print(f"Error in make_isa: {e}")
            # 如果发生异常，打印错误信息，跟踪异常
            import traceback
            traceback.print_exc()

    @staticmethod
    def do_var_unify(ct):
        """
        Unify variables in a compound term
        Args:
            ct: The compound term
        Returns:
            str: Result status
        """
        try:
            from linars.edu.memphis.ccrg.lida.PAM.PamImpl0 import PamImpl0

            # 调用PamImpl0.isAllInMemVar方法
            is_all_in_mem = PamImpl0.isAllInMemVar(ct)

            # 根据结果返回“true”或“false”
            return "true" if is_all_in_mem else "false"
        except Exception as e:
            print(f"Error in do_var_unify: {e}")
            return "false"

    class Result:
        """Class to hold result data"""

        def __init__(self, prec_term, prec):
            self.prec_term = prec_term
            self.prec = prec

    @staticmethod
    def get_result(nal, projected_goal, exec_precondition, subject):
        """
        Get result for a precondition

        Args:
            nal: The derivation context
            projected_goal: The projected goal
            exec_precondition: The executable precondition
            subject: The subject term

        Returns:
            Result: The result
        """
        try:
            from linars.org.opennars.language.conjunction import Conjunction
            from linars.org.opennars.language.statement import Statement
            from linars.edu.memphis.ccrg.linars.term import Term
            from linars.org.opennars.io.parser import Parser

            # 获取前提条件术语
            term = exec_precondition.get_term()

            # 增强类型检查，处理不同类型的term
            # 如果term不是Statement类型，但是列表或有term属性，仍然可以尝试处理
            if not (isinstance(term, Statement) or
                   (isinstance(term, list) and len(term) >= 2) or
                   (hasattr(term, 'term') and isinstance(term.term, list) and len(term.term) >= 2)):
                print(f"警告: get_result中的term不是有效类型: {type(term)}")
                return None

            # 获取前提条件
            prec_term = ProcessGoal.get_prec_term(nal, projected_goal, exec_precondition, subject)
            if prec_term is None:
                return None

            # 获取前提条件术语数组
            prec = prec_term.term
            result = ProcessGoal.Result(prec_term, prec)
            return result
        except Exception as e:
            print(f"Error in get_result: {e}")
            return None

    @staticmethod
    def get_prec_term(nal, projected_goal2, t, term):
        """
        Get precondition term from a statement

        Args:
            nal: The derivation context
            projected_goal2: The projected goal
            t: The task
            term: The term

        Returns:
            Conjunction: The precondition term
        """
        try:
            from linars.org.opennars.language.conjunction import Conjunction
            from linars.org.opennars.operator.operator import Operator
            from linars.org.opennars.language.operation import Operation
            from linars.org.opennars.entity.sentence import Sentence
            from linars.org.opennars.entity.task import Task
            from linars.org.opennars.entity.budget_value import BudgetValue
            from linars.org.opennars.io.symbols import GOAL_MARK
            from linars.org.opennars.io.parser import Parser
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            from linars.edu.memphis.ccrg.linars.term import Term
            from linars.org.opennars.io.narsese import Narsese
            from linars.org.opennars.entity.stamp import Stamp

            # 获取内存引用
            ProcessGoal.get_mem()
            memory = ProcessGoal.memory
            nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None

            if term is None:
                return None

            if isinstance(term, Conjunction):
                prec_term = term
            elif isinstance(term, Operator):
                # 单个操作，无self，与有self的operation等效
                try:
                    op = Operation.make(term, [Narsese.parse_term("{SELF}")], True)
                    # 直接执行
                    created_sentence = Sentence(
                        op,
                        GOAL_MARK,
                        t.sentence.truth,
                        projected_goal2.stamp
                    )
                    from linars.org.opennars.entity.task import EnumType
                    t0 = Task(
                        created_sentence,
                        BudgetValue(1.0, 1.0, 1.0, nar.narParameters),
                        EnumType.DERIVED
                    )
                    t0.from_goal = projected_goal2
                    ProcessGoal.execute_operation(nal, t0)
                    return None
                except Exception as e:
                    print(f"Error creating operation: {e}")
                    return None
            else:
                # 单词项单前提的状态或操作对等词
                try:
                    from linars.org.opennars.language.tense import Tense
                    stamp = None
                    if nar is not None:
                        stamp = Stamp(nar.time(), Tense.Present, memory.new_stamp_serial(),
                                                    nar.narParameters.DURATION)
                    else:
                        # 创建一个默认的stamp
                        stamp = Stamp(0, Tense.Present, 0, 1)

                    truth = None
                    if nar is not None:
                        from linars.org.opennars.entity.truth_value import TruthValue
                        truth = TruthValue(1.0, 0.9, nar.narParameters)
                    else:
                        # 创建一个默认的truth
                        from linars.org.opennars.entity.truth_value import TruthValue
                        truth = TruthValue(1.0, 0.9, None)

                    sentence = Sentence(term, GOAL_MARK, truth, stamp)
                    budget = None
                    if nar is not None:
                        budget = BudgetValue(0.8, 0.5, 1, nar.narParameters)
                    else:
                        # 创建一个默认的budget
                        budget = BudgetValue(0.8, 0.5, 1, None)
                    from linars.org.opennars.entity.task import EnumType
                    state_task0 = Task(sentence, budget, EnumType.DERIVED)
                    state_task0.from_goal = projected_goal2
                    if memory is not None:
                        memory.add_new_task(state_task0, "lida")
                        if nar is not None:
                            memory.do_all(nar)
                    return None
                except Exception as e:
                    print(f"Error creating state task: {e}")
                    return None

            return prec_term
        except Exception as e:
            print(f"Error in get_prec_term: {e}")
            return None

    @staticmethod
    def is_action(prec, is_action):
        """
        Check if a term array contains actions

        Args:
            prec: The precondition terms
            is_action: Current action status

        Returns:
            bool: True if contains actions
        """
        try:
            from linars.org.opennars.language.operation import Operation
            from linars.org.opennars.language.statement import Statement

            # 检查每个前提条件是否包含操作
            for i in range(len(prec)):
                if prec[i] is None:
                    continue

                # 检查是否是操作
                if isinstance(prec[i], Operation):
                    return True

                # 检查是否是语句，如果是，检查主语和谓语
                if isinstance(prec[i], Statement):
                    subject = prec[i].get_subject()
                    predicate = prec[i].get_predicate()

                    # 检查主语和谓语是否是操作
                    if (isinstance(subject, Operation) or
                        isinstance(predicate, Operation)):
                        return True

                # 检查术语的字符串表示是否包含操作符号
                term_str = str(prec[i])
                if "^" in term_str:
                    return True

            return is_action
        except Exception as e:
            print(f"Error in is_action: {e}")
            return False

    class MultiAct:
        """Class to hold multi-action data"""

        def __init__(self, attention_task, task, precondition, is_single_ac):
            self.attention_task = attention_task
            self.task = task
            self.precondition = precondition
            self.is_single_ac = is_single_ac

    @staticmethod
    def get_multi_act(ops, attention_task, predicate_term, task, is_single_ac, prec_term, prec_intervals):
        """
        Extract multi-action data from a precondition

        Args:
            ops: List to store operations
            attention_task: Attention task
            predicate_term: Predicate term
            task: Task
            is_single_ac: Whether this is a single action
            prec_term: Precondition term
            prec_intervals: List to store intervals

        Returns:
            MultiAct: The multi-action data
        """
        try:
            from linars.org.opennars.language.operation import Operation
            from linars.org.opennars.language.statement import Statement
            from linars.org.opennars.language.conjunction import Conjunction
            from linars.edu.memphis.ccrg.linars.term import Term
            from linars.org.opennars.language.interval import Interval

            # 初始化变量
            is_single_ac = False

            # 如果前提条件不是连接词，则返回单个操作
            if not isinstance(prec_term, Conjunction):
                return ProcessGoal.MultiAct(attention_task, task, prec_term, True)

            # 获取前提条件的术语数组
            terms = prec_term.term

            # 遍历前提条件的每个术语
            for i in range(len(terms)):
                term = terms[i]

                # 检查是否是操作
                if isinstance(term, Operation):
                    ops.append(term)

                    # 检查是否有间隔
                    if i < len(terms) - 1 and isinstance(terms[i+1], Interval):
                        interval = terms[i+1]
                        prec_intervals.append(float(interval.get_magnitude()))
                    else:
                        prec_intervals.append(0.0)

                # 检查是否是语句，如果是，检查主语和谓语
                elif isinstance(term, Statement):
                    subject = term.get_subject()
                    predicate = term.get_predicate()

                    # 检查主语和谓语是否是操作
                    if isinstance(subject, Operation):
                        ops.append(subject)

                        # 检查是否有间隔
                        if i < len(terms) - 1 and isinstance(terms[i+1], Interval):
                            interval = terms[i+1]
                            prec_intervals.append(float(interval.get_magnitude()))
                        else:
                            prec_intervals.append(0.0)

                    if isinstance(predicate, Operation):
                        ops.append(predicate)

                        # 检查是否有间隔
                        if i < len(terms) - 1 and isinstance(terms[i+1], Interval):
                            interval = terms[i+1]
                            prec_intervals.append(float(interval.get_magnitude()))
                        else:
                            prec_intervals.append(0.0)

                # 检查术语的字符串表示是否包含操作符号
                elif "^" in str(term):
                    # 尝试解析操作
                    try:
                        from linars.org.opennars.io.narsese import Narsese
                        op_term = Narsese.parse_term(str(term))
                        if isinstance(op_term, Operation):
                            ops.append(op_term)

                            # 检查是否有间隔
                            if i < len(terms) - 1 and isinstance(terms[i+1], Interval):
                                interval = terms[i+1]
                                prec_intervals.append(float(interval.get_magnitude()))
                            else:
                                prec_intervals.append(0.0)
                    except Exception as e:
                        print(f"Error parsing operation term: {e}")

            # 如果只有一个操作，则设置为单个操作
            if len(ops) == 1:
                is_single_ac = True

            return ProcessGoal.MultiAct(attention_task, task, prec_term, is_single_ac)
        except Exception as e:
            print(f"Error in get_multi_act: {e}")
            return ProcessGoal.MultiAct(attention_task, task, prec_term, is_single_ac)

    class BestSofar:
        """Class to hold best-so-far data"""

        def __init__(self, subs_best, bestsofar):
            self.subs_best = subs_best
            self.bestsofar = bestsofar

    @staticmethod
    def get_best_sofar0(nal, seq_bag1, newest_time, subs_conc, multi_act, prec_intervals, bestsofar, subs_best):
        """
        Find the best matching recent event

        Args:
            nal: The derivation context
            seq_bag1: The sequence bag
            newest_time: The newest time
            subs_conc: Substitution map for conclusion
            multi_act: Multi-action data
            prec_intervals: Precondition intervals
            bestsofar: Current best task
            subs_best: Best substitution map

        Returns:
            BestSofar: The best-so-far data
        """
        try:
            from linars.org.opennars.language.variables import Variables
            from linars.org.opennars.io.symbols import VAR_INDEPENDENT
            from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm

            # 获取内存引用
            ProcessGoal.get_mem()
            memory = ProcessGoal.memory

            # 如果序列包为空，则返回当前最佳值
            if seq_bag1 is None:
                return ProcessGoal.BestSofar(subs_best, bestsofar)

            # 遍历序列包中的每个任务
            for p in seq_bag1:
                if p is None or p.sentence is None:
                    continue

                # 检查是否是判断
                if p.sentence.punctuation != ".":
                    continue

                # 检查是否比当前最佳值更新
                try:
                    # 使用正确的方法名 get_occurrence_time
                    if newest_time > 0 and p.sentence.get_occurrence_time() > newest_time:
                        continue
                except AttributeError:
                    # 如果方法不存在，尝试使用其他方式获取时间
                    try:
                        if newest_time > 0 and p.sentence.stamp.get_occurrence_time() > newest_time:
                            continue
                    except Exception as e:
                        print(f"Error getting occurrence time: {e}")
                        # 如果无法获取时间，则继续处理下一个任务
                        continue

                # 检查前提条件是否匹配
                subs = {}
                precondition_matches = Variables.find_substitute(
                    memory.random_number,
                    VAR_INDEPENDENT,
                    CompoundTerm.replace_intervals(multi_act.precondition) if hasattr(multi_act.precondition, 'replace_intervals') else multi_act.precondition,
                    CompoundTerm.replace_intervals(p.sentence.term) if hasattr(p.sentence.term, 'replace_intervals') else p.sentence.term,
                    subs,
                    {}
                )

                # 如果前提条件匹配，则更新最佳值
                if precondition_matches:
                    # 创建新任务
                    p_new = p.clone()
                    try:
                        # 使用正确的方法名 get_occurrence_time
                        newest_time = p.sentence.get_occurrence_time()
                    except AttributeError:
                        # 如果方法不存在，尝试使用其他方式获取时间
                        try:
                            newest_time = p.sentence.stamp.get_occurrence_time()
                        except Exception as e:
                            print(f"Error getting occurrence time for newest_time: {e}")
                            # 如果无法获取时间，使用当前时间
                            newest_time = nal.time.time() if hasattr(nal, 'time') and hasattr(nal.time, 'time') else 0

                    # 应用间隔投影对间隔差异的惩罚
                    ProcessGoal.interval_projection(nal, p_new.sentence.term, multi_act.precondition, prec_intervals, p_new.sentence.truth)

                    # 更新最佳值
                    bestsofar = p_new
                    subs_best = subs

            # 返回最佳值
            return ProcessGoal.BestSofar(subs_best, bestsofar)
        except Exception as e:
            print(f"Error in get_best_sofar0: {e}")
            return ProcessGoal.BestSofar(subs_best, bestsofar)

    @staticmethod
    def get_executable_preconditions(nal, concept1, projected_goal0, anticipations_to_make,
                                   exec_precondition, best_sofar0, multi_act, result,
                                   ops, actions, r):
        """
        Create executable preconditions

        Args:
            nal: The derivation context
            concept1: The concept
            projected_goal0: The projected goal
            anticipations_to_make: Map to store anticipations
            exec_precondition: The executable precondition
            best_sofar0: Best-so-far data
            multi_act: Multi-action data
            result: Result data
            ops: List of operations
            actions: List to store actions
            r: Random generator

        Returns:
            List: The executable preconditions
        """
        try:
            from linars.org.opennars.language.operation import Operation
            from linars.org.opennars.language.statement import Statement
            from linars.org.opennars.language.conjunction import Conjunction
            from linars.edu.memphis.ccrg.linars.term import Term
            from linars.org.opennars.language.interval import Interval
            from linars.org.opennars.language.variables import Variables
            from linars.org.opennars.io.symbols import VAR_INDEPENDENT
            from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm

            # 获取内存引用
            ProcessGoal.get_mem()
            memory = ProcessGoal.memory

            # 如果最佳值为空，则返回空
            if best_sofar0.bestsofar is None:
                return None

            # 如果没有操作，则返回空
            if len(ops) == 0:
                return None

            # 创建可执行前提条件列表
            for i in range(len(ops)):
                op = ops[i]

                # 创建可执行前提条件
                precon = ProcessGoal.ExecutablePrecondition()
                precon.bestop = op
                precon.executable_precond = exec_precondition

                # 设置真值期望值
                if exec_precondition.sentence.truth is not None:
                    precon.bestop_truth = exec_precondition.sentence.truth
                    precon.bestop_truthexp = exec_precondition.sentence.truth.get_expectation()
                else:
                    # 设置默认真值
                    from linars.org.opennars.entity.truth_value import TruthValue
                    precon.bestop_truth = TruthValue(1.0, 0.9, None)
                    precon.bestop_truthexp = 0.9

                # 设置时间间隔
                if i < len(prec_intervals):
                    interval = prec_intervals[i]
                    precon.mintime = nal.time.time() + interval - memory.narParameters.DURATION
                    precon.maxtime = nal.time.time() + interval + memory.narParameters.DURATION
                    precon.time_offset = nal.time.time() + interval
                else:
                    precon.mintime = nal.time.time()
                    precon.maxtime = nal.time.time() + memory.narParameters.DURATION
                    precon.time_offset = nal.time.time()

                # 设置替换映射
                precon.substitution = best_sofar0.subs_best

                # 添加到操作列表
                actions.append(precon)

                # 添加到预期映射
                if op not in anticipations_to_make:
                    anticipations_to_make[op] = []
                anticipations_to_make[op].append(precon)

            # 返回操作列表
            return actions
        except Exception as e:
            print(f"Error in get_executable_preconditions: {e}")
            return None

    @staticmethod
    def execute_precondition(nal, precon1, concept, projected_goal, task):
        """
        Execute the operation suggested by the most applicable precondition

        Args:
            nal: The derivation context
            precon1: The procedural hypothesis leading to goal
            concept: The concept of the goal
            projected_goal: The goal projected to the current time
            task: The goal task

        Returns:
            bool: True if executed
        """
        try:
            from linars.org.opennars.entity.sentence import Sentence
            from linars.org.opennars.entity.task import Task
            from linars.org.opennars.entity.budget_value import BudgetValue
            from linars.org.opennars.io.symbols import VAR_INDEPENDENT, GOAL_MARK
            from linars.edu.memphis.ccrg.linars.term import Term
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter

            # 获取内存引用
            ProcessGoal.get_mem()
            memory = ProcessGoal.memory
            nar = AgentStarter.nar if hasattr(AgentStarter, 'nar') else None

            # 检查是否满足决策阈值
            if (precon1.bestop is not None and
                precon1.bestop_truthexp > nal.nar.narParameters.DECISION_THRESHOLD):

                # 创建操作目标
                created_sentence = Sentence(
                    precon1.bestop,
                    GOAL_MARK,
                    precon1.bestop_truth,
                    projected_goal.stamp
                )
                from linars.org.opennars.entity.task import EnumType
                # 创建任务
                t = Task(
                    created_sentence,
                    BudgetValue(1.0, 1.0, 1.0, nar.narParameters),
                    EnumType.DERIVED
                )

                # 设置目标来源
                t.from_goal = task

                # 检查证据是否循环
                if not task.sentence.stamp.evidence_is_cyclic():
                    # 执行操作
                    if not ProcessGoal.execute_operation(nal, t):
                        # 发送不可执行目标事件
                        if hasattr(concept, 'memory') and hasattr(concept.memory, 'emit'):
                            concept.memory.emit("UnexecutableGoal", task, concept, nal)
                        return False
                    return True

            return False
        except Exception as e:
            print(f"Error in execute_precondition: {e}")
            return False

    @staticmethod
    def execute_operation(nal, t):
        """
        Entry point for all potentially executable operation tasks.
        Returns true if the Task has a Term which can be executed

        Args:
            nal: The derivation context
            t: The operation goal task

        Returns:
            bool: True if executed
        """
        try:
            content = t.get_term()

            # Get memory if not initialized
            if ProcessGoal.memory is None:
                ProcessGoal.get_mem()

                # If memory is still not initialized, use nal's memory
                if ProcessGoal.memory is None and hasattr(nal, 'memory'):
                    ProcessGoal.memory = nal.memory
                    print("Using nal's memory in execute_operation")

                # If memory is still not initialized, use a default
                if ProcessGoal.memory is None:
                    print("Warning: No memory available in execute_operation")
                    return False

            # Check if execution is allowed
            if ((not hasattr(ProcessGoal.memory, 'allow_execution') or not ProcessGoal.memory.allow_execution) and
                (not hasattr(ProcessGoal.memory, 'multi_action') or not ProcessGoal.memory.multi_action) or
                (not hasattr(content, 'get_operator'))):
                return False

            # Get the operator
            op = content
            oper = op.get_operator()

            # Get the arguments
            prod = op.get_subject()
            arg = prod.term[0]

            # Check for variables
            if hasattr(content, 'has_var_dep') and (content.has_var_dep() or content.has_var_indep()):
                if not "^search" in str(content):
                    print(f"-----有变量-----not say---: {content}")
                    return False

            # Set the task for the operation
            op.set_task(t)

            # Call the operator
            if not oper.call(op, ProcessGoal.memory, nal.time):
                return False

            # Debug output
            print(t.to_string_long())

            return True
        except Exception as e:
            print(f"Error in execute_operation: {e}")
            return False
