"""
Graph Page Record

This module provides a class for representing paginated graph data.
"""

class GraphPageRecord:
    """Graph Page Record class for representing paginated graph data."""
    
    def __init__(self, page_index=1, page_size=10, total_count=0, node_list=None, visbles=None):
        """
        Initialize a Graph Page Record.
        
        Args:
            page_index: The current page index
            page_size: The page size
            total_count: The total count of records
            node_list: The list of nodes
            visbles: The visibility settings
        """
        self.page_index = page_index
        self.page_size = page_size
        self.total_count = total_count
        self.node_list = node_list or []
        self.visbles = visbles or {}
    
    def set_page_index(self, page_index):
        """
        Set the page index.
        
        Args:
            page_index: The page index
        """
        self.page_index = page_index
        return self
    
    def set_page_size(self, page_size):
        """
        Set the page size.
        
        Args:
            page_size: The page size
        """
        self.page_size = page_size
        return self
    
    def set_total_count(self, total_count):
        """
        Set the total count.
        
        Args:
            total_count: The total count
        """
        self.total_count = total_count
        return self
    
    def set_node_list(self, node_list):
        """
        Set the node list.
        
        Args:
            node_list: The node list
        """
        self.node_list = node_list
        return self
    
    def set_visbles(self, visbles):
        """
        Set the visibility settings.
        
        Args:
            visbles: The visibility settings
        """
        self.visbles = visbles
        return self
    
    def to_dict(self):
        """
        Convert the record to a dictionary.
        
        Returns:
            A dictionary representation of the record
        """
        return {
            'pageIndex': self.page_index,
            'pageSize': self.page_size,
            'totalCount': self.total_count,
            'nodeList': self.node_list,
            'visbles': self.visbles
        }
