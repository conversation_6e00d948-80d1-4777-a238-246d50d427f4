# LIDA认知框架
"""
行为是Scheme的实例化对象，可以被选择执行
"""

from abc import abstractmethod
from typing import Collection, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.Activatible import Activatible
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Scheme import Scheme
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Condition import Condition

class Behavior(Activatible):
    """
    行为是Scheme的实例化对象，可以被选择执行
    """

    @abstractmethod
    def get_id(self) -> int:
        """
        获取行为的ID

        返回:
            行为的ID
        """
        pass

    @abstractmethod
    def set_id(self, id: int) -> None:
        """
        设置行为的ID

        参数:
            id: 要设置的ID
        """
        pass

    @abstractmethod
    def get_name(self) -> str:
        """
        获取行为的名称

        返回:
            行为的名称
        """
        pass

    @abstractmethod
    def set_name(self, name: str) -> None:
        """
        设置行为的名称

        参数:
            name: 要设置的名称
        """
        pass

    @abstractmethod
    def get_scheme(self) -> Scheme:
        """
        获取行为对应的Scheme

        返回:
            行为对应的Scheme
        """
        pass

    @abstractmethod
    def set_scheme(self, scheme: Scheme) -> None:
        """
        设置行为对应的Scheme

        参数:
            scheme: 要设置的Scheme
        """
        pass

    @abstractmethod
    def get_context_conditions(self) -> Collection[Condition]:
        """
        获取行为的上下文条件

        返回:
            行为的上下文条件集合
        """
        pass

    @abstractmethod
    def get_adding_list(self) -> Collection[Condition]:
        """
        获取行为的添加条件列表

        返回:
            行为的添加条件列表
        """
        pass

    @abstractmethod
    def get_deleting_list(self) -> Collection[Condition]:
        """
        获取行为的删除条件列表

        返回:
            行为的删除条件列表
        """
        pass

    @abstractmethod
    def get_action(self) -> Action:
        """
        获取行为对应的动作

        返回:
            行为对应的动作
        """
        pass

    @abstractmethod
    def get_label(self) -> str:
        """
        获取行为的标签

        返回:
            行为的标签
        """
        pass

    @abstractmethod
    def get_broadcost_id(self) -> Optional[int]:
        """
        获取行为的广播ID

        返回:
            行为的广播ID
        """
        pass

    @abstractmethod
    def set_broadcost_id(self, id: int) -> None:
        """
        设置行为的广播ID

        参数:
            id: 要设置的广播ID
        """
        pass
