# LIDA Cognitive Framework
"""
A BroadcastTrigger that triggers a broadcast if no new coalition has arrived for a certain period.
"""

import logging
from typing import Collection
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.NoBroadcastOccurringTrigger import NoBroadcastOccurringTrigger

class NoCoalitionArrivingTrigger(NoBroadcastOccurringTrigger):
    """
    A BroadcastTrigger that triggers a broadcast if no new coalition has arrived for a certain period.
    Inherits most of its fields and methods from its parent class NoBroadcastOccurringTrigger.
    """
    
    def __init__(self):
        """
        Initialize a NoCoalitionArrivingTrigger.
        """
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def check_for_trigger_condition(self, coalitions: Collection[Coalition]) -> None:
        """
        Called each time a new coalition is added to the GlobalWorkspace.
        Specifically for this trigger, reset() is called which resets the TriggerTask object.
        Thus this trigger fires when a certain number of ticks have passed without a new Coalition
        entering the GlobalWorkspace.
        
        Args:
            coalitions: The coalitions in the GlobalWorkspace
        """
        self.reset()
