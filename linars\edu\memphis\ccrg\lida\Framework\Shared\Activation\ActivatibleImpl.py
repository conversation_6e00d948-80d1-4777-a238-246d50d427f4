# LIDA认知框架
"""
可激活接口的具体实现
"""

from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.Activatible import Activatible
from linars.edu.memphis.ccrg.lida.Framework.Strategies.ExciteStrategy import ExciteStrategy
from linars.edu.memphis.ccrg.lida.Framework.Strategies.DecayStrategy import DecayStrategy
from linars.edu.memphis.ccrg.lida.Framework.Strategies.LinearExciteStrategy import LinearExciteStrategy
from linars.edu.memphis.ccrg.lida.Framework.Strategies.DefaultDecayStrategy import DefaultDecayStrategy

class ActivatibleImpl(Activatible):
    """
    可激活接口的具体实现
    """

    # Default values
    DEFAULT_ACTIVATION = 0.0
    DEFAULT_ACTIVATION_THRESHOLD = 0.0
    DEFAULT_REMOVAL_THRESHOLD = 0.0
    DEFAULT_BASE_LEVEL_ACTIVATION = 0.0
    DEFAULT_INCENTIVE_SALIENCE = 0.0

    def __init__(self):
        """
        初始化可激活对象实现
        """
        # print("ActivatibleImpl.__init__")
        self.incentiveSalience = self.DEFAULT_INCENTIVE_SALIENCE
        self.activation = self.DEFAULT_ACTIVATION
        self.activation_threshold = self.DEFAULT_ACTIVATION_THRESHOLD
        self.removal_threshold = self.DEFAULT_REMOVAL_THRESHOLD
        self.base_level_activation = self.DEFAULT_BASE_LEVEL_ACTIVATION
        self.excite_strategy = LinearExciteStrategy()
        self.decay_strategy = DefaultDecayStrategy()

    def get_activation(self) -> float:
        """
        获取此对象的激活值

        返回:
            此对象的激活值
        """
        return self.activation

    def set_activation(self, activation: float) -> None:
        """
        设置此对象的激活值

        参数:
            activation: 要设置的激活值
        """
        self.activation = activation

    def get_activation_threshold(self) -> float:
        """
        获取此对象的激活阈值

        返回:
            此对象的激活阈值
        """
        return self.activation_threshold

    def set_activation_threshold(self, threshold: float) -> None:
        """
        设置此对象的激活阈值

        参数:
            threshold: 要设置的激活阈值
        """
        self.activation_threshold = threshold

    def get_removal_threshold(self) -> float:
        """
        获取此对象的移除阈值

        返回:
            此对象的移除阈值
        """
        return self.removal_threshold

    def set_removal_threshold(self, threshold: float) -> None:
        """
        设置此对象的移除阈值

        参数:
            threshold: 要设置的移除阈值
        """
        self.removal_threshold = threshold

    def is_above_activation_threshold(self) -> bool:
        """
        检查此对象是否超过其激活阈值

        返回:
            如果超过激活阈值返回True，否则返回False
        """
        return self.activation >= self.activation_threshold

    def is_above_removal_threshold(self) -> bool:
        """
        检查此对象是否超过其移除阈值

        返回:
            如果超过移除阈值返回True，否则返回False
        """
        return self.activation >= self.removal_threshold

    def get_base_level_activation(self) -> float:
        """
        获取此对象的基础激活水平

        返回:
            此对象的基础激活水平
        """
        return self.base_level_activation

    def set_base_level_activation(self, activation: float) -> None:
        """
        设置此对象的基础激活水平

        参数:
            activation: 要设置的基础激活水平
        """
        self.base_level_activation = activation

    def get_excite_strategy(self) -> ExciteStrategy:
        """
        获取此对象的激励策略

        返回:
            此对象的激励策略
        """
        return self.excite_strategy

    def set_excite_strategy(self, strategy: ExciteStrategy) -> None:
        """
        设置此对象的激励策略

        参数:
            strategy: 要设置的激励策略
        """
        self.excite_strategy = strategy

    def get_decay_strategy(self) -> DecayStrategy:
        """
        获取此对象的衰减策略

        返回:
            此对象的衰减策略
        """
        return self.decay_strategy

    def set_decay_strategy(self, strategy: DecayStrategy) -> None:
        """
        设置此对象的衰减策略

        参数:
            strategy: 要设置的衰减策略
        """
        self.decay_strategy = strategy

    def excite(self, amount: float) -> None:
        """
        按给定值激励此对象

        参数:
            amount: 激励值
        """
        self.activation = self.excite_strategy.excite(self.activation, self.base_level_activation, amount)

    def decay(self, ticks: int) -> None:
        """
        按给定的ticks数衰减此对象

        参数:
            ticks: 衰减的ticks数
        """
        self.activation = self.decay_strategy.decay(self.activation, self.base_level_activation, ticks)

    def total_activation(self) -> float:
        """
        获取此对象的总激活值

        返回:
            此对象的总激活值
        """
        return self.activation

    def init(self, params: Optional[Dict[str, Any]] = None) -> None:
        """
        使用给定参数初始化此对象

        参数:
            params: 初始化参数
        """
        if params is None:
            return

        if "activation" in params:
            self.activation = float(params["activation"])
        if "activationThreshold" in params:
            self.activation_threshold = float(params["activationThreshold"])
        if "removalThreshold" in params:
            self.removal_threshold = float(params["removalThreshold"])
        if "baseLevelActivation" in params:
            self.base_level_activation = float(params["baseLevelActivation"])


    def reinforce(self, amount: float) -> None:
        pass

    def set_incentive_salience(self, salience: float) -> None:
        self.incentive_salience = salience

    def get_incentive_salience(self) -> float:
        return self.incentive_Salience

    def is_removable(self) -> bool:
        return self.get_activation() <= self.removal_threshold # and Math.abs(getIncentiveSalience()) <= removalThreshold
