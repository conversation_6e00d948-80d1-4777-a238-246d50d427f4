"""
左转操作实现

本模块提供ALife环境中左转操作的实现
"""
from typing import List, Optional, Any

from linars.edu.memphis.ccrg.alife.elements.alife_object import ALifeObject
from linars.edu.memphis.ccrg.alife.opreations.world_operation import WorldOperation

class TurnLeftOperation(WorldOperation):
    """
    左转操作实现

    该类实现ALife环境中的左转操作
    """

    def __init__(self):
        """初始化左转操作"""
        super().__init__("left")

    def execute(self, actor: ALifeObject, target: Optional[ALifeObject], *params) -> bool:
        """
        执行左转操作

        参数:
            actor: 执行左转行为的对象
            target: 左转目标(未使用)
            params: 附加参数

        返回:
            操作成功返回True，否则返回False
        """
        # Get the actor's current direction
        direction = actor.get_attribute("direction")
        if direction is None:
            return False

        # Turn left (90 degrees counter-clockwise)
        new_direction = None
        if direction == 'N':
            new_direction = 'W'
        elif direction == 'S':
            new_direction = 'E'
        elif direction == 'E':
            new_direction = 'N'
        elif direction == 'W':
            new_direction = 'S'

        # Set the new direction
        actor.set_attribute("direction", new_direction)

        return True
