# LIDA认知框架
"""
链接类别(LinkCategory)表示链接(Link)的类型
"""

from abc import ABC, abstractmethod

class LinkCategory(ABC):
    """
    链接类别(LinkCategory)表示链接(Link)的类型
    """

    @abstractmethod
    def get_id(self) -> int:
        """
        获取此类别的ID

        返回:
            此类别的ID
        """
        pass

    @abstractmethod
    def get_label(self) -> str:
        """
        获取此类别的标签

        返回:
            此类别的标签
        """
        pass
