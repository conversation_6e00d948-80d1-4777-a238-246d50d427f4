# LIDA Cognitive Framework
"""
A Scheme is a template for a behavior. It consists of a context, an action, and a result.
"""

from abc import ABC, abstractmethod
from typing import Collection, Dict, Any
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.Learnable import Learnable
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Condition import Condition
from linars.edu.memphis.ccrg.lida.ProceduralMemory.ProceduralUnit import ProceduralUnit

class Scheme(Learnable, ProceduralUnit, ABC):
    """
    A Scheme is a template for a behavior. It consists of a context, an action, and a result.
    """

    @abstractmethod
    def get_id(self) -> int:
        """
        Get the ID of this scheme.

        Returns:
            The ID of this scheme
        """
        pass

    @abstractmethod
    def set_id(self, id: int) -> None:
        """
        Set the ID of this scheme.

        Args:
            id: The ID to set
        """
        pass

    @abstractmethod
    def get_name(self) -> str:
        """
        Get the name of this scheme.

        Returns:
            The name of this scheme
        """
        pass

    @abstractmethod
    def set_name(self, name: str) -> None:
        """
        Set the name of this scheme.

        Args:
            name: The name to set
        """
        pass

    @abstractmethod
    def get_action(self) -> Action:
        """
        Get the action of this scheme.

        Returns:
            The action of this scheme
        """
        pass

    @abstractmethod
    def set_action(self, action: Action) -> None:
        """
        Set the action of this scheme.

        Args:
            action: The action to set
        """
        pass

    @abstractmethod
    def get_context_conditions(self) -> Collection[Condition]:
        """
        Get the context conditions of this scheme.

        Returns:
            The context conditions of this scheme
        """
        pass

    @abstractmethod
    def add_context_condition(self, condition: Condition) -> None:
        """
        Add a context condition to this scheme.

        Args:
            condition: The condition to add
        """
        pass

    @abstractmethod
    def get_adding_list(self) -> Collection[Condition]:
        """
        Get the adding list of this scheme.

        Returns:
            The adding list of this scheme
        """
        pass

    @abstractmethod
    def add_to_adding_list(self, condition: Condition) -> None:
        """
        Add a condition to the adding list of this scheme.

        Args:
            condition: The condition to add
        """
        pass

    @abstractmethod
    def get_deleting_list(self) -> Collection[Condition]:
        """
        Get the deleting list of this scheme.

        Returns:
            The deleting list of this scheme
        """
        pass

    @abstractmethod
    def add_to_deleting_list(self, condition: Condition) -> None:
        """
        Add a condition to the deleting list of this scheme.

        Args:
            condition: The condition to add
        """
        pass

    @abstractmethod
    def add_condition(self, condition: Condition, condition_type: Any) -> bool:
        """
        Add a condition to this scheme.

        Args:
            condition: The condition to add
            condition_type: The type of the condition

        Returns:
            True if the condition was added, False otherwise
        """
        pass

    @abstractmethod
    def get_label(self) -> str:
        """
        Get the label of this scheme.

        Returns:
            The label of this scheme
        """
        pass

    @abstractmethod
    def set_label(self, label: str) -> None:
        """
        Set the label of this scheme.

        Args:
            label: The label to set
        """
        pass

    @abstractmethod
    def action_executed(self) -> None:
        """
        Called when Scheme's action is executed.
        Scheme should update the number of times its action has been executed in order to calculate
        reliability.
        """
        pass

    @abstractmethod
    def action_successful(self) -> None:
        """
        Called when Scheme's action produces expected result.
        """
        pass

    @abstractmethod
    def get_reliability(self) -> float:
        """
        Get the reliability of this scheme.

        Returns:
            Frequency that result is observed after scheme's Action is taken
        """
        pass

    @abstractmethod
    def is_reliable(self) -> bool:
        """
        Check if this scheme is reliable.

        Returns:
            True if reliability is over threshold, False otherwise
        """
        pass

    @abstractmethod
    def set_innate(self, innate: bool) -> None:
        """
        Set whether this scheme is innate.

        Args:
            innate: Whether this Scheme is hard-wired and cannot be decayed
        """
        pass

    @abstractmethod
    def is_innate(self) -> bool:
        """
        Check if this scheme is innate.

        Returns:
            True if this scheme should not be decayed, False otherwise
        """
        pass
