"""
NARS事件类型定义
包含系统使用的各种事件类定义
"""
from typing import List, Any, Optional, Type, Callable
import traceback
import time

from linars.org.opennars.io.events.event_emitter import EventEmitter

class Events:
    """NARS事件类型定义集合"""

    # Basic events
    class CycleStart:
        """周期开始事件"""
        pass

    class CycleEnd:
        """周期结束事件"""
        pass

    class CyclesStart:
        """多周期开始事件"""
        pass

    class CyclesEnd:
        """多周期结束事件"""
        pass

    class WorkCycleStart:
        """工作周期开始事件"""
        pass

    class WorkCycleEnd:
        """工作周期结束事件"""
        pass

    class ResetStart:
        """重置开始事件"""
        pass

    class ResetEnd:
        """重置结束事件"""
        pass

    class Answer:
        """答案事件"""
        pass

    class Unsolved:
        """未解决事件"""
        pass

    class TrySolution:
        """尝试解决方案事件"""
        pass

    class TaskImmediateProcess:
        """任务即时处理事件"""

        @staticmethod
        def on_processed(task, derivation_context):
            """
            处理任务处理事件

            参数:
                task: 待处理的任务
                derivation_context: 推导上下文
            """
            pass

        def event(self, event, args):
            """
            处理事件回调

            参数:
                event: 事件类型
                args: 事件参数
            """
            self.on_processed(args[0], args[1])

    class ConceptNew:
        """新概念创建事件"""

        def __init__(self, c, when):
            """
            构造函数

            参数:
                c: 新创建的概念
                when: 创建时间
            """
            self.object = c
            self.when = when

        def __str__(self):
            """
            获取字符串表示

            返回:
                str: 字符串表示
            """
            return f"Concept Created: {self.object}"

    class Perceive:
        """感知事件"""
        pass

    class ConceptForget:
        """概念遗忘事件"""
        pass

    class EnactableExplainationAdd:
        """可执行解释添加事件"""
        pass

    class EnactableExplainationRemove:
        """可执行解释移除事件"""
        pass

    class ConceptBeliefAdd(EventEmitter.EventObserver):
        """概念信念添加事件"""

        def on_belief_add(self, c, t, extra):
            """
            处理信念添加事件

            参数:
                c: 相关概念
                t: 相关任务
                extra: 附加信息
            """
            pass

        def event(self, event, args):
            """
            处理事件回调

            参数:
                event: 事件类型
                args: 事件参数
            """
            self.on_belief_add(args[0], args[1], args[2])

    class ConceptBeliefRemove(EventEmitter.EventObserver):
        """概念信念移除事件"""

        def on_belief_remove(self, c, removed, t, extra):
            """
            处理信念移除事件

            参数:
                c: 相关概念
                removed: 移除的句子
                t: 相关任务
                extra: 附加信息
            """
            pass

        def event(self, event, args):
            """
            处理事件回调

            参数:
                event: 事件类型
                args: 事件参数
            """
            self.on_belief_remove(args[0], args[1], args[2], args[3])

    class ConceptGoalAdd:
        """概念目标添加事件"""
        pass

    class ConceptGoalRemove:
        """概念目标移除事件"""
        pass

    class ConceptQuestionAdd:
        """概念问题添加事件"""
        pass

    class ConceptQuestionRemove:
        """概念问题移除事件"""
        pass

    # Executive & Planning
    class UnexecutableGoal:
        """不可执行目标事件"""
        pass

    class UnexecutableOperation:
        """不可执行操作事件"""
        pass

    class NewTaskExecution:
        """新任务执行事件"""
        pass

    class InduceSucceedingEvent:
        """诱导后续事件"""
        pass

    class TermLinkAdd:
        """词项链接添加事件"""
        pass

    class TermLinkRemove:
        """词项链接移除事件"""
        pass

    class TaskLinkAdd:
        """任务链接添加事件"""
        pass

    class TaskLinkRemove:
        """任务链接移除事件"""
        pass

    class ConceptFire(EventEmitter.EventObserver):
        """概念触发事件"""

        def on_fire(self, n):
            """
            处理概念触发事件

            参数:
                n: 推理控制对象
            """
            pass

        def event(self, event, args):
            """
            处理事件回调

            参数:
                event: 事件类型
                args: 事件参数
            """
            self.on_fire(args[0])

    class TermLinkSelect:
        """词项链接选择事件"""
        pass

    class BeliefSelect:
        """信念选择事件"""
        pass

    class BeliefReason:
        """信念推理事件"""
        pass

    class ConceptUnification:
        """概念统一事件"""
        pass

    class TaskAdd(EventEmitter.EventObserver):
        """任务添加事件"""

        def on_task_add(self, t, reason):
            """
            处理任务添加事件

            参数:
                t: 添加的任务
                reason: 添加原因
            """
            pass

        def event(self, event, args):
            """
            处理事件回调

            参数:
                event: 事件类型
                args: 事件参数
            """
            self.on_task_add(args[0], args[1])

    class TaskRemove:
        """任务移除事件"""
        pass

    class TaskDerive:
        """任务派生事件"""
        pass

    class PluginsChange:
        """插件变更事件"""
        pass

    class ConceptDirectProcessedTask:
        """概念直接处理任务事件"""
        pass

    class InferenceEvent:
        """推理事件基类"""

        def __init__(self, when, stack_frames=0):
            """
            构造函数

            参数:
                when: 事件发生时间
                stack_frames: 要记录的调用栈帧数
            """
            self.when = when

            # Stack trace handling
            if stack_frames > 0:
                stack = traceback.extract_stack()

                # Find the frame where the class is Nar
                frame = 0
                for e in stack:
                    frame += 1
                    if "Nar" in e.filename:
                        break

                # Adjust frame based on stack frames
                stack_prefix = 4
                if frame - stack_prefix > stack_frames:
                    frame = stack_prefix + stack_frames

                self.stack = stack[stack_prefix:frame]
            else:
                self.stack = None

        def get_type(self):
            """
            获取事件类型

            返回:
                Type: 事件类型
            """
            return self.__class__

    class ParametricInferenceEvent(InferenceEvent):
        """参数化推理事件"""

        def __init__(self, object, when):
            """
            构造函数

            参数:
                object: 相关对象
                when: 事件发生时间
            """
            super().__init__(when)
            self.object = object

# 为了向后兼容，保留原来的类定义
CycleStart = Events.CycleStart
CycleEnd = Events.CycleEnd
CyclesStart = Events.CyclesStart
CyclesEnd = Events.CyclesEnd
ResetStart = Events.ResetStart
ResetEnd = Events.ResetEnd
Answer = Events.Answer
TaskImmediateProcess = Events.TaskImmediateProcess
ConceptNew = Events.ConceptNew
Perceive = Events.Perceive
ConceptForget = Events.ConceptForget
EnactableExplainationAdd = Events.EnactableExplainationAdd
EnactableExplainationRemove = Events.EnactableExplainationRemove
ConceptBeliefAdd = Events.ConceptBeliefAdd
ConceptFire = Events.ConceptFire
TermLinkSelect = Events.TermLinkSelect
BeliefSelect = Events.BeliefSelect
BeliefReason = Events.BeliefReason
ConceptUnification = Events.ConceptUnification
TaskAdd = Events.TaskAdd
TaskRemove = Events.TaskRemove
TaskDerive = Events.TaskDerive
PluginsChange = Events.PluginsChange
ConceptDirectProcessedTask = Events.ConceptDirectProcessedTask
InferenceEvent = Events.InferenceEvent
ParametricInferenceEvent = Events.ParametricInferenceEvent
