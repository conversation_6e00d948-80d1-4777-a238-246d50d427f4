"""
Alternative entry point for the ALife agent.

This module provides an alternative entry point for starting the ALife agent.
"""
import logging
import os
import sys
import threading
from py2neo import Graph
from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
from linars.edu.memphis.ccrg.lida.Data.neo_util import init as neo_init

def main(args=None):
    """
    Alternative main entry point for the ALife agent.

    Args:
        args: Command line arguments
    """
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    print("=== 启动 ALife Agent ===")
    print("开始初始化...")

    # 初始化Neo4j数据库连接
    try:
        neo4j_uri = "bolt://localhost:7687"
        neo4j_user = "neo4j"
        neo4j_password = "12345678"
        logger.info(f"Connecting to Neo4j at {neo4j_uri} with user {neo4j_user}")
        graph_db = Graph(neo4j_uri, auth=(neo4j_user, neo4j_password))
        print("Successfully connected to Neo4j")

        # 初始化Neo4j工具
        neo_init(graph_db, None, None)  # PAM和Narsese实例将在后续设置
        logger.info("Neo4j utility initialized")
    except Exception as e:
        logger.warning(f"Failed to connect to Neo4j: {e}")

    # Log current working directory and file locations
    # logger.info(f"Current working directory: {os.getcwd()}")
    # logger.info(f"Script location: {os.path.abspath(__file__)}")

    # Check if configs directory exists in current directory
    configs_dir = os.path.join(os.getcwd(), "configs")
    # logger.info(f"Checking if configs directory exists at: {configs_dir}")
    # logger.info(f"Configs directory exists: {os.path.exists(configs_dir)}")

    # Check if agent.xml exists in current directory
    agent_xml = os.path.join(configs_dir, "agent.xml")
    # logger.info(f"Checking if agent.xml exists at: {agent_xml}")
    logger.info(f"agent.xml exists: {os.path.exists(agent_xml)}")

    # Get the project root directory
    current_dir = os.path.abspath(__file__)
    for _ in range(7):  # Go up 7 levels to reach the project root
        current_dir = os.path.dirname(current_dir)
    project_root = current_dir

    # Check if configs directory exists at project root
    configs_at_root = os.path.join(project_root, "configs")
    # logger.info(f"Checking if configs directory exists at project root: {configs_at_root}")
    # logger.info(f"Configs directory at project root exists: {os.path.exists(configs_at_root)}")

    # Check if agent.xml exists at project root
    agent_xml_at_root = os.path.join(configs_at_root, "agent.xml")
    # logger.info(f"Checking if agent.xml exists at project root: {agent_xml_at_root}")
    # logger.info(f"agent.xml at project root exists: {os.path.exists(agent_xml_at_root)}")

    # If configs directory doesn't exist in current directory but exists at project root,
    # create a symbolic link or copy the files
    if not os.path.exists(configs_dir) and os.path.exists(configs_at_root):
        # logger.info(f"Creating configs directory at: {configs_dir}")
        try:
            # Try to create a symbolic link first
            os.makedirs(os.path.dirname(configs_dir), exist_ok=True)
            if sys.platform == 'win32':
                # On Windows, use directory junction
                import subprocess
                subprocess.run(['mklink', '/J', configs_dir, configs_at_root], shell=True, check=True)
            else:
                # On Unix-like systems, use symbolic link
                os.symlink(configs_at_root, configs_dir)
            # logger.info(f"Created symbolic link from {configs_dir} to {configs_at_root}")
        except Exception as e:
            logger.warning(f"Failed to create symbolic link: {e}")
            # logger.info(f"Will use absolute paths instead")
    # Run agent and web server in separate threads
    try:
        from linars.com.warmer.kgmaker.app import app
    except Exception as e:
        logger.error(f"Error importing web app: {e}")
        return

    # Define functions to run agent and web server in separate threads
    def run_agent():
        try:
            print("-------Starting agent...")
            AgentStarter.main(args)
        except Exception as e:
            logger.error(f"Error running agent: {e}")
            import traceback
            traceback.print_exc()

    def run_web_server():
        try:
            logger.info("Starting web server...")
            app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)  # Set debug=False and use_reloader=False to avoid extra output
        except Exception as e:
            logger.error(f"Error starting web server: {e}")
            import traceback
            traceback.print_exc()

    # Create and start threads
    agent_thread = threading.Thread(target=run_agent)
    web_thread = threading.Thread(target=run_web_server)

    agent_thread.daemon = True  # Make thread daemon so it exits when main thread exits
    web_thread.daemon = True    # Make thread daemon so it exits when main thread exits

    web_thread.start()
    agent_thread.start()

    # Keep the main thread alive
    try:
        # Wait for threads to complete (which they won't unless there's an error)
        while agent_thread.is_alive() and web_thread.is_alive():
            web_thread.join(1)  # Wait for 1 second, then check again
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, exiting...")
    except Exception as e:
        logger.error(f"Error in main thread: {e}")

if __name__ == "__main__":
    main()
