# LIDA Cognitive Framework
"""
Task which operates a Sensory Memory. This class provides a general way to control various type of
sensory memory -- It is the meaning of "background" here.
"""

import logging
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemory import SensoryMemory

class SensoryMemoryBackgroundTask(FrameworkTaskImpl):
    """
    Task which operates a Sensory Memory. This class provides a general way to control various type of
    sensory memory -- It is the meaning of "background" here.
    """
    
    def __init__(self, ticks_per_run: int = 1):
        """
        Initialize a SensoryMemoryBackgroundTask.
        
        Args:
            ticks_per_run: The number of ticks between runs of this task
        """
        super().__init__(ticks_per_run)
        self.sm = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module for this task.
        
        Args:
            module: The module to associate with this task
            module_usage: How this task will use the module
        """
        if isinstance(module, SensoryMemory):
            self.sm = module
        else:
            self.logger.warning(f"Cannot add module {module} at tick {TaskManager.get_current_tick()}")
    
    def run_this_framework_task(self) -> None:
        """
        Run this task.
        """
        if self.sm is not None:
            self.sm.run_sensors()
