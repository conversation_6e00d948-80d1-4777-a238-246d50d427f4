# LIDA认知框架
"""
链接(Link)连接两个可链接(Linkable)对象
"""

from abc import abstractmethod
from typing import Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategory import LinkCategory

class Link(Linkable):
    """
    链接(Link)连接两个可链接(Linkable)对象
    """

    @abstractmethod
    def is_simple_link(self) -> bool:
        """
        返回链接是否为简单链接(连接两个节点)

        返回:
            如果是简单链接返回True，如果是复杂链接(连接节点和简单链接)返回False
        """
        pass

    @abstractmethod
    def get_source(self) -> Node:
        """
        链接的一端，向接收端提供激活

        返回:
            源可链接对象
        """
        pass

    @abstractmethod
    def get_sink(self) -> Linkable:
        """
        链接的一端，从源接收激活

        返回:
            接收端可链接对象
        """
        pass

    @abstractmethod
    def set_source(self, source: Node) -> None:
        """
        设置源可链接对象

        参数:
            source: 新的源
        """
        pass

    @abstractmethod
    def set_sink(self, sink: Linkable) -> None:
        """
        设置接收端可链接对象

        参数:
            sink: 新的接收端
        """
        pass

    @abstractmethod
    def get_category(self) -> LinkCategory:
        """
        获取此链接的链接类别

        返回:
            链接类别
        """
        pass

    @abstractmethod
    def set_category(self, category: LinkCategory) -> None:
        """
        设置链接类别

        参数:
            category: 新的类别
        """
        pass

    @abstractmethod
    def set_grounding_pam_link(self, link: 'PamLink') -> None:
        """
        设置此链接的基础PamLink

        参数:
            link: 新的基础pam链接
        """
        pass

    @abstractmethod
    def get_grounding_pam_link(self) -> 'PamLink':
        """
        获取此链接的基础PamLink

        返回:
            基础pam链接
        """
        pass

    @abstractmethod
    def update_link_values(self, link: 'Link') -> None:
        """
        Link的子类应重写此方法，使用指定链接的值设置其特定类型的成员数据。
        指定的链接必须是相同子类类型。

        参数:
            link: 用于更新值的链接
        """
        pass

    @abstractmethod
    def get_id(self) -> int:
        """
        获取此链接的ID

        返回:
            此链接的ID
        """
        pass

    @abstractmethod
    def set_id(self, id: int) -> None:
        """
        设置此链接的ID

        参数:
            id: 要设置的ID
        """
        pass
