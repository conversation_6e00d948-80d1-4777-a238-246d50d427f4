"""
操作类，表示NARS中的操作。
"""
from typing import List, Optional, Set, Dict, Any
import copy

from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.io.symbols import NativeOperator

class Operation(CompoundTerm):
    """
    操作类，表示NARS中的操作。
    """
    
    def __init__(self, name: str = None):
        """
        构造函数
        
        参数:
            name: 操作名称
        """
        super().__init__(name)
        self.operator = None
        self.args = []
    
    @staticmethod
    def make(operator: Term, args: List[Term]) -> 'Operation':
        """
        创建操作
        
        参数:
            operator: 操作符
            args: 参数列表
            
        返回:
            Operation: 创建的操作
        """
        if not args:
            raise ValueError("Args cannot be empty")
        
        t = Operation()
        t.operator = operator
        t.args = args.copy()
        
        # 设置操作的名称
        name_builder = []
        name_builder.append("(^")
        name_builder.append(str(operator))
        
        for arg in args:
            name_builder.append(" ")
            name_builder.append(str(arg))
        
        name_builder.append(")")
        t.name = "".join(name_builder)
        
        return t
    
    def get_operator(self) -> Term:
        """
        获取操作符
        
        返回:
            Term: 操作符
        """
        return self.operator
    
    def get_args(self) -> List[Term]:
        """
        获取参数列表
        
        返回:
            List[Term]: 参数列表
        """
        return self.args
    
    def operator(self) -> NativeOperator:
        """
        获取操作的原生操作符
        
        返回:
            NativeOperator: 原生操作符
        """
        return NativeOperator.OPERATOR
    
    def clone(self) -> 'Operation':
        """
        克隆操作
        
        返回:
            Operation: 克隆的操作
        """
        c = Operation()
        c.operator = self.operator
        c.args = self.args.copy()
        c.name = self.name
        return c
    
    def __eq__(self, other: Any) -> bool:
        """
        比较两个操作是否相等
        
        参数:
            other: 要比较的对象
            
        返回:
            bool: 如果相等返回True
        """
        if not isinstance(other, Operation):
            return False
        
        if self.operator != other.operator:
            return False
        
        if len(self.args) != len(other.args):
            return False
        
        for i in range(len(self.args)):
            if self.args[i] != other.args[i]:
                return False
        
        return True
    
    def __hash__(self) -> int:
        """
        获取哈希值
        
        返回:
            int: 哈希值
        """
        result = hash(self.operator)
        for arg in self.args:
            result = 31 * result + hash(arg)
        return result
