from typing import List

from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.concept import Concept
from linars.org.opennars.control.derivation_context import DerivationContext
from linars.org.opennars.entity.task import Task
from linars.org.opennars.inference.local_rules import LocalRules
from linars.org.opennars.io.events.events import Events
from linars.org.opennars.io.symbols import QUEST_MARK, VAR_QUERY
from linars.org.opennars.language.variable import Variable

class ProcessQuestion:
    """
    处理问题任务的类
    """

    @staticmethod
    def processQuestion(concept: Concept, nal: DerivationContext, task: Task) -> None:
        """
        通过现有信念回答问题

        Args:
            concept: 问题的概念
            nal: 推导上下文
            task: 要处理的任务
        """
        quesTask = task
        questions = concept.questions
        if task.sentence.punctuation == QUEST_MARK:
            questions = concept.quests

        if task.sentence.is_Eternal():
            eternalQuestionTask = None
            for iQuestionTask in questions:
                if iQuestionTask.sentence.isEternal():
                    eternalQuestionTask = iQuestionTask
                    break

            # 如果找到了永恒问题任务，我们可以用它覆盖问题任务
            if eternalQuestionTask is not None:
                quesTask = eternalQuestionTask

        if len(questions) + 1 > AgentStarter.nar.narParameters.CONCEPT_QUESTIONS_MAX:
            removed = questions.pop(0)  # FIFO
            concept.memory.event.emit(Events.ConceptQuestionRemove, concept, removed)

        questions.append(quesTask)
        concept.memory.event.emit(Events.ConceptQuestionAdd, concept, task)

        ques = quesTask.sentence
        newAnswerT = (concept.selectCandidate(quesTask, concept.beliefs, nal.time)
                      if ques.isQuestion()
                      else concept.selectCandidate(quesTask, concept.desires, nal.time))

        if newAnswerT is not None:
            LocalRules.try_solution(newAnswerT.sentence, task, nal, True)
        elif (task.isInput() and not quesTask.get_term().has_var_query() and
              quesTask.get_best_solution() is not None):  # 在输入的情况下仍然显示先前找到的解决方案
            concept.memory.emit(Events.Answer, quesTask, quesTask.get_best_solution())

    @staticmethod
    def ProcessWhatQuestion(concept: Concept, ques: Task, nal: DerivationContext) -> None:
        """
        将现有信念任务识别为包含查询变量的what问题任务的解决方案

        Args:
            concept: 可能过时的预期应该被处理的概念
            ques: 信念任务
            nal: 推导上下文
        """
        # 仅在概念选择时在GeneralInferenceControl.insertTaskLink中调用
        if (not ques.sentence.is_judgment()) and ques.get_term().has_var_query():  # 确认查询变量，搜索
            newAnswer = False
            for t in concept.taskLinks:
                u = [CompoundTerm.replace_intervals(ques.get_term()), CompoundTerm.replace_intervals(t.get_term())]
                if not t.get_term().has_var_query() and Variable.unify(AgentStarter.nar.memory.randomNumber, VAR_QUERY, u):
                    c = AgentStarter.nar.memory.concept(t.get_term())
                    if c is None:
                        continue  # 目标概念已经消失

                    with c:  # 更改目标概念，锁定它
                        answers = c.desires if ques.sentence.is_quest() else c.beliefs
                        if c is not None and len(answers) > 0:
                            taskAnswer = answers[0]
                            if taskAnswer is not None:
                                newAnswer |= LocalRules.try_solution(taskAnswer.sentence, ques, nal, False)  # 这里顺序很重要

            if newAnswer and ques.isInput():
                AgentStarter.nar.memory.emit(Events.Answer, ques, ques.get_best_solution())

    @staticmethod
    def ProcessWhatQuestionAnswer(concept: Concept, t: Task, nal: DerivationContext) -> None:
        """
        将添加的信念任务识别为what问题的解决方案，即那些包含查询变量的问题

        Args:
            concept: 可能过时的预期应该被处理的概念
            t: 信念任务
            nal: 推导上下文
        """
        # 仅在概念选择时在GeneralInferenceControl.insertTaskLink中调用
        if (not t.sentence.term.has_var_query() and (t.sentence.is_judgment() or t.sentence.is_goal())):  # 确认查询变量，搜索
            for quess in concept.taskLinks:
                ques = quess.get_target()
                if (((ques.sentence.is_question() and t.sentence.is_judgment()) or
                     (ques.sentence.is_goal() and t.sentence.is_judgment()) or
                     (ques.sentence.is_quest() and t.sentence.is_goal())) and
                    ques.get_term().has_var_query()):
                    newAnswer = False
                    u = [CompoundTerm.replace_intervals(ques.get_term()), CompoundTerm.replace_intervals(t.get_term())]
                    if (ques.sentence.term.has_var_query() and not t.get_term().has_var_query() and
                        Variable.unify(AgentStarter.nar.memory.randomNumber, VAR_QUERY, u)):
                        c = AgentStarter.nar.memory.concept(t.get_term())
                        if c is None:
                            continue  # 目标不再存在

                        with c:  # 更改目标概念，锁定它
                            answers = c.desires if ques.sentence.is_quest() else c.beliefs
                            if c is not None and len(answers) > 0:
                                taskAnswer = answers[0]
                                if taskAnswer is not None:
                                    newAnswer |= LocalRules.try_solution(taskAnswer.sentence, ques, nal, False)  # 这里顺序很重要

                    if newAnswer and ques.isInput():
                        AgentStarter.nar.memory.emit(Events.Answer, ques, ques.get_best_solution())
