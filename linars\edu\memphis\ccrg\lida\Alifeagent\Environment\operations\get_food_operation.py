"""
获取食物操作实现

本模块提供ALife环境中获取食物操作的实现
"""
from typing import List, Optional, Any

from linars.edu.memphis.ccrg.alife.elements.alife_object import ALifeObject
from linars.edu.memphis.ccrg.alife.elements.cell import Cell
from linars.edu.memphis.ccrg.alife.opreations.world_operation import WorldOperation

class GetFoodOperation(WorldOperation):
    """
    获取食物操作实现

    该类实现ALife环境中的获取食物操作
    """

    def __init__(self):
        """初始化获取食物操作"""
        super().__init__("getfood")

    def execute(self, actor: ALifeObject, target: Optional[ALifeObject], *params) -> bool:
        """
        执行获取食物操作

        参数:
            actor: 执行获取食物行为的对象
            target: 获取食物目标(未使用)
            params: 附加参数

        返回:
            操作成功返回True，否则返回False
        """
        # Get the actor's cell
        cell = actor.get_container()
        if not isinstance(cell, Cell):
            return False

        # Get the world
        world = cell.get_world()

        # Find food in the world
        food_found = False
        min_distance = float('inf')
        food_x, food_y = -1, -1

        # Search all cells for food
        for y in range(world.get_height()):
            for x in range(world.get_width()):
                current_cell = world.get_cell(x, y)
                for obj in current_cell.get_objects():
                    if "food" in obj.get_name().lower():
                        # Calculate distance to food
                        distance = abs(x - cell.get_x_coordinate()) + abs(y - cell.get_y_coordinate())
                        if distance < min_distance:
                            min_distance = distance
                            food_x, food_y = x, y
                            food_found = True

        if not food_found:
            return False

        # Determine direction to food
        actor_x = cell.get_x_coordinate()
        actor_y = cell.get_y_coordinate()

        # Set direction towards food
        if abs(food_x - actor_x) > abs(food_y - actor_y):
            # Move horizontally first
            if food_x > actor_x:
                actor.set_attribute("direction", 'E')
            else:
                actor.set_attribute("direction", 'W')
        else:
            # Move vertically first
            if food_y > actor_y:
                actor.set_attribute("direction", 'S')
            else:
                actor.set_attribute("direction", 'N')

        return True
