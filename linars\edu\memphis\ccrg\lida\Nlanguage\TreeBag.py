#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
TreeBag class for the LIDA framework.
"""

from typing import Dict, List, Set, Any, Optional, Collection, Tuple
import logging
import heapq

from linars.edu.memphis.ccrg.lida.Nlanguage.TreeChart import TreeChart

class TreeBag:
    """
    A tree bag in the LIDA framework.
    """
    
    def __init__(self, capacity: int, max_size: int, max_level: int):
        """
        Initialize a TreeBag.
        
        Args:
            capacity: The capacity of the bag
            max_size: The maximum size of the bag
            max_level: The maximum level of the bag
        """
        self.capacity = capacity
        self.max_size = max_size
        self.max_level = max_level
        self.items = []  # List of TreeChart
        self.logger = logging.getLogger("TreeBag")
        
    def put_in(self, chart: TreeChart) -> None:
        """
        Put a chart in the bag.
        
        Args:
            chart: The chart to put in
        """
        # Check if the chart is already in the bag
        for i, c in enumerate(self.items):
            if c.chart_cmp(chart):
                # Update the existing chart
                self.items[i].merge(chart)
                return
                
        # Add the chart to the bag
        self.items.append(chart)
        
        # Sort the items by priority
        self.items.sort(key=lambda x: x.get_priority(), reverse=True)
        
        # Remove excess items
        if len(self.items) > self.capacity:
            self.items.pop()
            
    def take_out(self) -> Optional[TreeChart]:
        """
        Take a chart out of the bag.
        
        Returns:
            The chart with the highest priority, or None if the bag is empty
        """
        if not self.items:
            return None
            
        # Get the chart with the highest priority
        chart = self.items[0]
        
        # Remove the chart from the bag
        self.items.pop(0)
        
        return chart
        
    def put_back(self, chart: TreeChart) -> None:
        """
        Put a chart back in the bag.
        
        Args:
            chart: The chart to put back
        """
        self.put_in(chart)
        
    def get_charts(self) -> List[TreeChart]:
        """
        Get all charts in the bag.
        
        Returns:
            The list of charts
        """
        return self.items
        
    def clear(self) -> None:
        """
        Clear the bag.
        """
        self.items.clear()
        
    def __len__(self) -> int:
        """
        Get the number of charts in the bag.
        
        Returns:
            The number of charts
        """
        return len(self.items)
