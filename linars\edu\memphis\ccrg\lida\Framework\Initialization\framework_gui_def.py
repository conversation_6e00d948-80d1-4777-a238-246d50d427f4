"""
Framework GUI definition.

This module provides a class for defining framework GUIs.
"""
from typing import Dict, Any, Optional, List

from linars.edu.memphis.ccrg.lida.Framework.Initialization.framework_gui_panel_def import FrameworkGuiPanelDef

class FrameworkGuiDef:
    """
    Framework GUI definition.
    
    This class defines a framework GUI.
    """
    
    def __init__(self, name: str):
        """
        Initialize the framework GUI definition.
        
        Args:
            name: The name of the GUI
        """
        self.name = name
        self.panel_defs = []
    
    def get_name(self) -> str:
        """
        Get the name of the GUI.
        
        Returns:
            The name of the GUI
        """
        return self.name
    
    def add_panel_def(self, panel_def: FrameworkGuiPanelDef) -> None:
        """
        Add a panel definition to the GUI.
        
        Args:
            panel_def: The panel definition to add
        """
        self.panel_defs.append(panel_def)
    
    def get_panel_defs(self) -> List[FrameworkGuiPanelDef]:
        """
        Get the panel definitions.
        
        Returns:
            The panel definitions
        """
        return self.panel_defs
