"""
变量(Variable)是NARS中的特殊项，不对应具体概念

变量类型:
- 独立变量: $开头
- 依赖变量: #开头
- 查询变量: ?开头
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar
import sys

from linars.edu.memphis.ccrg.linars.term import Term

# Variable type symbols
VAR_INDEPENDENT = '$'
VAR_DEPENDENT = '#'
VAR_QUERY = '?'

class Variable(Term):
    """
    变量类，表示NARS中的变量

    主要功能:
    1. 表示不同类型的变量
    2. 管理变量作用域
    3. 提供变量比较和克隆方法
    """

    # Static caches for variable names
    MAX_CACHED_VARNAME_INDEXES = 64
    vn1: ClassVar[List[str]] = [None] * MAX_CACHED_VARNAME_INDEXES  # Independent variables
    vn2: ClassVar[List[str]] = [None] * MAX_CACHED_VARNAME_INDEXES  # Dependent variables
    vn3: ClassVar[List[str]] = [None] * MAX_CACHED_VARNAME_INDEXES  # Query variables

    def __init__(self, name: str, scope: Optional[Term] = None):
        """
        变量构造函数

        参数:
            name: 变量名称(必须包含类型前缀$/#/?)
            scope: 变量作用域(默认为自身)

        注意:
        1. 会自动检测变量类型
        2. 会初始化hash_code为0(延迟计算)
        """
        super().__init__()
        self.type = '\0'  # Caches the type character for faster lookup
        self.scope = None
        self.hash_code = 0
        self.set_scope(scope, name)

    def set_scope(self, scope: Optional[Term], name: str) -> Optional['Variable']:
        """
        Set the scope of the variable

        Args:
            scope: The scope
            name: The name

        Returns:
            Variable: Self or None if invalid
        """
        if not name:
            return None

        self.type = name[0]
        if not self.valid_variable_type(self.type):
            return None

        self.scope = scope if scope is not None else self
        self.hash_code = 0  # Calculate lazily
        self.set_term_name(name)
        return self

    def clone(self) -> 'Variable':
        """
        Clone a Variable

        Returns:
            Variable: The cloned Variable
        """
        v = Variable(self.name(), self.scope)
        if self.scope == self:
            v.scope = v
        return v

    def clone_deep(self) -> 'Variable':
        """
        Deep clone of the variable

        Returns:
            Variable: The cloned variable
        """
        return self.clone()

    def get_type(self) -> str:
        """
        Get the type of the variable

        Returns:
            str: The variable type
        """
        return self.type

    def is_constant(self) -> bool:
        """
        A variable is not constant

        Returns:
            bool: False
        """
        return False

    def get_complexity(self) -> int:
        """
        The syntactic complexity of a variable is 0, because it does not refer to any concept

        Returns:
            int: The complexity of the term, an integer
        """
        return 0

    def has_var(self) -> bool:
        """
        Check if the term has variables

        Returns:
            bool: True for variables
        """
        return True

    def has_var_indep(self) -> bool:
        """
        Check if the term has independent variables

        Returns:
            bool: True if it's an independent variable
        """
        return self.is_independent_variable()

    def has_var_dep(self) -> bool:
        """
        Check if the term has dependent variables

        Returns:
            bool: True if it's a dependent variable
        """
        return self.is_dependent_variable()

    def has_var_query(self) -> bool:
        """
        Check if the term has query variables

        Returns:
            bool: True if it's a query variable
        """
        return self.is_query_variable()

    def equals(self, that: Any) -> bool:
        """
        Check equality

        Args:
            that: The object to compare with

        Returns:
            bool: True if equal
        """
        if that is self:
            return True

        if not isinstance(that, Variable):
            return False

        if not super().equals(that):
            return False

        v = that
        if self.name() != v.name():
            return False

        if ((self.get_scope() == self and v.get_scope() != v) or
            (self.get_scope() != self and v.get_scope() == v)):
            return False

        return v.get_scope().name() == self.get_scope().name()

    def equals_term(self, that: Any) -> bool:
        """
        Check equality with another term

        Args:
            that: The object to compare with

        Returns:
            bool: True if equal
        """
        if not isinstance(that, Variable):
            return False

        v = that
        if (v.scope == v) and (self.scope == self):
            # Both are unscoped, so compare by name only
            return self.name() == v.name()
        elif (v.scope != v) and (self.scope == self):
            return False
        elif (v.scope == v) and (self.scope != self):
            return False
        else:
            if self.name() != v.name():
                return False

            if self.scope is v.scope:
                return True

            if hash(self.scope) != hash(v.scope):
                return False

            # Use name for comparison to avoid infinite recursion
            return self.scope.name() == v.scope.name()

    def __hash__(self) -> int:
        """
        Hash code

        Returns:
            int: Hash code
        """
        if self.hash_code == 0:
            if self.scope != self:
                self.hash_code = 31 * hash(self.name()) + hash(self.scope)
            else:
                self.hash_code = hash(self.name())

        return self.hash_code

    def compare_to(self, that: Term) -> int:
        """
        Compare to another term

        Args:
            that: The term to compare with

        Returns:
            int: Comparison result (-1, 0, 1)
        """
        if self is that:
            return 0

        super_cmp = super().compare_to(that)
        if super_cmp != 0:
            return super_cmp

        if not isinstance(that, Variable):
            return 0

        that_var = that
        name_cmp = (self.name() > that_var.name()) - (self.name() < that_var.name())
        if name_cmp != 0:
            return name_cmp

        if self.get_scope() == self and that_var.get_scope() != that_var:
            return 1

        if self.get_scope() != self and that_var.get_scope() == that_var:
            return -1

        return (self.get_scope().name() > that_var.get_scope().name()) - (self.get_scope().name() < that_var.get_scope().name())

    def is_query_variable(self) -> bool:
        """
        检查是否是查询变量(?开头)

        返回:
            bool: 如果是查询变量返回True
        """
        return self.get_type() == VAR_QUERY

    def is_dependent_variable(self) -> bool:
        """
        检查是否是依赖变量(#开头)

        返回:
            bool: 如果是依赖变量返回True
        """
        return self.get_type() == VAR_DEPENDENT

    def is_independent_variable(self) -> bool:
        """
        检查是否是独立变量($开头)

        返回:
            bool: 如果是独立变量返回True
        """
        return self.get_type() == VAR_INDEPENDENT

    def is_common(self) -> bool:
        """
        Check if this is a common variable

        Returns:
            bool: True if it's a common variable
        """
        n = self.name()
        return n and n[-1] == '$'

    def get_scope(self) -> Term:
        """
        Get the scope

        Returns:
            Term: The scope
        """
        return self.scope

    @staticmethod
    def compare(a: 'Variable', b: 'Variable') -> int:
        """
        Compare two variables

        Args:
            a: First variable
            b: Second variable

        Returns:
            int: Comparison result (-1, 0, 1)
        """
        i = (a.name() > b.name()) - (a.name() < b.name())
        if i == 0:
            a_scoped = a.scope != a
            b_scoped = b.scope != b

            if not a_scoped and not b_scoped:
                # If the two variables are each without scope, they are not equal.
                # So use their identityHashCode to determine a stable ordering
                a_s = id(a.scope)
                b_s = id(b.scope)
                return (a_s > b_s) - (a_s < b_s)
            elif a_scoped and not b_scoped:
                return -1
            elif b_scoped and not a_scoped:
                return 1
            else:
                return (a.get_scope().name() > b.get_scope().name()) - (a.get_scope().name() < b.get_scope().name())

        return i

    @staticmethod
    def valid_variable_type(c: str) -> bool:
        """
        Check if a character is a valid variable type

        Args:
            c: The character to check

        Returns:
            bool: True if it's a valid variable type
        """
        return c == VAR_QUERY or c == VAR_DEPENDENT or c == VAR_INDEPENDENT

    @staticmethod
    def get_name(type_char: str, index: int) -> str:
        """
        Get a variable name

        Args:
            type_char: The variable type
            index: The index

        Returns:
            str: The variable name
        """
        if index > Variable.MAX_CACHED_VARNAME_INDEXES:
            return Variable.new_name(type_char, index)

        if type_char == VAR_INDEPENDENT:
            cache = Variable.vn1
        elif type_char == VAR_DEPENDENT:
            cache = Variable.vn2
        elif type_char == VAR_QUERY:
            cache = Variable.vn3
        else:
            raise ValueError(f"Invalid variable type: {type_char}")

        c = cache[index]
        if c is None:
            c = Variable.new_name(type_char, index)
            cache[index] = c

        return c

    @staticmethod
    def new_name(type_char: str, index: int) -> str:
        """
        Create a new variable name

        Args:
            type_char: The variable type
            index: The index

        Returns:
            str: The variable name
        """
        digits = 3 if index >= 256 else (2 if index >= 16 else 1)
        result = type_char

        while index != 0:
            result += "0123456789abcdef"[index % 16]
            index //= 16

        return result

    def count_term_recursively(self, term_map=None) -> Dict[Term, int]:
        """
        Count terms recursively

        Args:
            term_map: The map to store counts

        Returns:
            Dict: The term count map
        """
        if term_map is None:
            term_map = {}

        # Don't count variables
        return term_map
