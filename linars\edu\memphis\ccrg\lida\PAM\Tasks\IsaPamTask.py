#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to add a PamLink and its sink to the percept.
"""

import logging
import traceback
from typing import Dict, Set, Optional

from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.PAM.PamNodeImpl import PamNodeImpl


class IsaPamTask(FrameworkTaskImpl):
    """
    A task to add a PamLink and its sink to the percept.

    See Also:
        ExcitationTask creates this task
        PropagationT<PERSON> creates this task
    """

    seq_ns = None  # NodeStructure
    pam_node_structure = None  # PamNodeStructure
    logger = logging.getLogger("IsaPamTask")

    def __init__(self, pn: Node, sink: Node, pam: PAMemory, pam_node_structure, seq_ns: NodeStructure, task_type: str):
        """
        Initialize an IsaPamTask.

        Args:
            pn: The pam node
            sink: The sink node
            pam: The PAMemory
            pam_node_structure: The PamNodeStructure
            seq_ns: The sequence NodeStructure
            task_type: The type of task
        """
        super().__init__(1, task_type)
        self.pam = pam
        self.sink = sink
        self.pn = pn
        IsaPamTask.pam_node_structure = pam_node_structure
        IsaPamTask.seq_ns = seq_ns

        # 记录初始化信息
        self.logger.debug(f"IsaPamTask初始化: pn={pn.get_node_name() if pn else 'None'}, "
                         f"sink={sink.get_node_name() if sink else 'None'}, "
                         f"task_type={task_type}")

    def run_this_framework_task(self):
        """
        Adds link's sink to the percept and tries to add the link as well then finishes.
        """
        try:
            self.logger.debug(f"开始执行IsaPamTask: pn={self.pn.get_node_name() if self.pn else 'None'}, "
                           f"sink={self.sink.get_node_name() if self.sink else 'None'}")

            # 检查必要的对象是否为None
            if self.sink is None:
                self.logger.error("IsaPamTask执行失败: sink为None")
                self.cancel()
                return

            if self.pn is None:
                self.logger.error("IsaPamTask执行失败: pn为None")
                self.cancel()
                return

            candidate_links = set()

            # 获取变量链接
            try:
                candidate_links = NeoUtil.get_some_links(self.sink, None, None, None, "变量")
                self.logger.debug(f"获取到变量链接数量: {len(candidate_links) if candidate_links else 0}")
            except Exception as e:
                self.logger.error(f"获取变量链接时出错: {str(e)}")
                self.logger.debug(traceback.format_exc())
                candidate_links = set()

            index0 = 1
            link_type = ""
            var_map = {}
            # 复杂表达式，如8+8*8，同一个对象多次运用，自指+回指+环指，
            # 目前先按最简单的情况，变量边类型唯一，非同对象自指，可有typemap等map
            for link in candidate_links:
                try:
                    if link is None or link.get_category() is None:
                        self.logger.warning(f"跳过无效链接: {link}")
                        continue

                    link_type = link.get_category().get_tn_name()
                    source = link.get_source()

                    if source is None:
                        self.logger.warning(f"链接的源节点为None: {link}")
                        continue

                    var_map[index0] = source
                    IsaPamTask.pam_node_structure.add_node(source, "PamNodeImpl")
                    self.logger.debug(f"处理变量链接: {link_type}, 源节点: {source.get_node_name() if source else 'None'}")
                    index0 += 1
                except Exception as e:
                    self.logger.error(f"处理变量链接时出错: {str(e)}")
                    self.logger.debug(traceback.format_exc())

            if not link_type:
                self.logger.warning("未找到有效的链接类型，任务结束")
                self.cancel()
                return

            self.logger.debug(f"使用链接类型: {link_type}, 变量映射数量: {len(var_map)}")

            index0 = 1
            node = None

            # 获取pn的链接
            try:
                link_set = NeoUtil.get_some_links(self.pn, link_type, None, None, None)
                self.logger.debug(f"获取到pn链接数量: {len(link_set) if link_set else 0}")
            except Exception as e:
                self.logger.error(f"获取pn链接时出错: {str(e)}")
                self.logger.debug(traceback.format_exc())
                link_set = set()

            for link0 in link_set:
                try:
                    if link0 is None or link0.get_source() is None:
                        self.logger.warning(f"跳过无效链接: {link0}")
                        continue

                    node = var_map.get(index0)
                    if node is None:
                        self.logger.warning(f"变量映射中找不到索引 {index0} 的节点")
                        continue

                    self.logger.debug(f"创建nowisa关系: {link0.get_source().get_node_name() if link0.get_source() else 'None'} -> "
                                   f"{node.get_node_name() if node else 'None'}")
                    IsaPamTask.make_nowisa(link0.get_source(), node)
                    index0 += 1
                except Exception as e:
                    self.logger.error(f"处理pn链接时出错: {str(e)}")
                    self.logger.debug(traceback.format_exc())

            # 某些框架可能没有动作，如纯表达式
            try:
                link_set1 = NeoUtil.get_some_links(self.sink, "动作", None, None, None)
                self.logger.debug(f"获取到动作链接数量: {len(link_set1) if link_set1 else 0}")
            except Exception as e:
                self.logger.error(f"获取动作链接时出错: {str(e)}")
                self.logger.debug(traceback.format_exc())
                link_set1 = set()

            for link1 in link_set1:
                try:
                    if link1 is None or link1.get_sink() is None or link1.get_source() is None:
                        self.logger.warning(f"跳过无效动作链接: {link1}")
                        continue

                    sink_name = link1.get_sink().get_tn_name() if link1.get_sink() else "None"
                    self.logger.debug(f"处理动作链接: {link1}, sink名称: {sink_name}")
                    self.pam.put_map(link1.get_sink(), sink_name)

                    # 就只加入无意识？
                    self.pam.get_listener().receive_percept(link1.get_sink(), ModuleName.NonGraph)
                    self.pam.get_listener().receive_percept(link1.get_source(), ModuleName.NonGraph)
                    self.pam.get_listener().receive_percept(link1, ModuleName.NonGraph)

                    # todo 不再pam激活，直接加激活度即可，需要激活新链接，则直接查到激活
                    # 实例化变量后，需要再激活一下原动作，原动作与当前实例可能有共同场景
                    # self.pam.propagate_activation_to_parents(link1.get_source(), 1, "varscene")

                    current_activation = link1.get_source().get_activation()
                    link1.get_source().set_activation(current_activation + 0.3)
                    self.logger.debug(f"增加源节点激活度: {current_activation} -> {current_activation + 0.3}")

                    # 实例化对象本身也再激活一次，避免传递链过长衰减完
                    # 如果没有动作，对象也不激活，多个动作=多次激活？多个对象，也多次激活
                    for link0 in link_set:
                        try:
                            if link0 is None or link0.get_sink() is None or link0.get_source() is None:
                                continue

                            # 就只加入无意识？
                            self.pam.get_listener().receive_percept(link0.get_sink(), ModuleName.NonGraph)
                            self.pam.get_listener().receive_percept(link0.get_source(), ModuleName.NonGraph)
                            self.pam.get_listener().receive_percept(link0, ModuleName.NonGraph)

                            # self.pam.propagate_activation_to_parents(link0.get_source(), 1, "varscene")
                            self.logger.debug(f"{link0} -- varscene_var--- --deep {1}")

                            current_activation = link0.get_source().get_activation()
                            link0.get_source().set_activation(current_activation + 0.3)
                            self.logger.debug(f"增加源节点激活度: {current_activation} -> {current_activation + 0.3}")
                        except Exception as e:
                            self.logger.error(f"处理link_set中的链接时出错: {str(e)}")
                            self.logger.debug(traceback.format_exc())

                    self.logger.debug("默认只处理一个动作，跳出循环")
                    break  # 默认只一个动作
                except Exception as e:
                    self.logger.error(f"处理动作链接时出错: {str(e)}")
                    self.logger.debug(traceback.format_exc())

            self.logger.debug("IsaPamTask执行完成")
            self.cancel()
        except Exception as e:
            self.logger.error(f"IsaPamTask执行过程中发生未处理的异常: {str(e)}")
            self.logger.debug(traceback.format_exc())
            self.cancel()

    @staticmethod
    def make_nowisa(var_type: Node, var_now: Node):
        """
        Make a nowisa link between var_type and var_now.

        Args:
            var_type: The variable type node
            var_now: The variable now node
        """
        logger = logging.getLogger("IsaPamTask")

        try:
            if var_type is None:
                logger.error("make_nowisa失败: var_type为None")
                return

            if var_now is None:
                logger.error("make_nowisa失败: var_now为None")
                return

            logger.debug(f"开始创建nowisa关系: var_type={var_type.get_node_name() if var_type else 'None'}, "
                       f"var_now={var_now.get_node_name() if var_now else 'None'}")

            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter

            # 检查AgentStarter.pam是否存在
            if not hasattr(AgentStarter, 'pam') or AgentStarter.pam is None:
                logger.error("make_nowisa失败: AgentStarter.pam为None")
                return

            IsaPamTask.pam_node_structure = AgentStarter.pam.get_pam_node_structure()
            if IsaPamTask.pam_node_structure is None:
                logger.error("make_nowisa失败: 无法获取pam_node_structure")
                return

            # 获取seq缓冲区
            seq_buffer = AgentStarter.pam.get_workspace_buffer("seq")
            if seq_buffer is None:
                logger.error("make_nowisa失败: 无法获取seq缓冲区")
                return

            IsaPamTask.seq_ns = seq_buffer.get_buffer_content(None)
            if IsaPamTask.seq_ns is None:
                logger.error("make_nowisa失败: 无法获取seq_ns")
                return

            # pn -- isa --sink, var_type -- nowisa --> var_now, 变量类型指向具体值
            IsaPamTask.pam_node_structure.add_node(var_now, "PamNodeImpl")
            IsaPamTask.pam_node_structure.add_node(var_type, "PamNodeImpl")

            category = PamNodeImpl()
            category.set_node_name("nowisa")

            # 两个都是source，在前，以实际方向为准，而不是查询语句先后
            now_link = IsaPamTask.pam_node_structure.add_default_link(var_type, var_now, category, 1.0, 0.0)
            if now_link is None:
                logger.error(f"创建nowisa链接失败: {var_type.get_node_name() if var_type else 'None'} -> {var_now.get_node_name() if var_now else 'None'}")
                return

            print(f"成功创建nowisa链接: {now_link}")

            # todo 加入无意识buffer？
            try:
                # 获取源节点的链接
                source_links = IsaPamTask.seq_ns.get_links_of_source(var_type.get_tn_name())  # var_type.get_node_id()
                logger.debug(f"获取到源节点的链接数量: {len(source_links) if source_links else 0}")

                # 如果已有变量值，全部覆盖，以后面的为准
                for ll in source_links:
                    if ll is None:
                        continue
                    logger.debug(f"移除现有链接: {ll}")
                    IsaPamTask.seq_ns.remove_link(ll)
            except Exception as e:
                logger.error(f"处理源节点链接时出错: {str(e)}")
                logger.error(traceback.format_exc())

            # 将新链接发送到感知器
            try:
                if AgentStarter.pam.get_listener() is None:
                    logger.error("make_nowisa失败: 无法获取PAM监听器")
                    return

                AgentStarter.pam.get_listener().receive_percept(now_link, ModuleName.SeqGraph)
                AgentStarter.pam.get_listener().receive_percept(now_link.get_source(), ModuleName.SeqGraph)
                AgentStarter.pam.get_listener().receive_percept(now_link.get_sink(), ModuleName.SeqGraph)
                logger.debug("成功将nowisa链接发送到感知器")
            except Exception as e:
                logger.error(f"发送感知时出错: {str(e)}")
                logger.error(traceback.format_exc())
        except Exception as e:
            logger.error(f"make_nowisa方法执行过程中发生未处理的异常: {str(e)}")
            logger.error(traceback.format_exc())
