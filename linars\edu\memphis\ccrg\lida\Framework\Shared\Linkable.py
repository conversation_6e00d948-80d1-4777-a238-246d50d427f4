# LIDA认知框架
"""
可以被附加链接的对象
"""

from abc import abstractmethod
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.ExtendedId import ExtendedId
from linars.edu.memphis.ccrg.lida.Framework.Initialization.Initializable import Initializable
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.Activatible import Activatible

class Linkable(Activatible, Initializable):
    """
    可以被附加链接的对象
    """

    @abstractmethod
    def get_label(self) -> str:
        """
        获取标签
        返回:
            可读的标签
        """
        pass

    @abstractmethod
    def get_extended_id(self) -> ExtendedId:
        """
        获取扩展ID

        返回:
            可链接对象的通用ID
        """
        pass

    @abstractmethod
    def get_factory_type(self) -> str:
        """
        获取工厂类型

        返回:
            可链接对象的工厂类型
        """
        pass

    @abstractmethod
    def set_factory_type(self, factory_type: str) -> None:
        """
        设置工厂类型

        参数:
            factory_type: 可链接对象的工厂类型
        """
        pass

    @abstractmethod
    def set_properties(self, properties: Dict[str, Any]) -> None:
        """
        设置属性

        参数:
            properties: 要设置的属性
        """
        pass

    @abstractmethod
    def get_properties(self) -> Dict[str, Any]:
        """
        获取属性

        返回:
            属性字典
        """
        pass

    @abstractmethod
    def get_property(self, key: str) -> Any:
        """
        获取单个属性

        参数:
            key: 属性键

        返回:
            属性值
        """
        pass

    @abstractmethod
    def is_node(self) -> bool:
        """
        返回此可链接对象是否为节点

        返回:
            如果是节点返回True，否则返回False
        """
        pass
