"""
时间戳用于跟踪已完成的推导
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar
import copy
import enum
import functools

# Temporal order constants
ORDER_NONE = 0
ORDER_FORWARD = 1
ORDER_CONCURRENT = 2
ORDER_BACKWARD = 3

# Symbols
STAMP_OPENER = '{'
STAMP_CLOSER = '}'
STAMP_STARTER = ':'
STAMP_SEPARATOR = ';'
TENSE_PAST = ":\\:"
TENSE_PRESENT = ":|:"
TENSE_FUTURE = ":/:"

class Tense(enum.Enum):
    """时态枚举"""
    Past = 0
    Present = 1
    Future = 2
    Eternal = 3

class BaseEntry:
    """
    时间戳证据基础的元素
    """

    def __init__(self, nar_id: int, input_id: int):
        """
        证据基础条目

        参数:
            nar_id: 获取输入证据的NAR ID
            input_id: 特定于NAR的输入ID
        """
        self.nar_id = nar_id
        self.input_id = input_id

    def get_nar_id(self) -> int:
        """
        获取NAR ID

        返回:
            int: NAR ID
        """
        return self.nar_id

    def get_input_id(self) -> int:
        """
        获取输入ID

        返回:
            int: 输入ID
        """
        return self.input_id

    def __str__(self) -> str:
        """
        字符串表示

        返回:
            str: 字符串表示
        """
        return f"({self.nar_id},{self.input_id})"

    def __eq__(self, other: Any) -> bool:
        """
        检查相等性

        参数:
            other: 要比较的对象

        返回:
            bool: 如果相等则为True
        """
        if other is self:
            return True

        if not isinstance(other, BaseEntry):
            return False

        return other.input_id == self.input_id and other.nar_id == self.nar_id

    def __hash__(self) -> int:
        """
        哈希码

        返回:
            int: 哈希码
        """
        prime = 31
        result = 1
        result = prime * result + hash(self.nar_id)
        result = prime * result + hash(self.input_id)
        return result

    def __lt__(self, other: 'BaseEntry') -> bool:
        """
        小于比较

        参数:
            other: 要比较的对象

        返回:
            bool: 如果小于则为True
        """
        if self.nar_id != other.nar_id:
            return self.nar_id < other.nar_id
        return self.input_id < other.input_id

class Stamp:
    """
    时间戳用于跟踪已完成的推导
    """

    # Default for atemporal events means "always" in Judgment/Question, but "current" in Goal/Quest
    ETERNAL = -2147483648  # Integer.MIN_VALUE

    def __init__(self, time: int = None, tense: Tense = None, serial: BaseEntry = None, duration: int = None):
        """
        构造函数

        参数:
            time: 时间戳的创建时间
            tense: 时间戳的时态
            serial: 序列号
            duration: 持续时间
        """
        # Serial numbers
        self.evidential_base: List[BaseEntry] = []
        self.base_length = 0

        # Creation time of the stamp
        self.creation_time = -1

        # Estimated occurrence time of the event
        self.occurrence_time = self.ETERNAL

        # When was put into buffer
        self.put_in_time = -1

        # Tense of the item
        self.tense = tense

        # Is it a neg confirmation task that was already checked
        self.already_anticipated_neg_confirmation = False

        # Caches
        self.name_cache = None
        self.evidential_set = None
        self._evidential_hash = 0

        if serial is not None:
            self.base_length = 1
            self.evidential_base = [serial]
            self.tense = tense

            if time is not None and duration is not None:
                self.set_creation_time(time, duration)

    @classmethod
    def from_stamp(cls, old: 'Stamp') -> 'Stamp':
        """
        生成与给定时间戳相同的新时间戳

        参数:
            old: 要克隆的时间戳

        返回:
            Stamp: 新的时间戳
        """
        return cls.from_stamp_with_time(old, old.creation_time)

    @classmethod
    def from_stamp_with_time(cls, old: 'Stamp', creation_time: int, use_evidential_base: 'Stamp' = None) -> 'Stamp':
        """
        从现有时间戳生成新时间戳，具有相同的证据基础但不同的创建时间

        参数:
            old: 单个前提的时间戳
            creation_time: 当前时间
            use_evidential_base: 用于证据基础的时间戳

        返回:
            Stamp: 新的时间戳
        """
        stamp = cls()
        use_base = use_evidential_base if use_evidential_base else old

        stamp.evidential_base = use_base.evidential_base
        stamp.base_length = use_base.base_length
        stamp.creation_time = creation_time
        stamp.occurrence_time = old.get_occurrence_time()
        stamp.tense = old.tense

        return stamp

    @classmethod
    def from_stamps(cls, first: 'Stamp', second: 'Stamp', time: int, narParameters) -> 'Stamp':
        """
        通过合并父级时间戳为派生句子生成新时间戳

        参数:
            first: 第一个时间戳
            second: 第二个时间戳
            time: 当前时间
            narParameters: NAR参数

        返回:
            Stamp: 新的时间戳
        """
        stamp = cls()

        i1 = i2 = j = 0
        stamp.base_length = min(first.base_length + second.base_length, narParameters.MAXIMUM_EVIDENTAL_BASE_LENGTH)
        stamp.evidential_base = [None] * stamp.base_length

        first_base = first.evidential_base
        second_base = second.evidential_base
        first_length = len(first_base)
        second_length = len(second_base)

        stamp.creation_time = time
        stamp.occurrence_time = first.get_occurrence_time()  # Use the occurrence of task
        stamp.tense = first.tense

        while j < stamp.base_length:
            if i2 < second_length:
                stamp.evidential_base[j] = second_base[i2]
                j += 1
                i2 += 1

            if i1 < first_length:
                stamp.evidential_base[j] = first_base[i1]
                j += 1
                i1 += 1

        return stamp

    @classmethod
    def from_timable(cls, time, memory, tense: Tense = Tense.Present) -> 'Stamp':
        """
        从可定时对象创建时间戳

        参数:
            time: 可定时对象
            memory: 内存
            tense: 时态

        返回:
            Stamp: 新的时间戳
        """
        return cls(time.time(), tense, memory.new_stamp_serial(), memory.narParameters.DURATION)

    @staticmethod
    def base_overlap(a: 'Stamp', b: 'Stamp') -> bool:
        """
        检测证据基础重叠

        参数:
            a: 第一个时间戳
            b: 第二个时间戳

        返回:
            bool: 如果有重叠则为True
        """
        base1 = a.evidential_base
        base2 = b.evidential_base

        task_base = set()
        for base_entry in base1:
            if base_entry in task_base:  # Can have an overlap in itself already
                return True
            task_base.add(base_entry)

        for base_entry in base2:
            if base_entry in task_base:
                return True
            task_base.add(base_entry)  # Also add to detect collision with itself

        return False

    def evidence_is_cyclic(self) -> bool:
        """
        检查证据是否循环

        返回:
            bool: 如果循环则为True
        """
        task_base = set()
        for base_entry in self.evidential_base:
            if base_entry in task_base:  # Can have an overlap in itself already
                return True
            task_base.add(base_entry)

        return False

    def is_eternal(self) -> bool:
        """
        检查时间戳是否为永恒

        返回:
            bool: 如果永恒则为True
        """
        eternal_occurrence = self.occurrence_time == self.ETERNAL

        # 详细调试模式下，如果时间戳的时态与永恒性不一致，则输出警告
        # if eternal_occurrence and self.tense != Tense.Eternal:
            # import logging
            # logging.getLogger(__name__).warning(f"Stamp has inconsistent tense and eternal occurrenceTime: tense={self.tense}")

        return eternal_occurrence

    def set_creation_time(self, time: int, duration: int):
        """
        设置创建时间

        参数:
            time: 创建时间
            duration: 持续时间
        """
        self.creation_time = time

        if self.tense is None:
            self.occurrence_time = self.ETERNAL
        elif self.tense == Tense.Past:
            self.occurrence_time = time - duration
        elif self.tense == Tense.Future:
            self.occurrence_time = time + duration
        elif self.tense == Tense.Present:
            self.occurrence_time = time
        else:
            self.occurrence_time = time

    def clone(self) -> 'Stamp':
        """
        克隆时间戳

        返回:
            Stamp: 克隆的时间戳
        """
        return Stamp.from_stamp(self)

    @staticmethod
    def to_set_array(x: List[BaseEntry]) -> List[BaseEntry]:
        """
        将数组转换为集合数组

        参数:
            x: 要转换的数组

        返回:
            List[BaseEntry]: 集合数组
        """
        if len(x) < 2:
            return x.copy()

        # 1. Copy evidentialBase
        # 2. Sort
        # 3. Count duplicates
        # 4. Create new array

        set_array = sorted(x)
        last_value = None
        j = 0  # # of unique items

        for v in set_array:
            if last_value is None or last_value != v:
                j += 1
            last_value = v

        last_value = None
        sorted_array = [None] * j
        j = 0

        for v in set_array:
            if last_value is None or last_value != v:
                sorted_array[j] = v
                j += 1
            last_value = v

        return sorted_array

    def to_set(self) -> List[BaseEntry]:
        """
        将证据基础转换为集合

        返回:
            List[BaseEntry]: 证据基础的集合表示
        """
        if self.evidential_set is None:
            self.evidential_set = self.to_set_array(self.evidential_base)
            self._evidential_hash = hash(tuple(self.evidential_set))

        return self.evidential_set

    def equals(self, s: 'Stamp', creation_time: bool, occurrence_time: bool, evidential_base: bool) -> bool:
        """
        检查两个时间戳是否包含相同类型的内容
        参数:
            s: 要比较的时间戳
            creation_time: 是否比较创建时间
            occurrence_time: 是否比较发生时间
            evidential_base: 是否比较证据基础
        返回:
            bool: 两者是否包含相同的证据基础
        """
        if self is s:
            return True
        if creation_time:
            if self.get_creation_time() != s.get_creation_time():
                return False
        if occurrence_time:
            if self.get_occurrence_time() != s.get_occurrence_time():
                return False
        if evidential_base:
            if self.evidential_hash() != s.evidential_hash():
                return False
            return self.to_set() == s.to_set()
        return True

    def evidential_hash(self) -> int:
        """
        时间戳的哈希码

        返回:
            int: 哈希码
        """
        if self.evidential_set is None:
            self.to_set()
        return self._evidential_hash

    def clone_with_new_occurrence_time(self, new_occurrence_time: int) -> 'Stamp':
        """
        使用新的发生时间克隆

        参数:
            new_occurrence_time: 新的发生时间

        返回:
            Stamp: 克隆的时间戳
        """
        s = self.clone()
        if new_occurrence_time == self.ETERNAL:
            s.tense = Tense.Eternal
        s.set_occurrence_time(new_occurrence_time)
        return s

    def get_occurrence_time(self) -> int:
        """
        获取真值的发生时间

        返回:
            int: 发生时间
        """
        return self.occurrence_time

    def set_eternal(self):
        """
        将真值的发生时间设置为永恒
        """
        self.occurrence_time = self.ETERNAL
        self.tense = Tense.Eternal

    def append_occurrence_time(self, sb: str) -> str:
        """
        将发生时间附加到字符串

        参数:
            sb: 要附加到的字符串

        返回:
            str: 附加后的字符串
        """
        if self.occurrence_time != self.ETERNAL:
            # 确保字符串有足够的容量，在Python中不需要显式操作
            return f"{sb}[{self.occurrence_time}]"
        return sb

    def get_occurrence_time_string(self) -> str:
        """
        获取真值的发生时间字符串

        返回:
            str: 发生时间字符串
        """
        if self.is_eternal():
            return ""
        else:
            return self.append_occurrence_time("")

    def get_tense(self, current_time: int, duration: int) -> str:
        """
        获取时态

        参数:
            current_time: 当前时间
            duration: 持续时间

        返回:
            str: 时态字符串
        """
        if self.is_eternal():
            return ""

        order_result = self.order(current_time, self.occurrence_time, duration)

        if order_result == ORDER_FORWARD:
            return TENSE_FUTURE
        elif order_result == ORDER_BACKWARD:
            return TENSE_PAST
        else:
            return TENSE_PRESENT

    @staticmethod
    def order(given_time: int, event_time: int, duration: int) -> int:
        """
        确定两个事件之间的时间顺序

        参数:
            given_time: 基准时间
            event_time: 事件时间
            duration: 持续时间

        返回:
            int: 时间顺序
        """
        if event_time == Stamp.ETERNAL or given_time == Stamp.ETERNAL:
            return ORDER_NONE

        d1 = event_time - given_time
        positive_duration = abs(duration)

        if d1 >= positive_duration:
            return ORDER_FORWARD
        elif d1 <= -positive_duration:
            return ORDER_BACKWARD
        else:
            return ORDER_CONCURRENT

    def set_occurrence_time(self, time: int):
        """
        设置发生时间

        参数:
            time: 新的发生时间
        """
        if self.occurrence_time != time:
            self.occurrence_time = time

            if time == self.ETERNAL:
                self.tense = Tense.Eternal

            self.name_cache = None

    def name(self) -> str:
        """
        获取时间戳的名称

        返回:
            str: 名称
        """
        if self.name_cache is None:
            # 估计初始大小为 10 * base_length
            buffer = f"{STAMP_OPENER}{self.get_creation_time()}"

            if not self.is_eternal():
                buffer += f"|{self.occurrence_time}"

            buffer += f" {STAMP_STARTER} "

            for i in range(self.base_length):
                buffer += str(self.evidential_base[i])
                if i < (self.base_length - 1):
                    buffer += STAMP_SEPARATOR

            buffer += f"{STAMP_CLOSER} "

            self.name_cache = buffer

        return self.name_cache

    def __str__(self) -> str:
        """
        字符串表示

        返回:
            str: 字符串表示
        """
        return self.name()

    def get_creation_time(self) -> int:
        """
        获取创建时间

        返回:
            int: 创建时间
        """
        return self.creation_time

    def before(self, s: 'Stamp', duration: int) -> bool:
        """
        检查此时间戳是否在另一个时间戳之前

        参数:
            s: 另一个时间戳
            duration: 持续时间

        返回:
            bool: 如果在之前则为True
        """
        if self.is_eternal() or s.is_eternal():
            return False
        return self.order(s.occurrence_time, self.occurrence_time, duration) == ORDER_BACKWARD

    def after(self, s: 'Stamp', duration: int) -> bool:
        """
        检查此时间戳是否在另一个时间戳之后

        参数:
            s: 另一个时间戳
            duration: 持续时间

        返回:
            bool: 如果在之后则为True
        """
        if self.is_eternal() or s.is_eternal():
            return False
        return self.order(s.occurrence_time, self.occurrence_time, duration) == ORDER_FORWARD

    def get_originality(self) -> float:
        """
        获取原创性

        返回:
            float: 原创性
        """
        return 1.0 / (len(self.evidential_base) + 1)

    def get_put_in_time(self) -> int:
        """
        获取放入时间

        返回:
            int: 放入时间
        """
        return self.put_in_time

    def __sub__(self, other):
        """
        实现减法操作符，用于比较两个时间戳的时间差异

        参数:
            other: 另一个时间戳或整数

        返回:
            int: 时间差异
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 如果 other 是整数，直接返回时间差异
            if isinstance(other, int):
                if self.is_eternal():
                    return 100000  # 一个大的数字表示无法比较
                return self.occurrence_time - other

            # 如果 other 不是 Stamp 对象，返回0
            if not isinstance(other, Stamp):
                logger.warning(f"Attempted to subtract {type(other)} from Stamp")
                return 0

            # 如果两个时间戳都是永恒的，返回0
            if self.is_eternal() and other.is_eternal():
                return 0

            # 如果其中一个是永恒的，返回一个大的数字
            if self.is_eternal() or other.is_eternal():
                return 100000  # 一个大的数字表示无法比较

            # 返回两个时间戳的发生时间差异
            return self.occurrence_time - other.occurrence_time
        except Exception as e:
            logger.error(f"Error in Stamp.__sub__: {e}")
            return 0  # 默认返回0

    def __rsub__(self, other):
        """
        实现反向减法操作符，用于处理 int - Stamp 的情况

        参数:
            other: 整数或其他对象

        返回:
            int: 时间差异
        """
        import logging
        logger = logging.getLogger(__name__)

        try:
            # 如果 other 是整数，直接返回时间差异
            if isinstance(other, int):
                if self.is_eternal():
                    return 100000  # 一个大的数字表示无法比较
                return other - self.occurrence_time
            else:
                logger.warning(f"Attempted to subtract Stamp from {type(other)}")
                return 0
        except Exception as e:
            logger.error(f"Error in Stamp.__rsub__: {e}")
            return 0  # 默认返回0

    def set_put_in_time(self, time: int):
        """
        设置放入时间

        参数:
            time: 放入时间
        """
        self.put_in_time = time
