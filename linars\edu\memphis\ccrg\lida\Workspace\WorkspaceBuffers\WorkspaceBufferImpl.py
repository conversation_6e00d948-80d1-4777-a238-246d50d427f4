# LIDA Cognitive Framework
"""
Default implementation of WorkspaceBuffer.
"""

import logging
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WorkspaceBuffer import WorkspaceBuffer
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceContent import WorkspaceContent

class WorkspaceBufferImpl(FrameworkModuleImpl, WorkspaceBuffer):
    """
    Default implementation of WorkspaceBuffer.
    """
    
    def __init__(self):
        """
        Initialize a WorkspaceBufferImpl.
        """
        super().__init__()
        self.buffer_content = NodeStructureImpl()
        self.is_decay = True
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def init(self) -> None:
        """
        Initialize this WorkspaceBufferImpl.
        """
        self.is_decay = self.get_param("workspace.bufferDecay", True)
    
    def get_param(self, name: str, default_value: Any) -> Any:
        """
        Get a parameter value with a default.
        
        Args:
            name: The name of the parameter
            default_value: The default value
            
        Returns:
            The parameter value or the default value
        """
        parameters = getattr(self, "parameters", {})
        if parameters and name in parameters:
            return parameters[name]
        return default_value
    
    def get_buffer_content(self, params: Optional[Dict[str, Any]] = None) -> WorkspaceContent:
        """
        Gets buffer content based on specified parameters.
        
        Args:
            params: Optional parameters to specify what content is returned
            
        Returns:
            The WorkspaceContent
        """
        return self.buffer_content
    
    def add_buffer_content(self, content: WorkspaceContent) -> None:
        """
        Adds specified content to this workspace buffer.
        
        Args:
            content: The WorkspaceContent to add
        """
        self.buffer_content.merge_with(content)
    
    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.
        
        Args:
            ticks: The number of ticks to decay by
        """
        if self.is_decay:
            self.logger.debug(f"Decaying buffer at tick {TaskManager.get_current_tick()}")
            self.buffer_content.decay_node_structure(ticks)
    
    def get_module_content(self, *params: Any) -> WorkspaceContent:
        """
        Get the content of this module.
        
        Args:
            params: Parameters specifying what content to return
            
        Returns:
            The content of this module
        """
        return self.buffer_content
    
    def learn(self, coalition: Coalition) -> None:
        """
        Learn from a coalition.
        
        Args:
            coalition: The coalition to learn from
        """
        # Not applicable
        pass
