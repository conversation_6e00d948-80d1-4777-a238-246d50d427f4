#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A process which detects a pattern (feature) in SensoryMemory content and excites PamNodes representing that pattern.
"""

from abc import ABC, abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTask import FrameworkTask
from linars.edu.memphis.ccrg.lida.PAM.PamLinkable import PamLinkable

class DetectionAlgorithm(FrameworkTask, ABC):
    """
    A process which detects a pattern (feature) in SensoryMemory content and excites PamNodes representing that pattern.
    
    See Also:
        BasicDetectionAlgorithm
    """
    
    @abstractmethod
    def detect(self) -> float:
        """
        Detects a feature.
        
        Returns:
            Value from 0.0 to 1.0 representing the degree to which the feature occurs.
        """
        pass
        
    @abstractmethod
    def get_pam_linkable(self) -> PamLinkable:
        """
        Returns PamLinkable this algorithm can detect.
        
        Returns:
            The pam nodes
        """
        pass
        
    @abstractmethod
    def set_pam_linkable(self, linkable: PamLinkable) -> None:
        """
        Adds PamLinkable that will be detected by this algorithm.
        
        Args:
            linkable: A PamLinkable
        """
        pass
