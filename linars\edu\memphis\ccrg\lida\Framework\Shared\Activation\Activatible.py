# LIDA认知框架
"""
可被激活对象的接口
"""

from abc import ABC, abstractmethod
from typing import Optional
from linars.edu.memphis.ccrg.lida.Framework.Strategies.ExciteStrategy import ExciteStrategy
from linars.edu.memphis.ccrg.lida.Framework.Strategies.DecayStrategy import DecayStrategy

class Activatible(ABC):
    """
    可被激活对象的接口
    """

    @abstractmethod
    def get_activation(self) -> float:
        """
        获取此对象的激活值
        返回:
            此对象的激活值
        """
        pass

    @abstractmethod
    def set_activation(self, activation: float) -> None:
        """
        设置此对象的激活值
        参数:
            activation: 要设置的激活值
        """
        pass

    @abstractmethod
    def get_activation_threshold(self) -> float:
        """
        获取此对象的激活阈值

        返回:
            此对象的激活阈值
        """
        pass

    @abstractmethod
    def set_activation_threshold(self, threshold: float) -> None:
        """
        设置此对象的激活阈值

        参数:
            threshold: 要设置的激活阈值
        """
        pass

    @abstractmethod
    def get_removal_threshold(self) -> float:
        """
        获取此对象的移除阈值

        返回:
            此对象的移除阈值
        """
        pass

    @abstractmethod
    def set_removal_threshold(self, threshold: float) -> None:
        """
        设置此对象的移除阈值

        参数:
            threshold: 要设置的移除阈值
        """
        pass

    @abstractmethod
    def is_above_activation_threshold(self) -> bool:
        """
        检查此对象是否超过其激活阈值

        返回:
            如果超过激活阈值返回True，否则返回False
        """
        pass

    @abstractmethod
    def is_above_removal_threshold(self) -> bool:
        """
        检查此对象是否超过其移除阈值

        返回:
            如果超过移除阈值返回True，否则返回False
        """
        pass

    @abstractmethod
    def get_base_level_activation(self) -> float:
        """
        获取此对象的基础激活水平

        返回:
            此对象的基础激活水平
        """
        pass

    @abstractmethod
    def set_base_level_activation(self, activation: float) -> None:
        """
        设置此对象的基础激活水平

        参数:
            activation: 要设置的基础激活水平
        """
        pass

    @abstractmethod
    def get_excite_strategy(self) -> ExciteStrategy:
        """
        获取此对象的激励策略

        返回:
            此对象的激励策略
        """
        pass

    @abstractmethod
    def set_excite_strategy(self, strategy: ExciteStrategy) -> None:
        """
        设置此对象的激励策略

        参数:
            strategy: 要设置的激励策略
        """
        pass

    @abstractmethod
    def get_decay_strategy(self) -> DecayStrategy:
        """
        获取此对象的衰减策略

        返回:
            此对象的衰减策略
        """
        pass

    @abstractmethod
    def set_decay_strategy(self, strategy: DecayStrategy) -> None:
        """
        设置此对象的衰减策略

        参数:
            strategy: 要设置的衰减策略
        """
        pass

    @abstractmethod
    def excite(self, amount: float) -> None:
        """
        按给定值激励此对象

        参数:
            amount: 激励值
        """
        pass

    @abstractmethod
    def decay(self, ticks: int) -> None:
        """
        按给定的ticks数衰减此对象

        参数:
            ticks: 衰减的ticks数
        """
        pass

    @abstractmethod
    def total_activation(self) -> float:
        """
        获取此对象的总激活值

        返回:
            此对象的总激活值
        """
        pass
