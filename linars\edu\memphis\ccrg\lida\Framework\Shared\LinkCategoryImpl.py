# LIDA认知框架
"""
链接类别的默认实现
"""

from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategory import LinkCategory
from linars.edu.memphis.ccrg.lida.Framework.Initialization.Initializable import Initializable
from linars.edu.memphis.ccrg.lida.Framework.Initialization.InitializableImpl import InitializableImpl

class LinkCategoryImpl(InitializableImpl, LinkCategory):
    """
    链接类别的默认实现
    """

    # Class variable for ID generator
    _id_generator = 0

    def __init__(self, label: str = None):
        """
        初始化链接类别实现

        参数:
            label: 类别标签
        """
        super().__init__()
        self.category_id = LinkCategoryImpl._id_generator
        LinkCategoryImpl._id_generator += 1
        self.label = label

    def get_id(self) -> int:
        """
        获取类别ID

        返回:
            类别ID
        """
        return self.category_id

    def set_id(self, id: int) -> None:
        """
        设置类别ID

        参数:
            id: 要设置的ID
        """
        self.category_id = id

    def get_label(self) -> str:
        """
        获取类别标签

        返回:
            类别标签
        """
        return self.label

    def set_label(self, label: str) -> None:
        """
        设置类别标签

        参数:
            label: 要设置的标签
        """
        self.label = label

    def init(self, params: Optional[Dict[str, Any]] = None) -> None:
        """
        使用给定参数初始化此类别

        参数:
            params: 初始化参数
        """
        super().init(params)
        if params is not None:
            if "id" in params:
                self.category_id = int(params["id"])
            if "label" in params:
                self.label = params["label"]

    def __eq__(self, other) -> bool:
        """
        检查此类别是否与另一个相等

        参数:
            other: 要比较的另一个类别

        返回:
            如果类别具有相同的ID则返回True，否则返回False
        """
        if isinstance(other, LinkCategory):
            return self.category_id == other.get_id()
        return False

    def __hash__(self) -> int:
        """
        返回此类别的哈希码

        返回:
            类别ID的哈希码
        """
        return self.category_id

    def __str__(self) -> str:
        """
        返回此类别的字符串表示

        返回:
            类别标签
        """
        return self.label
