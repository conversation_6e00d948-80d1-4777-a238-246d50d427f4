# LIDA Cognitive Framework
"""
This class implements module of WorkspaceBuffer. WorkspaceBuffer is a submodule of workspace and 
it contains nodeStructures. Also this class maintains activation lower bound of its nodeStructures.
"""

import logging
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.UnmodifiableNodeStructureImpl import UnmodifiableNodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WorkspaceBuffer import WorkspaceBuffer
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceContent import WorkspaceContent
from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter

# Try to import Memory if available
try:
    from linars.edu.memphis.ccrg.linars.memory import Memory
    MEMORY_AVAILABLE = True
except ImportError:
    MEMORY_AVAILABLE = False
    from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl

class WMBufferImpl_mem(FrameworkModuleImpl, WorkspaceBuffer):
    """
    This class implements module of WorkspaceBuffer. WorkspaceBuffer is a submodule of workspace and 
    it contains nodeStructures. Also this class maintains activation lower bound of its nodeStructures.
    """
    
    def __init__(self):
        """
        Initialize a WMBufferImpl_mem.
        """
        super().__init__()
        # Create buffer based on availability of Memory class
        if MEMORY_AVAILABLE:
            self.nar = AgentStarter.nar
            self.buffer = self.nar.init_mem(Memory())
        else:
            self.buffer = NodeStructureImpl()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def add_buffer_content(self, content: WorkspaceContent) -> None:
        """
        Adds specified content to this workspace buffer.
        Note that this method merges the specified content into the buffer.
        
        Args:
            content: The WorkspaceContent to add
        """
        self.buffer.merge_with(content)
    
    def get_buffer_content(self, params: Optional[Dict[str, Any]] = None) -> WorkspaceContent:
        """
        Gets buffer content based on specified parameters.
        
        Args:
            params: Optional parameters to specify what content is returned
            
        Returns:
            The WorkspaceContent
        """
        return self.buffer
    
    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.
        
        Args:
            ticks: The number of ticks to decay by
        """
        self.logger.debug(f"Decaying buffer at tick {TaskManager.get_current_tick()}")
        self.buffer.decay_node_structure(ticks)
    
    def get_module_content(self, *params: Any) -> WorkspaceContent:
        """
        Get the content of this module.
        
        Args:
            params: Parameters specifying what content to return
            
        Returns:
            The content of this module
        """
        return UnmodifiableNodeStructureImpl(self.buffer)
