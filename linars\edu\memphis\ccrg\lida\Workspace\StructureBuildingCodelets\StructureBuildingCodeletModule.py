# LIDA Cognitive Framework
"""
A module which maintains Codelets of workspace. This module manages Codelets
and responds for sending events to framework GUI.
"""

import logging
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import Module<PERSON>ame
from linars.edu.memphis.ccrg.lida.Framework.Tasks.Codelet import Codelet
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Workspace.Workspace import Workspace
from linars.edu.memphis.ccrg.lida.Workspace.StructureBuildingCodelets.StructureBuildingCodelet import StructureBuildingCodelet
from linars.edu.memphis.ccrg.lida.Workspace.StructureBuildingCodelets.BasicStructureBuildingCodelet import BasicStructureBuildingCodelet

class StructureBuildingCodeletModule(FrameworkModuleImpl):
    """
    A module which maintains Codelets of workspace. This module manages Codelets
    and responds for sending events to framework GUI.
    """

    # Default values
    DEFAULT_CODELET_ACTIVATION = 1.0
    DEFAULT_CODELET_REMOVAL_THRESHOLD = -1.0
    DEFAULT_CODELET_TYPE = "BasicStructureBuildingCodelet"

    def __init__(self):
        """
        Initialize a StructureBuildingCodeletModule.
        """
        super().__init__()
        self.codelet_activation = self.DEFAULT_CODELET_ACTIVATION
        self.codelet_removal_threshold = self.DEFAULT_CODELET_REMOVAL_THRESHOLD
        self.default_codelet_type = self.DEFAULT_CODELET_TYPE
        self.modules_map: Dict[ModuleName, FrameworkModule] = {}
        self.logger = logging.getLogger(self.__class__.__name__)

    def init(self, params=None) -> None:
        """
        Initialize this module.
        Will set parameters with the following names:

        sbcModule.defaultCodeletType: type of attention codelets obtained from this module
        sbcModule.codeletActivation: initial activation of codelets obtained from this module
        sbcModule.codeletRemovalThreshold: initial removal threshold for codelets obtained from this module

        Args:
            params: Parameters for initialization, defaults to None
        """
        super().init(params)
        self.default_codelet_type = self.get_param("sbcModule.defaultCodeletType", self.DEFAULT_CODELET_TYPE)
        self.codelet_activation = self.get_param("sbcModule.codeletActivation", self.DEFAULT_CODELET_ACTIVATION)
        self.codelet_removal_threshold = self.get_param("sbcModule.codeletRemovalThreshold", self.DEFAULT_CODELET_REMOVAL_THRESHOLD)

    def get_param(self, name: str, default_value: Any) -> Any:
        """
        Get a parameter value with a default.

        Args:
            name: The name of the parameter
            default_value: The default value

        Returns:
            The parameter value or the default value
        """
        parameters = getattr(self, "parameters", {})
        if parameters and name in parameters:
            return parameters[name]
        return default_value

    def set_default_codelet_type(self, type_name: str) -> None:
        """
        Set the default codelet type.

        Args:
            type_name: The default codelet type
        """
        # In a real implementation, this would check if the factory contains the type
        # For now, we'll just set the default codelet type
        self.default_codelet_type = type_name

    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module for this module.

        Args:
            module: The module to associate with this module
            module_usage: How this module will use the module
        """
        if isinstance(module, Workspace):
            self.modules_map[module.get_module_name()] = module
            submodules = module.get_submodules()
            if submodules:
                self.modules_map.update(submodules)
            else:
                self.logger.warning(f"Cannot add submodules at tick {TaskManager.get_current_tick()}")
        else:
            self.logger.warning(f"Cannot associate module {module} at tick {TaskManager.get_current_tick()}")

    def get_default_codelet(self) -> Optional[StructureBuildingCodelet]:
        """
        Get a default structure building codelet.

        Returns:
            A default structure building codelet
        """
        return self.get_codelet(self.default_codelet_type, None)

    def get_default_codelet_with_params(self, params: Dict[str, Any]) -> Optional[StructureBuildingCodelet]:
        """
        Get a default structure building codelet with the given parameters.

        Args:
            params: Parameters for the codelet

        Returns:
            A default structure building codelet with the given parameters
        """
        return self.get_codelet(self.default_codelet_type, params)

    def get_codelet(self, type_name: str, params: Optional[Dict[str, Any]] = None) -> Optional[StructureBuildingCodelet]:
        """
        Get a structure building codelet of the specified type.

        Args:
            type_name: The type of the codelet
            params: Parameters for the codelet

        Returns:
            A structure building codelet of the specified type
        """
        # In a real implementation, this would use a factory to get the codelet
        # For now, we'll just return a BasicStructureBuildingCodelet
        if type_name == "BasicStructureBuildingCodelet":
            codelet = BasicStructureBuildingCodelet()
            if params is not None:
                codelet.init(params)
            codelet.set_activation(self.codelet_activation)
            codelet.set_removal_threshold(self.codelet_removal_threshold)
            return codelet
        else:
            self.logger.warning(f"Codelet type not supported: {type_name} at tick {TaskManager.get_current_tick()}")
            return None

    def add_codelet(self, codelet: Codelet) -> None:
        """
        Add a codelet to this module.

        Args:
            codelet: The codelet to add
        """
        if isinstance(codelet, StructureBuildingCodelet):
            self.logger.debug(f"New codelet {codelet} spawned at tick {TaskManager.get_current_tick()}")
            self.get_assisting_task_spawner().add_task(codelet)
        else:
            self.logger.warning(f"Codelet must be a structure-building codelet at tick {TaskManager.get_current_tick()}")

    def get_module_content(self, *params: Any) -> Any:
        """
        Get the content of this module.

        Args:
            params: Parameters specifying what content to return

        Returns:
            The content of this module
        """
        return None

    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.

        Args:
            ticks: The number of ticks to decay by
        """
        # Not applicable
        pass
