#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
LanListener interface for the LIDA framework.
"""

from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure

class LanListener(ModuleListener):
    """
    A LanListener receives percepts from PAMemory asynchronously.
    """

    def receive_percept(self, ns: NodeStructure) -> None:
        """
        Receive a NodeStructure percept.

        Args:
            ns: A NodeStructure
        """
        pass

    def receive_percept(self, n: Node) -> None:
        """
        Receive a Node percept.

        Args:
            n: A Node
        """
        pass

    def receive_percept(self, n: Node, name: ModuleName) -> None:
        """
        Receive a Node percept with a module name.

        Args:
            n: A Node
            name: The name of the module that sent the percept
        """
        pass

    def receive_percept(self, l: Link) -> None:
        """
        Receive a Link percept.

        Args:
            l: A Link
        """
        pass

    def receive_percept(self, l: Link, name: ModuleName) -> None:
        """
        Receive a Link percept with a module name.

        Args:
            l: A Link
            name: The name of the module that sent the percept
        """
        pass
