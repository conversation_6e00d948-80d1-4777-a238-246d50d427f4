"""
Concept as defined by the NARS-theory

Concepts are used to keep track of interrelated sentences
"""
from typing import Dict, List, Optional, Set, Any, TYPE_CHECKING
import copy
import threading

from linars.org.opennars.entity.item import Item
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.entity.budget_value import BudgetValue
from linars.org.opennars.inference.budget_functions import BudgetFunctions
from linars.org.opennars.storage.bag1 import Bag1
from linars.org.opennars.entity.term_link import TermLink
from linars.org.opennars.entity.task_link import TaskLink

# 使用TYPE_CHECKING来避免循环导入
# if TYPE_CHECKING:
#     from linars.edu.memphis.ccrg.linars.memory import Memory

class Concept(Item[Term]):
    """
    Concept as defined by the NARS-theory
    """

    def __init__(self, budget: Optional[BudgetValue] = None, term: Optional[Term] = None, memory=None):
        """
        Constructor
        Args:
            budget: The budget value
            term: The term
            memory: Reference to the memory
        """
        super().__init__(budget)
        self.term = term
        self.memory = memory  # 类型为'Memory'
        self.lock = threading.RLock()  # 添加锁，用于线程安全

        # Initialize collections
        self.questions: List[Any] = []
        self.beliefs: List[Any] = []
        self.executable_preconditions: List[Any] = []
        self.general_executable_preconditions: List[Any] = []
        self.quests: List[Any] = []
        self.desires: List[Any] = []

        # Initialize bags
        if memory and hasattr(memory, 'narParameters'):
            self.taskLinks = Bag1(memory.narParameters.TASK_LINK_BAG_SIZE)
        else:
            self.taskLinks = Bag1(100)  # Default size

        # Initialize term links
        self.termLinks: Set[TermLink] = set()

        # Initialize term link templates
        if term and hasattr(term, 'prepare_component_links'):
            self.termLinkTemplates = term.prepare_component_links()
            # Copy to termLinks
            self.termLinks = set(self.termLinkTemplates)
        else:
            self.termLinkTemplates = None
            self.termLinks = set()

        # 为了兼容性，添加别名
        self.task_links = self.taskLinks
        self.term_links = self.termLinks

        # Other fields
        self.seq_before = None
        self.recent_intervals = []
        self.observable = False
        self.allowBabbling = True
        self.acquiredQuality = 0.0
        self.anticipations = []  # 预期列表

    def __eq__(self, other: Any) -> bool:
        """
        Check equality
        Args:
            other: Object to compare with
        Returns:
            bool: True if equal
        """
        if self is other:
            return True

        if not isinstance(other, Concept):
            return False

        name = other.name()
        if name is None:
            name = other.name()

        name2 = self.name()
        if name2 is None:
            name2 = self.name()

        return name == name2

    def __hash__(self) -> int:
        """
        Hash code
        Returns:
            int: Hash code
        """
        return hash(self.name())

    def __enter__(self):
        """
        Enter the context manager
        Returns:
            Concept: Self
        """
        self.lock.acquire()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        Exit the context manager
        Args:
            exc_type: Exception type
            exc_val: Exception value
            exc_tb: Exception traceback
        """
        self.lock.release()

    def name(self):
        """
        Get the name of the concept

        Returns:
            Term: The term
        """
        return self.term

    def get_term(self):
        """
        Get the term of the concept
        Returns:
            Term: The term
        """
        return self.term

    def selectCandidate(self, query, list_items, time):
        """
        选择给定查询的信念值或欲望值
        Args:
            query: 要处理的查询
            list_items: 要使用的信念或欲望列表
            time: 时间
        Returns:
            Task: 选择的最佳候选项
        """
        from linars.org.opennars.inference.local_rules import LocalRules

        current_best = 0.0
        belief_quality = 0.0
        candidate = None
        rate_by_confidence = True  # 表格投票，是/否问题/本地处理

        with threading.RLock():
            for judg_t in list_items:
                judg = judg_t.sentence
                belief_quality = LocalRules.solution_quality(rate_by_confidence, query, judg, self.memory, time)
                if belief_quality > current_best:
                    current_best = belief_quality
                    candidate = judg_t

        return candidate

    def name(self) -> Term:
        """
        Get the name of the concept
        Returns:
            Term: The term
        """
        return self.term

    def add_to_table(self, task, rank_truth_expectation: bool, table: List, max_size: int,
                     event_add, event_remove, *extra_event_arguments):
        """
        Add a task to a table
        Args:
            task: The task to add
            rank_truth_expectation: Whether to rank by truth expectation
            table: The table to add to
            max_size: The maximum size of the table
            event_add: The event to emit when a task is added
            event_remove: The event to emit when a task is removed
            extra_event_arguments: Extra arguments for the event
        """
        pre_size = len(table)
        removed_t = self.add_to_table_static(task, table, max_size, rank_truth_expectation)
        removed = removed_t.sentence if removed_t else None

        if removed:
            self.memory.event.emit(event_remove, self, removed, task, *extra_event_arguments)

        if pre_size != len(table) or removed:
            self.memory.event.emit(event_add, self, task, *extra_event_arguments)

    @staticmethod
    def add_to_table_static(new_task, table: List, capacity: int, rank_truth_expectation: bool):
        """
        Add a new belief (or goal) into the table
        Args:
            new_task: The new task
            table: The table to add to
            capacity: The capacity of the table
            rank_truth_expectation: Whether to rank by truth expectation
        Returns:
            Any: The removed task, or None
        """
        from linars.org.opennars.inference.budget_functions import BudgetFunctions

        new_sentence = new_task.sentence
        rank1 = BudgetFunctions.rank_belief(new_sentence, rank_truth_expectation)  # 使用BudgetFunctions中的rank_belief方法
        # print(f"DEBUG: Ranking new belief with rank: {rank1}")

        # Find the position to insert
        i = 0
        for i in range(len(table)):
            judgment2 = table[i].sentence
            rank2 = BudgetFunctions.rank_belief(judgment2, rank_truth_expectation)  # 使用BudgetFunctions中的rank_belief方法

            if rank1 >= rank2:
                # Check for equivalent belief
                if (new_sentence.truth and judgment2.truth and
                    new_sentence.truth == judgment2.truth and
                    new_sentence.stamp and judgment2.stamp and
                    new_sentence.stamp.equals(judgment2.stamp, False, True, True)):
                    # print(f"DEBUG: Equivalent belief found, not adding")
                    return None

                table.insert(i, new_task)
                # print(f"DEBUG: Inserted task at position {i}")
                break

        # If we didn't insert in the loop
        if i == len(table):
            table.append(new_task)
            # print(f"DEBUG: Appended task at the end of table")

        # Check if we need to remove an item
        if len(table) > capacity:
            removed = table.pop()
            # print(f"DEBUG: Removed task due to capacity limit")
            return removed

        return None

    def link_to_task(self, task, content):
        """
        Link to a new task from all relevant concepts
        Args:
            task: The task to link
            content: The derivation context
        Returns:
            TaskLink: The created task link
        """
        from linars.org.opennars.inference.budget_functions import BudgetFunctions

        task_budget = task.budget
        # print(f"DEBUG: Linking task to concept: {task}, budget: {task_budget}")

        # Create a task link
        ret_link = TaskLink(task, None, task_budget, content.narParameters.TERM_LINK_RECORD_LENGTH)
        self.insert_task_link(ret_link, content)
        # print(f"DEBUG: Created and inserted task link: {ret_link}")

        # If the term is not a compound term, return
        if not hasattr(self.term, 'term') or not self.termLinkTemplates:
            # print(f"DEBUG: Term is not a compound term or no templates, returning")
            return ret_link

        # Distribute budget among links
        sub_budget = BudgetFunctions.distribute_among_links(task_budget, len(self.termLinkTemplates), content.narParameters)
        # print(f"DEBUG: Distributed budget: {sub_budget} among {len(self.termLinkTemplates)} links")

        if sub_budget and sub_budget.above_threshold():
            # print("DEBUG: Sub-budget above threshold, processing term links")
            for term_link in self.termLinkTemplates:
                if term_link.type == TermLink.TEMPORAL:
                    # print(f"DEBUG: Skipping temporal term link: {term_link}")
                    continue

                component_term = term_link.target
                # print(f"DEBUG: Processing component term: {component_term}")
                try:
                    component_concept = self.memory.conceptualize(sub_budget, component_term)

                    if component_concept:
                        # print(f"DEBUG: Inserting task link into component concept: {component_concept}")
                        component_concept.insert_task_link(
                            TaskLink(task, term_link, sub_budget, content.narParameters.TERM_LINK_RECORD_LENGTH),
                            content
                        )
                    else:
                        # 不再打印错误，而是尝试创建简单的概念
                        # print(f"error: Failed to conceptualize component term: {component_term}")
                        try:
                            # 尝试创建简单的原子概念
                            from linars.edu.memphis.ccrg.linars.term import Term
                            if isinstance(component_term, str):
                                simple_term = Term(component_term)
                            else:
                                simple_term = component_term

                            # 再次尝试概念化
                            component_concept = self.memory.conceptualize(sub_budget, simple_term)
                            if component_concept:
                                component_concept.insert_task_link(
                                    TaskLink(task, term_link, sub_budget, content.narParameters.TERM_LINK_RECORD_LENGTH),
                                    content
                                )
                        except Exception as inner_e:
                            # 静默处理，避免大量错误输出
                            pass
                except Exception as e:
                    # print(f"ERROR in link_to_task: {e}")
                    # import traceback
                    # traceback.print_exc()
                    # 静默处理，避免大量错误输出
                    pass

            # Build term links
            # print("DEBUG: Building term links")
            self.build_term_links(task_budget, content.narParameters)

        return ret_link

    def insert_task_link(self, task_link, nal) -> bool:
        """
        Insert a TaskLink into the TaskLink bag
        Args:
            task_link: The task link to insert
            nal: The derivation context

        Returns:
            bool: True if successful
        """
        complexity = task_link.get_term().get_complexity()
        if complexity > 10:
            return False

        target = task_link.get_target()

        # Handle max per content
        is_eternal = target.sentence.is_eternal()
        n_same_content = 0
        lowest_priority = float('inf')
        lowest = None

        for tl in self.taskLinks:
            s = tl.get_target().sentence
            if s.get_term() == task_link.get_term() and s.is_eternal() == is_eternal:
                n_same_content += 1
                if tl.get_priority() < lowest_priority:
                    lowest_priority = tl.get_priority()
                    lowest = tl

                if n_same_content > self.memory.narParameters.TASKLINK_PER_CONTENT:
                    self.taskLinks.pick_out(str(lowest))
                    self.memory.emit("TaskLinkRemove", lowest, self)
                    break

        # Insert the task link
        removed = self.taskLinks.put_in(task_link)

        if removed:
            if removed == task_link:
                self.memory.emit("TaskLinkRemove", task_link, self)
                return False
            else:
                self.memory.emit("TaskLinkRemove", removed, self)

        self.memory.emit("TaskLinkAdd", task_link, self)
        return True

    def build_term_links(self, task_budget, narParameters):
        """
        Recursively build TermLinks between a compound and its components
        Args:
            task_budget: The budget value of the task
            narParameters: The NAR parameters
        """
        from linars.org.opennars.inference.budget_functions import BudgetFunctions

        if not self.termLinkTemplates:
            # print("DEBUG: No termLinkTemplates, returning from build_term_links")
            return

        # 使用BudgetFunctions中的distribute_among_links方法
        sub_budget = BudgetFunctions.distribute_among_links(task_budget, len(self.termLinkTemplates), narParameters)
        # print(f"DEBUG: Distributed budget among {len(self.termLinkTemplates)} links")

        if not sub_budget or not sub_budget.above_threshold():
            # print("DEBUG: Budget below threshold, returning from build_term_links")
            return

        for template in self.termLinkTemplates:
            if template.type == TermLink.TRANSFORM:
                continue

            target = template.target
            # print(f"DEBUG: Conceptualizing target: {target}")
            concept = self.memory.conceptualize(task_budget, target)

            if not concept:
                # print(f"DEBUG: Failed to conceptualize target: {target}")
                continue

            # Create term links
            # print(f"DEBUG: Creating term links for target: {target}")
            self.insert_term_link(TermLink(target, template, sub_budget))
            concept.insert_term_link(TermLink(self.term, template, sub_budget))

            # Recursively build term links
            if isinstance(target, Term) and hasattr(target, 'term') and template.type != TermLink.TEMPORAL:
                # print(f"DEBUG: Recursively building term links for: {target}")
                concept.build_term_links(sub_budget, narParameters)

    def insert_term_link(self, term_link) -> bool:
        """
        Insert a TermLink into the TermLink set
        Args:
            term_link: The term link to insert
        Returns:
            bool: True if successful
        """
        complexity = term_link.target.get_complexity()
        if complexity > 10:
            return False
        # 如果还没有，则添加到termLinks中
        if term_link not in self.termLinks:
            self.termLinks.add(term_link)
            self.memory.emit("TermLinkAdd", term_link, self)
            return True
        return False

    def to_string_long(self) -> str:
        """
        Detailed string representation
        Returns:
            str: Detailed string representation
        """
        res = (f"{self.to_string_external()} {self.term.name()} "
               f"{self.to_string_if_not_null(len(self.termLinks), 'termLinks')} "
               f"{self.to_string_if_not_null(self.taskLinks.name_size(), 'taskLinks')} "
               f"{self.to_string_if_not_null(len(self.beliefs), 'beliefs')} "
               f"{self.to_string_if_not_null(len(self.desires), 'desires')} "
               f"{self.to_string_if_not_null(len(self.questions), 'questions')} "
               f"{self.to_string_if_not_null(len(self.quests), 'quests')}")

        return res

    def to_string_if_not_null(self, item, title: str) -> str:
        """
        Format an item for string representation if not null
        Args:
            item: The item to format
            title: The title
        Returns:
            str: Formatted string
        """
        if item is None:
            return ""

        item_string = str(item)
        return f" {title}:{item_string}"

    def inc_acquired_quality(self):
        """Increment the acquired quality"""
        self.acquiredQuality += 0.1
        if self.acquiredQuality > 1.0:
            self.acquiredQuality = 1.0

    def get_average_priority(self) -> float:
        """
        Get the average priority of task links
        Returns:
            float: The average priority
        """
        link_priority = 0.0
        for tl in self.taskLinks:
            link_priority += tl.get_priority()

        return link_priority

    @staticmethod
    def or_op(a: float, b: float) -> float:
        """
        两个值的逻辑OR
        Args:
            a: 第一个值
            b: 第二个值
        Returns:
            float: OR结果
        """
        return a + b - (a * b)

    def get_quality(self) -> float:
        """
        Recalculate the quality of the concept
        Returns:
            float: The quality value
        """
        link_priority = self.get_average_priority()
        term_complexity_factor = 1.0 / (self.term.get_complexity() * self.memory.narParameters.COMPLEXITY_UNIT)
        # 使用or_op方法替代max，与Java版本保持一致
        result = self.or_op(self.acquiredQuality, self.or_op(link_priority, term_complexity_factor))
        # print(f"DEBUG: Calculated quality: {result} (acquired={self.acquiredQuality}, linkPriority={link_priority}, termFactor={term_complexity_factor})")

        if result < 0:
            return 0.01

        return result

    def to_task(self, punctuation):
        """
        将概念转换为任务

        Args:
            punctuation: 标点符号，表示任务类型（判断、目标、问题等）

        Returns:
            Task: 创建的任务
        """
        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        from linars.org.opennars.entity.sentence import Sentence
        from linars.org.opennars.entity.truth_value import TruthValue
        from linars.org.opennars.entity.stamp import Stamp
        from linars.org.opennars.entity.budget_value import BudgetValue
        from linars.org.opennars.entity.task import Task

        # 创建真值、印记和句子
        truth = TruthValue(1.0, 0.9, False, AgentStarter.nar.narParameters)
        stamp = Stamp(AgentStarter.nar, AgentStarter.nar.memory)
        sentence = Sentence(self.term, punctuation, truth, stamp)

        # 创建预算值和任务
        budget = BudgetValue(0.8, 0.5, 1.0, AgentStarter.nar.narParameters)
        from linars.org.opennars.entity.task import EnumType
        task = Task(sentence, budget, EnumType.INPUT)

        print(f"DEBUG: Created task from concept: {task}")
        return task

    def get_term_link_templates(self) -> List[TermLink]:
        """
        Get the term link templates
        Returns:
            List: The term link templates
        """
        return self.termLinkTemplates

    def get_belief(self, nal, task):
        """
        Select a belief to interact with the given task in inference
        Args:
            nal: The derivation context
            task: The selected task
        Returns:
            Any: The selected belief
        """
        task_stamp = task.sentence.stamp
        current_time = nal.time.time()
        # print(f"DEBUG: Selecting belief for task: {task}, current time: {current_time}")

        for belief_t in self.beliefs:
            belief = belief_t.sentence
            # print(f"DEBUG: Considering belief: {belief}")
            try:
                nal.emit("BeliefSelect", belief)
                nal.set_the_new_stamp(task_stamp, belief.stamp, current_time)

                projected_belief = belief.projection(task_stamp.get_occurrence_time(), nal.time.time(), self.memory)
                # print(f"DEBUG: Projected belief: {projected_belief}")

                return projected_belief  # Return the first satisfying belief
            except Exception as e:
                # print(f"ERROR in get_belief: {e}")
                import traceback
                traceback.print_exc()

        # print("DEBUG: No suitable belief found")
        return None

    def get_desire(self):
        """
        Get the current overall desire value
        Returns:
            Any: The desire value
        """
        if not self.desires:
            return None

        top_value = self.desires[0].sentence.truth
        return top_value

    def select_term_link(self, task_link, time, narParameters):
        """
        Select a term link for inference
        Args:
            task_link: The selected task link
            time: The current time
            narParameters: The NAR parameters
        Returns:
            TermLink: The selected term link
        """
        to_match = narParameters.TERM_LINK_MAX_MATCHED
        # print(f"DEBUG: Selecting term link for task link: {task_link}, max to match: {to_match}")

        for i in range(min(to_match, len(self.termLinks))):
            term_link = self.get_one_term_link()
            # print(f"DEBUG: Got term link: {term_link}")

            if term_link is None:
                # print("DEBUG: No term link available")
                break

            try:
                if task_link.novel(term_link, time, narParameters):
                    # print(f"DEBUG: Found novel term link: {term_link}")
                    return term_link

                # Put back since it isn't novel
                # print(f"DEBUG: Term link not novel, returning to pool: {term_link}")
                self.return_term_link(term_link)
            except Exception as e:
                print(f"ERROR in select_term_link: {e}")
                import traceback
                traceback.print_exc()
                # Still return the term link to the pool
                self.return_term_link(term_link)

        # print("DEBUG: No suitable term link found")
        return None

    def get_one_term_link(self):
        """
        Get one term link from the set
        Returns:
            TermLink: A term link
        """
        if not self.termLinks:
            return None

        # Find a term link with a budget
        for term_link in self.termLinks:
            if term_link.budget is not None:
                self.termLinks.remove(term_link)
                return term_link

        return None

    def return_term_link(self, term_link):
        """
        Return a term link to the set
        Args:
            term_link: The term link to return
        """
        self.put_back(term_link, self.memory.cycles(self.memory.narParameters.TERMLINK_FORGET_DURATIONS), self.memory)

    def put_back(self, term_link, cycles, memory):
        """
        Put back a term link
        Args:
            term_link: The term link to put back
            cycles: The number of cycles
            memory: The memory
        """
        from linars.org.opennars.inference.budget_functions import BudgetFunctions

        relative_threshold = memory.narParameters.FORGET_QUALITY_RELATIVE
        # print(f"DEBUG: Putting back term link: {term_link}, cycles: {cycles}, threshold: {relative_threshold}")

        # 应用遗忘机制
        if hasattr(term_link, 'budget') and term_link.budget:
            BudgetFunctions.apply_forgetting(term_link.budget, cycles, relative_threshold)
            # print(f"DEBUG: Applied forgetting to term link budget: {term_link.budget}")

        self.insert_term_link(term_link)

    def get_questions(self) -> List:
        """
        Get the questions
        Returns:
            List: The questions
        """
        return list(self.questions)

    def get_quests(self) -> List:
        """
        Get the quests
        Returns:
            List: The quests
        """
        return list(self.quests)

    def discount_confidence(self, on_beliefs: bool):
        """
        Discount confidence of beliefs or desires
        Args:
            on_beliefs: Whether to discount beliefs or desires
        """
        if on_beliefs:
            for t in self.beliefs:
                t.sentence.discount_confidence(self.memory.narParameters)
        else:
            for t in self.desires:
                t.sentence.discount_confidence(self.memory.narParameters)

    def operator(self):
        """
        Get the operator type
        Returns:
            str: The operator type
        """
        return self.term.operator()

    def get_term(self) -> Term:
        """
        Get the term
        Returns:
            Term: The term
        """
        return self.term

    def get_beliefs(self) -> List:
        """
        Get the beliefs
        Returns:
            List: The beliefs
        """
        return list(self.beliefs)

    def get_desires(self) -> List:
        """
        Get the desires
        Returns:
            List: The desires
        """
        return list(self.desires)

    def clone(self):
        """
        Clone the concept
        Returns:
            Concept: The cloned concept
        """
        c = Concept()
        c.term = Term(self.term.name())
        c.beliefs = list(self.beliefs)
        c.desires = list(self.desires)
        c.questions = list(self.questions)
        c.quests = list(self.quests)
        c.memory = self.memory
        c.termLinks = set(self.termLinks)
        c.taskLinks = Bag1(self.memory.narParameters.TASK_LINK_BAG_SIZE)
        c.taskLinks = self.taskLinks  # This would need proper cloning
        c.executable_preconditions = list(self.executable_preconditions)
        c.general_executable_preconditions = list(self.general_executable_preconditions)

        return c
