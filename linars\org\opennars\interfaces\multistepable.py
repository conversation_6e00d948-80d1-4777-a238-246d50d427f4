"""
多步执行接口
定义支持分步执行和停止的对象接口
"""
from abc import ABC, abstractmethod

class Multistepable(ABC):
    """
    多步执行接口
    定义支持分步执行和停止的对象必须实现的方法
    """

    @abstractmethod
    def start(self, min_cycle_period_ms: int = None):
        """
        启动系统

        参数:
            min_cycle_period_ms: 最小执行周期时间(毫秒)
        """
        pass

    @abstractmethod
    def stop(self):
        """停止系统"""
        pass

    @abstractmethod
    def cycles(self, cycles: int):
        """
        执行指定次数的周期

        参数:
            cycles: 要执行的周期次数
        """
        pass

    @abstractmethod
    def cycle(self):
        """执行单个周期"""
        pass
