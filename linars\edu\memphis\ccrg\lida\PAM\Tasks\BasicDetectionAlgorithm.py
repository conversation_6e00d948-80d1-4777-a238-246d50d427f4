#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
This class implements the FeatureDetector interface and provides default methods.
"""

import logging
from abc import abstractmethod

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.PAM.PamLinkable import PamLinkable
from linars.edu.memphis.ccrg.lida.PAM.Tasks.DetectionAlgorithm import DetectionAlgorithm
from linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemory import SensoryMemory


class BasicDetectionAlgorithm(FrameworkTaskImpl, DetectionAlgorithm):
    """
    This class implements the FeatureDetector interface and provides default methods.
    Users should extend this class and overwrite the detect() and excitePam() methods.
    A convenience init() method is added to initialize the class. This method can be overwritten as well.
    This implementation is oriented to detect features from sensoryMemory, but the implementation can be
    used to detect and burstActivation from other modules, like Workspace, emotions or internal states.
    """
    
    def __init__(self):
        """
        Default constructor. Associated Linkable, PAMemory and SensoryMemory must be set using setters.
        """
        super().__init__()
        self.node_structure = NodeStructureImpl()
        self.sensory_memory = None  # SensoryMemory
        self.pam = None  # PAMemory
        self.linkable = None  # PamLinkable
        self.logger = logging.getLogger("BasicDetectionAlgorithm")
        
    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module.
        
        Args:
            module: The module to associate
            module_usage: The usage of the module
        """
        if isinstance(module, PAMemory):
            self.pam = module
        elif isinstance(module, SensoryMemory):
            self.sensory_memory = module
        else:
            self.logger.warning(f"Cannot set associated module {module}")
            
    def set_pam_linkable(self, linkable: PamLinkable) -> None:
        """
        Adds PamLinkable that will be detected by this algorithm.
        
        Args:
            linkable: A PamLinkable
        """
        self.linkable = linkable
        
    def get_pam_linkable(self) -> PamLinkable:
        """
        Returns PamLinkable this algorithm can detect.
        
        Returns:
            The pam nodes
        """
        return self.linkable
        
    def init(self, params=None) -> None:
        """
        This task can be initialized with the following parameters:
        
        node type=string: label of the Node in PAMemory this algorithm detects
        """
        super().init()
        node_label = self.get_param("node", "")
        if node_label is not None:
            node_label = node_label.strip()  # todo 图数据
            # node = GlobalInitializer.get_instance().get_attribute(node_label)
            node = self.node_structure.get_neo_node(node_label)
            if node is not None and node.get_node_id() != 0 and node_label != "Node":
                self.set_pam_linkable(node)
            else:
                self.logger.warning(f"Could not get node {node_label} from neo")
                
    def run_this_framework_task(self) -> None:
        """
        Run this framework task.
        """
        # 应该是信息激活神经信息，接受是被动的，而不是探测
        # 注意力调动的探测才是主动的，注意力越集中，信息越精准丰富，粒度越小，但也范围越小
        # 如果注意力被调动但是不集中，只是平均水平的激活，集中后是更快更高的解析度
        # 注意不完全是筛选，也是焦点本身信息的丰富，概念信息、场景属性等更完善，调动焦点是筛选，其他是完善
        # 听觉焦点=类似视觉，音轨分解？
        
        amount = self.detect()
        if self.logger.isEnabledFor(logging.DEBUG):
            self.logger.debug(f"Detection performed {self}: {amount}")
            
        if amount > 0.0:
            if self.logger.isEnabledFor(logging.DEBUG):
                self.logger.debug(f"Pam excited: {amount} by {self}")
                
            # self.pam.receive_excitation(self.linkable, amount)
            
    @abstractmethod
    def detect(self) -> float:
        """
        Override this method implementing feature detection algorithm.
        
        Returns:
            Degree [0,1] to which the feature was detected
        """
        pass
