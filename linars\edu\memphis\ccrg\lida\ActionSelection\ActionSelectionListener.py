# LIDA认知框架
"""
行为选择模块的监听器
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action

class ActionSelectionListener(ModuleListener):
    """
    行为选择模块的监听器
    """

    @abstractmethod
    def receive_action(self, action: Action) -> None:
        """
        监听器必须接收行为。行为网络选择的每个行为都会调用此方法。

        参数:
            action: 存储在感觉运动记忆中的行为
        """
        pass
