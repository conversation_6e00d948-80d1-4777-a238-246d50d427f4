"""
Abstract Term class for NARS.

This module provides an abstract base class for terms in the NARS system.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Set, Tuple, Union, Any

class AbstractTerm(ABC):
    """
    Abstract base class for terms in the NARS system.
    """
    
    @abstractmethod
    def name(self) -> str:
        """
        Get the name of this term.
        
        Returns:
            The name of this term
        """
        pass
    
    @abstractmethod
    def complexity(self) -> int:
        """
        Get the complexity of this term.
        
        Returns:
            The complexity of this term
        """
        pass
    
    @abstractmethod
    def is_constant(self) -> bool:
        """
        Check if this term is a constant.
        
        Returns:
            True if this term is a constant, False otherwise
        """
        pass
