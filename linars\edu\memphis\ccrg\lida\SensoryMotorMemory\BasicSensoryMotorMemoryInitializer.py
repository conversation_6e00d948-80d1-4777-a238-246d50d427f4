# LIDA Cognitive Framework
"""
Basic SensoryMotorMemory Initializer which reads String parameters beginning with 'smm.'
and creates an action-algorithm mapping based on the parameter.
"""

import logging
from typing import Dict, Any, Optional, TypeVar, Generic, Type

from linars.edu.memphis.ccrg.lida.Framework.Initialization.initializer import Initializer
from linars.edu.memphis.ccrg.lida.Framework.Initialization.FullyInitializable import FullyInitializable
from linars.edu.memphis.ccrg.lida.Framework.Initialization.global_initializer import GlobalInitializer
from linars.edu.memphis.ccrg.lida.Framework.agent import Agent
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action
from linars.edu.memphis.ccrg.lida.SensoryMotorMemory.BasicSensoryMotorMemory import BasicSensoryMotorMemory

T = TypeVar('T')

class BasicSensoryMotorMemoryInitializer(Initializer):
    """
    Basic SensoryMotorMemory Initializer which reads String parameters beginning with 'smm.'
    and creates an action-algorithm mapping based on the parameter.
    The definition is: actionName,algorithm
    """

    def __init__(self):
        """
        Initialize a BasicSensoryMotorMemoryInitializer.
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.initializer = GlobalInitializer.get_instance()
        self.smm = None

    def initModule(self, module: FullyInitializable, agent: Agent, params: Dict[str, Any]) -> None:
        """
        Initialize the module.

        Args:
            module: The module to initialize
            agent: The agent
            params: The parameters for initialization
        """
        # print(f"BasicSensoryMotorMemoryInitializer.initModule() called with module: {module}, agent: {agent}, params: {params}")
        # print(f"BasicSensoryMotorMemoryInitializer.initModule() called with module: {module}")
        self.smm = module

        if not isinstance(self.smm, BasicSensoryMotorMemory):
            self.logger.warning(f"Module is not a BasicSensoryMotorMemory at tick {TaskManager.get_current_tick()}")
            return

        for key in params.keys():
            if key.startswith("smm.mapping."):
                value = params.get(key)
                smm_def = ""
                if isinstance(value, str):
                    smm_def = value
                else:
                    self.logger.warning(f"Parameter name: {key} started with smm.mapping. but did not contain a valid def at tick {TaskManager.get_current_tick()}")
                    continue

                self.logger.info(f"Loading smm action-algorithm mapping: {smm_def} at tick {TaskManager.get_current_tick()}")
                elements = smm_def.split(",")
                if len(elements) == 2:
                    action_name = elements[0].strip()
                    algorithm_name = elements[1].strip()
                    if algorithm_name == "":
                        self.logger.warning(f"Missing algorithm name for smm: {smm_def} at tick {TaskManager.get_current_tick()}")
                        continue

                    action = self.initializer.get_attribute(action_name)
                    if action is not None and isinstance(action, Action):
                        self.smm.add_action_algorithm(action.get_id(), algorithm_name)
                    # else:
                        # self.logger.warning(f"Could not find agent action: {action_name} at tick {TaskManager.get_current_tick()}")
                else:
                    self.logger.warning(f"Incorrect smm def: {smm_def} must have form 'actionName,algorithm' at tick {TaskManager.get_current_tick()}")

    # 删除不需要的方法，因为我们已经有了initModule方法
