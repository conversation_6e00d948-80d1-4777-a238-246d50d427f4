# LIDA认知框架
"""
节点结构(NodeStructure)是节点(Node)和链接(Link)的集合
"""

from abc import ABC, abstractmethod
from typing import Set, Dict, Any, Optional, Collection, TYPE_CHECKING

# 避免循环导入
if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
    from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
    from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable
    from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategory import LinkCategory

class NodeStructure(ABC):
    """
    节点结构(NodeStructure)是节点(Node)和链接(Link)的集合
    """

    @abstractmethod
    def add_node(self, node: 'Node', copy: bool = True) -> 'Node':
        """
        向此结构添加节点

        参数:
            node: 要添加的节点
            copy: 是否复制节点

        返回:
            添加的节点
        """
        pass

    @abstractmethod
    def add_default_node(self, node: 'Node') -> 'Node':
        """
        使用默认设置向此结构添加节点

        参数:
            node: 要添加的节点

        返回:
            添加的节点
        """
        pass

    @abstractmethod
    def add_link(self, link_type: str, source: 'Node', sink: 'Linkable', category: 'LinkCategory', copy: bool = True) -> 'Link':
        """
        向此结构添加链接

        参数:
            link_type: 链接类型
            source: 链接源节点
            sink: 链接目标
            category: 链接类别
            copy: 是否复制链接

        返回:
            添加的链接
        """
        pass

    @abstractmethod
    def add_default_link(self, *args) -> 'Link':
        """
        使用默认设置向此结构添加链接

        参数:
            可以是以下两种形式之一:
            1. link: 要添加的链接
            2. source: 链接源端, sink: 链接汇端, category: 链接类别, activation: 激活值, removal_threshold: 移除阈值

        返回:
            添加的链接
        """
        pass

    @abstractmethod
    def get_node(self, id: int) -> Optional['Node']:
        """
        通过ID获取节点

        参数:
            id: 节点ID

        返回:
            指定ID的节点
        """
        pass

    @abstractmethod
    def get_nodes(self) -> Collection['Node']:
        """
        获取此结构中的所有节点

        返回:
            此结构中的所有节点
        """
        pass

    @abstractmethod
    def get_links(self) -> Collection['Link']:
        """
        获取此结构中的所有链接

        返回:
            此结构中的所有链接
        """
        pass

    @abstractmethod
    def get_linkables(self) -> Collection['Linkable']:
        """
        获取此结构中的所有可链接对象

        返回:
            此结构中的所有可链接对象
        """
        pass

    @abstractmethod
    def get_link(self, source: 'Node', sink: 'Linkable', category: 'LinkCategory') -> Optional['Link']:
        """
        通过源节点、目标和类别获取链接

        参数:
            source: 链接源节点
            sink: 链接目标
            category: 链接类别

        返回:
            具有指定源节点、目标和类别的链接
        """
        pass

    @abstractmethod
    def get_links_of_source(self, source: 'Node') -> Collection['Link']:
        """
        获取来自源节点的所有链接

        参数:
            source: 链接源节点

        返回:
            来自指定源节点的所有链接
        """
        pass

    @abstractmethod
    def get_links_of_sink(self, sink: 'Linkable') -> Collection['Link']:
        """
        获取指向目标的所有链接

        参数:
            sink: 链接目标

        返回:
            指向指定目标的所有链接
        """
        pass

    @abstractmethod
    def get_attractor_links(self) -> Collection['Link']:
        """
        获取此结构中的所有吸引子链接

        返回:
            此结构中的所有吸引子链接
        """
        pass

    @abstractmethod
    def get_node_count(self) -> int:
        """
        获取此结构中的节点数量

        返回:
            此结构中的节点数量
        """
        pass

    @abstractmethod
    def get_link_count(self) -> int:
        """
        获取此结构中的链接数量

        返回:
            此结构中的链接数量
        """
        pass

    @abstractmethod
    def get_linkable_count(self) -> int:
        """
        获取此结构中的可链接对象数量

        返回:
            此结构中的可链接对象数量
        """
        pass

    @abstractmethod
    def contains_linkable(self, linkable: 'Linkable') -> bool:
        """
        检查此结构是否包含指定的可链接对象

        参数:
            linkable: 要检查的可链接对象

        返回:
            如果包含返回True，否则返回False
        """
        pass

    @abstractmethod
    def get_linkable(self, linkable: 'Linkable') -> Optional['Linkable']:
        """
        从此结构中获取可链接对象

        参数:
            linkable: 要获取的可链接对象

        返回:
            此结构中的可链接对象
        """
        pass

    @abstractmethod
    def merge_with(self, ns: 'NodeStructure') -> None:
        """
        将此结构与另一个结构合并

        参数:
            ns: 要合并的结构
        """
        pass

    @abstractmethod
    def copy(self) -> 'NodeStructure':
        """
        创建此结构的副本

        返回:
            此结构的副本
        """
        pass

    @abstractmethod
    def clear_structure(self) -> None:
        """
        清空此结构
        """
        pass

    @abstractmethod
    def decay_node_structure(self, ticks: int) -> None:
        """
        衰减此结构

        参数:
            ticks: 衰减的ticks数
        """
        pass

    @abstractmethod
    def get_scene_time(self) -> str:
        """
        获取此结构的场景时间

        返回:
            此结构的场景时间
        """
        pass

    @abstractmethod
    def set_scene_time(self, time: str) -> None:
        """
        设置此结构的场景时间

        参数:
            time: 要设置的场景时间
        """
        pass

    @abstractmethod
    def get_scene_site(self) -> str:
        """
        获取此结构的场景位置

        返回:
            此结构的场景位置
        """
        pass

    @abstractmethod
    def set_scene_site(self, site: str) -> None:
        """
        设置此结构的场景位置

        参数:
            site: 要设置的场景位置
        """
        pass

    @abstractmethod
    def get_broad_scene_count(self) -> int:
        """
        获取此结构的广播场景计数

        返回:
            此结构的广播场景计数
        """
        pass

    @abstractmethod
    def set_broad_scene_count(self, count: int) -> None:
        """
        设置此结构的广播场景计数

        参数:
            count: 要设置的广播场景计数
        """
        pass

    @abstractmethod
    def save_scene(self, ns: 'NodeStructure') -> None:
        """
        保存此结构的场景

        参数:
            ns: 要保存场景的结构
        """
        pass
