#LIDA Cognitive Framework

"""
A task to process a goal tree.

这个任务用于处理目标树，寻找最佳的可执行前提条件，并尝试执行它们以实现目标。
"""

from typing import List, Dict, Optional, Any, Union, Set
# Import Memory for type checking
from typing import TYPE_CHECKING

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.linars.process_goal import ProcessGoal
from linars.org.opennars.control.derivation_context import DerivationContext
from linars.org.opennars.entity.task import Task
from linars.org.opennars.main.parameters import Parameters

if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.linars.memory import Memory


class ProcessGTreeTask(FrameworkTaskImpl):
    """
    处理目标树的任务。

    这个任务用于处理目标树，寻找最佳的可执行前提条件，并尝试执行它们以实现目标。
    它是由ExcitationTask或PropagationTask创建的。
    """

    def __init__(self, task: Task, mem: 'Memory'):
        """
        初始化ProcessGTreeTask。

        参数:
            task: 要处理的任务
            mem: 内存对象
        """
        super().__init__(1, "tact")
        self.task = task
        self.mem = mem

    def run_this_framework_task(self):
        """
        运行任务。

        这个方法会执行以下步骤：
        1. 创建执行前提条件列表，并添加当前任务
        2. 创建推导上下文
        3. 获取目标概念
        4. 获取或创建目标任务
        5. 调用ProcessGoal.calc_best_executable_precondition方法寻找最佳可执行前提条件
        6. 取消任务
        """
        try:
            # 创建执行前提条件列表
            exec_preconditions = []
            # 顺承时序，加入当前已决定言行
            exec_preconditions.append(self.task)

            # 创建推导上下文
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            nal = DerivationContext(self.mem, Parameters(), AgentStarter.nar)

            # 获取目标概念
            try:
                # 获取任务的谓词作为目标
                term = self.task.get_term()
                # 检查term的类型并相应处理
                from linars.org.opennars.language.statement import Statement
                from linars.edu.memphis.ccrg.linars.term import Term

                predicate = None

                # 如果term是Statement类型
                if isinstance(term, Statement):
                    predicate = term.get_predicate()
                # 如果term是列表类型
                elif isinstance(term, list) and len(term) >= 2:
                    # 列表中第二个元素通常是谓词
                    predicate = term[1]
                # 如果term有term属性且是列表
                elif hasattr(term, 'term') and isinstance(term.term, list) and len(term.term) >= 2:
                    # CompoundTerm类型的term属性中第二个元素通常是谓词
                    predicate = term.term[1]

                # 检查是否成功获取谓词
                if predicate is not None:
                    predicate_str = str(predicate)
                    goal_c = self.mem.concepts.get(predicate_str)
                    if goal_c is not None:
                        goal_c = goal_c.clone()
                    else:
                        print(f"警告: 无法找到谓词 '{predicate_str}' 的概念")
                        self.cancel()
                        return
                else:
                    print(f"警告: 无法从任务的term中获取谓词: {type(term)}")
                    # 打印更多调试信息
                    if isinstance(term, list):
                        print(f"term列表内容: {term}")
                    elif hasattr(term, 'term'):
                        print(f"term.term内容: {term.term}")
                    self.cancel()
                    return
            except Exception as e:
                print(f"错误: 获取目标概念时出错: {e}")
                import traceback
                traceback.print_exc()
                self.cancel()
                return

            # 获取或创建目标任务
            try:
                # 确保goal_c和goal_c.term不为None
                if not hasattr(goal_c, 'term') or goal_c.term is None:
                    print("错误: goal_c没有term属性或term为None")
                    self.cancel()
                    return

                task00 = self.mem.globalBuffer.getByTerm(str(goal_c.term))
                if task00 is None:
                    # 确保to_task方法存在
                    if not hasattr(goal_c, 'to_task'):
                        print(f"错误: goal_c没有to_task方法: {type(goal_c)}")
                        self.cancel()
                        return
                    goal_task = goal_c.to_task('!')
                else:
                    goal_task = task00

                # 确保goal_task和goal_task.sentence不为None
                if goal_task is None or not hasattr(goal_task, 'sentence') or goal_task.sentence is None:
                    print(f"错误: goal_task或goal_task.sentence为None")
                    self.cancel()
                    return
            except Exception as e:
                print(f"错误: 获取或创建目标任务时出错: {e}")
                import traceback
                traceback.print_exc()
                self.cancel()
                return

            # 调用ProcessGoal.calc_best_executable_precondition方法
            try:
                # 检查ProcessGoal是否有calc_best_executable_precondition方法
                if not hasattr(ProcessGoal, 'calc_best_executable_precondition'):
                    print(f"错误: ProcessGoal没有calc_best_executable_precondition方法")
                    self.cancel()
                    return

                # 检查exec_preconditions是否为列表类型
                if not isinstance(exec_preconditions, list):
                    print(f"错误: exec_preconditions不是列表类型: {type(exec_preconditions)}")
                    self.cancel()
                    return

                # 调用方法
                ProcessGoal.calc_best_executable_precondition(nal, goal_c,
                                                          goal_task.sentence, exec_preconditions, {}, goal_task)
            except Exception as e:
                print(f"错误: 计算最佳可执行前提条件时出错: {e}")
                import traceback
                traceback.print_exc()
        except Exception as e:
            print(f"错误: 在ProcessGTreeTask.run_this_framework_task中: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 取消任务
            self.cancel()
