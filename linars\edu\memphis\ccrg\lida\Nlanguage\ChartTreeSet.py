#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
ChartTreeSet class for the LIDA framework.
"""

from typing import Collection, List

from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Nlanguage.TreeBag import TreeBag
from linars.edu.memphis.ccrg.linars.term import Term


class ChartTreeSet(NodeStructureImpl):
    """
    A chart tree set in the LIDA framework.
    """
    
    def __init__(self):
        """
        Initialize a ChartTreeSet.
        """
        super().__init__()
        self.chart_set = TreeBag(100, 100, 100)
        
    def get_tree_bag(self) -> TreeBag:
        """
        Get the tree bag.
        
        Returns:
            The tree bag
        """
        return self.chart_set
        
    def get_links_of_sink_t(self, node_name: str) -> Collection[Term]:
        """
        Get the links of a sink as terms.
        
        Args:
            node_name: The name of the sink node
            
        Returns:
            The links as terms
        """
        lls = self.get_links_of_sink(node_name)
        result = []
        for l in lls:
            result.append(l)
        return result
