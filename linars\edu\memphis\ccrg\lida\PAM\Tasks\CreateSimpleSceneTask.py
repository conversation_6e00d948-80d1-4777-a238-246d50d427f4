#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to create a simple scene.
"""

from typing import Optional
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.PAM.PamNode import PamNode
from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
from linars.edu.memphis.ccrg.lida.PAM.Tasks.IsaPamTask import IsaPamTask

# 导入 linars 组件
try:
    from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
    from linars.edu.memphis.ccrg.linars.term import Term
    from linars.org.opennars.io.parser import Parser
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False

class CreateSimpleSceneTask(FrameworkTaskImpl):
    """
    A task to create a simple scene.
    """

    def __init__(self, link: Link, son_head: Node, act_stamp: Optional[str] = None):
        """
        Initialize a CreateSimpleSceneTask.

        Args:
            link: The link
            son_head: The son head
            act_stamp: The action stamp
        """
        super().__init__(1, "tact")
        self.link = link
        self.son_head = son_head
        self.act_stamp = act_stamp

        self.sources = None
        self.sinks = None
        self.sink = None
        self.source = None
        self.pam = None
        self.scene_ns = None
        self.goal_ns = None
        self.root_node = None
        self.mark = None

    def run_this_framework_task(self):
        """
        Run the task.
        """
        # 待生成父场景，根场景，单句
        # self.pam.set_scene_main_node(self.sink)

        # nar.add_input_to("(^say,{SELF}," + self.sink.get_tn_name() + ")! :|:", self.goal_ns)

        # 变量句，定义变量并赋值，用具体值实例化，提交到参数表中
        # self.var_task(self.sink, self.sink.get_tn_name())
        # 用于某些初始化
        # self.var_task(self.sink, None)

        # 直接将link的尾节点name改为sonhead，
        query = f"match (n) where n.name = '{self.link.get_sink().get_tn_name()}' set n.name = '{self.son_head.get_tn_name()}' return n"
        node = NeoUtil.get_node_cypher(query)

        # 然后新建结构边arg0123等，根据sonhead的name的嵌套结构，构建结构边，如（从，个位数，开始），分别是arg0，arg1，arg2，指向node
        compound_term = None
        try:
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            compound_term = AgentStarter.narsese.parse_term(self.son_head.get_tn_name())
        except Exception as e:
            raise RuntimeError(e)

        print("构建时序中------------SimpleSceneTask")
        self.create_args_with_nest(compound_term, node)

        from linars.edu.memphis.ccrg.lida.PAM.Tasks.CreateSuccTask import CreateSuccTask

        create_succ_task = CreateSuccTask(self.link, self.pam, 60, self.act_stamp)
        self.pam.get_assisting_task_spawner().add_task(create_succ_task)

        print(f"目前任务总数-----------------》 {len(self.pam.get_assisting_task_spawner().get_tasks())}")

        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        AgentStarter.is_create_var = True
        AgentStarter.create_start_tick = TaskManager.get_current_tick()

        self.cancel()

    @staticmethod
    def create_args_with_nest(compound_term: CompoundTerm, node: Node):
        """
        Create args with nest.

        Args:
            compound_term: The compound term
            node: The node
        """
        terms = compound_term.get_terms()
        for i in range(len(terms)):
            query1 = f"merge (n{{name: '{str(terms[i])}'}})"
            node1 = NeoUtil.get_node_cypher(query1)
            # 新建结构边
            query2 = f"match (n),(m) where n.name = '{node.get_node_id()}' and m.name = '{str(terms[i])}' merge (n)-[r:arg{i}]->(m) return r"
            link1 = NeoUtil.get_link_cypher(query2)

            # 如果是复合结构，则递归调用
            if isinstance(terms[i], CompoundTerm):
                CreateSimpleSceneTask.create_args_with_nest(terms[i], node1)

    @staticmethod
    def var_task(sink: Node, change: Optional[str]):
        """
        Variable task.

        Args:
            sink: The sink node
            change: The change
        """
        query = ""
        if change is None or change == "":
            # 初始化变量，没有参考句
            query = f"match (n)-[r:初始句]->(m) where n.name = '{sink.get_tn_name()}' return r"
        else:
            query = f"match (n)-[r:变量句]->(m) where n.name = '{sink.get_tn_name()}' return r"

        print(f"query = {query}")
        link1 = None

        try:
            # 使用NeoUtil获取链接
            link1 = NeoUtil.get_link_cypher(query)
            if link1 is not None:
                to_node = link1.get_sink()

                if change is None or change == "":
                    # 初始化变量，没有参考句
                    g_terms = to_node.get_tn_name().split(",")
                    if "$" in g_terms[0]:
                        from linars.edu.memphis.ccrg.lida.Data.term_util import TermUtil
                        type_node = TermUtil.get_pam_node(g_terms[0], 20001 + 5)
                        value_node = TermUtil.get_pam_node(g_terms[1], 20001 + 5)
                        IsaPamTask.make_nowisa(type_node, value_node)
                else:
                    p_terms = change.split(",")
                    g_terms = to_node.get_tn_name().split(",")

                    if len(p_terms) != len(g_terms):
                        print("变量句中变量个数不匹配")
                        return
                    else:
                        for i in range(len(p_terms)):
                            if "$" in g_terms[i]:
                                # if "加数" in g_terms[i]:
                                #     continue

                                # .replace("$","")
                                from linars.edu.memphis.ccrg.lida.Data.term_util import TermUtil
                                type_node = TermUtil.get_pam_node(g_terms[i].replace(")", ""), 20001 + i)
                                value_node = TermUtil.get_pam_node(p_terms[i].replace(")", ""), 20001 + i)
                                IsaPamTask.make_nowisa(type_node, value_node)
                                print(f"---------时序---变量定义----------{type_node} = {value_node}")
            else:
                print("变量句不存在")
        except Exception as e:
            print(e)