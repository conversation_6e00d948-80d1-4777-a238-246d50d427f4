#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to handle selection tree processing.
"""

from typing import List, Dict, Set, Optional, Any
import logging
from collections import defaultdict

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.PAM.PamNode import PamNode
from linars.edu.memphis.ccrg.lida.PAM.PamNodeImpl import PamNodeImpl
from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil

class DoSelectTreeTask(FrameworkTaskImpl):
    """
    A task to handle selection tree processing.
    """

    pam = None  # PAMemory
    else_size = 0
    else_links = None  # List[Link]
    else_map = None  # Dict[int, List[Link]]

    def __init__(self, link: Link, pam: PAMemory, goal_ns: NodeStructure, scene_ns: NodeStructure, act_stamp: Optional[str] = None):
        """
        Initialize a DoSelectTreeTask.

        Args:
            link: The link
            pam: The PAMemory
            goal_ns: The goal NodeStructure
            scene_ns: The scene NodeStructure
            act_stamp: The action stamp
        """
        super().__init__(1, "tact")
        self.link = link
        DoSelectTreeTask.pam = pam
        self.scene_ns = scene_ns
        self.goal_ns = goal_ns
        self.act_stamp = act_stamp

        self.sink = None
        self.source = None
        self.seq_ns = None
        self.yufa_ns = None
        self.cosc_list = None
        self.logger = logging.getLogger("DoSelectTreeTask")

    def run_this_framework_task(self):
        """
        Run the task.
        """
        self.seq_ns = DoSelectTreeTask.PAM.get_workspace_buffer("seq").get_buffer_content(None)
        self.yufa_ns = DoSelectTreeTask.PAM.get_workspace_buffer("yufa").get_buffer_content(None)

        # 进入判断结构体内，需要存入上位时序，以便回溯，判断遍历也只需一条上位边
        self.seq_ns.get_do_main_path_map().get(self.act_stamp).append(self.link)
        # 如果有else，则需要用到，但无论有没有都要存下来
        DoSelectTreeTask.else_map = {}

        query = f"match (m:场景)-[r:判断首]->(i:场景) where m.name = '{self.link.get_sink().get_tn_name()}' return r"
        # todo 动机不同，描述不同，具体心算按具体分支
        # 结构体执行，判断、循环等类似
        self.do_select_root(self.link, query)

        self.cancel()

    def do_select_root(self, link: Link, query: str):
        """
        Process the selection root.

        Args:
            link: The link
            query: The query
        """
        self.sink = link.get_sink()
        self.source = link.get_source()
        # 从判断首开始执行，递归查找到最上头时序
        print(f"query = {query}")
        link0 = None

        try:
            # 使用NeoUtil获取链接
            link0 = NeoUtil.get_link_cypher(query)

            if link0 is not None:
                head_sink = link0.get_sink()
                c_link_list = []
                done_num = 0
                self.cosc_list = []

                done = DoSelectTreeTask.get_result(link, head_sink, c_link_list, done_num, self.cosc_list)

                # todo 各种复杂条件嵌套，与或非是认知层，目前硬编码，先低层次小范围试错
                # 如果满足当前条件集，则直接输出当前，不继续进行下面判断，上面没满足，需要继续，直到满足或完成
                if ((done.done_num == len(c_link_list) and done.conect_str == "且") or
                    (done.done_num == 1 and done.conect_str == "或") or
                    (done.done_num == 1 and len(c_link_list) == 1)):

                    if done.conect is not None:
                        self.cosc_list.append(done.conect)

                        sb0 = DoSelectTreeTask.get_string_builder_safe(self.cosc_list)

                        if sb0:
                            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                            AgentStarter.nar.add_input_to(f"(^say,{{SELF}},(#,{sb0}))! :|:", self.goal_ns)
                            print(f"---------判断首和判断---|||---s-a-y----{sb0}")
                        else:
                            print(f"---------判断首和判断----s-a-y----空--------------{link0}")

                            # 自助激活语法
                            # GrammarTask task = new GrammarTask(yufaNs, sceneNs,1,pam)
                            # pam.getAssistingTaskSpawner().addTask(task)

                            print(f"目前任务总数-------------》 {len(DoSelectTreeTask.PAM.get_assisting_task_spawner().get_tasks())}")
                            AgentStarter.is_do_var = True
                            AgentStarter.do_start_tick = TaskManager.get_current_tick()

                            for scene_link in self.scene_ns.get_links():
                                scene_link.set_activation(scene_link.get_activation() + 0.4)
                                isa_source = scene_link.get_source()
                                isa_sink = scene_link.get_sink()
                                isa_source.set_activation(isa_source.get_activation() + 0.3)
                                isa_sink.set_activation(isa_sink.get_activation() + 0.3)

                            print("选择结构执行时序中---|||-SelectTreeTask")

                            # 接下来是执行判断结构体内的时序，设置成任务，有效跟当前待生成错开时间
                            from linars.edu.memphis.ccrg.lida.PAM.Tasks.DoSelectSeqTask import DoSelectSeqTask
                            select_seq_task = DoSelectSeqTask(link0, DoSelectTreeTask.pam, 80, self.act_stamp)
                            DoSelectTreeTask.PAM.get_assisting_task_spawner().add_task(select_seq_task)
                    else:
                        # 从判断首开始顺着时序链执行，link0是各条判断边
                        query = f"match (n:场景)-[r:判断]->(m:场景)<-[r0:顺承]-(i:场景) where n.name = '{link0.get_source().get_tn_name()}'  and i.name = '{link0.get_sink().get_tn_name()}' return r"

                        self.do_select_root(link0, query)
                else:
                    # 没有连续判断 else if 后的最后else，实际上还是一堆判断，且要汇总上面的所有反面，跟else if还是有区别
                    # 不能直接说"否则"，"否则"是概括描述时简略说法，或者代码上的简略写法，实际描述需要有理有据，离不开"因为所以"
                    # 但可以省略部分判断，直接否定else之前的所有判断结构即可，一个if或else if可以只需一部分即可否定
                    query = f"match (n:场景)-[r:else]->(m:场景)<-[r0:顺承]-(i:场景) where n.name = '{self.source.get_tn_name()}'  and i.name = '{self.sink.get_tn_name()}' return r"

                    link0 = NeoUtil.get_one_link_tx(query, session)

                    if link0 is not None:
                        DoSelectTreeTask.PAM.add_default_node(link0.get_sink())
                        DoSelectTreeTask.PAM.set_scene_main_node(link0.get_sink())
                        co_num = 0
                        sb = []
                        for link_id in DoSelectTreeTask.else_map.keys():
                            for cs_link in DoSelectTreeTask.else_map[link_id]:
                                if cs_link.get_id() == link_id:
                                    co_num += 1
                                    category = PamNodeImpl()
                                    category.set_node_name(f"条件{co_num}")

                                    # 新建各条件到else节点的链接，并激活相关语法，尾节点是else边尾节点
                                    else_link = DoSelectTreeTask.PAM.add_default_link(cs_link.get_source(), link0.get_sink(), category)

                                    sn = else_link.get_source().get_tn_name()
                                    if "(" in sn:
                                        pass
                                    elif co_num != len(DoSelectTreeTask.else_map[link_id]):
                                        sb.append(f"{sn},")
                                    else:
                                        sb.append(sn)
                                else:
                                    # 非条件边，直接激活语法
                                    sn = cs_link.get_source().get_tn_name()
                                    if "(" in sn:
                                        pass
                                    elif co_num != len(DoSelectTreeTask.else_map[link_id]):
                                        sb.append(f"{sn},")
                                    else:
                                        sb.append(sn)

                        # sb不为null或者空字符串，则输出say
                        if sb:
                            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                            AgentStarter.nar.add_input_to(f"(^say,{{SELF}},(#,{''.join(sb)}))! :|:", self.goal_ns)
                            print(f"------else---判断首和判断---|||---s-a-y----{''.join(sb)}")
                        else:
                            print(f"------else---判断首和判断----s-a-y----空--------------{link0}")

                        if len(DoSelectTreeTask.else_map) > 1:
                            print("---------------else条件数量大于1------------------")
                            # todo 复杂的与或非else，需要硬编码很多

                        # 自助激活语法
                        # GrammarTask task = new GrammarTask(yufaNs, sceneNs,1,pam)
                        # pam.getAssistingTaskSpawner().addTask(task)

                        print("else执行时序中---|||-SelectTreeTask")

                        # 接下来是执行判断结构体内的时序，设置成任务，有效跟当前待生成错开时间，以else边为准往下执行
                        from linars.edu.memphis.ccrg.lida.PAM.Tasks.DoSelectSeqTask import DoSelectSeqTask
                        select_seq_task = DoSelectSeqTask(link0, DoSelectTreeTask.pam, 60, self.act_stamp)
                        DoSelectTreeTask.PAM.get_assisting_task_spawner().add_task(select_seq_task)

                        print(f"目前任务总数-------------》 {len(DoSelectTreeTask.PAM.get_assisting_task_spawner().get_tasks())}")

                        AgentStarter.is_do_var = True
                        AgentStarter.do_start_tick = TaskManager.get_current_tick()
                    else:
                        # 执行条件=没有else+结束判断遍历，else不会与回溯同时进行
                        seq_main_path = []
                        is_only_if = False
                        # 判断时序执行完和判断遍历完，只会出现一个，时序执行完后，直接回溯上位时序，而非上位判断边
                        seq_main_path = self.seq_ns.get_do_main_path_map().get(self.act_stamp)
                        if seq_main_path is not None and seq_main_path:
                            p_link1 = seq_main_path[len(seq_main_path) - 1]
                            # 判断边是连续互斥else if，而连续独立if else是连续时序
                            if p_link1.get_category() == "判断" or p_link1.get_category() == "判断首":
                                if len(seq_main_path) > 1:
                                    p_link1 = seq_main_path[len(seq_main_path) - 2]
                                    seq_main_path.pop()
                                else:
                                    is_only_if = True

                            if not is_only_if:
                                # 回溯后按常规往下执行
                                from linars.edu.memphis.ccrg.lida.PAM.Tasks.DoSuccTask import DoSuccTask
                                do_succ_task = DoSuccTask(p_link1.get_sink(), p_link1.get_source(), DoSelectTreeTask.pam, 30, self.act_stamp)
                                DoSelectTreeTask.PAM.get_assisting_task_spawner().add_task(do_succ_task)

                            seq_main_path.pop()

                        print("------------------各判断都不满足，或者查询出错---------------------")
        except Exception as e:
            self.logger.error(f"Error in do_select_root: {e}")

    class Done:
        """
        A class to represent the result of a selection.
        """

        def __init__(self, done_num: int, conect: Optional[Link], conect_str: str):
            """
            Initialize a Done.

            Args:
                done_num: The number of done items
                conect: The connection link
                conect_str: The connection string
            """
            self.done_num = done_num
            self.conect = conect
            self.conect_str = conect_str

    class Compare:
        """
        A class to compare values.
        """

        def __init__(self, done_num: int, half_list: List[Link], con_link: Link, shi_shi: str,
                    shou_shi: str, no_shi_var: bool, no_shou_var: bool, is_done: bool):
            """
            Initialize a Compare.

            Args:
                done_num: The number of done items
                half_list: The half list
                con_link: The connection link
                shi_shi: The shi shi
                shou_shi: The shou shi
                no_shi_var: Whether there is no shi var
                no_shou_var: Whether there is no shou var
                is_done: Whether it is done
            """
            self.done_num = done_num
            self.half_list = half_list
            self.con_link = con_link
            self.shi_shi = shi_shi
            self.shou_shi = shou_shi
            self.no_shi_var = no_shi_var
            self.no_shou_var = no_shou_var
            self.is_done = is_done

        def get_done_num(self) -> int:
            """
            Get the number of done items.

            Returns:
                The number of done items
            """
            return self.done_num

        def is_is_done(self) -> bool:
            """
            Check if it is done.

            Returns:
                Whether it is done
            """
            return self.is_done

        def invoke(self, verb: str) -> 'DoSelectTreeTask.Compare':
            """
            Invoke the comparison.

            Args:
                verb: The verb

            Returns:
                The updated Compare
            """
            is_else = False
            shi_shi0 = int(self.shi_shi)
            shou_shi0 = int(self.shou_shi)
            # 判断变量是否有现值，没有则无法比较，直接归为不满足
            # todo 概括描述无需现值，不是不满足，也没满足。
            #  两者为零，可能只是初始值，非运行后现值，如当前进位
            if ((self.no_shi_var and self.no_shou_var) or
                (self.no_shou_var and not self.no_shi_var) or
                (not self.no_shou_var and self.no_shi_var) or
                (shi_shi0 == 0 and shou_shi0 == 0)):
                is_else = True
            elif shi_shi0 > shou_shi0:
                self.is_done = True
                self.done_num += 1

            if is_else:
                DoSelectTreeTask.else_size += 1
                for cs_link in self.half_list:
                    if cs_link.get_tn_name() == "动作" or cs_link.get_tn_name() == "arg1":
                        # 存入反命题，可能负负得正
                        cs_link.get_source().set_node_name(verb)
                    DoSelectTreeTask.else_links.append(cs_link)

                # else部分：【或】不用存任何，【与】要存至少一个不满足条件，【非】附属于与或，单独非要存不满足
                # 满足部分：与存所有，或存一个，非附属，单独非存满足，与短路
                DoSelectTreeTask.else_links.append(self.con_link)
                DoSelectTreeTask.else_map[self.con_link.get_id()] = DoSelectTreeTask.else_links

            return self

    @staticmethod
    def get_result(link: Link, head_sink: Node, c_link_list: List[Link], done_num: int, cosc_list: List[Link]) -> 'DoSelectTreeTask.Done':
        """
        Get the result of a selection.

        Args:
            link: The link
            head_sink: The head sink
            c_link_list: The c link list
            done_num: The number of done items
            cosc_list: The cosc list

        Returns:
            The result
        """
        link_type = ""
        for i in range(1, 3):
            link_type = f"条件{i}"
            c_link = NeoUtil.get_one_link(head_sink, link_type, "<", "场景", "场景")
            if c_link is not None:
                c_link_list.append(c_link)

        conect = NeoUtil.get_one_link(head_sink, "关系12", "<", "场景", None)

        if c_link_list:
            done_num = DoSelectTreeTask.get_done_num(link, c_link_list, done_num, conect, cosc_list)

        conect_str = ""
        if conect is not None:
            conect_str = conect.get_source().get_tn_name()

        result = DoSelectTreeTask.Done(done_num, conect, conect_str)
        return result

    @staticmethod
    def get_done_num(link: Link, c_link_list: List[Link], done_num: int, conect: Optional[Link], cosc_list: List[Link]) -> int:
        """
        Get the number of done items.

        Args:
            link: The link
            c_link_list: The c link list
            done_num: The number of done items
            conect: The connection link
            cosc_list: The cosc list

        Returns:
            The number of done items
        """
        conscene = set()
        half_list = []
        conect_str = ""
        if conect is not None:
            conect_str = conect.get_source().get_tn_name()

        for con_link in c_link_list:
            conscene = NeoUtil.get_some_links(con_link.get_source(), None, "<", "场景", None)
            type_str = ""
            source = None
            sink = None
            shi_shi = ""
            shou_shi = ""
            verb = ""
            shi_none = False
            shou_none = False
            no_shi_var = False
            no_shou_var = False
            half_list = []
            is_done = False

            DoSelectTreeTask.PAM.add_default_node(con_link.get_sink())
            DoSelectTreeTask.PAM.add_default_link(con_link)

            for cs_link in conscene:
                source = cs_link.get_source()
                sink = cs_link.get_sink()
                # 条件场景多个元素，还是要分个类
                DoSelectTreeTask.PAM.put_map(source, source.get_tn_name())
                DoSelectTreeTask.PAM.put_map(sink, sink.get_tn_name())
                type_str = cs_link.get_tn_name()

                DoSelectTreeTask.PAM.add_default_node(cs_link.get_sink())
                DoSelectTreeTask.PAM.add_default_link(cs_link)
                # 如数一数、位数
                from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                if source.get_tn_name() in AgentStarter.var_map:
                    result_map = DoSelectTreeTask.PAM.get_isa_link(source, cs_link.get_sink(), cs_link.get_category(), DoSelectTreeTask.pam)
                    if result_map.get("done") == "yes":
                        cs_link = result_map.get("link")
                        # todo 有些初始化了，另外空和o是不同的，空是没有，o是有，但是没有值
                    else:
                        # todo 概括方法论描述中，也没有变量值
                        if type_str == "arg0":
                            no_shi_var = True
                        elif type_str in ["arg2", "arg3"]:
                            no_shou_var = True

                if type_str == "arg0":
                    shi_shi = cs_link.get_source().get_tn_name()
                elif type_str in ["arg2", "arg3"]:  # 类事和受事二选一，什么是什么，什么做什么
                    shou_shi = cs_link.get_source().get_tn_name()
                elif type_str == "arg1":
                    verb = cs_link.get_source().get_tn_name()

                # todo 与或非 不太一样，或只需存一部分，与需要存所有，非需要所有（但可附属于与或）
                # 先存放一部分，等后半部分条件判断完成，是否加入待生成
                half_list.append(cs_link)

            compare = DoSelectTreeTask.Compare(done_num, half_list, con_link, shi_shi, shou_shi, no_shi_var, no_shou_var, is_done)
            # 初始化，每次判断都是空列表，全局是跨方法需求
            DoSelectTreeTask.else_links = []

            if shi_shi and shou_shi and verb:
                verb0 = ""
                if verb == "为":
                    if shi_shi == shou_shi:
                        is_done = True
                        done_num += 1
                    else:
                        DoSelectTreeTask.else_size += 1
                        cs_link_type = ""
                        for cs_link in half_list:
                            cs_link_type = cs_link.get_tn_name()
                            if cs_link_type == "动作" or cs_link_type == "arg1":
                                cs_link.get_source().set_node_name("不为")
                            DoSelectTreeTask.else_links.append(cs_link)
                        DoSelectTreeTask.else_links.append(con_link)
                        DoSelectTreeTask.else_map[con_link.get_id()] = DoSelectTreeTask.else_links
                elif verb == "不为":
                    if shi_shi != shou_shi:
                        is_done = True
                        done_num += 1
                    else:
                        DoSelectTreeTask.else_size += 1
                        cs_link_type = ""
                        for cs_link in half_list:
                            cs_link_type = cs_link.get_tn_name()
                            if cs_link_type == "动作" or cs_link_type == "arg1":
                                cs_link.get_source().set_node_name("为")
                            DoSelectTreeTask.else_links.append(cs_link)
                        DoSelectTreeTask.else_links.append(con_link)
                        DoSelectTreeTask.else_map[con_link.get_id()] = DoSelectTreeTask.else_links
                elif verb == "大于":
                    verb0 = "不大于"
                    compare = compare.invoke(verb0)
                    done_num = compare.get_done_num()
                    is_done = compare.is_is_done()
                elif verb == "小于":
                    verb0 = "不小于"
                    compare = compare.invoke(verb0)
                    done_num = compare.get_done_num()
                    is_done = compare.is_is_done()
                elif verb == "不大于":
                    verb0 = "大于"
                    compare = compare.invoke(verb0)
                    done_num = compare.get_done_num()
                    is_done = compare.is_is_done()
                elif verb == "小于等于":
                    verb0 = "大于"
                    compare = compare.invoke(verb0)
                    done_num = compare.get_done_num()
                    is_done = compare.is_is_done()
                elif verb == "不小于":
                    verb0 = "小于"
                    compare = compare.invoke(verb0)
                    done_num = compare.get_done_num()
                    is_done = compare.is_is_done()
                elif verb == "大于等于":
                    verb0 = "小于"
                    compare = compare.invoke(verb0)
                    done_num = compare.get_done_num()
                    is_done = compare.is_is_done()
                elif verb == "等于":
                    verb0 = "不等于"
                    compare = compare.invoke(verb0)
                    done_num = compare.get_done_num()
                    is_done = compare.is_is_done()
                elif verb == "不等于":
                    verb0 = "等于"
                    compare = compare.invoke(verb0)
                    done_num = compare.get_done_num()
                    is_done = compare.is_is_done()

            # 与短路，只要有一个不满足则后面的不用判断了，或只要一个满足即可
            if conect_str == "且":
                if not is_done:
                    break
                cosc_list.append(con_link)
                cosc_list.extend(half_list)
            elif conect_str == "或" and is_done:
                cosc_list.append(con_link)
                cosc_list.extend(half_list)
            elif conect is None:
                cosc_list.append(con_link)
                cosc_list.extend(half_list)

        return done_num

    @staticmethod
    def get_string_builder_safe(cosc_list: List[Link]) -> str:
        """
        Get a safe string builder.

        Args:
            cosc_list: The cosc list

        Returns:
            The string builder
        """
        string_builder = []
        current_index = 0
        copy_of_cosc_list = list(cosc_list)  # 创建cosclist的快照以防并发修改

        for current_link in copy_of_cosc_list:
            source = current_link.get_source()
            if source is None:  # 防止空指针异常
                continue
            source_name = source.get_tn_name()
            if "(" in source_name:
                continue
            string_builder.append(source_name)
            if current_index != len(copy_of_cosc_list) - 1:
                string_builder.append(",")
            current_index += 1

        return "".join(string_builder)
