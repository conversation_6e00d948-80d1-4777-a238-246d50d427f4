# LIDA Cognitive Framework
"""
Default implementation of Condition.
"""

import logging
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.ActivatibleImpl import ActivatibleImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Initialization.InitializableImpl import InitializableImpl
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Condition import Condition

class ConditionImpl(ActivatibleImpl, InitializableImpl, Condition):
    """
    Default implementation of Condition.
    """

    def __init__(self, name: str = None):
        """
        Initialize a ConditionImpl.

        Args:
            name: The name of this condition
        """
        super().__init__()
        self.condition_name = name
        self.incentive_salience = 0.0
        self.logger = logging.getLogger(self.__class__.__name__)

    def get_condition_id(self) -> str:
        """
        Get the ID of this condition.

        Returns:
            The ID of this condition
        """
        return self.condition_name

    def is_satisfied(self, content: NodeStructure) -> bool:
        """
        Check if this condition is satisfied by the given content.

        Args:
            content: The content to check

        Returns:
            True if this condition is satisfied, False otherwise
        """
        # This is a simple implementation that always returns True
        # Subclasses should override this method to provide custom functionality
        return True

    def get_condition_name(self) -> str:
        """
        Get the name of this condition.

        Returns:
            The name of this condition
        """
        return self.condition_name

    def set_condition_name(self, name: str) -> None:
        """
        Set the name of this condition.

        Args:
            name: The name to set
        """
        self.condition_name = name

    def init(self, params: Dict[str, Any]) -> None:
        """
        Initialize this condition with the given parameters.

        Args:
            params: Parameters for initialization
        """
        super().init(params)
        if "conditionName" in params:
            self.condition_name = params["conditionName"]
        if "incentiveSalience" in params:
            self.set_incentive_salience(float(params["incentiveSalience"]))

    def __eq__(self, other) -> bool:
        """
        Check if this condition is equal to another.

        Args:
            other: The other condition to compare with

        Returns:
            True if the conditions have the same name, False otherwise
        """
        if isinstance(other, ConditionImpl):
            return self.condition_name == other.get_condition_name()
        return False

    def __hash__(self) -> int:
        """
        Return the hash code of this condition.

        Returns:
            The hash code of the condition name
        """
        return hash(self.condition_name)

    def __str__(self) -> str:
        """
        Return the string representation of this condition.

        Returns:
            The name of this condition
        """
        return self.condition_name

    def get_incentive_salience(self) -> float:
        """
        Get the incentive salience of this condition.

        Returns:
            The incentive salience of this condition
        """
        return self.incentive_salience

    def set_incentive_salience(self, salience: float) -> None:
        """
        Set the incentive salience of this condition.

        Args:
            salience: The incentive salience to set
        """
        if salience > 1.0:
            self.incentive_salience = 1.0
        elif salience < -1.0:
            self.incentive_salience = -1.0
        else:
            self.incentive_salience = salience

    def get_total_incentive_salience(self) -> float:
        """
        Get the total incentive salience of this condition.

        Returns:
            The total incentive salience of this condition
        """
        return self.get_incentive_salience()

    def is_removable(self) -> bool:
        """
        Check if this condition is removable.

        Returns:
            True if this condition is removable, False otherwise
        """
        return (self.get_activation() <= self.get_removal_threshold() and
                abs(self.get_incentive_salience()) <= self.get_removal_threshold())
