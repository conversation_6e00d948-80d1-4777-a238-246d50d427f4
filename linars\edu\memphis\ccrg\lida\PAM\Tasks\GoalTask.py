#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to add a PamLink and its sink to the percept.
"""

from typing import Set
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil

class GoalTask(FrameworkTaskImpl):
    """
    A task to add a PamLink and its sink to the percept.
    
    See Also:
        ExcitationTask creates this task
        Propagation<PERSON><PERSON> creates this task
    """
    
    def __init__(self, sink: Node, pam: PAMemory, seq_ns: NodeStructure, non_ns: NodeStructure,
                goal_ns: NodeStructure, is_want: bool, task_type: str):
        """
        Initialize a GoalTask.
        
        Args:
            sink: The sink node
            pam: The PAMemory
            seq_ns: The sequence NodeStructure
            non_ns: The non-conscious NodeStructure
            goal_ns: The goal NodeStructure
            is_want: Whether this is a want goal
            task_type: The type of task
        """
        super().__init__(1, task_type)
        self.pam = pam
        self.sink = sink
        self.seq_ns = seq_ns
        self.non_ns = non_ns
        self.goal_ns = goal_ns
        self.is_want = is_want
        
    def run_this_framework_task(self):
        """
        Adds link's sink to the percept and tries to add the link as well then finishes.
        """
        self.cancel()
        
    def run_this_framework_task0(self):
        """
        Original implementation of runThisFrameworkTask.
        """
        sname = self.sink.get_tn_name()
        links = NeoUtil.get_some_links(self.sink, None, None, None, "变量")
        for link in links:
            for l in self.seq_ns.get_links_of_source(link.get_source().get_tn_name()):
                if l.get_tn_name() == "nowisa":
                    # 目前扩散激活不包括neo以外的新连接，头尾节点会各自扩散激活，节点激活值
                    l.set_activation(l.get_activation() + 0.3)
                    self.pam.put_map(l.get_sink(), l.get_sink().get_tn_name())
                    
                    # 就只加入无意识？暂无时序、自我、情感、动机等buffer需求
                    self.pam.get_listener().receive_percept(l.get_sink(), ModuleName.NonGraph)
                    self.pam.get_listener().receive_percept(l.get_source(), ModuleName.NonGraph)
                    self.pam.get_listener().receive_percept(l, ModuleName.NonGraph)
                    
                    # from0 = "varisa"
                    # if self.is_want:
                    # 欲求的变量， 欲求的动作，分别匹配汇合，深度2 = 只执行一次？
                    # self.propagate_activation_to_parents(l.get_source(), 1, "varwant_var")
                    print(f"{l} ---varwant_var--- --------- deep {1}")
                    
                    l.get_source().set_activation(l.get_source().get_activation() + 0.4)
                    
                    # todo 没有欲求，只有时序转动机，转=需要先天激励或动机经验？
                    # 来自欲求变量的激活，且激活的是场景，则判断是否有欲求动作激活
                    # if from == "varwant_var" and AgentStarter.scenemap.containsKey(sname):
                    
                    # sLinks = NeoUtil.get_some_links(l.get_source(), None, None, None, "场景")
                    # todo 这里少了一层遍历 pam的 for (Link parent : parentLinkSet)
                    node_id = 0
                    for slink in self.non_ns.get_links_of_sink(sname):
                        node_id = slink.get_source().get_node_id()
                        if slink.get_category().get_name() == "动作" and self.goal_ns.contains_node(node_id):
                            # 根据欲求的动作，答说 [1.00] ---动作 [.80]--->ft111 [1.00]
                            for link0 in self.goal_ns.get_links_of_source(node_id):
                                # if sname == link0.get_sink().get_name(): continue
                                for link2 in self.non_ns.get_links_of_sink(link0.get_sink().get_tn_name()):
                                    # 找到欲求节点，给实例化欲求赋值激励，ft10 [1.00] ---欲求 [.90]--->ft111 [1.00]
                                    if link2.get_category().get_name() == "欲求":
                                        for link1 in self.non_ns.get_links_of_source(sname):
                                            if link1.get_category().get_name() == "心理计划":
                                                invent = link2.get_sink().get_incentive_salience()
                                                
                                                if invent == 0.0: continue
                                                if self.goal_ns.contains_link(slink): continue  # 已经执行过一次
                                                
                                                # sink实例，ft151，ft150 = 不会进入这里，link的sink就是parent的sink
                                                self.sink.set_incentive_salience(invent)
                                                # 动作与实例的链接，加入目标buffer完成实例化，sink是动作link的尾节点
                                                self.pam.get_listener().receive_percept(self.sink, ModuleName.GoalGraph)
                                                self.pam.get_listener().receive_percept(slink, ModuleName.GoalGraph)
                                                
                                                link1.get_sink().set_incentive_salience(invent)
                                                # 加入计划，以备执行，头尾节点都要，当前parent的sink是头节点，如ft151
                                                self.pam.get_listener().receive_percept(self.sink, ModuleName.SeqGraph)
                                                self.pam.get_listener().receive_percept(link1.get_sink(), ModuleName.SeqGraph)
                                                self.pam.get_listener().receive_percept(link1, ModuleName.SeqGraph)
                                                # 实例化动机计划建模，不扩散激活，以免摊开太大，且要约束很多
                                                self.pam.get_act_root(link1, False, False, None)
                    
                    # else:
                    # 从变量开始激活，长传递链也能传递，变量--nowisa--》实例
                    # self.propagate_activation_to_parents(l.get_source(), deep, from0)
                    # print(f"{l} ---varisa_var--- --------- deep {deep}")
                    
                    link_set = NeoUtil.get_some_links(self.sink, "动作", None, None, None)
                    for link0 in link_set:
                        # 直接激活、加入等一串pam常规操作
                        self.pam.put_map(link0.get_sink(), link0.get_sink().get_tn_name())
                        # 就只加入无意识？
                        self.pam.get_listener().receive_percept(link0.get_sink(), ModuleName.NonGraph)
                        self.pam.get_listener().receive_percept(link0.get_source(), ModuleName.NonGraph)
                        self.pam.get_listener().receive_percept(link0, ModuleName.NonGraph)
                        
                        if self.is_want:
                            # self.propagate_activation_to_parents(link0.get_source(), 1, "varwant_verb")
                            # link_set = NeoUtil.get_some_links(link0.get_source(), "动作", None, None, None)
                            
                            print(f"{link0} ---varwant_verb--- ------ deep {1}")
                            
                            link0.get_source().set_activation(link0.get_source().get_activation() + 0.4)
                            
                        # else:
                        # self.propagate_activation_to_parents(link0.get_source(), deep, from0)
                        # print(f"{link0} ---varisa_verb--- ---- deep {deep}")
        
        self.cancel()
