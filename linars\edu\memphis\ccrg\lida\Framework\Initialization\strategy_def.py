"""
Strategy definition.

This module provides a class for defining strategies.
"""
import importlib
from typing import Dict, Any, Optional, Type

class StrategyDef:
    """
    Strategy definition.
    
    This class defines a strategy.
    """
    
    def __init__(self, name: str, strategy_class_name: str):
        """
        Initialize the strategy definition.
        
        Args:
            name: The name of the strategy
            strategy_class_name: The class name of the strategy
        """
        self.name = name
        self.strategy_class_name = strategy_class_name
    
    def get_name(self) -> str:
        """
        Get the name of the strategy.
        
        Returns:
            The name of the strategy
        """
        return self.name
    
    def get_strategy_class_name(self) -> str:
        """
        Get the class name of the strategy.
        
        Returns:
            The class name of the strategy
        """
        return self.strategy_class_name
    
    def get_strategy_class(self) -> Optional[Type]:
        """
        Get the strategy class.
        
        Returns:
            The strategy class or None if not found
        """
        try:
            # Split the class name into package and class
            parts = self.strategy_class_name.split(".")
            class_name = parts[-1]
            package_name = ".".join(parts[:-1])
            
            # Import the module
            module = importlib.import_module(package_name)
            
            # Get the class
            class_obj = getattr(module, class_name)
            
            return class_obj
        except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AttributeError, Exception):
            return None
