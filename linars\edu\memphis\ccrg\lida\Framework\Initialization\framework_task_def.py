"""
Framework task definition.

This module provides a class for defining framework tasks.
"""
import importlib
from typing import Dict, Any, Optional, Type

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTask import FrameworkTask

class FrameworkTaskDef:
    """
    Framework task definition.

    This class defines a framework task.
    """

    def __init__(self, name: str, task_class_name: str, ticks_per_run: str):
        """
        Initialize the framework task definition.

        Args:
            name: The name of the task
            task_class_name: The class name of the task
            ticks_per_run: The number of ticks per run
        """
        self.name = name
        self.task_class_name = task_class_name
        self.ticks_per_run = int(ticks_per_run) if ticks_per_run else 1

    def get_name(self) -> str:
        """
        Get the name of the task.

        Returns:
            The name of the task
        """
        return self.name

    def get_task_class_name(self) -> str:
        """
        Get the class name of the task.

        Returns:
            The class name of the task
        """
        return self.task_class_name

    def get_ticks_per_run(self) -> int:
        """
        Get the number of ticks per run.

        Returns:
            The number of ticks per run
        """
        return self.ticks_per_run

    def get_task_class(self) -> Optional[Type[FrameworkTask]]:
        """
        Get the task class.

        Returns:
            The task class or None if not found
        """
        try:
            # Split the class name into package and class
            parts = self.task_class_name.split(".")
            class_name = parts[-1]
            package_name = ".".join(parts[:-1])

            # Import the module
            module = importlib.import_module(package_name)

            # Get the class
            class_obj = getattr(module, class_name)

            return class_obj
        except (ImportError, AttributeError, Exception):
            return None
