"""
Utilities for XML processing.

This module provides utilities for loading and processing XML files.
"""
import logging
import xml.etree.ElementTree as ET
import os
import sys
from typing import Optional, Dict, Any, List

class XmlUtils:
    """
    Utilities for XML processing.

    This class provides static methods for loading and processing XML files.
    """

    logger = logging.getLogger(__name__)

    @staticmethod
    def load_xml_file(file_path: str) -> Optional[ET.Element]:
        """
        Load an XML file and return its root element.

        Args:
            file_path: Path to the XML file

        Returns:
            The root element of the XML file or None if loading fails
        """
        # List of possible locations to check
        import os
        import sys

        # Get the project root directory
        current_dir = os.path.abspath(__file__)
        for _ in range(7):  # Go up 7 levels to reach the project root
            current_dir = os.path.dirname(current_dir)
        project_root = current_dir

        # Get the main script directory
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller creates a temp folder and stores path in _MEIPASS
            script_dir = sys._MEIPASS
        elif '__file__' in globals():
            script_dir = os.path.dirname(os.path.abspath(__file__))
        else:
            script_dir = os.getcwd()

        # Build a list of possible paths to check
        possible_paths = [
            file_path,  # Original path
            os.path.join(os.getcwd(), file_path),  # Relative to current working directory
            os.path.join(project_root, file_path),  # Relative to project root
            os.path.join(os.path.dirname(project_root), file_path),  # One level up from project root
            os.path.join(script_dir, file_path),  # Relative to script directory
            os.path.join(os.path.dirname(script_dir), file_path),  # One level up from script directory
            os.path.join(os.path.dirname(os.path.dirname(script_dir)), file_path),  # Two levels up from script directory
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(script_dir))), file_path),  # Three levels up from script directory
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(script_dir)))), file_path),  # Four levels up from script directory
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(script_dir))))), file_path),  # Five levels up from script directory
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(script_dir)))))), file_path),  # Six levels up from script directory
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(script_dir))))))), file_path),  # Seven levels up from script directory
        ]

        # Add absolute path to configs directory at the project root
        configs_at_root = os.path.join(project_root, 'configs')
        if os.path.basename(file_path) == file_path:  # If file_path is just a filename without directories
            possible_paths.append(os.path.join(configs_at_root, os.path.basename(file_path)))

        # Remove duplicates while preserving order
        unique_paths = []
        for path in possible_paths:
            if path not in unique_paths:
                unique_paths.append(path)

        # Try each path
        for path in unique_paths:
            try:
                # XmlUtils.logger.info(f"Trying to load XML file from: {path}")
                tree = ET.parse(path)
                # XmlUtils.logger.info(f"Successfully loaded XML file from: {path}")
                return tree.getroot()
            except FileNotFoundError:
                continue  # Try the next path
            except ET.ParseError as e:
                XmlUtils.logger.error(f"Error parsing XML file {path}: {e}")
                return None
            except Exception as e:
                XmlUtils.logger.error(f"Error loading XML file {path}: {e}")
                continue  # Try the next path

        # If we get here, none of the paths worked
        XmlUtils.logger.error(f"XML file not found in any of the possible locations: {file_path}")
        return None

    @staticmethod
    def get_attribute(element: ET.Element, attribute_name: str, default_value: str = None) -> str:
        """
        Get an attribute from an XML element.

        Args:
            element: The XML element
            attribute_name: The name of the attribute
            default_value: The default value to return if the attribute is not found

        Returns:
            The attribute value or the default value if not found
        """
        return element.get(attribute_name, default_value)

    @staticmethod
    def get_child_elements(element: ET.Element, tag_name: str) -> List[ET.Element]:
        """
        Get all child elements with the specified tag name.

        Args:
            element: The parent XML element
            tag_name: The tag name to search for

        Returns:
            A list of matching child elements
        """
        # Register namespaces
        namespaces = {
            'lida': 'http://ccrg.cs.memphis.edu/LidaXMLSchema',
            'xsi': 'http://www.w3.org/2001/XMLSchema-instance'
        }

        # Try direct findall first
        result = element.findall(tag_name)

        # If no results, try with namespace
        if not result:
            result = element.findall(f"lida:{tag_name}", namespaces)

        # If no results, try with ./ prefix for direct children
        if not result:
            result = element.findall(f".//{tag_name}")

        # If still no results, try with namespace and ./ prefix
        if not result:
            result = element.findall(f".//lida:{tag_name}", namespaces)

        # If still no results, try with * for any namespace
        if not result:
            try:
                result = element.findall(f".//*[local-name()='{tag_name}']")
            except SyntaxError:
                # Fallback for parsers that don't support local-name()
                for child in element.findall(".//*"):
                    tag = child.tag
                    if '}' in tag:
                        tag = tag.split('}', 1)[1]  # Remove namespace
                    if tag == tag_name:
                        result.append(child)

        # If still no results, try direct children with any namespace
        if not result:
            for child in element:
                if child.tag.endswith(f"}}{tag_name}") or child.tag == tag_name:
                    result.append(child)

        return result

    @staticmethod
    def get_element_text(element: ET.Element, default_value: str = "") -> str:
        """
        Get the text content of an XML element.

        Args:
            element: The XML element
            default_value: The default value to return if the element has no text

        Returns:
            The element's text content or the default value if none
        """
        text = element.text
        return text.strip() if text else default_value

    @staticmethod
    def get_children(element: ET.Element, tag_name: str) -> List[ET.Element]:
        """
        Get all child elements with the specified tag name.

        This is an alias for get_child_elements for compatibility with Java code.

        Args:
            element: The parent XML element
            tag_name: The tag name to search for

        Returns:
            A list of matching child elements
        """
        return XmlUtils.get_child_elements(element, tag_name)

    @staticmethod
    def get_text_value(element: ET.Element, tag_name: str, default_value: str = None) -> str:
        """
        Get the text content of a child element with the specified tag name.

        Args:
            element: The parent XML element
            tag_name: The tag name of the child element
            default_value: The default value to return if the child element is not found

        Returns:
            The text content of the child element or the default value if not found
        """
        children = XmlUtils.get_child_elements(element, tag_name)
        if children and len(children) > 0:
            return XmlUtils.get_element_text(children[0])
        return default_value

    @staticmethod
    def parse_xml_file(file_path: str, schema_path: str = None) -> Optional[ET.Element]:
        """
        Parse an XML file and return its root element.

        This is an alias for load_xml_file for compatibility with Java code.

        Args:
            file_path: Path to the XML file
            schema_path: Path to the XML schema file (not used in this implementation)

        Returns:
            The root element of the XML file or None if parsing fails
        """
        try:
            # Register namespaces for ElementTree
            ET.register_namespace('lida', 'http://ccrg.cs.memphis.edu/LidaXMLSchema')
            ET.register_namespace('xsi', 'http://www.w3.org/2001/XMLSchema-instance')

            # Parse the XML file directly
            tree = ET.parse(file_path)
            root = tree.getroot()

            # Log the XML structure for debugging
            logging.debug(f"Parsed XML file: {file_path}")
            logging.debug(f"XML root tag: {root.tag}")
            logging.debug(f"XML root attributes: {root.attrib}")
            logging.debug(f"XML structure:\n{XmlUtils.debug_element(root)}")

            # Define namespaces for XPath
            namespaces = {
                'lida': 'http://ccrg.cs.memphis.edu/LidaXMLSchema',
                'xsi': 'http://www.w3.org/2001/XMLSchema-instance'
            }

            # Check for globalparams element using XPath
            globalparams = root.findall('.//lida:globalparams', namespaces)
            if globalparams:
                logging.debug(f"Found {len(globalparams)} globalparams elements")
                for i, gp in enumerate(globalparams):
                    logging.debug(f"Globalparams[{i}] structure:\n{XmlUtils.debug_element(gp)}")

                    # Check for param elements using XPath
                    params = gp.findall('.//lida:param', namespaces)
                    logging.debug(f"Found {len(params)} param elements in {gp.tag}")
                    for j, param in enumerate(params):
                        logging.debug(f"Param[{j}] attributes: {param.attrib}, text: {param.text}")
            else:
                logging.warning(f"No globalparams element found in {file_path}")

                # Try to find param elements directly in the root using XPath
                params = root.findall('.//lida:param', namespaces)
                logging.debug(f"Found {len(params)} param elements directly in root")

            return root
        except Exception as e:
            logging.error(f"Failed to parse XML file: {file_path}. Error: {e}")
            import traceback
            logging.error(f"Traceback: {traceback.format_exc()}")
            return None

    @staticmethod
    def get_typed_params(element: ET.Element) -> Dict[str, Any]:
        """
        Get typed parameters from an XML element.

        Args:
            element: The XML element containing parameters

        Returns:
            A dictionary of parameters with appropriate types
        """
        if element is None:
            logging.warning("Cannot get parameters from None element")
            return {}

        params = {}

        # Define namespaces for XPath
        namespaces = {
            'lida': 'http://ccrg.cs.memphis.edu/LidaXMLSchema',
            'xsi': 'http://www.w3.org/2001/XMLSchema-instance'
        }

        # Try to find param elements using XPath
        param_elements = element.findall('.//lida:param', namespaces)
        if not param_elements:
            # Try without namespace
            param_elements = element.findall('.//param')

        # Log for debugging
        logging.debug(f"Found {len(param_elements)} param elements in {element.tag}")

        for param_element in param_elements:
            # Get attributes
            name = param_element.get('name')
            value_type = param_element.get('type', 'string')

            if name:
                # Get text content
                value_text = param_element.text.strip() if param_element.text else ''

                # Log for debugging
                logging.debug(f"Processing param: name={name}, type={value_type}, text={value_text}")

                # Convert value to appropriate type
                if value_type == "int":
                    try:
                        value = int(value_text)
                    except (ValueError, TypeError):
                        logging.warning(f"Could not convert '{value_text}' to int for param '{name}'")
                        value = 0
                elif value_type == "double" or value_type == "float":
                    try:
                        value = float(value_text)
                    except (ValueError, TypeError):
                        logging.warning(f"Could not convert '{value_text}' to float for param '{name}'")
                        value = 0.0
                elif value_type == "boolean":
                    value = value_text.lower() == "true"
                else:  # Default to string
                    value = value_text

                params[name] = value
                logging.debug(f"Added param: {name}={value} ({type(value).__name__})")

        # If no parameters found, try to get attributes as parameters
        if not params:
            for attr_name, attr_value in element.attrib.items():
                # Skip namespace declarations and schema locations
                if attr_name.startswith("{http://www.w3.org/") or attr_name == "schemaLocation":
                    continue

                # Remove namespace prefix if present
                if "}" in attr_name:
                    _, attr_name = attr_name.split("}", 1)

                if attr_name not in ["type", "name"]:
                    params[attr_name] = attr_value
                    logging.debug(f"Added attribute as param: {attr_name}={attr_value}")

        return params

    @staticmethod
    def contains_tag(element: ET.Element, tag_name: str) -> bool:
        """
        Check if an element contains a child with the specified tag name.

        Args:
            element: The parent XML element
            tag_name: The tag name to check for

        Returns:
            True if the element contains a child with the specified tag name, False otherwise
        """
        return len(XmlUtils.get_children(element, tag_name)) > 0

    @staticmethod
    def get_integer_value(element: ET.Element, tag_name: str) -> Optional[int]:
        """
        Get an integer value from a child element with the specified tag name.

        Args:
            element: The parent XML element
            tag_name: The tag name of the child element

        Returns:
            The integer value or None if the value is not an integer
        """
        text = XmlUtils.get_text_value(element, tag_name)
        if text:
            try:
                return int(text)
            except (ValueError, TypeError):
                pass
        return None

    @staticmethod
    def debug_element(element: ET.Element, level: int = 0) -> str:
        """
        Generate a debug string representation of an XML element and its children.

        Args:
            element: The XML element to debug
            level: The current indentation level

        Returns:
            A string representation of the element and its children
        """
        if element is None:
            return "None"

        indent = "  " * level
        result = f"{indent}<{element.tag}"

        # Add attributes
        for name, value in element.attrib.items():
            result += f" {name}=\"{value}\""

        # Check if element has children or text
        children = list(element)
        if children or (element.text and element.text.strip()):
            result += ">"

            # Add text if present
            if element.text and element.text.strip():
                result += f"\n{indent}  {element.text.strip()}"

            # Add children
            for child in children:
                result += f"\n{XmlUtils.debug_element(child, level + 1)}"

            result += f"\n{indent}</{element.tag}>"
        else:
            result += " />"

        return result
