"""
SearchSB - 增强版搜索任务
基于Java版本的复杂搜索逻辑，实现动态查询构建、启发式搜索、双向搜索和与激活扩散的深度集成
"""

import logging
import threading
from typing import Dict, List, Set, Optional, Any, Tuple, Union
from collections import defaultdict, deque
import heapq
import math

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName


class SearchNode:
    """搜索节点，用于启发式搜索"""
    
    def __init__(self, node: Node, g_score: float, h_score: float, parent=None):
        self.node = node
        self.g_score = g_score  # 从起点到当前节点的实际成本
        self.h_score = h_score  # 从当前节点到目标的启发式估计成本
        self.f_score = g_score + h_score  # 总估计成本
        self.parent = parent
    
    def __lt__(self, other):
        return self.f_score < other.f_score


class SearchSB(FrameworkTaskImpl):
    """
    增强版搜索任务
    实现动态查询构建、启发式搜索、双向搜索和与激活扩散的深度集成
    """

    def __init__(self, search_targets, search_type="general", pam=None):
        """
        初始化搜索任务
        
        Args:
            search_targets: 搜索目标（节点列表或查询条件）
            search_type: 搜索类型（grammar, semantic, concept, general）
            pam: PAM实例
        """
        super().__init__(1)
        self.search_targets = search_targets if isinstance(search_targets, list) else [search_targets]
        self.search_type = search_type
        self.pam = pam
        
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 搜索参数
        self.max_search_depth = 6
        self.max_results = 50
        self.search_timeout = 5.0  # 秒
        
        # 搜索策略权重
        self.strategy_weights = {
            'heuristic': 0.4,      # 启发式搜索
            'bidirectional': 0.3,  # 双向搜索
            'activation_guided': 0.2,  # 激活引导搜索
            'semantic_similarity': 0.1  # 语义相似性搜索
        }
        
        # 搜索结果缓存
        self.search_cache = {}
        self.cache_lock = threading.RLock()
        
        # 搜索统计
        self.search_stats = {
            'nodes_visited': 0,
            'paths_explored': 0,
            'cache_hits': 0,
            'search_time': 0.0
        }

    def run_this_framework_task(self):
        """
        执行搜索任务
        """
        try:
            import time
            start_time = time.time()

            # 检查是否应该跳过搜索（避免无限循环）
            if self.should_skip_search():
                self.logger.info("Skipping search to avoid infinite loop")
                return

            # 多策略搜索
            results = self.multi_strategy_search()

            # 结果融合和排序
            final_results = self.merge_and_rank_results(results)

            # 提交结果
            self.submit_results(final_results)

            # 更新统计
            self.search_stats['search_time'] = time.time() - start_time
            # self.logger.info(f"Search completed: {len(final_results)} results in {self.search_stats['search_time']:.3f}s")

        except Exception as e:
            self.logger.error(f"Error in search task: {e}")
        finally:
            self.cancel()

    def should_skip_search(self):
        """
        检查是否应该跳过搜索
        """
        try:
            # 如果搜索次数过多，跳过
            if hasattr(self, '_search_count'):
                self._search_count += 1
                if self._search_count > 50:  # 限制搜索次数
                    return True
            else:
                self._search_count = 1

            return False
        except Exception as e:
            self.logger.error(f"Error checking search skip: {e}")
            return True

    def multi_strategy_search(self):
        """
        多策略搜索
        
        Returns:
            dict: 各策略的搜索结果
        """
        results = {}
        
        try:
            # 启发式搜索
            if self.strategy_weights['heuristic'] > 0:
                results['heuristic'] = self.heuristic_search()
            
            # 双向搜索
            if self.strategy_weights['bidirectional'] > 0:
                results['bidirectional'] = self.bidirectional_search()
            
            # 激活引导搜索
            if self.strategy_weights['activation_guided'] > 0:
                results['activation_guided'] = self.activation_guided_search()
            
            # 语义相似性搜索
            if self.strategy_weights['semantic_similarity'] > 0:
                results['semantic_similarity'] = self.semantic_similarity_search()
                
        except Exception as e:
            self.logger.error(f"Error in multi-strategy search: {e}")
            
        return results

    def heuristic_search(self):
        """
        启发式搜索（A*算法）
        
        Returns:
            list: 搜索结果
        """
        try:
            results = []
            
            for target in self.search_targets:
                if not hasattr(target, 'get_node_name') and not hasattr(target, 'getTNname'):
                    continue
                    
                # 获取起始节点集合
                start_nodes = self.get_start_nodes(target)
                
                for start_node in start_nodes:
                    paths = self.a_star_search(start_node, target)
                    results.extend(paths)
                    
            return results
            
        except Exception as e:
            self.logger.error(f"Error in heuristic search: {e}")
            return []

    def a_star_search(self, start_node, goal_node):
        """
        A*搜索算法实现
        
        Args:
            start_node: 起始节点
            goal_node: 目标节点
            
        Returns:
            list: 找到的路径列表
        """
        try:
            # 优先队列
            frontier = []
            heapq.heappush(frontier, SearchNode(start_node, 0, self.heuristic(start_node, goal_node)))
            
            # 已访问节点
            visited = set()
            
            # 节点到起点的成本
            g_scores = {self.get_node_id(start_node): 0}
            
            # 路径重建信息
            came_from = {}
            
            paths = []
            max_iterations = 1000
            iterations = 0
            
            while frontier and iterations < max_iterations:
                iterations += 1
                current_search_node = heapq.heappop(frontier)
                current_node = current_search_node.node
                current_id = self.get_node_id(current_node)
                
                if current_id in visited:
                    continue
                    
                visited.add(current_id)
                self.search_stats['nodes_visited'] += 1
                
                # 检查是否到达目标
                if self.is_goal_reached(current_node, goal_node):
                    path = self.reconstruct_path(current_node, came_from)
                    paths.append(path)
                    if len(paths) >= 3:  # 限制路径数量
                        break
                    continue
                
                # 扩展邻居节点
                neighbors = self.get_neighbors(current_node)
                for neighbor, edge_cost in neighbors:
                    neighbor_id = self.get_node_id(neighbor)
                    
                    if neighbor_id in visited:
                        continue
                        
                    tentative_g = g_scores[current_id] + edge_cost
                    
                    if neighbor_id not in g_scores or tentative_g < g_scores[neighbor_id]:
                        g_scores[neighbor_id] = tentative_g
                        h_score = self.heuristic(neighbor, goal_node)
                        search_node = SearchNode(neighbor, tentative_g, h_score, current_search_node)
                        heapq.heappush(frontier, search_node)
                        came_from[neighbor_id] = current_node
            
            return paths
            
        except Exception as e:
            self.logger.error(f"Error in A* search: {e}")
            return []

    def bidirectional_search(self):
        """
        双向搜索
        
        Returns:
            list: 搜索结果
        """
        try:
            results = []
            
            for target in self.search_targets:
                # 从起点和终点同时搜索
                forward_visited = {}
                backward_visited = {}
                
                forward_queue = deque([target])
                backward_queue = deque(self.get_start_nodes(target))
                
                depth = 0
                max_depth = self.max_search_depth // 2
                
                while (forward_queue or backward_queue) and depth < max_depth:
                    depth += 1
                    
                    # 前向搜索
                    if forward_queue:
                        current_size = len(forward_queue)
                        for _ in range(current_size):
                            if not forward_queue:
                                break
                            node = forward_queue.popleft()
                            node_id = self.get_node_id(node)
                            
                            if node_id in backward_visited:
                                # 找到连接点
                                path = self.build_bidirectional_path(
                                    node, forward_visited, backward_visited
                                )
                                if path:
                                    results.append(path)
                            
                            if node_id not in forward_visited:
                                forward_visited[node_id] = node
                                neighbors = self.get_neighbors(node)
                                for neighbor, _ in neighbors:
                                    if self.get_node_id(neighbor) not in forward_visited:
                                        forward_queue.append(neighbor)
                    
                    # 后向搜索
                    if backward_queue:
                        current_size = len(backward_queue)
                        for _ in range(current_size):
                            if not backward_queue:
                                break
                            node = backward_queue.popleft()
                            node_id = self.get_node_id(node)
                            
                            if node_id in forward_visited:
                                # 找到连接点
                                path = self.build_bidirectional_path(
                                    node, forward_visited, backward_visited
                                )
                                if path:
                                    results.append(path)
                            
                            if node_id not in backward_visited:
                                backward_visited[node_id] = node
                                neighbors = self.get_neighbors(node)
                                for neighbor, _ in neighbors:
                                    if self.get_node_id(neighbor) not in backward_visited:
                                        backward_queue.append(neighbor)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in bidirectional search: {e}")
            return []

    def activation_guided_search(self):
        """
        激活引导搜索
        
        Returns:
            list: 搜索结果
        """
        try:
            results = []
            
            # 获取高激活节点
            high_activation_nodes = self.get_high_activation_nodes()
            
            for target in self.search_targets:
                target_name = self.get_node_name(target)
                
                # 基于激活度的搜索
                for activated_node in high_activation_nodes:
                    if self.is_semantically_related(activated_node, target):
                        path = self.trace_activation_path(activated_node, target)
                        if path:
                            results.append(path)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in activation guided search: {e}")
            return []

    def semantic_similarity_search(self):
        """
        语义相似性搜索
        
        Returns:
            list: 搜索结果
        """
        try:
            results = []
            
            for target in self.search_targets:
                similar_nodes = self.find_similar_nodes(target)
                
                for similar_node, similarity_score in similar_nodes:
                    if similarity_score > 0.5:  # 相似度阈值
                        results.append({
                            'node': similar_node,
                            'target': target,
                            'similarity': similarity_score,
                            'type': 'semantic_similarity'
                        })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in semantic similarity search: {e}")
            return []

    def heuristic(self, current_node, goal_node):
        """
        启发式函数：估计从当前节点到目标节点的距离

        Args:
            current_node: 当前节点
            goal_node: 目标节点

        Returns:
            float: 启发式距离
        """
        try:
            # 语义距离
            semantic_distance = self.calculate_semantic_distance(current_node, goal_node)

            # 激活因子（激活度高的节点成本低）
            activation_factor = 1.0
            if hasattr(current_node, 'getActivation'):
                activation = current_node.getActivation()
                activation_factor = 1.0 - (activation * 0.5)

            # 结构距离
            structural_distance = self.calculate_structural_distance(current_node, goal_node)

            return (semantic_distance * 0.6 + structural_distance * 0.4) * activation_factor

        except Exception as e:
            self.logger.error(f"Error calculating heuristic: {e}")
            return 1.0

    def calculate_semantic_distance(self, node_a, node_b):
        """
        计算语义距离

        Args:
            node_a: 节点A
            node_b: 节点B

        Returns:
            float: 语义距离
        """
        try:
            name_a = self.get_node_name(node_a)
            name_b = self.get_node_name(node_b)

            if not name_a or not name_b:
                return 1.0

            # 使用编辑距离作为简单的语义距离度量
            edit_distance = self.levenshtein_distance(name_a, name_b)
            max_length = max(len(name_a), len(name_b))

            if max_length == 0:
                return 0.0

            return edit_distance / max_length

        except Exception as e:
            self.logger.error(f"Error calculating semantic distance: {e}")
            return 1.0

    def calculate_structural_distance(self, node_a, node_b):
        """
        计算结构距离

        Args:
            node_a: 节点A
            node_b: 节点B

        Returns:
            float: 结构距离
        """
        try:
            # 基于节点类型的距离
            type_a = self.get_node_type(node_a)
            type_b = self.get_node_type(node_b)

            if type_a == type_b:
                return 0.2  # 同类型节点距离较小
            else:
                return 0.8  # 不同类型节点距离较大

        except Exception as e:
            self.logger.error(f"Error calculating structural distance: {e}")
            return 1.0

    def levenshtein_distance(self, s1, s2):
        """
        计算编辑距离

        Args:
            s1: 字符串1
            s2: 字符串2

        Returns:
            int: 编辑距离
        """
        if len(s1) < len(s2):
            return self.levenshtein_distance(s2, s1)

        if len(s2) == 0:
            return len(s1)

        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row

        return previous_row[-1]

    def get_neighbors(self, node):
        """
        获取节点的邻居

        Args:
            node: 节点

        Returns:
            list: 邻居节点和边成本的列表
        """
        try:
            neighbors = []

            # 尝试从PAM获取邻居
            if self.pam and hasattr(self.pam, 'pam_node_structure'):
                node_structure = self.pam.pam_node_structure

                # 获取连接的源节点
                if hasattr(node_structure, 'getConnectedSources'):
                    sources = node_structure.getConnectedSources(node)
                    if sources:
                        for link in sources:
                            source = link.getSource() if hasattr(link, 'getSource') else None
                            if source:
                                edge_cost = self.calculate_edge_cost(node, source, link)
                                neighbors.append((source, edge_cost))

                # 获取连接的目标节点
                if hasattr(node_structure, 'getConnectedSinks'):
                    sinks = node_structure.getConnectedSinks(node)
                    if sinks:
                        for link in sinks:
                            sink = link.getSink() if hasattr(link, 'getSink') else None
                            if sink:
                                edge_cost = self.calculate_edge_cost(node, sink, link)
                                neighbors.append((sink, edge_cost))

            # 尝试使用Neo4j获取邻居
            if not neighbors:
                neighbors = self.get_neighbors_from_neo4j(node)

            return neighbors

        except Exception as e:
            self.logger.error(f"Error getting neighbors: {e}")
            return []

    def get_neighbors_from_neo4j(self, node):
        """
        从Neo4j获取邻居节点

        Args:
            node: 节点

        Returns:
            list: 邻居节点列表
        """
        try:
            neighbors = []
            node_name = self.get_node_name(node)

            if not node_name:
                return neighbors

            # 使用Neo4j查询邻居
            try:
                from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil

                # 查询出边
                query_out = f"MATCH (n{{name:'{node_name}'}})-[r]->(m) RETURN m, r LIMIT 20"
                result = NeoUtil.execute_query(query_out)

                if result:
                    while hasattr(result, "has_next") and result.has_next():
                        row = result.next()
                        for key in result.columns():
                            if key == 'm':
                                neighbor_neo = row.get(key)
                                if neighbor_neo and hasattr(NeoUtil, 'cast_neo_to_lida_node'):
                                    neighbor = NeoUtil.cast_neo_to_lida_node(neighbor_neo)
                                    if neighbor:
                                        neighbors.append((neighbor, 1.0))  # 默认成本

                # 查询入边
                query_in = f"MATCH (n{{name:'{node_name}'}})<-[r]-(m) RETURN m, r LIMIT 20"
                result = NeoUtil.execute_query(query_in)

                if result:
                    while hasattr(result, "has_next") and result.has_next():
                        row = result.next()
                        for key in result.columns():
                            if key == 'm':
                                neighbor_neo = row.get(key)
                                if neighbor_neo and hasattr(NeoUtil, 'cast_neo_to_lida_node'):
                                    neighbor = NeoUtil.cast_neo_to_lida_node(neighbor_neo)
                                    if neighbor:
                                        neighbors.append((neighbor, 1.0))  # 默认成本

            except Exception as e:
                self.logger.warning(f"Neo4j neighbor query failed: {e}")

            return neighbors

        except Exception as e:
            self.logger.error(f"Error getting neighbors from Neo4j: {e}")
            return []

    def calculate_edge_cost(self, source_node, target_node, link=None):
        """
        计算边的成本

        Args:
            source_node: 源节点
            target_node: 目标节点
            link: 连接链路

        Returns:
            float: 边成本
        """
        try:
            base_cost = 1.0

            # 根据链接类型调整成本
            if link and hasattr(link, 'getCategory'):
                category = link.getCategory()
                if category:
                    link_type = ""
                    if hasattr(category, 'get_node_name'):
                        link_type = category.get_node_name()
                    elif hasattr(category, 'getName'):
                        link_type = category.getName()
                    elif isinstance(category, dict) and 'label' in category:
                        link_type = category['label']

                    # 不同链接类型的成本权重
                    type_costs = {
                        '相似': 0.5,
                        '对等': 0.6,
                        '顺承': 0.7,
                        '语序': 0.8,
                        '否定': 1.2,
                        'arg0': 0.4,
                        'arg1': 0.4,
                        'feel': 0.3
                    }

                    base_cost = type_costs.get(link_type, base_cost)

            # 根据节点激活度调整成本
            if hasattr(target_node, 'getActivation'):
                activation = target_node.getActivation()
                base_cost *= (1.0 - activation * 0.3)  # 激活度高的节点成本低

            return max(0.1, base_cost)

        except Exception as e:
            self.logger.error(f"Error calculating edge cost: {e}")
            return 1.0

    def get_start_nodes(self, target):
        """
        获取搜索起始节点

        Args:
            target: 目标节点

        Returns:
            list: 起始节点列表
        """
        try:
            start_nodes = []

            # 如果有PAM，从高激活节点开始
            if self.pam:
                high_activation_nodes = self.get_high_activation_nodes()
                start_nodes.extend(high_activation_nodes[:5])  # 限制数量

            # 如果没有起始节点，使用目标节点本身
            if not start_nodes:
                start_nodes = [target]

            return start_nodes

        except Exception as e:
            self.logger.error(f"Error getting start nodes: {e}")
            return [target] if target else []

    def get_high_activation_nodes(self):
        """
        获取高激活度节点

        Returns:
            list: 高激活度节点列表
        """
        try:
            high_activation_nodes = []

            if self.pam and hasattr(self.pam, 'pam_node_structure'):
                node_structure = self.pam.pam_node_structure

                if hasattr(node_structure, 'getNodes'):
                    all_nodes = node_structure.getNodes()

                    # 按激活度排序
                    nodes_with_activation = []
                    for node in all_nodes:
                        if hasattr(node, 'getActivation'):
                            activation = node.getActivation()
                            if activation > 0.1:  # 激活度阈值
                                nodes_with_activation.append((node, activation))

                    # 排序并取前N个
                    nodes_with_activation.sort(key=lambda x: x[1], reverse=True)
                    high_activation_nodes = [node for node, _ in nodes_with_activation[:10]]

            return high_activation_nodes

        except Exception as e:
            self.logger.error(f"Error getting high activation nodes: {e}")
            return []

    def is_goal_reached(self, current_node, goal_node):
        """
        检查是否到达目标

        Args:
            current_node: 当前节点
            goal_node: 目标节点

        Returns:
            bool: 是否到达目标
        """
        try:
            # 直接比较节点ID
            if self.get_node_id(current_node) == self.get_node_id(goal_node):
                return True

            # 比较节点名称
            current_name = self.get_node_name(current_node)
            goal_name = self.get_node_name(goal_node)

            if current_name and goal_name and current_name == goal_name:
                return True

            # 语义相似性检查
            if current_name and goal_name:
                similarity = 1.0 - self.calculate_semantic_distance(current_node, goal_node)
                if similarity > 0.9:  # 高相似度阈值
                    return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking goal: {e}")
            return False

    def reconstruct_path(self, goal_node, came_from):
        """
        重建路径

        Args:
            goal_node: 目标节点
            came_from: 路径信息

        Returns:
            list: 路径节点列表
        """
        try:
            path = [goal_node]
            current = goal_node
            current_id = self.get_node_id(current)

            while current_id in came_from:
                current = came_from[current_id]
                path.append(current)
                current_id = self.get_node_id(current)

            path.reverse()
            return path

        except Exception as e:
            self.logger.error(f"Error reconstructing path: {e}")
            return [goal_node]

    def build_bidirectional_path(self, meeting_node, forward_visited, backward_visited):
        """
        构建双向搜索路径

        Args:
            meeting_node: 相遇节点
            forward_visited: 前向访问节点
            backward_visited: 后向访问节点

        Returns:
            list: 路径节点列表
        """
        try:
            path = []

            # 从前向路径构建
            current = meeting_node
            forward_path = [current]
            # 这里需要更复杂的路径重建逻辑

            # 从后向路径构建
            backward_path = []
            # 这里需要更复杂的路径重建逻辑

            # 合并路径
            path = forward_path + backward_path[1:]  # 避免重复中间节点

            return path

        except Exception as e:
            self.logger.error(f"Error building bidirectional path: {e}")
            return [meeting_node]

    def is_semantically_related(self, node_a, node_b):
        """
        检查两个节点是否语义相关

        Args:
            node_a: 节点A
            node_b: 节点B

        Returns:
            bool: 是否语义相关
        """
        try:
            # 计算语义距离
            distance = self.calculate_semantic_distance(node_a, node_b)
            return distance < 0.5  # 相关性阈值

        except Exception as e:
            self.logger.error(f"Error checking semantic relation: {e}")
            return False

    def trace_activation_path(self, activated_node, target_node):
        """
        追踪激活路径

        Args:
            activated_node: 激活节点
            target_node: 目标节点

        Returns:
            list: 激活路径
        """
        try:
            # 简单的激活路径追踪
            path = [activated_node]

            # 如果节点直接相关，返回简单路径
            if self.is_semantically_related(activated_node, target_node):
                path.append(target_node)

            return path

        except Exception as e:
            self.logger.error(f"Error tracing activation path: {e}")
            return [activated_node]

    def find_similar_nodes(self, target_node):
        """
        查找相似节点

        Args:
            target_node: 目标节点

        Returns:
            list: 相似节点和相似度的列表
        """
        try:
            similar_nodes = []
            target_name = self.get_node_name(target_node)

            if not target_name:
                return similar_nodes

            # 使用Neo4j查找相似节点
            try:
                from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil

                # 查询相似关系
                query = f"MATCH (n{{name:'{target_name}'}})-[r:相似]->(m) RETURN m, r.weight as weight"
                result = NeoUtil.execute_query(query)

                if result:
                    while hasattr(result, "has_next") and result.has_next():
                        row = result.next()
                        similar_neo = row.get('m')
                        weight = row.get('weight', 0.5)

                        if similar_neo and hasattr(NeoUtil, 'cast_neo_to_lida_node'):
                            similar_node = NeoUtil.cast_neo_to_lida_node(similar_neo)
                            if similar_node:
                                similar_nodes.append((similar_node, weight))

            except Exception as e:
                self.logger.warning(f"Neo4j similarity query failed: {e}")

            return similar_nodes

        except Exception as e:
            self.logger.error(f"Error finding similar nodes: {e}")
            return []

    def merge_and_rank_results(self, strategy_results):
        """
        合并和排序结果

        Args:
            strategy_results: 各策略的结果

        Returns:
            list: 排序后的最终结果
        """
        try:
            all_results = []

            # 合并所有策略的结果
            for strategy, results in strategy_results.items():
                weight = self.strategy_weights.get(strategy, 0.1)

                for result in results:
                    if isinstance(result, list):  # 路径结果
                        all_results.append({
                            'type': 'path',
                            'data': result,
                            'strategy': strategy,
                            'score': weight * len(result),  # 路径长度影响分数
                            'weight': weight
                        })
                    elif isinstance(result, dict):  # 其他结果
                        result['strategy'] = strategy
                        result['weight'] = weight
                        result['score'] = weight * result.get('similarity', 0.5)
                        all_results.append(result)

            # 按分数排序
            all_results.sort(key=lambda x: x.get('score', 0), reverse=True)

            # 去重和限制数量
            unique_results = []
            seen_nodes = set()

            for result in all_results:
                if len(unique_results) >= self.max_results:
                    break

                # 简单去重逻辑
                result_key = str(result.get('data', result))
                if result_key not in seen_nodes:
                    seen_nodes.add(result_key)
                    unique_results.append(result)

            return unique_results

        except Exception as e:
            self.logger.error(f"Error merging and ranking results: {e}")
            return []

    def submit_results(self, results):
        """
        提交搜索结果

        Args:
            results: 搜索结果列表
        """
        try:
            if not results:
                self.logger.info("No search results to submit")
                return

            # 提交到PAM监听器
            if self.pam and hasattr(self.pam, 'get_listener'):
                listener = self.pam.get_listener()
                if hasattr(listener, 'receive_percept'):
                    for result in results:
                        # 根据结果类型选择提交方式
                        if result.get('type') == 'path':
                            # 提交路径中的节点
                            for node in result['data']:
                                listener.receive_percept(node, ModuleName.CurrentSM)
                        else:
                            # 提交单个节点
                            node = result.get('node')
                            if node:
                                listener.receive_percept(node, ModuleName.CurrentSM)

            # self.logger.info(f"Submitted {len(results)} search results")

        except Exception as e:
            self.logger.error(f"Error submitting results: {e}")

    def get_node_id(self, node):
        """
        获取节点ID

        Args:
            node: 节点

        Returns:
            str: 节点ID
        """
        try:
            if hasattr(node, 'get_node_id'):
                return str(node.get_node_id())
            elif hasattr(node, 'getNodeId'):
                return str(node.getNodeId())
            elif hasattr(node, 'id'):
                return str(node.id)
            else:
                return str(id(node))  # 使用Python对象ID作为后备
        except:
            return str(id(node))

    def get_node_name(self, node):
        """
        获取节点名称

        Args:
            node: 节点

        Returns:
            str: 节点名称
        """
        try:
            if hasattr(node, 'get_node_name'):
                return node.get_node_name()
            elif hasattr(node, 'getTNname'):
                return node.getTNname()
            elif hasattr(node, 'getName'):
                return node.getName()
            elif hasattr(node, 'name'):
                return node.name
            else:
                return str(node)
        except:
            return str(node)

    def get_node_type(self, node):
        """
        获取节点类型

        Args:
            node: 节点

        Returns:
            str: 节点类型
        """
        try:
            node_name = self.get_node_name(node)

            if "_NP" in node_name or "_VP" in node_name or "_IP" in node_name:
                return "grammar"
            elif "arg" in node_name:
                return "argument"
            elif "happy" in node_name or "开心" in node_name:
                return "emotion"
            else:
                return "concept"

        except:
            return "unknown"
