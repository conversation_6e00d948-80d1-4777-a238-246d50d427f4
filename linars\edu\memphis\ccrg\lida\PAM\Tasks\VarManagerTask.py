#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to manage variables.
"""

import logging
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory

class VarManagerTask(FrameworkTaskImpl):
    """
    A task to manage variables.
    """

    def __init__(self):
        """
        Initialize a VarManagerTask.
        """
        super().__init__()
        self.logger = logging.getLogger("VarManagerTask")
        self.pam = None
        self.seq_ns = None

    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module.

        Args:
            module: The module to associate
            module_usage: The usage of the module
        """
        # 检查模块类型
        if isinstance(module, PAMemory):  # 用factorydata里的初始化，不是alifeagent里的
            self.pam = module
            self.logger.info(f"VarManagerTask: PAM module set to {module}")
        else:
            # 尝试使用模块类型判断
            if module_usage == "PAM" or module_usage == "PAMemory" or \
               (hasattr(module, 'get_module_name') and module.get_module_name() == "PAMemory"):
                self.pam = module
                self.logger.info(f"VarManagerTask: PAM module set by usage type to {module}")
            # 尝试使用模块类名判断
            elif module.__class__.__name__ in ["PamImpl0", "PAMemoryImpl"]:
                self.pam = module
                self.logger.info(f"VarManagerTask: PAM module set by class name to {module}")
            else:
                self.logger.warning(f"Cannot add module {module} with usage {module_usage}")

    def run_this_framework_task(self):
        """
        Run the task.
        """
        # 检查PAM是否已设置
        if not hasattr(self, 'pam') or self.pam is None:
            # 尝试从全局变量中获取PAM模块
            try:
                from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                if AgentStarter.pam is not None:
                    self.pam = AgentStarter.pam
                    self.logger.info(f"VarManagerTask: PAM module set from global variable to {self.pam}")
                else:
                    self.logger.warning("PAM is not set for VarManagerTask and not available globally")
                    return
            except Exception as e:
                self.logger.warning(f"Error getting global PAM module: {e}")
                return

        try:
            isa_source = None
            isa_sink = None

            # 尝试获取seq工作空间缓冲区
            try:
                workspace = self.pam.get_listener()
                if workspace and hasattr(workspace, "get_submodule"):
                    seq_buffer = workspace.get_submodule("seq")
                    if seq_buffer and hasattr(seq_buffer, "get_buffer_content"):
                        self.seq_ns = seq_buffer.get_buffer_content(None)
            except Exception as e:
                self.logger.warning(f"Error getting seq buffer: {e}")
                return

            # 如果seq_ns为None，则返回
            if self.seq_ns is None:
                return

            # 获取当前tick
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            current_tick = TaskManager.get_current_tick()
            start_tick = 0

            if hasattr(AgentStarter, 'do_start_tick'):
                start_tick = AgentStarter.do_start_tick

            tick = current_tick - start_tick

            # 检查是否需要处理变量
            is_do_var = False
            if hasattr(AgentStarter, 'is_do_var'):
                is_do_var = AgentStarter.is_do_var

            if is_do_var and tick < 200:
                # 动机维持，目前在时序buffer，todo 放动机管理
                for seq_link in self.seq_ns.get_links():
                    if seq_link.get_tn_name() == "nowisa":
                        seq_link.set_activation(seq_link.get_activation() + 0.3)
                        isa_source = seq_link.get_source()
                        isa_sink = seq_link.get_sink()
                        isa_source.set_activation(isa_source.get_activation() + 0.2)
                        isa_sink.set_activation(isa_sink.get_activation() + 0.2)
            else:
                if hasattr(AgentStarter, 'is_do_var'):
                    AgentStarter.is_do_var = False
        except Exception as e:
            self.logger.warning(f"Error in VarManagerTask: {e}")

        # 不取消就是一直执行
        # self.cancel()
