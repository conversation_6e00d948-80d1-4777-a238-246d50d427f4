# LIDA Cognitive Framework
"""
A BroadcastTrigger determines when a new broadcast must be triggered.
Its start() method should be invoked once and only once (this is likely to be when the GlobalWorkspace starts)
Its check_for_trigger_condition() method is called every time a new Coalition enters the GlobalWorkspace.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Collection
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition

# Forward reference to avoid circular import
GlobalWorkspace = 'GlobalWorkspace'

class BroadcastTrigger(ABC):
    """
    A BroadcastTrigger determines when a new broadcast must be triggered.
    Its start() method should be invoked once and only once (this is likely to be when the GlobalWorkspace starts)
    Its check_for_trigger_condition() method is called every time a new Coalition enters the GlobalWorkspace.
    """

    @abstractmethod
    def init(self, params: Dict[str, Any], gw: GlobalWorkspace) -> None:
        """
        Provides a generic way to setup a BroadcastTrigger. It should be called when
        the trigger is created.

        Args:
            params: A map for generic parameters
            gw: A TriggerListener and likely a class that implements the GlobalWorkspace interface
        """
        pass

    @abstractmethod
    def check_for_trigger_condition(self, coalitions: Collection[Coalition]) -> None:
        """
        When called the trigger checks if its firing condition. If it has it initiates a competition for consciousness.
        This method is called for all registered triggers each time a new Coalition is put in the GlobalWorkspace.

        Args:
            coalitions: All the Coalition objects currently in the GlobalWorkspace
        """
        pass

    @abstractmethod
    def reset(self) -> None:
        """
        Resets the trigger. Called each time a new broadcast is triggered.
        """
        pass

    @abstractmethod
    def start(self) -> None:
        """
        Starts the trigger.
        """
        pass
