"""
Linkable definition.

This module provides a class for defining linkable objects.
"""
import importlib
from typing import Dict, Any, Optional, Type

from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable

class LinkableDef:
    """
    Linkable definition.

    This class defines a linkable object.
    """

    def __init__(self, name: str, module_class_name: str):
        """
        Initialize the linkable definition.

        Args:
            name: The name of the linkable
            module_class_name: The class name of the module
        """
        self.name = name
        self.module_class_name = module_class_name

    def get_name(self) -> str:
        """
        Get the name of the linkable.

        Returns:
            The name of the linkable
        """
        return self.name

    def get_module_class_name(self) -> str:
        """
        Get the class name of the module.

        Returns:
            The class name of the module
        """
        return self.module_class_name

    def get_module_class(self) -> Optional[Type[Linkable]]:
        """
        Get the module class.

        Returns:
            The module class or None if not found
        """
        try:
            # Split the class name into package and class
            parts = self.module_class_name.split(".")
            class_name = parts[-1]
            package_name = ".".join(parts[:-1])

            # Import the module
            module = importlib.import_module(package_name)

            # Get the class
            class_obj = getattr(module, class_name)

            return class_obj
        except (ImportError, AttributeError, Exception):
            return None
