# LIDA Cognitive Framework
"""
Listens to cues from the Workspace.
This interface is typically implemented by EpisodicMemory modules.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure

class CueListener(ModuleListener):
    """
    Listens to cues from the Workspace.
    This interface is typically implemented by EpisodicMemory modules.
    """
    
    @abstractmethod
    def receive_cue(self, cue: NodeStructure) -> None:
        """
        Receive a cue.
        
        Args:
            cue: A NodeStructure to cue EpisodicMemory with
        """
        pass
