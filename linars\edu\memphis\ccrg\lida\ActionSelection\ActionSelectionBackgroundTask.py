# LIDA认知框架
"""
行为选择的后台任务
"""

import logging
from typing import List, Set, Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.ActionSelection.BasicActionSelection import BasicActionSelection

class ActionSelectionBackgroundTask(FrameworkTaskImpl):
    """
    行为选择的后台任务
    """

    def __init__(self, ticks_per_run: int, action_selection: BasicActionSelection):
        """
        初始化后台任务

        参数:
            ticks_per_run: 每次运行的tick数
            action_selection: 行为选择模块
        """
        super().__init__(ticks_per_run)
        self.action_selection = action_selection
        self.logger = logging.getLogger(self.__class__.__name__)

    def run_this_framework_task(self) -> None:
        """
        运行任务，选择行为
        """
        # 在实际实现中，这里会进行行为选择
        pass
