#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
Tree_nest class for the LIDA framework.
"""

from typing import Dict, List, Set, Any, Optional, Collection, Tuple
import logging
from linars.edu.memphis.ccrg.lida.Nlanguage.TreeNode import TreeNode

class Tree_nest:
    """
    A tree nest in the LIDA framework.
    """
    
    def __init__(self, tree_node: Optional[TreeNode] = None):
        """
        Initialize a Tree_nest.
        
        Args:
            tree_node: The root node of the tree
        """
        self.root = tree_node  # 根节点
        self.layer = 0  # 层数
        self.all_node_size = 1 if tree_node else 0  # 节点数量
        self.logger = logging.getLogger("Tree_nest")
        
    def add_node(self, value: Any, parent: Optional[TreeNode]) -> None:
        """
        Add a node to the tree.
        
        Args:
            value: The value of the node
            parent: The parent node
        """
        new_node = TreeNode(value)
        if parent is None:
            if self.root is None:
                self.root = new_node
                self.all_node_size = 1
        else:
            is_add = parent.add_child(new_node)
            if is_add:
                self.all_node_size += 1
                
    def remove_node(self, value: Any) -> None:
        """
        Remove a node from the tree.
        
        Args:
            value: The value of the node to remove
        """
        if self.root is None:
            return  # 树为空，无需删除
            
        # 递归查找并删除节点
        self._remove_node_recursive(self.root, value)
        
    def _remove_node_recursive(self, node: TreeNode, value: Any) -> bool:
        """
        Recursively remove a node from the tree.
        
        Args:
            node: The current node
            value: The value of the node to remove
            
        Returns:
            True if the node was removed, False otherwise
        """
        child = node.find_child_by_value(value)
        if child:
            node.remove_child(child)
            self.all_node_size -= 1
            return True
            
        for child_node in node.get_children():
            if self._remove_node_recursive(child_node, value):
                return True
                
        return False
        
    def find_node(self, value: Any) -> Optional[TreeNode]:
        """
        Find a node in the tree.
        
        Args:
            value: The value of the node to find
            
        Returns:
            The node if found, None otherwise
        """
        if self.root is None:
            return None
            
        return self._find_node_recursive(self.root, value)
        
    def _find_node_recursive(self, node: TreeNode, value: Any) -> Optional[TreeNode]:
        """
        Recursively find a node in the tree.
        
        Args:
            node: The current node
            value: The value of the node to find
            
        Returns:
            The node if found, None otherwise
        """
        # 如果当前节点的值等于目标值
        if node.get_value() == value:
            return node
            
        # 遍历当前节点的所有子节点
        for child_node in node.get_children():
            # 递归调用_find_node_recursive方法，查找子节点中是否包含目标值
            found = self._find_node_recursive(child_node, value)
            # 如果找到了目标节点，返回找到的节点
            if found:
                return found
                
        # 如果没有找到节点，返回None
        return None
        
    def update_node_value(self, old_value: Any, new_value: Any) -> None:
        """
        Update the value of a node.
        
        Args:
            old_value: The old value of the node
            new_value: The new value of the node
        """
        node = self.find_node(old_value)
        if node:
            node.set_value(new_value)
        else:
            self.logger.warning(f"Node with value {old_value} not found.")
            
    def find_path_to_node(self, target_value: Any) -> Optional[List[TreeNode]]:
        """
        Find the path from the root to a node.
        
        Args:
            target_value: The value of the target node
            
        Returns:
            The path if found, None otherwise
        """
        path = []
        return self._find_path_to_node_dfs(self.root, target_value, path)
        
    def _find_path_to_node_dfs(self, node: Optional[TreeNode], target_value: Any, 
                              path: List[TreeNode]) -> Optional[List[TreeNode]]:
        """
        Recursively find the path from a node to a target node using DFS.
        
        Args:
            node: The current node
            target_value: The value of the target node
            path: The current path
            
        Returns:
            The path if found, None otherwise
        """
        if node is None:
            return None
            
        # 将当前节点添加到路径中
        path.append(node)
        
        # 如果找到目标节点，返回路径
        if node.get_value() == target_value:
            return path
            
        # 递归查找子节点
        for child in node.get_children():
            result = self._find_path_to_node_dfs(child, target_value, path)
            if result:
                return result
                
        # 如果当前分支没有找到目标节点，则移除当前节点并回溯
        path.pop()
        return None
        
    def traverse_tree_dfs(self) -> None:
        """
        Traverse the tree using DFS.
        """
        visited_nodes = []
        self._dfs(self.root, visited_nodes)
        
    def _dfs(self, node: Optional[TreeNode], visited_nodes: List[TreeNode]) -> None:
        """
        Recursively traverse the tree using DFS.
        
        Args:
            node: The current node
            visited_nodes: The list of visited nodes
        """
        if node is None:
            return
            
        # 访问当前节点
        print(node.get_value())
        visited_nodes.append(node)
        
        # 递归访问子节点
        for child in node.get_children():
            self._dfs(child, visited_nodes)
            
    def add_alias_to_node(self, value: Any, alias: Any) -> None:
        """
        Add an alias to a node.
        
        Args:
            value: The value of the node
            alias: The alias to add
        """
        node = self.find_node(value)
        if node:
            node.add_alias(alias)
        else:
            self.logger.warning(f"Node with value {value} not found.---- add_alias_to_node")
            
    def remove_alias_from_node(self, value: Any, alias: Any) -> None:
        """
        Remove an alias from a node.
        
        Args:
            value: The value of the node
            alias: The alias to remove
        """
        node = self.find_node(value)
        if node:
            node.remove_alias(alias)
        else:
            self.logger.warning(f"Node with value {value} not found.---- remove_alias_from_node")
            
    def has_alias_for_node(self, value: Any, alias: Any) -> bool:
        """
        Check if a node has an alias.
        
        Args:
            value: The value of the node
            alias: The alias to check
            
        Returns:
            True if the node has the alias, False otherwise
        """
        node = self.find_node(value)
        return node.has_alias(alias) if node else False
        
    def get_aliases_for_node(self, value: Any) -> List[Any]:
        """
        Get the aliases of a node.
        
        Args:
            value: The value of the node
            
        Returns:
            The list of aliases
        """
        node = self.find_node(value)
        return node.get_aliases() if node else []
        
    def add_topic_data_to_node(self, value: Any, data: Any) -> None:
        """
        Add topic data to a node.
        
        Args:
            value: The value of the node
            data: The data to add
        """
        node = self.find_node(value)
        if node:
            node.add_topic_data(data)
        else:
            self.logger.warning(f"Node with value {value} not found.")
            
    def remove_topic_data_from_node(self, value: Any, data: Any) -> None:
        """
        Remove topic data from a node.
        
        Args:
            value: The value of the node
            data: The data to remove
        """
        node = self.find_node(value)
        if node:
            node.remove_topic_data(data)
        else:
            self.logger.warning(f"Node with value {value} not found.")
            
    def has_topic_data_for_node(self, value: Any, data: Any) -> bool:
        """
        Check if a node has topic data.
        
        Args:
            value: The value of the node
            data: The data to check
            
        Returns:
            True if the node has the data, False otherwise
        """
        node = self.find_node(value)
        return node.has_topic_data(data) if node else False
        
    def get_topic_data_for_node(self, value: Any) -> List[Any]:
        """
        Get the topic data of a node.
        
        Args:
            value: The value of the node
            
        Returns:
            The list of topic data
        """
        node = self.find_node(value)
        return node.get_topic_data_pool() if node else []
