# LIDA Cognitive Framework
"""
Interface for the Environment module of the LIDA framework.
"""

from abc import abstractmethod
from typing import Dict, Any
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule

class Environment(FrameworkModule):
    """
    Interface for the Environment module of the LIDA framework.
    """
    
    @abstractmethod
    def get_state(self, params: Dict[str, Any]) -> Any:
        """
        Get the state of the environment.
        
        Args:
            params: Parameters specifying what state to return
            
        Returns:
            The state of the environment
        """
        pass
    
    @abstractmethod
    def process_action(self, action: Any) -> None:
        """
        Process an action in the environment.
        
        Args:
            action: The action to process
        """
        pass
