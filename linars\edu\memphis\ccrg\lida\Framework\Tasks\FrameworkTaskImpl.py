# LIDA Cognitive Framework
"""
Implementation of the FrameworkTask interface.
"""

import logging
from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTask import FrameworkTask, TaskStatus
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawner import TaskSpawner
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.LearnableImpl import LearnableImpl

class FrameworkTaskImpl(LearnableImpl, FrameworkTask):
    """
    This class implements the FrameworkTask Interface.
    This class should be used as the base class for all FrameworkTasks.
    """

    # Class variables
    DEFAULT_TICKS_PER_RUN = 1
    _next_task_id = 0

    # Parameters dictionary
    parameters = {}

    def __init__(self, ticks_per_run: int = DEFAULT_TICKS_PER_RUN, task_type: str = "normal"):
        """
        Constructs a FrameworkTaskImpl with specified ticksPerRun.

        Args:
            ticks_per_run: Task's run frequency
            task_type: Type of the task
        """
        super().__init__()
        self.task_id = FrameworkTaskImpl._next_task_id
        FrameworkTaskImpl._next_task_id += 1
        self.controlling_ts = None
        self.set_ticks_per_run(ticks_per_run)
        self.task_name = f"{self.__class__.__name__}[{self.task_id}]"
        self.type = task_type
        self.status = TaskStatus.RUNNING
        self.next_execution_ticks_per_run = ticks_per_run
        self.scheduled_tick = 0
        self.logger = logging.getLogger(self.__class__.__name__)
        self.parameters = {}

    def get_task_id(self) -> int:
        """
        Get the ID of this task.

        Returns:
            The ID of this task
        """
        return self.task_id

    def get_ticks_per_run(self) -> int:
        """
        Get the number of ticks between runs of this task.

        Returns:
            The number of ticks between runs
        """
        return self.ticks_per_run

    def set_ticks_per_run(self, ticks: int) -> None:
        """
        Set the number of ticks between runs of this task.

        Args:
            ticks: The number of ticks between runs
        """
        if ticks <= 0:
            self.ticks_per_run = self.DEFAULT_TICKS_PER_RUN
        else:
            self.ticks_per_run = ticks

    def get_task_status(self) -> TaskStatus:
        """
        Get the status of this task.

        Returns:
            The status of this task
        """
        return self.status

    def set_task_status(self, status: TaskStatus) -> None:
        """
        Set the status of this task.

        Args:
            status: The new status of this task
        """
        if self.status == TaskStatus.CANCELED:
            from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
            self.logger.warning(f"Cannot set TaskStatus to {status}. TaskStatus is already CANCELED so it cannot be modified again at tick {TaskManager.get_current_tick()}")
        else:
            self.status = status

    def cancel(self) -> None:
        """
        Cancel this task.
        """
        self.status = TaskStatus.CANCELED

    def set_next_ticks_per_run(self, ticks: int) -> None:
        """
        Set the number of ticks until the next run of this task.

        Args:
            ticks: The number of ticks until the next run
        """
        self.next_execution_ticks_per_run = ticks

    def get_next_ticks_per_run(self) -> int:
        """
        Get the number of ticks until the next run of this task.

        Returns:
            The number of ticks until the next run
        """
        return self.next_execution_ticks_per_run

    def get_controlling_task_spawner(self) -> Optional[TaskSpawner]:
        """
        Get the TaskSpawner that controls this task.

        Returns:
            The TaskSpawner that controls this task
        """
        return self.controlling_ts

    def set_controlling_task_spawner(self, ts: TaskSpawner) -> None:
        """
        Set the TaskSpawner that controls this task.

        Args:
            ts: The TaskSpawner that controls this task
        """
        self.controlling_ts = ts

    def call(self) -> 'FrameworkTask':
        """
        Call this task. This method should not be called directly nor should it be overridden.
        Override run_this_framework_task() instead.

        Returns:
            This task
        """
        self.next_execution_ticks_per_run = self.ticks_per_run
        try:
            self.run_this_framework_task()
        except Exception as e:
            from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
            # 50的倍数再打印
            if TaskManager.get_current_tick() % 5 == 0:
                self.logger.warning(f"Exception encountered during the execution of task {self.task_name} at tick {TaskManager.get_current_tick()}. \n {e}")
            # print(f"报错10----》 {str(e)}")
        return self

    def run_this_framework_task(self) -> None:
        """
        To be overridden by extending classes. Overriding method should execute a
        handful of statements considered to constitute a single iteration of the
        task. For example, a codelet might look in a buffer for some
        content and make a change to it in a single iteration.
        The overriding method may also change the TaskStatus of a task.
        For example, if the task should only run once and stop, then the method cancel()
        may be used to stop the task from further execution (calls of this run_this_framework_task() beyond the current one.
        """
        raise NotImplementedError("Subclasses must implement run_this_framework_task()")

    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module for this task.
        Subclasses may override this method.

        Args:
            module: The module to associate with this task
            module_usage: How this task will use the module
        """
        pass

    def get_param(self, name: str, default_value: Any) -> Any:
        """
        Get a parameter value with a default.

        Args:
            name: The name of the parameter
            default_value: The default value

        Returns:
            The parameter value or the default value
        """
        if hasattr(self, "parameters") and self.parameters and name in self.parameters:
            return self.parameters[name]
        return default_value

    def set_param(self, name: str, value: Any) -> None:
        """
        Set a parameter value.

        Args:
            name: The name of the parameter
            value: The value to set
        """
        if not hasattr(self, "parameters") or self.parameters is None:
            self.parameters = {}
        self.parameters[name] = value

    def get_parameters(self) -> Dict[str, Any]:
        """
        Get all parameters.

        Returns:
            All parameters
        """
        return getattr(self, "parameters", {})

    def __eq__(self, other) -> bool:
        """
        Check if this task is equal to another.

        Args:
            other: The other task to compare with

        Returns:
            True if the tasks have the same ID, False otherwise
        """
        if isinstance(other, FrameworkTaskImpl):
            return self.task_id == other.get_task_id()
        return False

    def __hash__(self) -> int:
        """
        Return the hash code of this task.

        Returns:
            The hash code of the task ID
        """
        return self.task_id

    def __str__(self) -> str:
        """
        Return the string representation of this task.

        Returns:
            The name of this task
        """
        return self.task_name
