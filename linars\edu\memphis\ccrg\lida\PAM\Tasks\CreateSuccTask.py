#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to create a successor.
"""

from typing import List, Optional

from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory


class CreateSuccTask(FrameworkTaskImpl):
    """
    A task to create a successor.

    构建完当前，则按顺承等链接往下构建，树状构建，后面的时序可以查到并加入buffer，方便查，
    提高性能？继续构建放到独立新任务去，以免时序构建过度重叠，稍微拉长构建路径
    异步构建，任务间可以互相监督，思维可以按任务流程来控制？也要按内容和激活激励

    认知构建期间的各变量值需要维持，动机目标需要维持，工作记忆维持，注意维持，思维维持
    识别判断流程+内容+激活激励，统筹规划，完全停止+构建中掐断+再唤醒
    """

    def __init__(self, link: Link, pam: PAMemory, ticks_per_run: int, act_stamp: Optional[str] = None):
        """
        Initialize a CreateSuccTask.

        Args:
            link: The link
            pam: The PAMemory
            ticks_per_run: The ticks per run
            act_stamp: The action stamp
        """
        super().__init__(1, "tact")
        self.sink = link.get_sink()
        self.source = link.get_source()
        self.pam = pam
        self.act_stamp = act_stamp

        self.link = None
        self.seq_ns = None
        self.goal_ns = None
        self.cosc_list = None

    def run_this_framework_task(self):
        """
        Run the task.
        """
        self.seq_ns = self.pam.get_workspace_buffer("seq").get_buffer_content(None)
        self.goal_ns = self.pam.get_workspace_buffer("goal").get_buffer_content(None)

        h_name = self.source.get_tn_name()
        t_name = self.sink.get_tn_name()
        query = f"match (n:场景)-[r:时序]->(m:场景)<-[r0:顺承]-(i:场景) where n.name = '{h_name}' and i.name = '{t_name}' return r"
        print(f"DoSuccTask----query = {query}")
        link1 = None

        try:
            # 使用NeoUtil获取链接
            link1 = NeoUtil.get_link_cypher(query)
            if link1 is not None:
                to_node = link1.get_sink()
                self.pam.get_listener().receive_percept(to_node, ModuleName.SeqGraph)
                self.pam.get_listener().receive_percept(link1, ModuleName.SeqGraph)

                to_node.set_incentive_salience(self.sink.get_incentive_salience())
                print(f"顺承时序---|||-{link1}")

            # 如果有后续时序，需要再递归遍历到最右子时序
            if link1 is not None:
                self.pam.get_act_root(link1, False, False, self.act_stamp)
            else:
                is_loop_done = False
                # 如果是循环体，需要重复构建。先判断头节点是否有循环标签，然后查循环条件，满足则继续构建，不满足则回溯
                from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                if h_name in AgentStarter.for_map:
                    # do while循环，之前已经构建过一次，所以这里先判断条件，再构建一次
                    query = f"match (n)-[r:循环条件]->(m) where id(m) = {self.source.get_node_id()} return r"
                    p_link0 = self.seq_ns.get_do_main_path_map().get(self.act_stamp)[1]
                    from linars.edu.memphis.ccrg.lida.PAM.Tasks.DoSelectTreeTask import DoSelectTreeTask
                    done = None
                    link2 = None

                    # 使用NeoUtil获取链接
                    link2 = NeoUtil.get_link_cypher(query)
                    c_link_list = []
                    done_num = 0
                    if link2 is not None:
                        self.cosc_list = []
                        # 参数：link无用，第二个是条件框架节点
                        done = DoSelectTreeTask.get_result(self.link, link2.get_source(), c_link_list, done_num, self.cosc_list)
                    else:
                        print("---------循环条件----空------------")

                    if done is not None and done.done_num == 2:
                        # 循环条件满足，继续构建
                        # 循环体构建跟普通时序一致。do的部分。目前默认主路径第一条
                        self.pam.get_act_root(p_link0, False, False, self.act_stamp)

                        sb0 = DoSelectTreeTask.get_string_builder_safe(self.cosc_list)

                        if sb0:
                            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                            AgentStarter.nar.add_input_to(f"(^say,{{SELF}},(#,{sb0}))! :|:", self.goal_ns)
                            print(f"------succ0---判断首和判断---|||---s-a-y----{sb0}")
                        else:
                            print(f"---------判断首和判断----s-a-y----空--------------{link2}")

                        is_loop_done = True

                # 如果不是循环体，或者循环条件不满足，则回溯上位时序，继续构建
                if not is_loop_done:
                    seq_main_path = []
                    is_only_if = False
                    # todo 放在动机系统，判断构建情况，需要反馈，有动机注意
                    # 判断时序构建完和判断遍历完，只会出现一个，时序构建完后，直接回溯上位时序，而非上位判断边
                    seq_main_path = self.seq_ns.get_do_main_path_map().get(self.act_stamp)
                    if seq_main_path is not None and seq_main_path:
                        # 倒序遍历回溯到上位时序，继续构建
                        p_link1 = seq_main_path[len(seq_main_path) - 1]
                        if p_link1.get_category() == "判断" or p_link1.get_category() == "判断首":
                            if len(seq_main_path) > 1:
                                p_link1 = seq_main_path[len(seq_main_path) - 2]
                                seq_main_path.pop()
                            else:
                                is_only_if = True

                        if not is_only_if:
                            # 回溯后按常规往下构建
                            do_succ_task = CreateSuccTask(p_link1, self.pam, 30, self.act_stamp)
                            self.pam.get_assisting_task_spawner().add_task(do_succ_task)

                        # 构建完，回溯后删除当前时序，以便下次回溯
                        seq_main_path.pop()
        except Exception as e:
            print(e)

        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        AgentStarter.is_do_var = True
        AgentStarter.do_start_tick = TaskManager.get_current_tick()

        self.cancel()
