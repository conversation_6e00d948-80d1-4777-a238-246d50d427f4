﻿{% extends "share/layout.html" %}

{% block content %}
<link href="{{ url_for('static', filename='css/jquery.jsonview.min.css') }}" rel="stylesheet">
<link href="{{ url_for('static', filename='wangeditor/wangEditor.css') }}" rel="stylesheet"/>
<link href="{{ url_for('static', filename='js/chatC/res.layui.com/layui/src/css/layui.css') }}" rel="stylesheet"/>
<link href="{{ url_for('static', filename='js/chatC/res.layui.com/layui/src/css/layui.demo.css') }}" rel="stylesheet"/>
<!-- 添加jQuery和Vue.js库 -->
<!--	<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>-->
<!--	<script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>-->
<!--	<script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.6/lib/index.js"></script>-->
<!--	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.6/lib/theme-chalk/index.css">-->

	<!-- 添加错误处理脚本 -->
<!--	<script src="{{ url_for('static', filename='js/error-handler.js') }}"></script>-->

<!-- 定义contextRoot变量 -->
<!--	<script>-->
<!--		var contextRoot = "/";-->
<!--	</script>-->


<body>
	<div layout:fragment="content">
	<!-- Vue内容 -->
	<div class="mind-box">
    <!-- 左侧 -->
    <el-scrollbar class="mind-l">
      <div class="ml-m">
<!-- style="min-height:280px"  -->
        <div class="ml-a-box" >
          <el-button type="info"style="margin: 2px 0 4px 2px;" plain size="small" @click="createdomain">新建</el-button>
          {% raw %}
          <a @click="matchdomaingraph(m,$event)" v-for="m in pageModel.nodeList" href="javascript:void(0)">
          	<el-tag style="margin:2px" @close="deletedomain(m.id,m.name)">{{m.name}}</el-tag>
          </a>
          {% endraw %}
          {% raw %}
          <el-button v-if="pageModel.pageIndex<pageModel.totalPage" type="info"style="margin: 2px 0 4px 2px;" plain size="small" @click="getmoredomain">加载更多</el-button>
          {% endraw %}
        </div>
      </div>
    </el-scrollbar>
    <!-- 左侧over -->
    <!-- 右侧 -->
    <div class="mind-con">
      <!-- 头部 -->
      <div class="mind-top clearfix">
        <div class="fl">
			{% raw %}
			<div class="search" v-show="domain!=''">
				<el-button @click="getdomaingraph(0)">
				  <svg class="icon" aria-hidden="true">
					<use xlink:href="#icon-search"></use>
				  </svg>
				</el-button>
				<el-input placeholder="请输入关键词" v-model="nodename" @keyup.enter.native="getdomaingraph"></el-input>
			</div>
			  <span v-show="domain!=''">
				  <span class="dibmr">
					<span>显示节点个数：</span>
					<a v-for="(m,index) in pagesizelist" @click="setmatchsize(m,this)" :title="m.size" href="javascript:void(0)" :class="[m.isactive ? 'sd-active' : '', 'sd']" >{{m.size}}</a>
				  </span>
			  </span>
			{% endraw %}
        </div>
        <div class="fr">
			<a href="javascript:void(0)" @click="getlabellist" class="svg-a-sm">
				<i class="el-icon-tickets" >更新标签</i>
			</a>
			<a href="javascript:void(0)" @click="cypherjson" class="svg-a-sm">
				<i class="el-icon-tickets" >显示json</i>
			</a>
			<a href="javascript:void(0)" @click="showCypher" class="svg-a-sm">
				<i class="el-icon-caret-right">Cypher</i>
			</a>
<!--			<a href="javascript:void(0)" @click="modeling" class="svg-a-sm">-->
<!--				<i class="el-icon-caret-right">建模</i>-->
<!--			</a>-->
          	<a href="javascript:void(0)" @click="updategraph" class="svg-a-sm">
            <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-shuaxin"></use>
            </svg>刷新
          </a>
          <a href="javascript:void(0)" @click="requestFullScreen" class="svg-a-sm" >
            <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-quanp"></use>
            </svg>全屏
          </a>
          <el-dropdown @command="operateCommand">
			  <el-button type="primary">
				操作<i class="el-icon-arrow-down el-icon--right"></i>
			  </el-button>
			  <el-dropdown-menu slot="dropdown">
				  <el-dropdown-item command="image">导出图片</el-dropdown-item>
				  <el-dropdown-item command="import">导入</el-dropdown-item>
				  <el-dropdown-item command="export">导出</el-dropdown-item>
			  </el-dropdown-menu>
			</el-dropdown>
        </div>
      </div>
		<div class="cypher_toolbar clearfix" v-show="cyphertextshow">
			<div style="width: 70%;float: left">
				<el-input id="docypher" type="textarea"  :rows="2" placeholder="请输入Cypher" v-model="cyphertext" style="font-size: 18px;"> </el-input>
			</div>
			<div >
				<el-button type="success" @click="cypherrun" style="margin-left: 15px;" icon="el-icon-caret-right" plain>执行</el-button>
				<el-button type="success" @click="clean" style="margin-left: 15px;" icon="el-icon-caret-right" plain>清屏</el-button>
			</div>
			<div style="float: left">
				<el-button type="primary" @click="text13()">权重</el-button>
				<el-button type="primary" @click="text12()">查边</el-button>
				<el-button type="primary" @click="text11()">查点</el-button>

				<el-button type="primary" @click="text1()">边属</el-button>
				<el-button type="primary" @click="text2()">点属</el-button>

				<el-button type="primary" @click="text3()">批量删</el-button>
				<el-button type="primary" @click="text4()">删点</el-button>
				<el-button type="primary" @click="text5()">删边</el-button>

				<el-button type="primary" @click="text6()">删签</el-button>
				<el-button type="primary" @click="text7()">加签</el-button>
				<el-button type="primary" @click="text8()">改签</el-button>

				<el-button type="primary" @click="text9()">加点</el-button>
				<el-button type="primary" @click="text10()">加多点</el-button>

			</div>
		</div>

      <!-- 头部over -->
      <!-- 中部 -->
      <el-scrollbar class="mind-cen" id="graphcontainerdiv">
		<div id="nodedetail" class="node_detail">
<!--			 <h5>详细数据</h5>-->
			 <span class="node_pd" v-for="(m,k) in nodedetail">{{k}}:{{m}}</span>
		</div>
		  <div id="linkattr" class="node_detail">
			  <h5>详细数据</h5>
			  <span class="node_pd" v-for="(m,k) in linkattr">{{k}}:{{m}}</span>
		  </div>
	  <el-scrollbar v-show="jsonshow" id="jsoncontainer"  class="jsoncontainer">
		  <pre id="json-renderer"></pre>
	  </el-scrollbar>
        <div id="graphcontainer"  class="graphcontainer"></div>
      </el-scrollbar>
      <!-- 中部over -->
        <div class="svg-set-box"></div>
      <!-- 底部 -->
		<el-dialog title="csv导入图谱" :visible.sync="dialogFormVisible" width="350px">
			  <el-form >
				  <div><span>注意字符集为utf-8无bom格式，三元组结构：节点-节点-关系</span></div>
			    <el-form-item label="图谱领域" label-width="70px">
			      <el-autocomplete style="width:100%"
				      v-model="uploadparam.domain" placeholder="请输入内容"><!--:fetch-suggestions="querySearch"-->
				  </el-autocomplete>
			    </el-form-item>
			    <el-form-item label="选择文件" label-width="70px">
				      <el-upload
					  class=""
					  :headers="headers"
					  ref="upload"
					  :action="uploadurl"
					  accept=".csv,.xls,.xlsx"
					  :show-file-list="true"
					  :data="uploadparam"
					  :on-success="csvsuccess"
					  :auto-upload="false">
					  <el-button  slot="trigger" class="btn-bo" style="padding: 12px 24px;margin-bottom: 0px;" >
		                <svg class="icon" aria-hidden="true">
		                  <use xlink:href="#icon-daoru"></use>
		                </svg>
		              	  选择文件
		              </el-button>
					</el-upload>
			    </el-form-item>
			  </el-form>
			  <div slot="footer" class="dialog-footer">
			    <el-button @click="dialogFormVisible = false">取 消</el-button>
			    <el-button type="primary" @click="submitUpload">确 定</el-button>
			  </div>
		</el-dialog>

		<el-dialog title="导出图谱csv" :visible.sync="exportFormVisible" width="350px">
			  <el-form >
			    <el-form-item label="图谱领域" label-width="70px">
			      <el-autocomplete style="width:100%"
				      v-model="uploadparam.domain" placeholder="请输入内容"><!--:fetch-suggestions="querySearch"-->
				  </el-autocomplete>
			    </el-form-item>
			    <el-button type="primary" @click="exportcsv">确 定</el-button>
				  </el-form >
		</el-dialog>

		<el-dialog id="editform" title="节点属性编辑" :visible.sync="nodeisedit" width="350px">
			<el-tabs type="card" tab-position="top" v-model="propactiveName" @tab-click="prophandleClick" style="margin: 10px">

<!--				<el-tab-pane label="属性编辑" name="propedit">-->
<!--					<el-form :model="graphEntity">-->
<!--						<el-form-item label="标签"  label-width="70px">-->
<!--							<el-input v-model="graphEntity.label0" style="width:324px"></el-input>-->
<!--						</el-form-item>-->
<!--					</el-form>-->
<!--				  <el-form :model="graphEntity">-->
<!--					<el-form-item label="名称"  label-width="70px">-->
<!--					  <el-input v-model="graphEntity.name" style="width:324px"></el-input>-->
<!--					</el-form-item>-->
<!--				  </el-form>-->
<!--				</el-tab-pane>-->

				<el-tab-pane :model="graphEntity" label="自由属性" name="propedit">
					<el-form >
						<el-form-item :label='k' v-for="(m,k) in graphEntity" :key="k" label-width="70px">
							<el-input v-model="graphEntity[k]" style="width:200px"></el-input>
							<!--							<el-button type="primary" @click="dellinkattr">删除</el-button>-->
						</el-form-item>
					</el-form>
				</el-tab-pane>

				<el-tab-pane label="可视化" name="propedit00">
					<el-form :model="graphEntity">
						<el-form-item label="颜色" label-width="70px">
							<el-color-picker id="colorpicker" v-model="graphEntity.color" :predefine="predefineColors">
							</el-color-picker>
						</el-form-item>
						<el-form-item label="半径" label-width="70px">
							<el-slider v-model="graphEntity.r" style="width:324px"></el-slider>
						</el-form-item>
					</el-form>
				</el-tab-pane>
				<el-tab-pane label="添加图片" name="propimage">
					<el-form >
						<el-form-item label="本地上传"  label-width="70px">
						  <el-upload class="" :headers="headers"
									 name="file"
									 ref="upload"
									 :action="uploadimageurl"
									 accept=".jpg,.png"
									 multiple
									 :show-file-list="false"
									 :data="uploadimageparam"
									 :on-success="uploadsuccess"
									 :auto-upload="true">
						   <el-button slot="trigger" size="small" type="primary">选择</el-button>
						  </el-upload>
						</el-form-item>
						<el-form-item label="网络地址" label-width="70px">
						 <el-input v-model="netimageurl" style="width: 60%"></el-input>
						  <a href="javascript:void(0)" @click="addnetimage" class="cg">
							  <svg class="icon" aria-hidden="true">
								  <use xlink:href="#icon-add-s"></use>
							  </svg>
						  </a>
						</el-form-item>
						<el-form-item label="已选图片" label-width="70px">
							 <ul class="el-upload-list el-upload-list--picture-card">
									<li v-for="item in nodeimagelist" class="el-upload-list__item is-success">
										<img :src="imageurlformat(item)" alt="" class="el-upload-list__item-thumbnail">
										<label class="el-upload-list__item-status-label">
											<i class="el-icon-upload-success el-icon-check"></i>
										</label>
										<i class="el-icon-close" @click="imagehandleRemove(item)"></i>
										<span class="el-upload-list__item-actions">
											<span class="el-upload-list__item-preview">
												 <i class="el-icon-zoom-in" @click="handlePictureCardPreview(item)"></i>
											</span>
											<span class="el-upload-list__item-delete">
												<i class="el-icon-delete" @click="imagehandleRemove(item)"></i>
											</span>
										</span>
									</li>
								</ul>
						</el-form-item>
					</el-form>
				  </el-tab-pane>
				<el-tab-pane label="添加描述" name="richtextedit">
	              <div ref="eidtorToolbar" id="eidtorToolbar" class="wange-toolbar"></div>
	              <div ref="eidtorContent" id="eidtorContent" class="wangeditor-form"></div>
	          	</el-tab-pane>
			</el-tabs>
		  	<div slot="footer" class="dialog-footer">
			    <el-button v-show="propactiveName=='propimage'" type="primary" @click="savenodeimage" class="btn-line cur">保存</el-button>
			    <el-button v-show="propactiveName=='richtextedit'" @click="savenodecontent" type="primary" class="btn-line cur">保存</el-button>
				<el-button v-show="propactiveName=='propedit00'" type="primary" @click="makevisible">更新</el-button>
			    <el-button v-show="propactiveName=='propedit'" type="primary" @click="updatenode">更新</el-button>
	          	<el-button v-show="propactiveName=='propedit'&&graphEntity.id==0" type="primary" @click="createNode">创建</el-button>
	    		<el-button @click="resetsubmit">取消</el-button>
			  </div>
		</el-dialog>

		<el-dialog id="editform0" title="关系属性编辑" :visible.sync="linkisedit" width="350px">
			<el-tabs type="card" tab-position="top" v-model="propactiveName0" @tab-click="prophandleClick0" style="margin: 10px">

				<el-tab-pane :model="linkattr1" label="自由属性" name="linkattr1">
					<el-form >
						<el-form-item :label='k' v-for="(m,k) in linkattr1" :key="k" label-width="70px">
							<el-input v-model="linkattr1[k]" style="width:200px"></el-input>
<!--							<el-button type="primary" @click="dellinkattr">删除</el-button>-->
						</el-form-item>
					</el-form>
				</el-tab-pane>

				<el-tab-pane label="固定属性" name="propedit0">
					<el-form :model="linkattr">
						<el-form-item label="类型"  label-width="70px">
							<el-input v-model="linkattr.type" style="width:150px"></el-input>
						</el-form-item>
						<el-form-item label="id"  label-width="70px">
							<el-input v-model="linkattr.id" style="width:150px"></el-input>
						</el-form-item>
						<el-form-item label="起点"  label-width="70px">
							<el-input v-model="linkattr.sourceid" style="width:150px"></el-input>
						</el-form-item>
						<el-form-item label="终点"  label-width="70px">
							<el-input v-model="linkattr.targetid" style="width:150px"></el-input>
						</el-form-item>
					</el-form>
				</el-tab-pane>
			</el-tabs>
			<div slot="footer" class="dialog-footer">
				<el-button v-show="propactiveName0=='linkattr1'&&linkattr.id!=0" type="primary" @click="updatelink">更新</el-button>
<!--				<el-button v-show="propactiveName0=='linkattr1'&&linkattr.id!=0" type="primary" @click="createlinkattr">新增</el-button>-->
				<el-button v-show="propactiveName0=='propedit0'&&linkattr.id!=0" type="primary" @click="changelink">更换</el-button>
				<el-button v-show="propactiveName0=='propedit0'&&linkattr.id==0" type="primary" @click="createlink">创建</el-button>
				<el-button @click="resetsubmit">取消</el-button>
			</div>
		</el-dialog>

		<el-dialog id="editform00" title="新建节点" :visible.sync="iscreatenode" width="350px">
			<el-tabs type="card" tab-position="top" v-model="propactiveName00" @tab-click="prophandleClick00" style="margin: 10px">
				<el-tab-pane label="属性编辑" name="propedit00">
					<el-form :model="graphEntity">
						<el-form-item label="标签"  label-width="70px">
							<el-input v-model="graphEntity.label0" style="width:150px"></el-input>
						</el-form-item>
						<el-form-item label="名称"  label-width="70px">
							<el-input v-model="graphEntity.name" style="width:150px"></el-input>
						</el-form-item>
					</el-form>
				</el-tab-pane>

			</el-tabs>
			<div slot="footer" class="dialog-footer">
				<el-button v-show="propactiveName00=='propedit00'" type="primary" @click="createNode">创建</el-button>
				<el-button @click="resetsubmit">取消</el-button>
			</div>
		</el-dialog>


		<el-dialog id="batchcreateform" :title="operatenameformat(operatetype)":visible.sync="isbatchcreate" width="350px">

			<el-form class="mb-r">
				<el-form-item label="源节点"  label-width="70px" v-show="operatetype!=2" style="margin:10px;">
					<div class="mb-con">
						<el-input v-model="batchcreate.sourcenodename"></el-input>
						<span v-show="operatetype==3" class="mb-label">（只能一个）</span>
						<span v-show="operatetype==1" class="mb-label">（多个以英文逗号隔开）
						</span>
					</div>
				</el-form-item>

				<el-form-item label="关系"  label-width="70px" v-show="operatetype!==1" style="margin:10px;">
					<div class="mb-con" v-show="operatetype!==1">
						<el-input v-model="batchcreate.relation"></el-input>
						<div class="mb-label">（只能一个）</div>
					</div>
				</el-form-item>

				<el-form-item label="子节点" v-show="operatetype!=1" label-width="70px" style="margin:10px;">
					<div class="mb-con">
						<el-input v-model="batchcreate.targetnodenames"></el-input>
						<span class="mb-label " v-show="operatetype==3">（英逗号隔开,可不填）
						</span>
						<span class="mb-label " v-show="operatetype==2">（英文逗号隔开）
						</span>
					</div>
				</el-form-item>
			</el-form>
			<div slot="footer" class="dialog-footer">
				<el-button v-show="operatetype==1" type="primary" @click="batchcreatesamenode">确定</el-button>
				<el-button v-show="operatetype==2" type="primary" @click="batchcreatechildnode">确定</el-button>
				<el-button v-show="operatetype==3" type="primary" @click="batchcreatenode">确定</el-button>
				<el-button @click="resetsubmit">取消</el-button>
			</div>
		</el-dialog>
      <!-- 底部over -->
    </div>
    <!-- 右侧over -->
    <!-- 空白处右键 -->
    <ul class="el-dropdown-menu el-popper blankmenubar" id="blank_menubar" style="display: none;">
    	<li  class="el-dropdown-menu__item" @click="btnaddsingle">
	    	<svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-jiedian"></use>
            </svg>
			<span class="pl-15">添加节点</span>
        </li>
	    <li  class="el-dropdown-menu__item" @click="btnquickaddnode">
	    	<svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-add-rd"></use>
            </svg>
			<span class="pl-15">快速添加</span>
        </li>
    </ul>
    <!-- 连线按钮组 -->
    <ul class="el-dropdown-menu el-popper linkmenubar" id="link_menubar" style="display: none;">
    	<li  class="el-dropdown-menu__item" @click="updatelink0">
	    	<svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-editor"></use>
            </svg>
			<span class="pl-15">编辑</span>
        </li>
	    <li  class="el-dropdown-menu__item" @click="deletelink">
	    	<svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-shanchu"></use>
            </svg>
			<span class="pl-15">删除</span>
        </li>
    </ul>
  	<!-- 富文本展示 -->
    <div id="richContainer" style="display: none;width: 400px">
		<div class="mind-fj-box" v-show="showImageList.length>0 ||editorcontent!=''">
			<div class="mind-carousel" v-show="showImageList.length>0">
				<el-carousel height="197px" :interval="2000" arrow="always">
					<el-carousel-item v-for="item in showImageList" :key="item.ID">
						<div class="carous-img">
							<img  :src="item.FileName" alt="">
						</div>
					</el-carousel-item>
				</el-carousel>
			</div>
			<el-scrollbar v-show="editorcontent!=''" class="mind-fj-p">
				<p v-html="editorcontent" ></p>
			</el-scrollbar>
		</div>
	</div>

  </div>
	</div>


<!--</div>-->
</div>
</body>
{% endblock %}

{% block jscontent %}
<!--<div layout:fragment="jscontent">-->
    <!--	<script th:src="@{/js/directives.js}"></script>-->
    <!--	<script src="https://d3js.org/d3.v4.js"></script>-->
    <script src="{{ url_for('static', filename='js/jquery.jsonview.js') }}"></script>
    <script src="{{ url_for('static', filename='static/d3.js') }}"></script>
    <!--</head>-->

    <script src="{{ url_for('static', filename='js/lodash.min.js') }}"></script>

    <script src="{{ url_for('static', filename='js/html2canvas.min.js') }}"></script>

    <script src="{{ url_for('static', filename='js/chatC/res.layui.com/layui/src/layui.js') }}"></script>
<!--    <script src="{{ url_for('static', filename='js/chatC/res.layui.com/mods/contextMenu.js') }}"></script>-->
<!--    <script src="{{ url_for('static', filename='js/chatC/res.layui.com/mods/socket.js') }}"></script>-->
    <script src="{{ url_for('static', filename='js/chatC/res.layui.com/mods/webim.config.js') }}"></script>
    <script src="{{ url_for('static', filename='js/chatC/res.layui.com/mods/websdk.js') }}"></script>

    <script  type="text/javascript" src="{{ url_for('static', filename='wangeditor/wangEditor.js') }}"></script>
    <script  type="text/javascript">

        layui.config({
            base: '/js/chatC/res.layui.com/mods/'
        }).extend({
            socket: 'socket',
        });

        layui.use(['layim','socket','req'], function(layim,socket,req){
            var layim = layui.layim;
            var socket = layui.socket;
            // var selfFlag = false;
            var cache = layim.cache();
            var $ = layui.jquery;

            //基础配置
            layim.config({
                // 初始化接口
                init: {
                    "mine": {
                        "username": "柯东"
                        ,"id": "100000"
                        ,"status": "online"
                        ,"sign": "精神物质是一体的"
                        ,"avatar": "/images/me.png"
                    }
                    ,"friend": [{
                        "groupname": "球长助理"
                        ,"id": 0
                        ,"list": [{
                            "username": "mos"
                            ,"id": "100001"
                            ,"avatar": "/images/logo/logo-3.jpg"
                            ,"sign": "世界那么大，我们要拿下"
                            ,"status": "online"
                        }]
                    }]
                    // url: '/webim/base'
                }
                // //查看群员接口
                // ,members: {
                // 	url: '/webim/group/members'
                // }
                //
                // ,uploadImage: {
                // 	url: '/imupload/file' //（返回的数据格式见下文）
                // }
                // ,uploadFile: {
                // 	url: '/imupload/file' //（返回的数据格式见下文）
                // }

                ,isAudio: true //开启聊天工具栏音频
                ,isVideo: true //开启聊天工具栏视频

                //扩展工具栏
                // ,tool: [{
                //     alias: 'code'
                //     ,title: '代码'
                //     ,icon: '&#xe64e;'
                // }]

                //,brief: true //是否简约模式（若开启则不显示主面板）
                //,title: 'WebIM' //自定义主面板最小化时的标题
                //,right: '100px' //主面板相对浏览器右侧距离
                //,minRight: '90px' //聊天面板最小化时相对浏览器右侧距离

                ,initSkin: '3.jpg' //1-5 设置初始背景
                //,skin: ['aaa.jpg'] //新增皮肤
                ,isfriend: true //是否开启好友
                // ,isgroup: false //是否开启群组
                ,min: true //是否始终最小化主面板，默认false
                ,notice: true //是否开启桌面消息提醒，默认false
                ,voice: false //声音提醒，默认开启，声音文件为：default.mp3

                // ,msgbox: '../../template/1/default/_files/chatC/www.layui.com/msgbox.html' //消息盒子页面地址，若不开启，剔除该项即可
                // ,find: '../../template/1/default/_files/chatC/www.layui.com/find.html' //发现页面地址，若不开启，剔除该项即可
                ,chatLog: '../../template/1/default/_files/chatC/www.layui.com/chatlog.html' //聊天记录页面地址，若不开启，剔除该项即可
                // , createGroup: '../../template/1/default/_files/chatC/www.layui.com/createGroup.html' //创建群页面地址，若不开启，剔除该项即可
                , Information:  '../../template/1/default/_files/chatC/www.layui.com/getInformation.html' //好友群资料页面

                //自定义皮肤
                ,uploadSkin: {
                    url: '../../../../../../webim/uploadSkin'
                }
                //选择系统皮肤
                ,systemSkin: {
                    url: '../../../../../../webim/systemSkin'
                }
                //获取推荐好友
                ,getRecommend:{
                    url: '../../../../../../webim/getRecommend'
                    , type: 'get' //默认
                }
                //查找好友总数
                ,findFriendTotal:{
                    url: '../../../../../../webim/findFriendTotal'
                    , type: 'get' //默认
                }
                //查找好友
                ,findFriend:{
                    url: '../../../../../../webim/findFriend'
                    , type: 'get' //默认
                }

                // //获取好友资料
                // ,getInformation:{
                //     url: '../../../../../../webim/getInformation'
                //     , type: 'get' //默认
                // }
                // //保存我的资料
                // ,saveMyInformation:{
                //     url: '../../../../../../webim/saveMyInformation'
                // }
                // //提交建群信息
                // ,commitGroupInfo:{
                //     url: '../../../../../../webim/commitGroupInfo'
                //     , type: 'get' //默认
                // }

                //获取系统消息
                ,getMsgBox:{
                    url: '../../../../../../webim/getMsgBox'
                    , type: 'get' //默认post
                }
                //获取总的记录数
                ,getChatLogTotal:{
                    url: '../../../../../../webim/message/getChatLogTotal'
                    , type: 'get' //默认post
                }
                //获取历史记录
                ,getChatLog:{
                    url: '../../../../../../webim/message/getChatLog'
                    , type: 'get' //默认post
                }

            });

            // socket.config({
            // 	log:true,
            // 	token:'/webim/token',
            // 	server:'ws://localhost:8666',
            // 	user:layim.cache().friend,
            // 	pwd:''
            // });

            socket.on('open',function (e) {
                console.log("监听到事件：open");
            });
            socket.on('close',function (e) {
                console.log("监听到事件：close");
            });
            socket.on('error',function (e) {
                console.log("监听到事件：error");
            });
            socket.on('msg',function (e) {
                var msg = JSON.parse(e.data);
                var data = msg.data;
                console.log(msg);
                layim.getMessage(data);
            });
            console.log(socket.msgType);


            //监听在线状态的切换事件
            layim.on('online', function(status){
                layer.msg(status);
            });

            //监听签名修改
            layim.on('sign', function(value){
                layer.msg(value);
            });

            //监听自定义工具栏点击，以添加代码为例
            layim.on('tool(code)', function(insert){
                layer.prompt({
                    title: '插入代码 - 工具栏扩展示例'
                    ,formType: 2
                    ,shade: 0
                }, function(text, index){
                    layer.close(index);
                    insert('[pre class=layui-code]' + text + '[/pre]'); //将内容插入到编辑器
                });
            });

            //监听发送消息
            // layim.on('sendMessage', function(data){
            //     var To = data.to;

            // if(To.type === 'friend'){
            //     layim.setChatStatus('<span style="color:#FF5722;">对方正在输入。。。</span>');
            // }

            // //t是是与否，判断
            // var t = data.to.type=='friend';
            // if(!t){
            //     selfFlag = true;
            // }

            // socket.send({
            //     msgType:(t?socket.msgType.chatFriend:socket.msgType.chatGroup)
            //     ,content:data.mine.content
            //     ,toId:data.to.id});

            //     return;
            // });

            // setInterval(layim.getMessage0(),500);
            setTimeout(function(){
                layim.setIntervalGet0();
            }, 5000);



            $('.site-demo-layim').on('click', function(){
                var type = $(this).data('type');
                active[type] ? active[type].call(this) : '';
            });
        });

        // var initdomain = function () {
        //     var domain = [[${domain}]];
        //     var domainid = [[${domainid}]];
        //     this.domain = domain;
        //     this.domainid = domainid;
        // }

        // function getMessage0() {
        // 	$.ajax({
        // 		url: '/webim/getMessage',
        // 		async:true,
        // 		type: 'get',
        // 		success: function (data) {
        // 			if(data.data != null){
        // 				layim.getMessage(data.data);
        // 			}
        // 		}
        // 	})
        // }

    </script>
<!--    <script type="text/javascript" src="{{ url_for('static', filename='js/kgbuilder.js') }}"></script>-->

    <!--	<script type="text/javascript" src="{{ url_for('static', filename='js/d3.v4.js') }}"></script>-->
    <script type="text/javascript" src="{{ url_for('static', filename='js/kgbuilder.js') }}"></script>
<!--</div>-->
{% endblock %}