# LIDA Cognitive Framework
"""
The Workspace contains the Perceptual and Episodic Buffers as well as the Broadcast Queue and Current Situational Model.
This class implements the Facade pattern. Any outside module that wishes to access and/or
modify these Workspace components must do so through this class.
Thus this class defines the methods to access the data of these submodules.
"""

import logging
from typing import List, Any
from typing import Optional, List, Dict, Any

from linars.edu.memphis.ccrg.lida.EpisodicMemory.CueListener import CueListener
from linars.edu.memphis.ccrg.lida.EpisodicMemory.LocalAssociationListener import LocalAssociationListener
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkImpl import LinkImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener import BroadcastListener
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.PAM.PamListener import PamListener
from linars.edu.memphis.ccrg.lida.Workspace.Workspace import Workspace
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WorkspaceBuffer import WorkspaceBuffer
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceListener import WorkspaceListener

from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.term import Term

# 延迟导入Memory以避免循环导入
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.linars.memory import Memory

from linars.org.opennars.entity.budget_value import BudgetValue
from linars.org.opennars.entity.sentence import Sentence
from linars.org.opennars.entity.stamp import Stamp
from linars.org.opennars.entity.task import Task, EnumType
from linars.org.opennars.entity.truth_value import TruthValue
from linars.org.opennars.inference.temporal_rules import TemporalRules
from linars.org.opennars.io.parser import Parser
from linars.org.opennars.language.equivalence import Equivalence
from linars.org.opennars.language.implication import Implication
from linars.org.opennars.language.inheritance import Inheritance
from linars.org.opennars.language.similarity import Similarity


class WorkspaceImpl(FrameworkModuleImpl, Workspace, PamListener, LocalAssociationListener, BroadcastListener):
    """
    The Workspace contains the Perceptual and Episodic Buffers as well as the Broadcast Queue and Current Situational Model.
    This class implements the Facade pattern. Any outside module that wishes to access and/or
    modify these Workspace components must do so through this class.
    Thus this class defines the methods to access the data of these submodules.
    """

    def __init__(self):
        """
        Default constructor.
        """
        super().__init__()
        self.cue_listeners: List[CueListener] = []
        self.workspace_listeners: List[WorkspaceListener] = []
        self.logger = logging.getLogger(self.__class__.__name__)
        self.nar = None  # Will be initialized later

        # Initialize workspace buffers
        self.csm = None  # Current Situational Model
        self.perceptual_buffer = None
        self.episodic_buffer = None
        self.broadcast_queue = None

    def add_listener(self, listener: ModuleListener) -> None:
        """
        Add a listener to this Workspace.
        Args:
            listener: The listener to add
        """
        if isinstance(listener, WorkspaceListener):
            self.add_workspace_listener(listener)
        elif isinstance(listener, CueListener):
            self.add_cue_listener(listener)
        elif hasattr(listener, 'add_pam_listener') and callable(getattr(listener, 'add_pam_listener')):
            # This is likely a PAMemory implementation
            # Add this workspace as a listener to PAM
            try:
                listener.add_pam_listener(self)
                self.logger.debug(f"Added workspace as listener to PAMemory at tick {TaskManager.get_current_tick()}")
            except Exception as e:
                self.logger.error(f"Error adding workspace as listener to PAMemory: {e} at tick {TaskManager.get_current_tick()}")
        else:
            self.logger.warning(f"Listener {listener} was not added, wrong type at tick {TaskManager.get_current_tick()}")

    def add_cue_listener(self, listener: CueListener) -> None:
        """
        Add episodic memory that will listen for cues from the Workspace.
        Args:
            listener: The listener to add
        """
        self.cue_listeners.append(listener)

    def add_workspace_listener(self, listener: WorkspaceListener) -> None:
        """
        Adds specified WorkspaceListener.
        Args:
            listener: The listener of this Workspace
        """
        self.workspace_listeners.append(listener)

    def cue_episodic_memories(self, content: NodeStructure) -> None:
        """
        Cue episodic memories with the specified content.
        Args:
            content: The content to cue with
        """
        for listener in self.cue_listeners:
            listener.receive_cue(content)
        self.logger.debug(f"Cue performed at tick {TaskManager.get_current_tick()}")

    def receive_local_association(self, association: NodeStructure) -> None:
        """
        Received local associations are merged into the episodic buffer.
        Then they are sent to PAM.
        Args:
            association: The local association
        """
        if self.contains_submodule(ModuleName.EpisodicBuffer):
            buffer = self.get_submodule(ModuleName.EpisodicBuffer)
            if isinstance(buffer, WorkspaceBuffer):
                buffer.add_buffer_content(association)
                for listener in self.workspace_listeners:
                    listener.receive_workspace_content(ModuleName.EpisodicBuffer, buffer.get_buffer_content(None))
        else:
            self.logger.warning(f"Received a Local association but Workspace does not have an episodic buffer at tick {TaskManager.get_current_tick()}")

    def receive_percept(self, percept: Any, module_name: ModuleName = None) -> None:
        """
        Implementation of the PamListener interface. Adds newPercept to the perceptualBuffer.
        Args:
            percept: The percept to receive
            module_name: The name of the module that sent the percept
        """
        if isinstance(percept, NodeStructure):
            self.receive_percept_node_structure(percept)
        # 要先link后node，因为link继承node，所有都是node
        elif isinstance(percept, Link):
            if module_name is None:
                self.receive_percept_link(percept)
            else:
                self.receive_percept_link_with_name(percept, module_name)
        elif isinstance(percept, Node):
            if module_name is None:
                self.receive_percept_node(percept)
            else:
                self.receive_percept_node_with_name(percept, module_name)

    def receive_percept_node_structure(self, new_percept: NodeStructure) -> None:
        """
        Receive a NodeStructure percept.
        Args:
            new_percept: The NodeStructure percept
        """
        if self.contains_submodule(ModuleName.PerceptualBuffer):
            buffer = self.get_submodule(ModuleName.PerceptualBuffer)
            if isinstance(buffer, WorkspaceBuffer):
                buffer.add_buffer_content(new_percept)
        else:
            self.logger.warning(f"0Received a percept but Workspace does not have a perceptual buffer at tick {TaskManager.get_current_tick()}")

    def receive_percept_node(self, node: Node) -> None:
        """
        Receive a Node percept.
        Args:
            node: The Node percept
        """
        self.receive_percept_node_with_name(node, ModuleName.PerceptualBuffer)

    def receive_percept_node_with_name(self, node: Node, name: ModuleName) -> None:
        """
        Receive a Node percept with a module name.
        Args:
            node: The Node percept
            name: The name of the module that sent the percept
        """
        if self.contains_submodule(name):
            buffer = self.get_submodule(name)
            if isinstance(buffer, WorkspaceBuffer):
                ns = buffer.get_buffer_content(None)
                ns.set_scene_time(str(TaskManager.get_current_tick()))
                ns.set_scene_site(node.get_location())
                # Copy values, not the entire address
                ns.add_default_node(node)
        else:
            self.logger.warning(f"1Received a percept but Workspace does not have a perceptual buffer at tick {TaskManager.get_current_tick()}")

    def receive_percept_link(self, link: Link) -> None:
        """
        Receive a Link percept.
        Args:
            link: The Link percept
        """
        self.receive_percept_link_with_name(link, ModuleName.PerceptualBuffer)

    def receive_percept_link_with_name(self, link: Link, name: ModuleName) -> None:
        """
        Receive a Link percept with a module name.
        Args:
            link: The Link percept
            name: The name of the module that sent the percept
        """
        if self.contains_submodule(name):
            buffer = self.get_submodule(name)
            if isinstance(buffer, WorkspaceBuffer):
                ns = buffer.get_buffer_content(None)
                ns.add_default_link(link)

                # Internal direct reasoning, no need to create additional tasks
                if isinstance(link, LinkImpl):
                    self.make_task(link, name, ns)
        else:
            self.logger.warning(f"2Received a percept but Workspace does not have a perceptual buffer at tick {TaskManager.get_current_tick()}")

    @staticmethod
    def get_link_term(l: LinkImpl) -> Optional[Term]:
        """
        Get a Term from a LinkImpl based on its category.
        Args:
            l: The LinkImpl to get a Term from
        Returns:
            Term: The created Term, or None if no valid Term could be created
        """
        # if not NARS_AVAILABLE:
        #     return None
        term = None
        exclude = ["属性", "心理计划", "具身计划", "返回赋值", "整体赋值", "子类", "媒介",
                  "赋值", "情绪", "时间", "顺发", "关键帧", "具象", "抽象", "时态",
                  "名字", "对象", "实例", "手段", "表示", "break", "拆分"]
        exclude3 = ["isa", "顺承", "蕴含"]
        # Get the category name
        pcate = l.get_category().get_name() if hasattr(l.get_category(), 'get_name') else str(l.get_category())
        # Check if the link has term attributes
        if not hasattr(l, 'term') or not isinstance(l.term, list) or len(l.term) < 3:
            return None
        # Process based on category
        if pcate == "isa":
            # Skip if source is SELF
            if hasattr(l.term[0], 'to_string') and l.term[0].to_string() == "{SELF}":
                return None
            # <ss4 --> ss1>
            term = Inheritance.make(l.term[0], l.term[2])
        elif pcate == "顺承":
            # <ss11 =/> ss22>
            if l.term[0].TNname == "(*,(*,听到1,$问题),<$问题 --> 问题>)":
                print(l.term[0].TNname)
            term = Implication.make(l.term[0], l.term[2], TemporalRules.ORDER_FORWARD)
        elif pcate == "蕴含":
            # <ss11 <=> ss22>
            term = Equivalence.make(l.term[0], l.term[2], TemporalRules.ORDER_NONE)
        elif pcate == "对等":
            # <ss1 <=> ss2>
            term = Equivalence.make(l.term[0], l.term[2], TemporalRules.ORDER_NONE)
        elif pcate == "相似":
            # <ss1 <-> ss2>
            term = Similarity.make(l.term[0], l.term[2])
        else:
            # Skip if category is in exclude list
            if pcate in exclude:
                return None
        return term

    def add_cp_to(self, l: LinkImpl, headcpstr: str, memory, tailcpstr: str) -> None:
        """
        Add CP (Conceptual Processing) to a link.

        Args:
            l: The link
            headcpstr: The head CP string
            memory: The memory
            tailcpstr: The tail CP string
        """
        if self.nar is None:
            return

        text = ""
        if headcpstr is not None and headcpstr:
            text = f"<{headcpstr} <-> {l.get_source().get_TNname()}>."
            self.nar.add_input_to(text, memory)

        if tailcpstr is not None and tailcpstr:
            text = f"<{tailcpstr} <-> {l.get_sink().get_TNname()}>."
            self.nar.add_input_to(text, memory)

    def do_make_task(self, punctuation: str, memory, term: Term, target: str) -> None:
        """
        Create and process a task from a term.
        Args:
            punctuation: The punctuation character for the sentence
            memory: The memory to add the task to
            term: The term for the task
            target: The target type ("goal" or "belief")
        """
        if self.nar is None:
            return

        # Check if memory is None
        if memory is None:
            self.logger.error(f"Memory is None in do_make_task at tick {TaskManager.get_current_tick()}")
            # Try to get memory from nar
            if hasattr(self.nar, 'memory'):
                memory = self.nar.memory
                self.logger.info(f"Using memory from nar at tick {TaskManager.get_current_tick()}")
            else:
                self.logger.error(f"Cannot get memory from nar at tick {TaskManager.get_current_tick()}")
                return

        try:
            # Only process compound terms
            # 检查是否为CompoundTerm或其子类
            if not isinstance(term, CompoundTerm) and not hasattr(term, 'term') and not hasattr(term, 'get_subject'):
                return
            # Count compound terms in the term
            num = 0
            if hasattr(term, 'term') and isinstance(term.term, list):
                for term1 in term.term:
                    if isinstance(term1, CompoundTerm):
                        num += 1
                # Skip if all terms are simple and there are variables
                if num == 0 and hasattr(term, 'has_var') and term.has_var():
                    return
                # Create stamp with current time
                stamp = Stamp(self.nar.time(), None, memory.new_stamp_serial(),
                            self.nar.narParameters.DURATION if hasattr(self.nar, 'narParameters') else 5)
                # Create sentence with truth value
                sentence = Sentence(term, punctuation,
                                TruthValue(1.0, 0.9, False, self.nar.narParameters if hasattr(self.nar, 'narParameters') else None),
                                stamp)
                # Create budget value
                budget = BudgetValue(0.8, 0.5, 1, self.nar.narParameters if hasattr(self.nar, 'narParameters') else None)
                # Create task
                task = Task(sentence, budget, EnumType.INPUT)
                # Add task to memory
                if hasattr(memory, 'add_new_task'):
                    memory.add_new_task(task, "lida")
                    # Process based on target
                    if target == "goal" and hasattr(memory, 'do_g_buffer'):
                        # 循环5次，消耗堆积任务
                        for i in range(5):
                            memory.do_g_buffer("goal")
                    else:
                        for i in range(3):
                            if hasattr(memory, 'do_g_buffer'):
                                memory.do_g_buffer("belief")
                            if hasattr(memory, 'do_reason'):
                                memory.do_reason(self.nar)
                self.logger.debug(f"Created task from term at tick {TaskManager.get_current_tick()}")
        except Exception as e:
            self.logger.error(f"Error making task: {e} at tick {TaskManager.get_current_tick()}")
            import traceback
            traceback.print_exc()

    def make_task(self, link: LinkImpl, name: ModuleName, ns: NodeStructure) -> None:
        """
        Make a task from a link.
        Args:
            link: The link
            name: The name of the module
            ns: The NodeStructure
        """
        # Check if NARS is available
        if self.nar is None:
            return
        try:
            # Get memory based on module name
            memory = None
            target = "both"
            punctuation = '.'
            if name == ModuleName.GoalGraph:
                # Use ns as memory for goal graph
                memory = ns
                # Check if memory is valid
                # if memory is None or not hasattr(memory, 'globalBuffer') or memory.globalBuffer is None:
                #     self.logger.warning(f"Invalid memory from ns at tick {TaskManager.get_current_tick()}, using nar.memory instead")
                #     memory = self.nar.memory
                target = "goal"
            else:
                memory = self.nar.memory

            misnone = memory is None
            nogbf = not hasattr(memory, 'globalBuffer')
            gbfisnone = memory.globalBuffer is None

            # Final check for memory
            if memory is None:
                self.logger.warning(f"Invalid memory at tick {TaskManager.get_current_tick()}, cannot proceed")
                # print("misnone--", misnone, "---nogbf---", nogbf, "--gbfisnone--", gbfisnone)
                return
            # Get term from link
            term = self.get_link_term(link)
            if term is not None:
                try:
                    # Parse the term string if needed
                    if self.narsese is not None and hasattr(self.narsese, 'parse_term'):
                        term = self.narsese.parse_term(str(term))
                except Parser.InvalidInputException as e:
                    self.logger.error(f"Invalid term: {e} at tick {TaskManager.get_current_tick()}")
                    return
                # Create and process task
                self.do_make_task(punctuation, memory, term, target)
                # Process CP strings if available
                if hasattr(link.get_source(), 'get_properties') and hasattr(link.get_sink(), 'get_properties'):
                    headcpstr = link.get_source().get_properties().get("cpstr") if hasattr(link.get_source().get_properties(), 'get') else None
                    tailcpstr = link.get_sink().get_properties().get("cpstr") if hasattr(link.get_sink().get_properties(), 'get') else None
                    self.add_cp_to(link, headcpstr, memory, tailcpstr)
            self.logger.debug(f"Processed link at tick {TaskManager.get_current_tick()}")
        except Exception as e:
            self.logger.error(f"Error making task: {e} at tick {TaskManager.get_current_tick()}")
            import traceback
            traceback.print_exc()

    def receive_broadcast(self, coalition: Coalition) -> None:
        """
        Receive a broadcast from the GlobalWorkspace.
        Args:
            coalition: The Coalition that won the competition for consciousness
        """
        if self.contains_submodule(ModuleName.BroadcastQueue):
            # Forward the broadcast to the BroadcastQueue module
            broadcast_queue = self.get_submodule(ModuleName.BroadcastQueue)
            if isinstance(broadcast_queue, BroadcastListener):
                try:
                    broadcast_queue.receive_broadcast(coalition)
                    self.logger.debug(f"Forwarded broadcast to BroadcastQueue at tick {TaskManager.get_current_tick()}")
                except Exception as e:
                    self.logger.error(f"Error forwarding broadcast to BroadcastQueue: {e} at tick {TaskManager.get_current_tick()}")
                    import traceback
                    traceback.print_exc()
            else:
                # Fallback if BroadcastQueue doesn't implement BroadcastListener
                if isinstance(broadcast_queue, WorkspaceBuffer):
                    broadcast_queue.add_buffer_content(coalition.get_content())
                    self.logger.debug(f"Added broadcast content to BroadcastQueue at tick {TaskManager.get_current_tick()}")
        else:
            self.logger.warning(f"Received a broadcast but Workspace does not have a broadcast queue at tick {TaskManager.get_current_tick()}")

    def get_module_content(self, *params: Any) -> Any:
        """
        Get the content of this module.
        Args:
            params: Parameters specifying what content to return
        Returns:
            The content of this module
        """
        return None

    def learn(self, coalition: Coalition) -> None:
        """
        Learn from a coalition.
        Args:
            coalition: The coalition to learn from
        """
        # Not applicable
        pass

    def init(self, params=None):
        """
        Initialize the Workspace module with parameters.
        Args:
            params: The parameters to initialize with
        """
        super().init(params)

        # Initialize workspace buffers from submodules
        if self.contains_submodule(ModuleName.CurrentSM):
            self.csm = self.get_submodule(ModuleName.CurrentSM)

        if self.contains_submodule(ModuleName.PerceptualBuffer):
            self.perceptual_buffer = self.get_submodule(ModuleName.PerceptualBuffer)

        if self.contains_submodule(ModuleName.EpisodicBuffer):
            self.episodic_buffer = self.get_submodule(ModuleName.EpisodicBuffer)

        if self.contains_submodule(ModuleName.BroadcastQueue):
            self.broadcast_queue = self.get_submodule(ModuleName.BroadcastQueue)

        # Initialize NARS if available
        try:
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            if AgentStarter.nar is not None:
                self.nar = AgentStarter.nar
                self.narsese = AgentStarter.narsese
                self.logger.info(f"NARS initialized successfully at tick {TaskManager.get_current_tick()}")
        except Exception as e:
            # self.logger.warning(f"Could not initialize NARS at tick {TaskManager.get_current_tick()}: {e}")
            import traceback
            self.logger.warning(traceback.format_exc())

    def is_workspace(self):
        """
        Check if this module is a Workspace.
        Returns:
            True, as this is a Workspace implementation
        """
        return True

    def decay_module(self, ticks: int) -> None:
        """
        Should do nothing, submodules' decayModule method will be called
        in FrameworkModuleImpl#taskManagerDecayModule.
        Args:
            ticks: The number of ticks to decay
        """
        # Submodules' decay methods will be called by the TaskManager
        pass

    def get_buffer_content(self, buffer_name=None):
        """
        Get the content of a workspace buffer.
        Args:
            buffer_name: The name of the buffer to get content from, or None for current situational model
        Returns:
            The buffer content or None if the buffer doesn't exist
        """
        # if buffer_name is None or buffer_name == ModuleName.CurrentSM:
        #     if self.csm is not None and hasattr(self.csm, 'get_buffer_content'):
        #         return self.csm.get_buffer_content(None)
        # elif buffer_name == ModuleName.PerceptualBuffer:
        #     if self.perceptual_buffer is not None and hasattr(self.perceptual_buffer, 'get_buffer_content'):
        #         return self.perceptual_buffer.get_buffer_content(None)
        # elif buffer_name == ModuleName.EpisodicBuffer:
        #     if self.episodic_buffer is not None and hasattr(self.episodic_buffer, 'get_buffer_content'):
        #         return self.episodic_buffer.get_buffer_content(None)
        # elif buffer_name == ModuleName.BroadcastQueue:
        #     if self.broadcast_queue is not None and hasattr(self.broadcast_queue, 'get_buffer_content'):
        #         return self.broadcast_queue.get_buffer_content(None)



        return
