# LIDA Cognitive Framework
"""
WorkspaceContent is a general name for the content of the workspace.
Currently it is a NodeStructure only.
In the future it may include Sensory Scene layers.
"""

from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure

class WorkspaceContent(NodeStructure):
    """
    WorkspaceContent is a general name for the content of the workspace.
    Currently it is a NodeStructure only.
    In the future it may include Sensory Scene layers.
    """
    pass
