#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task which adds a PamNode to the percept.
"""

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory

class AddNodeToPerceptTask(FrameworkTaskImpl):
    """
    A task which adds a PamNode to the percept.
    """
    
    def __init__(self, node: Node, pam: PAMemory):
        """
        Initialize an AddNodeToPerceptTask.
        
        Args:
            node: The node to add to the percept
            pam: The PAMemory
        """
        super().__init__()
        self.node = node
        self.pam = pam
        
    def run_this_framework_task(self):
        """
        Adds Node to the percept then finishes.
        """
        self.pam.add_to_percept(self.node)
        self.cancel()
