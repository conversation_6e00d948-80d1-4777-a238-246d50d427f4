# LIDA Cognitive Framework
"""
An AttentionCodelet is a type of FrameworkTask that looks for specific content in the
Current Situational Model and creates a Coalition with that content.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTask import FrameworkTask
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.GlobalWorkspace import GlobalWorkspace

class AttentionCodelet(FrameworkTask):
    """
    An AttentionCodelet is a type of FrameworkTask that looks for specific content in the
    Current Situational Model and creates a Coalition with that content.
    """
    
    @abstractmethod
    def set_global_workspace(self, gw: GlobalWorkspace) -> None:
        """
        Set the GlobalWorkspace for this AttentionCodelet.
        
        Args:
            gw: The GlobalWorkspace
        """
        pass
    
    @abstractmethod
    def get_global_workspace(self) -> GlobalWorkspace:
        """
        Get the GlobalWorkspace for this AttentionCodelet.
        
        Returns:
            The GlobalWorkspace
        """
        pass
    
    @abstractmethod
    def create_coalition(self, content: NodeStructure) -> None:
        """
        Create a Coalition with the specified content.
        
        Args:
            content: The content for the Coalition
        """
        pass
