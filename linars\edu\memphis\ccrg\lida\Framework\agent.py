"""
Agent interface.

This module provides an interface for agents.
"""
from abc import ABC, abstractmethod
from typing import TYPE_CHECKING

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule

# if TYPE_CHECKING:
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager

class Agent(FrameworkModule, ABC):
    """
    A FrameworkModule containing all of the FrameworkModules of an agent.
    """

    @abstractmethod
    def get_task_manager(self) -> TaskManager:
        """
        Returns the Task Manager.
        Returns:
            The TaskManager in charge of all tasks
        """
        pass

    def is_running(self) -> bool:
        """
        Check if the agent is running.

        Returns:
            True if the agent is running, False otherwise
        """
        return True

    def start(self) -> None:
        """
        Start the agent.
        """
        pass

    def stop(self) -> None:
        """
        Stop the agent.
        """
        pass
