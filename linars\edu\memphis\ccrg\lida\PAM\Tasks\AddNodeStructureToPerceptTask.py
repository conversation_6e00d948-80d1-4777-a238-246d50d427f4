#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task which adds a NodeStructure to the percept.
"""

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory

class AddNodeStructureToPerceptTask(FrameworkTaskImpl):
    """
    A task which adds a NodeStructure to the percept.
    """
    
    def __init__(self, ns: NodeStructure, pam: PAMemory):
        """
        Initialize an AddNodeStructureToPerceptTask.
        
        Args:
            ns: The NodeStructure to add to the percept
            pam: The PAMemory
        """
        super().__init__()
        self.ns = ns
        self.pam = pam
        
    def run_this_framework_task(self):
        """
        Adds NodeStructure to the percept then finishes.
        """
        self.pam.add_to_percept(self.ns)
        self.cancel()
