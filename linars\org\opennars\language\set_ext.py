"""
外延集合定义
按照NARS理论定义的外延集合操作
"""
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.io.symbols import NativeOperator
from linars.org.opennars.language.set_tensional import SetTensional

class SetExt(SetTensional):
    """
    外延集合类
    按照NARS理论定义的外延集合操作
    """

    def __init__(self, arg):
        """
        构造函数

        参数:
            arg: 项的组成部分列表(必须唯一且已排序)
        """
        super().__init__(arg)

    def clone(self, replaced=None):
        """
        克隆对象

        参数:
            replaced: 替换的组件项列表，如果为None则使用原始组件项

        返回:
            SetExt: 新的外延集合对象
        """
        if replaced is not None:
            return SetExt(replaced)
        return SetExt(self.term)

    def clone_with_terms(self, replaced):
        """
        使用替换项克隆对象

        参数:
            replaced: 被替换的项列表

        返回:
            Term: 包含替换组件的新项
        """
        if replaced is None:
            return None
        return SetExt.make(replaced)

    @staticmethod
    def make(t):
        """
        尝试创建新的外延集合

        参数:
            t: 项列表

        返回:
            Term: 从参数生成的项
        """
        # Sort and remove duplicates
        t = Term.to_sorted_set_array(t)

        if len(t) == 0:
            return None

        return SetExt(t)

    @staticmethod
    def make_from_collection(collection):
        """
        从项集合创建外延集合

        参数:
            collection: 项集合

        返回:
            SetExt: 新的外延集合
        """
        return SetExt.make(list(collection))

    def operator(self):
        """
        获取项的操作符

        返回:
            str: 项的操作符
        """
        return NativeOperator.SET_EXT_OPENER

    def make_name(self):
        """
        创建集合的字符串表示(覆盖默认实现)

        返回:
            str: 项的名称
        """
        return self.make_set_name(
            NativeOperator.SET_EXT_OPENER.ch,
            self.term,
            NativeOperator.SET_EXT_CLOSER.ch
        )
