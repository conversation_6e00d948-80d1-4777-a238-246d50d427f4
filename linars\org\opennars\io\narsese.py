"""
Narsese工具类
用于处理和解析Narsese输入

未来将与NarseseParser集成，实现系统化的输入解析和预测
"""
from typing import List, Dict, Optional, Tuple, Any

from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.entity.budget_value import BudgetValue
from linars.org.opennars.entity.sentence import Sentence
from linars.org.opennars.entity.stamp import Stamp
from linars.org.opennars.entity.task import Task
from linars.org.opennars.entity.truth_value import TruthValue
from linars.org.opennars.inference.budget_functions import BudgetFunctions
from linars.org.opennars.io.parser import Parser
from linars.org.opennars.io.symbols import (
    BUDGET_VALUE_MARK, TRUTH_VALUE_MARK, VALUE_SEPARATOR,
    TENSE_MARK, JUDGMENT_MARK, QUESTION_MARK, GOAL_MARK, QUEST_MARK,
    COMPOUND_TERM_CLOSER,INTERVAL_PREFIX, SET_EXT_CLOSER,SET_INT_CLOSER,STATEMENT_CLOSER,
    ARGUMENT_SEPARATOR,NativeOperator, get_opener, get_operator, get_relation, get_closer,
    is_relation
)
from linars.org.opennars.language.set_ext import SetExt
from linars.org.opennars.language.set_int import SetInt
from linars.org.opennars.language.statement import Statement
from linars.org.opennars.language.tense import Tense
from linars.org.opennars.language.terms import Terms
from linars.org.opennars.operator.operation import Operation
from linars.org.opennars.language.variables import Variables
from linars.org.opennars.language.variable import Variable
from linars.org.opennars.language.interval import Interval


class Narsese(Parser):
    """
    Narsese解析工具类
    提供处理Narsese输入的各种实用方法

    未来将与NarseseParser集成，实现系统化的输入解析和预测
    """

    def __init__(self, memory_or_nar):
        """
        构造函数

        参数:
            memory_or_nar: 内存或NAR系统实例
        """
        if hasattr(memory_or_nar, 'memory'):
            self.memory = memory_or_nar.memory
        else:
            self.memory = memory_or_nar

    def parse_task(self, s: str) -> Task:
        """
        解析输入字符串为任务对象

        参数:
            s: 单行输入字符串

        返回:
            Task: 解析后的任务对象

        异常:
            InvalidInputException: 当输入无效时抛出
        """
        buffer = s

        budget_string = self.get_budget_string(buffer)
        truth_string = self.get_truth_string(buffer)

        # 解析时态并获取修改后的字符串
        tense, buffer = self.parse_tense_static(buffer)

        str_content = buffer.strip()
        last = len(str_content) - 1
        punc = str_content[last]

        stamp = Stamp(
            -1,  # if -1, will be set right before the Task is input
            tense,
            self.memory.new_stamp_serial(),
            self.memory.narParameters.DURATION
        )

        truth = self.parse_truth(truth_string, punc)
        content = self.parse_term(str_content[:last])

        if content is None:
            raise self.InvalidInputException("Content term missing")

        sentence = Sentence(
            content,
            punc,
            truth,
            stamp
        )
        from linars.org.opennars.entity.task import EnumType
        budget = self.parse_budget(budget_string, punc, truth)
        return Task(sentence, budget, EnumType.INPUT)

    def get_budget_string(self, buffer: str) -> Optional[str]:
        """
        从字符串中提取预算部分

        参数:
            buffer: 包含预算的字符串

        返回:
            str: 预算字符串
        """
        if buffer.startswith(BUDGET_VALUE_MARK):
            index = buffer.find(BUDGET_VALUE_MARK, 1)
            if index >= 0:
                budget = buffer[1:index]
                buffer = buffer[index + 1:]
                return budget
        return None

    def get_truth_string(self, buffer: str) -> Optional[str]:
        """
        从字符串中提取真值部分

        参数:
            buffer: 包含真值的字符串

        返回:
            str: 真值字符串
        """
        if buffer.endswith(TRUTH_VALUE_MARK):
            index = buffer.rfind(TRUTH_VALUE_MARK, 0, len(buffer) - 1)
            if index >= 0:
                truth = buffer[index + 1:-1]
                buffer = buffer[:index]
                return truth
        return None

    def parse_tense(self, buffer: str) -> tuple[Tense, str]:
        """
        识别输入句子的时态

        参数:
            buffer: 输入字符串

        返回:
            tuple: (时态值, 修改后的字符串)
        """
        # 直接调用静态方法
        return self.parse_tense_static(buffer)

    @staticmethod
    def parse_tense_static(s: str) -> tuple[Tense, str]:
        """
        识别输入句子的时态（静态方法版本）

        参数:
            s: 输入字符串

        返回:
            tuple: (时态值, 修改后的字符串)
        """
        i = s.find(TENSE_MARK)
        t = ""
        modified_s = s
        if i > 0:
            t = s[i:].strip()
            modified_s = s[:i]  # 删除时态标记及其后面的内容
        return Tense.tense(t), modified_s

    def parse_truth(self, s: str, punctuation: str) -> Optional[TruthValue]:
        """
        解析判断的真值

        参数:
            s: 输入字符串
            punctuation: 标点符号

        返回:
            TruthValue: 真值对象

        异常:
            InvalidInputException: 当输入无效时抛出
        """
        if s is None:
            return None

        if punctuation == QUESTION_MARK or punctuation == QUEST_MARK:
            return None

        i = s.find(VALUE_SEPARATOR)
        j = s.find(VALUE_SEPARATOR, i + 1)

        frequency = float(s[:i]) if i > 0 else 1.0
        confidence = float(s[i + 1:j]) if j > i else float(s[i + 1:])

        return TruthValue(frequency, confidence, self.memory.narParameters)

    def parse_budget(self, s: str, punctuation: str, truth: Optional[TruthValue]) -> BudgetValue:
        """
        解析任务的预算值

        参数:
            s: 输入字符串
            punctuation: 标点符号
            truth: 真值对象

        返回:
            BudgetValue: 预算值对象

        异常:
            InvalidInputException: 当输入无效时抛出
        """
        priority = 0.0
        durability = 0.0

        if punctuation == JUDGMENT_MARK:
            priority = self.memory.narParameters.DEFAULT_JUDGMENT_PRIORITY
            durability = self.memory.narParameters.DEFAULT_JUDGMENT_DURABILITY
        elif punctuation == QUESTION_MARK:
            priority = self.memory.narParameters.DEFAULT_QUESTION_PRIORITY
            durability = self.memory.narParameters.DEFAULT_QUESTION_DURABILITY
        elif punctuation == GOAL_MARK:
            priority = self.memory.narParameters.DEFAULT_GOAL_PRIORITY
            durability = self.memory.narParameters.DEFAULT_GOAL_DURABILITY
        elif punctuation == QUEST_MARK:
            priority = self.memory.narParameters.DEFAULT_QUEST_PRIORITY
            durability = self.memory.narParameters.DEFAULT_QUEST_DURABILITY
        else:
            raise self.InvalidInputException(f"unknown punctuation: '{punctuation}'")

        if s is not None:  # override default
            i = s.find(VALUE_SEPARATOR)
            if i < 0:  # default durability
                priority = float(s)
            else:
                i2 = s.find(VALUE_SEPARATOR, i + 1)
                if i2 == -1:
                    i2 = len(s)
                priority = float(s[:i])
                durability = float(s[i + 1:i2])

        quality = 1.0 if truth is None else BudgetFunctions.truth_to_quality(truth)
        return BudgetValue(priority, durability, quality, self.memory.narParameters)

    def parse_term(self, s: str) -> Optional[Term]:
        """
        解析字符串中的项
        参数:
            s: 包含项的字符串
        返回:
            Term: 项对象
        异常:
            InvalidInputException: 当输入无效时抛出
        """
        try:
            # 处理空字符串
            s = s.strip() if s else ""
            if not s:
                return None

            # 获取第一个字符和对应的开始符号
            try:
                first = s[0]
                opener = get_opener(first)
            except IndexError:
                raise self.InvalidInputException(f"Empty or invalid term string: '{s}'")
            except Exception as e:
                raise self.InvalidInputException(f"Error getting opener for '{s}': {str(e)}")

            # 处理开始符号
            if opener is not None:
                try:
                    index = len(s) - 1
                    if index < 0:
                        raise self.InvalidInputException(f"Invalid term format: '{s}'")
                    last = s[index]
                except IndexError:
                    raise self.InvalidInputException(f"Invalid term format, missing closing character: '{s}'")

                # 根据不同的开始符号处理不同类型的项
                try:
                    if opener == NativeOperator.COMPOUND_TERM_OPENER:
                        if last == COMPOUND_TERM_CLOSER:
                            return self.parse_compound_term(s[1:index])
                        else:
                            raise self.InvalidInputException(f"Missing CompoundTerm closer in '{s}', expected '{COMPOUND_TERM_CLOSER}' but got '{last}'")
                    elif opener == NativeOperator.SET_EXT_OPENER:
                        if last == SET_EXT_CLOSER:
                            try:
                                args = self.parse_arguments(s[1:index] + ARGUMENT_SEPARATOR)
                                return SetExt.make(args)
                            except Exception as e:
                                raise self.InvalidInputException(f"Error parsing ExtensionSet arguments in '{s}': {str(e)}")
                        else:
                            raise self.InvalidInputException(f"Missing ExtensionSet closer in '{s}', expected '{SET_EXT_CLOSER}' but got '{last}'")
                    elif opener == NativeOperator.SET_INT_OPENER:
                        if last == SET_INT_CLOSER:
                            try:
                                args = self.parse_arguments(s[1:index] + ARGUMENT_SEPARATOR)
                                return SetInt.make(args)
                            except Exception as e:
                                raise self.InvalidInputException(f"Error parsing IntensionSet arguments in '{s}': {str(e)}")
                        else:
                            raise self.InvalidInputException(f"Missing IntensionSet closer in '{s}', expected '{SET_INT_CLOSER}' but got '{last}'")
                    elif opener == NativeOperator.STATEMENT_OPENER:
                        if last == STATEMENT_CLOSER:
                            return self.parse_statement(s[1:index])
                        else:
                            raise self.InvalidInputException(f"Missing Statement closer in '{s}', expected '{STATEMENT_CLOSER}' but got '{last}'")
                    else:
                        raise self.InvalidInputException(f"Unrecognized opener '{opener}' in '{s}'")
                except Exception as e:
                    if isinstance(e, self.InvalidInputException):
                        raise
                    raise self.InvalidInputException(f"Error parsing term with opener '{opener}' in '{s}': {str(e)}")
            else:
                # 如果没有开始符号，尝试解析操作符
                try:
                    o = self.get_op(s)
                    if o is not None:
                        return o
                except Exception as e:
                    raise self.InvalidInputException(f"Error parsing operator in '{s}': {str(e)}")

                # 如果不是操作符，尝试解析原子项
                return self.parse_atomic_term(s)
        except Exception as e:
            if isinstance(e, self.InvalidInputException):
                raise
            raise self.InvalidInputException(f"Unexpected error parsing term '{s}': {str(e)}")

    def parse_atomic_term(self, s: str) -> Term:
        """
        解析字符串中的原子项
        参数:
            s: 包含原子项的字符串
        返回:
            Term: 原子项对象
        异常:
            InvalidInputException: 当输入无效时抛出
        """
        try:
            # 检查字符串是否为空
            if s is None or not s.strip():
                raise self.InvalidInputException(f"Cannot parse empty atomic term")

            try:
                o_registered = self.memory.get_operator(s)
            except Exception as e:
                raise self.InvalidInputException(f"Error getting registered operator 00 '{op}': {str(e)}")

            if o_registered is not None:
                return o_registered

            # 检查是否包含非法字符
            invalid_chars = [' ', '\t', '\n', '\r']
            for char in invalid_chars:
                if char in s:
                    raise self.InvalidInputException(f"Atomic term contains invalid character '{char}' in '{s}'")

            c = s[0]
            if c == INTERVAL_PREFIX:
                return Interval.interval(s)

            if Variables.contain_var(s) and not s.__eq__("#"):
                return Variable(s)
            else:
                return Term(s)
        except Exception as e:
            if isinstance(e, self.InvalidInputException):
                raise
            raise self.InvalidInputException(f"Error parsing atomic term '{s}': {str(e)}")

    def parse_statement(self, s: str) -> Statement:
        """
        解析字符串中的陈述

        参数:
            s: 包含陈述的字符串

        返回:
            Statement: 陈述对象

        异常:
            InvalidInputException: 当输入无效时抛出
        """
        try:
            # 检查字符串是否为空
            s = s.strip() if s else ""
            if not s:
                raise self.InvalidInputException("invalid statement: topRelation(s) < 0")

            # 使用top_relation方法定位顶级关系符
            i = Narsese.top_relation(s)
            if i < 0:
                raise self.InvalidInputException(f"invalid statement: topRelation(s) < 0 in '{s}'")

            # 提取关系符号
            relation = s[i:i + 3]

            # 解析主语和谓语
            try:
                subject = self.parse_term(s[:i])
                if subject is None:
                    raise self.InvalidInputException(f"Invalid or missing subject in statement '{s}'")

                predicate = self.parse_term(s[i + 3:])
                if predicate is None:
                    raise self.InvalidInputException(f"Invalid or missing predicate in statement '{s}'")

                # 创建陈述
                relation_op = get_relation(relation)
                if relation_op is None:
                    raise self.InvalidInputException(f"Unknown relation operator '{relation}' in '{s}'")

                # 确保 relation_op 是有效的

                # 确保 relation_op 是 NativeOperator 枚举值
                from linars.org.opennars.io.symbols import NativeOperator
                if isinstance(relation_op, tuple):
                    # 如果是元组，尝试找到对应的枚举值
                    symbol = relation_op[0] if len(relation_op) > 0 else relation
                    for op in NativeOperator:
                        if op.symbol == symbol:
                            relation_op = op
                            break

                t = Statement.make_from_op(relation_op, subject, predicate, False, 0)
                if t is None:
                    subject = self.parse_term(s[:i])
                    raise self.InvalidInputException(f"invalid statement: statement unable to create: {subject} {get_operator(relation)} {predicate}")

                return t
            except Exception as e:
                if isinstance(e, self.InvalidInputException):
                    raise
                raise self.InvalidInputException(f"Error processing statement parts in '{s}': {str(e)}")
        except Exception as e:
            if isinstance(e, self.InvalidInputException):
                raise
            raise self.InvalidInputException(f"Unexpected error parsing statement '{s}': {str(e)}")

    def parse_compound_term(self, s: str) -> Term:
        """
        解析字符串中的复合项

        参数:
            s: 包含复合项的字符串

        返回:
            Term: 复合项对象

        异常:
            InvalidInputException: 当输入无效时抛出
        """
        try:
            # 检查字符串是否为空
            if s is None or not s.strip():
                raise self.InvalidInputException(f"Cannot parse empty compound term")

            # 查找第一个参数分隔符
            try:
                first_separator = s.find(ARGUMENT_SEPARATOR)
            except Exception as e:
                raise self.InvalidInputException(f"Error finding argument separator in '{s}': {str(e)}")

            # 提取操作符
            try:
                op = s[:first_separator].strip() if first_separator >= 0 else s.strip()
                if not op:
                    raise self.InvalidInputException(f"Missing operator in compound term '{s}'")

                # 获取原生操作符和注册操作符
                o_native = get_operator(op)

                if self.memory is None:
                    raise self.InvalidInputException(f"Memory reference is None, cannot get registered operator for '{op}'")

                o_registered = None
                try:
                    o_registered = self.memory.get_operator(op)
                except Exception as e:
                    raise self.InvalidInputException(f"Error getting registered operator '{op}': {str(e)}")

                # 检查操作符是否有效
                if o_registered is None and o_native is None:
                    raise self.InvalidInputException(f"Unknown operator--: '{op}' in '{s}'")
            except Exception as e:
                if isinstance(e, self.InvalidInputException):
                    raise
                raise self.InvalidInputException(f"Error processing operator in compound term '{s}': {str(e)}")

            # 解析参数
            try:
                if first_separator < 0:
                    arg = []
                else:
                    arg_str = s[first_separator + 1:] + ARGUMENT_SEPARATOR
                    arg = self.parse_arguments(arg_str)
                    if arg is None:
                        arg = []
                arg_a = arg
            except Exception as e:
                if isinstance(e, self.InvalidInputException):
                    raise
                raise self.InvalidInputException(f"Error parsing arguments in compound term '{s}': {str(e)}")

            # 创建复合项
            try:
                if o_native is not None:
                    t = Terms.term(o_native, arg_a)
                    if t is None:
                        raise self.InvalidInputException(f"Failed to create term with native operator '{op}' and arguments {arg_a}")
                elif o_registered is not None:
                    t = Operation.make(o_registered, arg_a, True)
                    if t is None:
                        raise self.InvalidInputException(f"Failed to create operation with registered operator '{op}' and arguments {arg_a}")
                else:
                    raise self.InvalidInputException(f"Invalid compound term: neither native nor registered operator available for '{op}'")

                return t
            except Exception as e:
                if isinstance(e, self.InvalidInputException):
                    raise
                raise self.InvalidInputException(f"Error creating compound term with operator '{op}' and arguments {arg_a}: {str(e)}")
        except Exception as e:
            if isinstance(e, self.InvalidInputException):
                raise
            raise self.InvalidInputException(f"Unexpected error parsing compound term '{s}': {str(e)}")

    def parse_arguments(self, s: str) -> List[Term]:
        """
        解析复合项中的参数
        参数:
            s: 包含参数的字符串
        返回:
            List[Term]: 参数列表
        异常:
            InvalidInputException: 当输入无效时抛出
        """
        try:
            # 检查字符串是否为空
            if s is None or not s.strip():
                raise self.InvalidInputException("null argument")

            s = s.strip()
            result = []
            start = 0
            end = 0
            term = None

            # 确保字符串以参数分隔符结尾
            if not s.endswith(ARGUMENT_SEPARATOR):
                s += ARGUMENT_SEPARATOR

            # 使用next_separator方法定位参数
            while end < len(s) - 1:
                end = Narsese.next_separator(s, start)
                if end == start:
                    break

                # 解析参数项
                try:
                    arg_str = s[start:end].strip()
                    term = self.parse_term(arg_str)  # 递归调用
                    if term is None:
                        raise self.InvalidInputException(f"Failed to parse argument '{arg_str}' in '{s}'")
                    result.append(term)
                except Exception as e:
                    if isinstance(e, self.InvalidInputException):
                        raise
                    raise self.InvalidInputException(f"Error parsing argument at position {start}-{end} in '{s}': {str(e)}")

                start = end + 1

            # 检查参数列表是否为空
            if not result:
                raise self.InvalidInputException(f"Empty argument list in '{s}'")

            return result
        except Exception as e:
            if isinstance(e, self.InvalidInputException):
                raise
            raise self.InvalidInputException(f"Unexpected error parsing arguments '{s}': {str(e)}")

    @staticmethod
    def next_separator(s: str, first: int) -> int:
        """
        定位复合项中的第一个顶级分隔符

        参数:
            s: 要解析的字符串
            first: 起始索引

        返回:
            int: 字符串中下一个分隔符的索引
        """
        level_counter = 0
        i = first
        while i < len(s) - 1:
            if Narsese.is_opener(s, i):
                level_counter += 1
            elif Narsese.is_closer(s, i):
                level_counter -= 1
            elif s[i] == ARGUMENT_SEPARATOR:
                if level_counter == 0:
                    break
            i += 1
        return i

    @staticmethod
    def top_relation(s: str) -> int:
        """
        定位顶级关系符在陈述中的位置

        参数:
            s: 要解析的字符串

        返回:
            int: 顶级关系符的索引，如果没有找到则返回-1
        """
        level_counter = 0
        i = 0
        while i < len(s) - 3:  # 不需要检查最后3个字符
            if level_counter == 0 and is_relation(s[i:i+3]):
                return i
            if Narsese.is_opener(s, i):
                level_counter += 1
            elif Narsese.is_closer(s, i):
                level_counter -= 1
            i += 1
        return -1

    @staticmethod
    def is_opener(s: str, i: int) -> bool:
        """
        检查复合项开始符号

        参数:
            s: 要检查的字符串
            i: 起始索引

        返回:
            bool: 如果给定字符串是开始符号则返回 True
        """
        try:
            c = s[i]
            opener = get_opener(c)
            if opener is None:
                return False

            # 确保不是关系符的一部分
            return i + 3 > len(s) or not is_relation(s[i:i+3])
        except IndexError:
            return False

    @staticmethod
    def is_closer(s: str, i: int) -> bool:
        """
        检查复合项结束符号

        参数:
            s: 要检查的字符串
            i: 起始索引

        返回:
            bool: 如果给定字符串是结束符号则返回 True
        """
        try:
            c = s[i]
            closer = get_closer(c)
            if closer is None:
                return False

            # 确保不是关系符的一部分
            return i < 2 or not is_relation(s[i-2:i+1])
        except IndexError:
            return False

    @staticmethod
    def possibly_narsese(s: str) -> bool:
        """
        检查字符串是否可能是Narsese格式

        参数:
            s: 要检查的字符串

        返回:
            bool: 如果字符串可能是Narsese格式则返回 True
        """
        return '(' not in s and ')' not in s and '<' not in s and '>' not in s

    def get_op(self, s: str) -> Optional[Operation]:
        """
        从字符串中获取操作符

        参数:
            s: 包含操作符的字符串

        返回:
            Operation: 操作符对象

        异常:
            InvalidInputException: 当输入无效时抛出
        """
        try:
            # 检查字符串是否为空
            if s is None or not s:
                return None

            # 解析函数操作:
            # function()
            # function(a)
            # function(a,b)

            # 测试是否存在匹配的括号
            p_open = s.find('(')
            p_close = s.rfind(')')
            if (p_open != -1) and (p_close != -1) and (p_close == len(s) - 1):
                # 获取操作符字符串
                from linars.org.opennars.operator.operator import Operator
                operator_string = Operator.add_prefix_if_missing(s[:p_open])
                operator = self.memory.get_operator(operator_string)
                if operator is None:
                    raise self.InvalidInputException(f"Unknown operator: {operator_string} in {s}")

                # 获取参数字符串
                arg_string = s[p_open + 1:p_close + 1]
                if len(arg_string) > 1:
                    args = self.parse_arguments(arg_string)
                    a = args
                else:
                    # 空参数"()"，默认为(SELF)
                    a = Operation.SELF_TERM_ARRAY

                # 创建操作
                o = Operation.make(operator, a, True)
                return o

            # 检查是否是操作符
            if s.startswith("^"):
                # 检查内存引用
                if self.memory is None:
                    raise self.InvalidInputException(f"Memory reference is None, cannot get operator '{s}'")

                try:
                    op = self.memory.get_operator(s)
                    if op is not None:
                        try:
                            operation = Operation.make(op, [], True)
                            return operation
                        except Exception as e:
                            raise self.InvalidInputException(f"Error creating operation for operator '{s}': {str(e)}")
                except Exception as e:
                    if isinstance(e, self.InvalidInputException):
                        raise
                    raise self.InvalidInputException(f"Error getting operator '{s}' from memory: {str(e)}")

            return None
        except Exception as e:
            if isinstance(e, self.InvalidInputException):
                raise
            raise self.InvalidInputException(f"Unexpected error getting operator '{s}': {str(e)}")
