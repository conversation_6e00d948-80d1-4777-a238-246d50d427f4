"""
Term Utility for LIDA framework.

This module provides utility functions for working with terms in the LIDA framework.
"""
from typing import Optional

from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategory import LinkCategory
from linars.edu.memphis.ccrg.lida.PAM.PamLinkImpl import Pam<PERSON>inkImpl
from linars.edu.memphis.ccrg.lida.PAM.PamNode import PamNode
from linars.edu.memphis.ccrg.lida.PAM.PamNodeImpl import PamNodeImpl
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.entity.sentence import Sentence

# 全局变量
pam = None  # 将在初始化时设置

# 初始化函数
def init(pam_instance):
    """
    初始化Term工具

    Args:
        pam_instance: PAM实例
    """
    global pam
    pam = pam_instance

class TermUtil:
    """Term工具类，提供与Term相关的工具方法"""

    @staticmethod
    def term_to_link(term0: Term) -> Link:
        """
        将Term转换为Link

        Args:
            term0: Term对象

        Returns:
            Link对象
        """
        link = PamLinkImpl()
        term = term0  # 假设term0是Statement类型
        link.set_source(term.get_subject())
        link.set_sink(term.get_predicate())

        class CustomLinkCategory(LinkCategory):
            def get_name(self) -> str:
                return "term2link"

            def get_node_id(self) -> int:
                return 10000

        link.set_category(CustomLinkCategory())
        return link

    @staticmethod
    def term_to_link0(from_goal: Sentence, term: Term, s: str) -> Link:
        """
        将Term转换为Link

        Args:
            from_goal: 源句子
            term: 目标Term
            s: 类别名称

        Returns:
            Link对象
        """
        link = PamLinkImpl()
        link.set_source(from_goal.get_term())
        link.set_sink(term)
        link.set_category(PamNodeImpl(s))
        return link

    @staticmethod
    def term_to_link1(from_goal: Term, term: Term, s: str) -> Link:
        """
        将Term转换为Link

        Args:
            from_goal: 源Term
            term: 目标Term
            s: 类别名称

        Returns:
            Link对象
        """
        link = PamLinkImpl()
        category = PamNodeImpl()
        category.set_node_name(s)
        category.set_node_id(10000)

        from_node = None
        from_node = TermUtil.get_pam_node(str(from_goal), 10001)
        link.set_source(from_node)

        to_node = None
        to_node = TermUtil.get_pam_node(str(term), 10002)
        link.set_sink(to_node)

        link.set_category(category)
        return link

    @staticmethod
    def get_pam_node(str_val: str, id: int) -> PamNode:
        """
        获取PAM节点
        Args:
            str_val: 节点名称
            id: 节点ID
        Returns:
            PAM节点
        """
        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter

        to_node = AgentStarter.pam.get_node(str_val)
        if to_node is None:
            to_node = AgentStarter.pam.get_pam_node_structure().get_neo_node(str_val)
            if to_node is None:
                to_node = PamNodeImpl()
                to_node.set_node_name(str_val)
                to_node.set_node_id(id)

        return to_node

    @staticmethod
    def count_str(varname: str, s: str) -> int:
        """
        计算字符串中子串出现的次数

        Args:
            varname: 源字符串
            s: 子串

        Returns:
            出现次数
        """
        return varname.count(s)
