(function (window) {
  if (window.CatchReport) {
    return true;
  }
  window.CatchReport = {
    init: true,
    version: "1.0.0",
    reduxMiddleware: (store) => (next) => (action) => {
      try {
        return next(action);
      } catch (error) {
        sendCrashReport({ error, message: error.message, type: "ReduxError" });
        return store.getState();
      }
    },
    sendCrashReport
  };

  function sendCrashReport(catachObj = { message: "", type: "", error:null }) {
    if (!catachObj || !catachObj.message || !catachObj.type) {
      console.warn("Ingore the crash");
    } else {
      console.error("CatchJS :", catachObj.type, ":", catachObj.message, catachObj.error);
    }
  }

  //1. Overrite the onerror function
  const onerrorFunc = window.onerror;
  window.onerror = function (message, url, lineNo, columnNo, error) {
    //Only chrome support the error param
    if (String(message).toLowerCase().indexOf("script error") > -1) {
      message = "Script Error: See Browser Console for Detail";
    } else {
      message = [
        "Message: " + message,
        "URL: " + url,
        "Line: " + lineNo,
        "Column: " + columnNo,
      ].join(" - ");
    }

    sendCrashReport({
      type: "error",
      error,
      message,
    });

    return onerrorFunc ? onerrorFunc.apply(window, arguments) : false;
  };

  // //2. Overrite the console.error function
  // const errorFunc = window.console.error;
  // window.console.error = function (...messages) {
  //   sendCrashReport({
  //     type: "consoleError",
  //     message: messages.join(";"),
  //   });

  //   return errorFunc ? errorFunc.apply(window.console, arguments) : false;
  // };

  //3. onunhandledrejection
  const onunhandledrejectionFunc = window.onunhandledrejection;
  window.onunhandledrejection = function (event) {
    const reason = event ? event.reason || {} : {};
    sendCrashReport({
      type: "unhandledrejection",
      message: reason.message || "",
      error: reason
    });
    return onunhandledrejectionFunc ? onunhandledrejectionFunc.apply(window, arguments) : false;
  };

  //4. handle the web workers error
  if (self === window) {
    //This feature is available in Web Workers.
    const onmessageerrorFunc = window.onmessageerror;
    window.onmessageerror = function (error) {
      sendCrashReport({
        type: "messageerror",
        message: error ? error.message || "" : "",
        error
      });
      return onmessageerrorFunc ? onmessageerrorFunc.apply(window, arguments) : false;
    };
  }
})(window || self);
