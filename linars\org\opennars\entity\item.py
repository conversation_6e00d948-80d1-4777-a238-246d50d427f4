"""
项目是可以放入Bag中的对象，
参与系统的资源竞争。
它具有一个键和一个预算。不能被克隆。
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar, Callable
import abc

from linars.org.opennars.entity.budget_value import BudgetValue

K = TypeVar('K')

class Item(Generic[K], abc.ABC):
    """
    项目是可以放入Bag中的对象，
    参与系统的资源竞争。
    它具有一个键和一个预算。不能被克隆。
    """

    class ItemPriorityComparator:
        """按优先级比较项目的比较器"""

        def compare(self, a: 'Item', b: 'Item') -> int:
            """
            按优先级比较两个项目

            参数:
                a: 第一个项目
                b: 第二个项目

            返回:
                int: 比较结果(-1, 0, 或 1)
            """
            ap = a.get_priority()
            bp = b.get_priority()

            if (a == b) or (a.name() == b.name()) or (ap == bp):
                return hash(a) - hash(b)
            elif ap < bp:
                return 1
            else:
                return -1

    def __init__(self, budget: Optional[BudgetValue] = None):
        """
        带初始预算的构造函数

        参数:
            budget: 初始预算
        """
        if budget is not None:
            self.budget = budget.clone()  # clone, not assignment
        else:
            self.budget = None

    @abc.abstractmethod
    def name(self) -> K:
        """
        获取当前键

        返回:
            K: 当前键值
        """
        pass

    def get_priority(self) -> float:
        """
        获取优先级值

        返回:
            float: 当前优先级值
        """
        return self.budget.get_priority()

    def set_priority(self, v: float):
        """
        设置优先级值

        参数:
            v: 设置新的优先级值
        """
        self.budget.set_priority(v)

    def inc_priority(self, v: float):
        """
        增加优先级值

        参数:
            v: 增加量
        """
        self.budget.inc_priority(v)

    def dec_priority(self, v: float):
        """
        减少优先级值

        参数:
            v: 减少量
        """
        self.budget.dec_priority(v)

    def get_durability(self) -> float:
        """
        获取持久性值

        返回:
            float: 当前持久性值
        """
        return self.budget.get_durability()

    def set_durability(self, v: float):
        """
        设置持久性值

        参数:
            v: 新的持久性值
        """
        self.budget.set_durability(v)

    def inc_durability(self, v: float):
        """
        增加持久性值

        参数:
            v: 增加量
        """
        self.budget.inc_durability(v)

    def dec_durability(self, v: float):
        """
        减少持久性值

        参数:
            v: 减少量
        """
        self.budget.dec_durability(v)

    def get_quality(self) -> float:
        """
        获取质量值

        返回:
            float: 质量值
        """
        return self.budget.get_quality()

    def set_quality(self, v: float):
        """
        设置质量值

        参数:
            v: 新的质量值
        """
        self.budget.set_quality(v)

    def merge(self, that: 'Item') -> 'Item':
        """
        与具有相同键的另一个项目合并

        参数:
            that: 要合并的项目

        返回:
            Item: 合并后的项目: this或that
        """
        self.budget.merge(that.budget)
        return self

    def __str__(self) -> str:
        """
        返回项目的字符串表示

        返回:
            str: 完整内容的字符串表示
        """
        budget_str = str(self.budget) if self.budget is not None else ""
        n = str(self.name())
        return f"{budget_str} {n}"

    def to_string_external(self) -> str:
        """
        返回简化后的项目字符串表示

        返回:
            str: 简化后的内容字符串表示
        """
        brief_budget = self.budget.to_string_external()
        n = str(self.name())
        return f"{brief_budget} {n}"

    def to_string_external2(self) -> str:
        """
        类似于toStringExternal但将预算放在名称后面

        返回:
            str: 名称后带预算的字符串表示
        """
        brief_budget = self.budget.to_string_external()
        n = str(self.name())
        return f"{n} {brief_budget}"

    def to_string_long(self) -> str:
        """
        长字符串表示

        返回:
            str: 长字符串表示
        """
        return str(self)

    def __hash__(self) -> int:
        """
        哈希码

        返回:
            int: 哈希码
        """
        return hash(self.name())

    def __eq__(self, obj: Any) -> bool:
        """
        相等性检查

        参数:
            obj: 要比较的对象

        返回:
            bool: 如果相等则为True
        """
        if obj is self:
            return True
        if isinstance(obj, Item):
            return obj.name() == self.name()
        return False

class StringKeyItem(Item[str]):
    """具有字符串键的项目的抽象类"""

    def __init__(self, budget: BudgetValue):
        """
        构造函数

        参数:
            budget: 预算
        """
        super().__init__(budget)

    def __hash__(self) -> int:
        """
        哈希码

        返回:
            int: 哈希码
        """
        return hash(self.name())

    def __eq__(self, obj: Any) -> bool:
        """
        相等性检查

        参数:
            obj: 要比较的对象

        返回:
            bool: 如果相等则为True
        """
        if obj is self:
            return True
        if isinstance(obj, Item):
            return obj.name() == self.name()
        return False

def get_priority_sum(items: List[Item]) -> float:
    """
    获取项目集合的优先级总和

    参数:
        items: 项目集合

    返回:
        float: 优先级总和
    """
    total_priority = 0.0
    for i in items:
        total_priority += i.get_priority()
    return total_priority
