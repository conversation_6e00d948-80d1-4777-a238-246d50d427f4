"""
代理工厂接口

本模块提供了创建和配置代理的接口
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List

from linars.edu.memphis.ccrg.lida.Framework.agent import Agent

class AgentFactory(ABC):
    """
    代理工厂接口

    该接口定义了创建和配置代理的方法
    """

    @abstractmethod
    def create_agent(self) -> Optional[Agent]:
        """
        创建新代理

        返回:
            新创建的代理，如果创建失败则返回None
        """
        pass

    @abstractmethod
    def configure_agent(self, agent: Agent) -> None:
        """
        配置代理

        参数:
            agent: 需要配置的代理
        """
        pass

    @abstractmethod
    def get_default_properties(self) -> Dict[str, Any]:
        """
        获取代理的默认属性

        返回:
            包含默认属性的字典
        """
        pass
