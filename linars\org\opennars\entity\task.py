
"""
待处理的任务，由句子(Sentence)和预算值(BudgetValue)组成。

任务会引用其父任务和可选的因果因素(通常是Operation实例)。
这些引用使用弱引用实现，以便通过垃圾回收过程进行遗忘。
否则，任务祖先关系将无限增长，违反资源不足假设(AIKR)。
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar
import enum
import weakref

from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.entity.sentence import Sentence
from linars.org.opennars.entity.budget_value import BudgetValue
from linars.org.opennars.entity.item import Item

T = TypeVar('T', bound=Term)

class EnumType(enum.Enum):
    """任务类型枚举"""
    INPUT = 0  # 输入任务
    DERIVED = 1  # 派生任务

class Task(Item[Sentence[T]]):
    """
    待处理的任务，由句子和预算值组成。
    """

    def __init__(self, s: Sentence[T], b: BudgetValue, parent_belief: Optional[Sentence] = None,
                solution: Optional[Sentence] = None, task_type: EnumType = None):
        """
        构造函数

        参数:
            s: 句子
            b: 预算
            parent_belief: 用于派生任务的信念
            solution: 任务的解决方案
            task_type: 任务类型
        """
        super().__init__(b)
        self.processed = False  # 是否已处理
        self.sequence_task = False  # 是否为序列任务
        self.achieved = 0.0  # 完成度，0到1之间，1表示完全完成

        # 任务的句子
        self.sentence = s

        # 派生任务的信念，如果是从定理派生的则为None
        self.parent_belief = parent_belief

        # 派生任务的tasklink，除非Debug.PARENTS开启否则为None
        self.from_derive = None

        # 从目标派生的任务，用于避免循环
        self.from_goal = None

        # 父子关系，树状结构，如复杂的嵌套时间任务
        self.parent_task = None

        # 对于问题和目标：目前找到的最佳解决方案
        self.best_solution = solution

        # 任务是否应进入事件包
        self.part_of_sequence_buffer = False

        # 是否为输入任务
        self.is_input_task = task_type == EnumType.INPUT if task_type is not None else False

    def get_from_goal(self) -> Optional[Sentence]:
        """
        获取派生任务的目标

        返回:
            Sentence: 目标
        """
        return self.from_goal

    def isInput(self) -> bool:
        """
        检查任务是否为输入任务

        返回:
            bool: 如果是输入任务则为True
        """
        return self.is_input_task

    def name(self) -> Sentence:
        """
        获取任务名称

        返回:
            Sentence: 句子
        """
        return self.sentence

    def __eq__(self, other: Any) -> bool:
        """
        检查相等性

        参数:
            other: 要比较的对象

        返回:
            bool: 如果相等则为True
        """
        if other is self:
            return True

        if isinstance(other, Task):
            return other.sentence == self.sentence

        return False

    def __hash__(self) -> int:
        """
        哈希码

        返回:
            int: 哈希码
        """
        return hash(self.sentence)

    def get_creation_time(self) -> int:
        """
        直接获取句子的创建时间

        返回:
            int: 句子的创建时间
        """
        return self.sentence.stamp.get_creation_time()

    def is_input(self) -> bool:
        """
        检查任务是否为直接输入

        返回:
            bool: 任务是否派生自另一个任务
        """
        return self.is_input_task

    def above_threshold(self) -> bool:
        """
        检查任务是否超过阈值

        返回:
            bool: 如果超过阈值则为True
        """
        return self.budget.above_threshold()

    def merge(self, that: Item) -> Item:
        """
        将一个任务合并到另一个任务

        参数:
            that: 另一个任务

        返回:
            Item: 合并后的项目
        """
        if self.get_creation_time() >= that.get_creation_time():
            return super().merge(that)
        else:
            return that.merge(self)

    def get_best_solution(self) -> Optional[Sentence]:
        """
        获取问题或目标的最佳解决方案

        返回:
            Sentence: 存储的句子或None
        """
        return self.best_solution

    def set_best_solution(self, memory, judg: Sentence, time):
        """
        设置问题或目标的最佳解决方案，并为输入问题报告答案

        参数:
            memory: 内存
            judg: 要记住的解决方案
            time: 时间
        """
        if memory.internal_experience is not None:
            # 从judg中提取内部经验并存储在内存中
            from linars.org.opennars.plugin.mental.internal_experience import InternalExperience
            InternalExperience.internal_experience_from_belief(memory, self, judg, time)

        self.best_solution = judg

    def get_parent_belief(self) -> Optional[Sentence]:
        """
        获取任务的父信念

        返回:
            Sentence: 派生任务的信念
        """
        if self.parent_belief is None:
            return None
        return self.parent_belief

    def to_string_long(self) -> str:
        """
        获取任务的字符串表示

        返回:
            str: 任务字符串
        """
        s = f"{super().__str__()} {self.sentence.stamp.name()}"
        if self.best_solution is not None:
            s += f"\n  解决方案: {self.best_solution}"
        return s

    def set_elem_of_sequence_buffer(self, b: bool):
        """
        标记此事件任务是否参与时间归纳

        参数:
            b: 标记值
        """
        self.part_of_sequence_buffer = b

    def is_elem_of_sequence_buffer(self) -> bool:
        """
        检查任务是否为序列缓冲区的元素

        返回:
            bool: 如果是序列缓冲区的元素则为True
        """
        return (not self.sentence.is_eternal() and
                (self.is_input() or self.part_of_sequence_buffer))

    def get_term(self) -> T:
        """
        获取任务的术语

        返回:
            T: 术语
        """
        return self.sentence.get_term()

    def aboveThreshold(self) -> bool:
        """
        检查任务是否高于阈值

        返回:
            bool: 如果任务高于阈值则为True
        """
        return self.budget.above_threshold()

    def to_other_type(self, type_str: str) -> 'Task':
        """
        转换为另一种类型的任务

        参数:
            type_str: 类型字符串

        返回:
            Task: 转换后的任务
        """
        from linars.org.opennars.entity.sentence import JUDGMENT_MARK, QUESTION_MARK, GOAL_MARK

        if type_str == "goal":
            self.sentence.punctuation = GOAL_MARK
        elif type_str == "question":
            self.sentence.punctuation = QUESTION_MARK
        elif type_str == "ju":
            self.sentence.punctuation = JUDGMENT_MARK

        return self

    def get_achievement(self) -> float:
        """
        获取完成度

        返回:
            float: 完成度
        """
        return self.achieved

    def set_achievement(self, achievement: float):
        """
        设置完成度

        参数:
            achievement: 完成度
        """
        self.achieved = achievement

    def clone(self) -> 'Task':
        """
        创建任务的克隆

        返回:
            Task: 克隆的任务
        """
        try:
            # 克隆句子
            cloned_sentence = None
            if self.sentence is not None and hasattr(self.sentence, 'clone'):
                cloned_sentence = self.sentence.clone()
            else:
                cloned_sentence = self.sentence

            # 克隆预算
            cloned_budget = None
            if self.budget is not None and hasattr(self.budget, 'clone'):
                cloned_budget = self.budget.clone()
            else:
                cloned_budget = self.budget

            # 克隆父信念
            cloned_parent_belief = None
            if self.parent_belief is not None and hasattr(self.parent_belief, 'clone'):
                cloned_parent_belief = self.parent_belief.clone()
            else:
                cloned_parent_belief = self.parent_belief

            # 克隆最佳解决方案
            cloned_best_solution = None
            if self.best_solution is not None and hasattr(self.best_solution, 'clone'):
                cloned_best_solution = self.best_solution.clone()
            else:
                cloned_best_solution = self.best_solution

            # 创建新任务
            from linars.org.opennars.entity.task import EnumType
            task_type = EnumType.INPUT if self.is_input_task else EnumType.DERIVED
            cloned_task = Task(cloned_sentence, cloned_budget, cloned_parent_belief, cloned_best_solution, task_type)

            # 复制其他属性
            cloned_task.processed = self.processed
            cloned_task.sequence_task = self.sequence_task
            cloned_task.achieved = self.achieved
            cloned_task.from_derive = self.from_derive
            cloned_task.from_goal = self.from_goal
            cloned_task.parent_task = self.parent_task
            cloned_task.part_of_sequence_buffer = self.part_of_sequence_buffer

            return cloned_task
        except Exception as e:
            print(f"Error in Task.clone: {e}")
            # 如果克隆失败，返回原始任务
            return self
