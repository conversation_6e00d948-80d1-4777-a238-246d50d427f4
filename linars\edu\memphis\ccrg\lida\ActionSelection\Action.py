# LIDA认知框架
"""
封装待执行的行为
"""

from abc import abstractmethod
from enum import Enum
from linars.edu.memphis.ccrg.lida.Framework.Initialization.Initializable import Initializable

class Topology(Enum):
    """
    行为的拓扑结构
    """
    BASIC = 0
    PARALLEL = 1
    SEQUENTIAL = 2

class Action(Initializable):
    """
    封装待执行的行为
    """

    @abstractmethod
    def get_name(self) -> str:
        """
        获取此行为的名称

        返回:
            此行为的名称
        """
        pass

    @abstractmethod
    def set_name(self, name: str) -> None:
        """
        设置此行为的名称

        参数:
            name: 要设置的名称
        """
        pass

    @abstractmethod
    def get_label(self) -> str:
        """
        获取此行为的标签

        返回:
            此行为的标签
        """
        pass

    @abstractmethod
    def set_label(self, label: str) -> None:
        """
        设置此行为的标签

        参数:
            label: 要设置的标签
        """
        pass

    @abstractmethod
    def get_id(self) -> int:
        """
        获取此行为的ID

        返回:
            此行为的ID
        """
        pass

    @abstractmethod
    def set_id(self, id: int) -> None:
        """
        设置此行为的ID

        参数:
            id: 要设置的ID
        """
        pass
