"""
否定项定义
按照NARS理论定义的语句否定操作
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar
import copy

from linars.edu.memphis.ccrg.linars.term import Term
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm

class NativeOperator:
    """Native operators enum"""
    NEGATION = "--"

class Negation(CompoundTerm):
    """
    否定项类
    按照NARS理论定义的语句否定操作
    """

    def __init__(self, t: Term):
        """
        构造函数

        注意：避免在外部直接使用此构造函数，因为双重否定可以通过Negation.make还原为原始项

        参数:
            t: 要否定的项
        """
        super().__init__()
        self.init([t])

    def make_name(self) -> str:
        """
        创建项的名称

        返回:
            str: 项的名称
        """
        return self.make_compound_name(NativeOperator.NEGATION, self.term[0])

    def clone(self) -> 'Negation':
        """
        克隆对象

        返回:
            Negation: 新的否定项对象
        """
        return Negation(self.term[0])

    def clone_with_terms(self, replaced: List[Term]) -> Optional[Term]:
        """
        使用新项克隆对象

        参数:
            replaced: 新的项列表

        返回:
            Term: 克隆后的对象
        """
        if replaced is None:
            return None
        if len(replaced) != 1:
            return None
        return self.make(replaced[0])

    @staticmethod
    def make(t: Term) -> Term:
        """
        尝试创建一个否定项

        参数:
            t: 要否定的项

        返回:
            Term: 生成的复合项或简化后的项
        """
        if isinstance(t, Negation):
            # (--,(--,P)) = P
            return t.term[0]
        return Negation(t)

    @staticmethod
    def make_from_array(argument: List[Term]) -> Optional[Term]:
        """
        尝试从数组创建新的否定项

        参数:
            argument: 项列表

        返回:
            Term: 从参数生成的项
        """
        if len(argument) != 1:
            return None
        return Negation.make(argument[0])

    def operator(self) -> str:
        """
        获取项的操作符

        返回:
            str: 项的操作符
        """
        return NativeOperator.NEGATION

    @staticmethod
    def are_mutually_inverse(tc: Term, ptc: Term) -> bool:
        """
        检查两个项是否互逆

        参数:
            tc: 第一个项
            ptc: 第二个项

        返回:
            bool: 如果互逆则返回True
        """
        # Doesn't seem necessary to check both, one seems sufficient.
        # Incurs cost of creating a Negation and its id
        return ptc == Negation.make(tc)
