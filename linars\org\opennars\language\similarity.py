"""
相似关系(Similarity)是NARS理论中的对称关系，表示"A与B相似"

相似关系定义:
- 语法结构: <A <-> B>
- 表示A与B互为相似项
- 是可交换的对称关系
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar

from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.language.statement import Statement, NativeOperator

class Similarity(Statement):
    """
    相似关系类，表示NARS中的"A <-> B"关系

    主要特性:
    1. 表示两个项之间的相似关系
    2. 是可交换的对称关系
    3. 提供克隆和创建方法
    """

    def __init__(self, arg: List[Term] = None):
        """
        Constructor with partial values, called by make

        Args:
            arg: The component list of the term
        """
        super().__init__(arg)

    @classmethod
    def create(cls, subj: Term, pred: Term) -> 'Similarity':
        """
        Create a Similarity statement

        Args:
            subj: The subject term
            pred: The predicate term

        Returns:
            Similarity: The created statement
        """
        return cls([subj, pred])

    def clone(self, replaced: List[Term] = None) -> 'Similarity':
        """
        Clone an object

        Args:
            replaced: The replacement terms, if None use original terms

        Returns:
            Similarity: A new object
        """
        if replaced is not None:
            if len(replaced) != 2:
                raise ValueError(f"Invalid terms for {self.__class__.__name__}: {replaced}")
            return Similarity(replaced)
        return Similarity(self.term)

    def clone_with_terms(self, replaced: List[Term]) -> Optional['Similarity']:
        """
        Clone with new terms

        Args:
            replaced: The new terms

        Returns:
            Similarity: The cloned object
        """
        if replaced is None:
            return None

        if len(replaced) != 2:
            return None

        return self.make(replaced[0], replaced[1])

    @staticmethod
    def make_term(subject: Term, predicate: Term) -> Term:
        """
        Alternate version of make that allows equivalent subject and predicate
        to be reduced to the common term

        Args:
            subject: The subject term
            predicate: The predicate term

        Returns:
            Term: The created term
        """
        if subject == predicate:
            return subject

        return Similarity.make(subject, predicate)

    @staticmethod
    def make(subject: Term, predicate: Term) -> Optional['Similarity']:
        """
        尝试创建新的相似关系(由推理规则调用)

        参数:
            subject: 第一个项
            predicate: 第二个项

        返回:
            Similarity: 创建的相似关系，如果无效返回None

        注意:
        1. 会检查无效的相似关系组合
        2. 如果subject和predicate相同，会返回单项
        3. 关系是可交换的，顺序不影响结果
        """
        try:
            # 参数验证
            if subject is None:
                print(f"警告: Similarity.make 收到了空的主语")
                return None

            if predicate is None:
                print(f"警告: Similarity.make 收到了空的谓语")
                return None

            # 检查语句有效性
            try:
                if Statement.invalid_statement(subject, predicate):
                    return None
            except Exception as e:
                print(f"警告: 在检查语句有效性时发生异常: {e}")
                # 继续执行，不要因为这个检查失败而中断整个方法

            # 如果两个项相同，返回其中一个
            try:
                if subject == predicate:
                    return subject
            except Exception as e:
                print(f"警告: 在比较subject和predicate是否相同时发生异常: {e}")
                # 继续执行，不要因为这个检查失败而中断整个方法

            # 根据比较结果排序项
            try:
                if hasattr(subject, 'compare_to') and callable(getattr(subject, 'compare_to')):
                    try:
                        if subject.compare_to(predicate) > 0:
                            # 交换项的顺序
                            temp = subject
                            subject = predicate
                            predicate = temp
                    except Exception as e:
                        print(f"警告: 在比较subject和predicate顺序时发生异常: {e}")
                        # 继续执行，不要因为这个检查失败而中断整个方法
            except Exception as e:
                print(f"警告: 在检查compare_to方法时发生异常: {e}")
                # 继续执行，不要因为这个检查失败而中断整个方法

            # 创建相似关系
            try:
                return Similarity([subject, predicate])
            except Exception as e:
                print(f"错误: 在创建相似关系时发生异常: {e}")
                return None

        except Exception as e:
            print(f"错误: Similarity.make方法发生未处理的异常: {e}")
            return None

    def operator(self) -> NativeOperator:
        """
        Get the operator of the term

        Returns:
            NativeOperator: The operator of the term
        """
        return NativeOperator.SIMILARITY

    def is_commutative(self) -> bool:
        """
        Check if the compound is commutative

        Returns:
            bool: True for commutative
        """
        return True
