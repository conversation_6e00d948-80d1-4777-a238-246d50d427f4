# LIDA Cognitive Framework
"""
Default implementation of ProceduralMemory. Indexes scheme by context
elements for quick access. Assumes that the Condition of Scheme are Node only.
"""

import logging
from typing import Dict, Set, List, Any, Collection, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener import BroadcastListener
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WorkspaceBuffer import WorkspaceBuffer
from linars.edu.memphis.ccrg.lida.Workspace.Workspace import Workspace
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action
from linars.edu.memphis.ccrg.lida.ActionSelection.Behavior import Behavior
from linars.edu.memphis.ccrg.lida.ActionSelection.BehaviorImpl import BehaviorImpl
from linars.edu.memphis.ccrg.lida.ProceduralMemory.ProceduralMemory import ProceduralMemory
from linars.edu.memphis.ccrg.lida.ProceduralMemory.ProceduralMemoryListener import ProceduralMemoryListener
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Scheme import Scheme
from linars.edu.memphis.ccrg.lida.ProceduralMemory.SchemeImpl import SchemeImpl, ConditionType
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Condition import Condition
from linars.edu.memphis.ccrg.lida.ProceduralMemory.ConditionImpl import ConditionImpl

class ProceduralMemoryImpl(FrameworkModuleImpl, ProceduralMemory, BroadcastListener):
    """
    Default implementation of ProceduralMemory. Indexes scheme by context
    elements for quick access. Assumes that the Condition of Scheme are Node only.
    """

    # Default values
    DEFAULT_SCHEME_SELECTION_THRESHOLD = 0.5
    DEFAULT_SCHEME_REMOVAL_THRESHOLD = 0.0

    class InternalNodeStructure(NodeStructureImpl):
        """
        Allows Nodes to be added without copying.
        Warning: doing so allows the same java object of Node to exist in multiple places.
        """

        def __init__(self, node_type: str = None, link_type: str = None):
            """
            Initialize an InternalNodeStructure.

            Args:
                node_type: The type of nodes in this structure
                link_type: The type of links in this structure
            """
            super().__init__()

        def add_node(self, node: Node, copy: bool = True) -> Node:
            """
            Add a node to this structure.

            Args:
                node: The node to add
                copy: Whether to copy the node

            Returns:
                The added node
            """
            return super().add_node(node, copy)

    def __init__(self):
        """
        Initialize a ProceduralMemoryImpl.
        """
        super().__init__()
        self.procedural_memory_listeners: List[ProceduralMemoryListener] = []
        self.scheme_set: Set[Scheme] = set()
        self.context_scheme_map: Dict[Any, Set[Scheme]] = {}
        self.adding_scheme_map: Dict[Any, Set[Scheme]] = {}
        self.condition_pool: Dict[Any, Condition] = {}
        self.broadcast_buffer = self.InternalNodeStructure()
        self.scheme_selection_threshold = self.DEFAULT_SCHEME_SELECTION_THRESHOLD
        self.scheme_removal_threshold = self.DEFAULT_SCHEME_REMOVAL_THRESHOLD
        self.non_graph = None
        self.seq_graph = None
        self.goal_graph = None
        self.word_graph = None
        self.logger = logging.getLogger(self.__class__.__name__)

    def init(self, params=None) -> None:
        """
        Initialize this ProceduralMemoryImpl.

        Args:
            params: Parameters for initialization, defaults to None
        """
        super().init(params)
        # print(f"ProceduralMemoryImpl.init() called with params: {params}")
        print("ProceduralMemoryImpl.init()")
        self.scheme_selection_threshold = self.get_param("proceduralMemory.schemeSelectionThreshold", self.DEFAULT_SCHEME_SELECTION_THRESHOLD)
        self.scheme_removal_threshold = self.get_param("proceduralMemory.schemeRemovalThreshold", self.DEFAULT_SCHEME_REMOVAL_THRESHOLD)

    def get_param(self, name: str, default_value: Any) -> Any:
        """
        Get a parameter value with a default.

        Args:
            name: The name of the parameter
            default_value: The default value

        Returns:
            The parameter value or the default value
        """
        parameters = getattr(self, "parameters", {})
        if parameters and name in parameters:
            return parameters[name]
        return default_value

    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module for this ProceduralMemory.

        Args:
            module: The module to associate with this ProceduralMemory
            module_usage: How this ProceduralMemory will use the module
        """
        if isinstance(module, Workspace):
            self.non_graph = module.get_submodule(ModuleName.get_module_name("NonGraph"))
            self.seq_graph = module.get_submodule(ModuleName.get_module_name("SeqGraph"))
            self.goal_graph = module.get_submodule(ModuleName.get_module_name("GoalGraph"))
            self.word_graph = module.get_submodule(ModuleName.get_module_name("WordGraph"))
        else:
            self.logger.warning(f"Cannot add module {module} at tick {TaskManager.get_current_tick()}")

    def add_listener(self, listener: ModuleListener) -> None:
        """
        Add a listener to this ProceduralMemory.

        Args:
            listener: The listener to add
        """
        if isinstance(listener, ProceduralMemoryListener):
            self.procedural_memory_listeners.append(listener)
        else:
            self.logger.warning(f"Requires ProceduralMemoryListener but received {listener} at tick {TaskManager.get_current_tick()}")

    def get_new_scheme(self, action: Action) -> Optional[Scheme]:
        """
        Gets a new Scheme having specified Action.

        Args:
            action: An Action

        Returns:
            A new Scheme with the specified Action
        """
        if action is None:
            self.logger.warning(f"Action is null, cannot create scheme at tick {TaskManager.get_current_tick()}")
            return None

        scheme = SchemeImpl()
        scheme.set_action(action)
        self.scheme_set.add(scheme)
        return scheme

    def contains_scheme(self, scheme: Scheme) -> bool:
        """
        Returns whether this procedural memory contains specified scheme.

        Args:
            scheme: A Scheme

        Returns:
            True if it contains an equal scheme, False otherwise
        """
        return scheme in self.scheme_set

    def add_scheme(self, scheme: Scheme) -> bool:
        """
        Adds specified scheme to this procedural memory.

        Args:
            scheme: A Scheme to add

        Returns:
            True if the scheme was added, False otherwise
        """
        if scheme is None:
            self.logger.warning(f"Cannot add null scheme at tick {TaskManager.get_current_tick()}")
            return False

        if self.contains_scheme(scheme):
            self.logger.warning(f"Scheme {scheme} already exists at tick {TaskManager.get_current_tick()}")
            return False

        self.scheme_set.add(scheme)
        self.add_to_map(scheme, scheme.get_context_conditions(), self.context_scheme_map)
        self.add_to_map(scheme, scheme.get_adding_list(), self.adding_scheme_map)
        self.logger.debug(f"Added scheme {scheme} at tick {TaskManager.get_current_tick()}")
        return True

    def add_to_map(self, scheme: Scheme, conditions: Collection[Condition], scheme_map: Dict[Any, Set[Scheme]]) -> None:
        """
        Add a scheme to a map.

        Args:
            scheme: The scheme to add
            conditions: The conditions to use as keys
            scheme_map: The map to add to
        """
        for condition in conditions:
            condition_id = condition.get_condition_id()
            if condition_id not in scheme_map:
                scheme_map[condition_id] = set()
            scheme_map[condition_id].add(scheme)

    def remove_scheme(self, scheme: Scheme) -> None:
        """
        Removes specified scheme from this procedural memory.

        Args:
            scheme: A Scheme to remove
        """
        self.scheme_set.remove(scheme)
        self.remove_from_map(scheme, scheme.get_context_conditions(), self.context_scheme_map)
        self.remove_from_map(scheme, scheme.get_adding_list(), self.adding_scheme_map)

    def remove_from_map(self, scheme: Scheme, conditions: Collection[Condition], scheme_map: Dict[Any, Set[Scheme]]) -> None:
        """
        Remove a scheme from a map.

        Args:
            scheme: The scheme to remove
            conditions: The conditions to use as keys
            scheme_map: The map to remove from
        """
        for condition in conditions:
            condition_id = condition.get_condition_id()
            if condition_id in scheme_map:
                scheme_map[condition_id].remove(scheme)
                if not scheme_map[condition_id]:
                    del scheme_map[condition_id]

    def add_condition(self, condition: Condition) -> Optional[Condition]:
        """
        Add a condition to the condition pool.

        Args:
            condition: The condition to add

        Returns:
            The added condition or None if the condition could not be added
        """
        if condition is None:
            return None

        condition_id = condition.get_condition_id()
        if condition_id in self.condition_pool:
            return self.condition_pool[condition_id]

        self.condition_pool[condition_id] = condition
        self.logger.debug(f"Added condition {condition} at tick {TaskManager.get_current_tick()}")
        return condition

    def index_scheme(self, scheme: Scheme, condition: Condition, condition_type: ConditionType) -> None:
        """
        Index a scheme by a condition.

        Args:
            scheme: The scheme to index
            condition: The condition to index by
            condition_type: The type of the condition
        """
        if scheme is None or condition is None:
            return

        condition_id = condition.get_condition_id()
        if condition_type == ConditionType.CONTEXT:
            if condition_id not in self.context_scheme_map:
                self.context_scheme_map[condition_id] = set()
            self.context_scheme_map[condition_id].add(scheme)
        elif condition_type == ConditionType.ADDINGLIST:
            if condition_id not in self.adding_scheme_map:
                self.adding_scheme_map[condition_id] = set()
            self.adding_scheme_map[condition_id].add(scheme)

    def receive_broadcast(self, coalition: Coalition) -> None:
        """
        Receive a broadcast from the GlobalWorkspace.

        Args:
            coalition: The coalition that won the competition for consciousness
        """
        content = coalition.get_content()
        if content is None:
            return

        self.broadcast_buffer = content

        # In a real implementation, this would process the broadcast content
        # For now, we'll just activate schemes
        self.activate_schemes()

    def learn(self, coalition: Coalition) -> None:
        """
        Learn from a coalition.

        Args:
            coalition: The coalition to learn from
        """
        # This method is a placeholder for learning from broadcasts
        # It should be implemented by the receiving module
        pass

    def activate_schemes(self) -> None:
        """
        Activates schemes based on the current broadcast buffer.
        """
        # To prevent a scheme from being instantiated multiple times all
        # schemes over threshold are stored in a set
        relevant_schemes: Set[Scheme] = set()

        # For each node in the broadcast buffer, check if it is in the context of any scheme
        for node in self.broadcast_buffer.get_nodes():
            node_id = node.get_id()
            if node_id in self.context_scheme_map:
                for scheme in self.context_scheme_map[node_id]:
                    if self.should_instantiate(scheme, self.broadcast_buffer):
                        relevant_schemes.add(scheme)

        # Instantiate all relevant schemes
        for scheme in relevant_schemes:
            self.create_instantiation(scheme)

    def should_instantiate(self, scheme: Scheme, broadcast_buffer: NodeStructure) -> bool:
        """
        A call-back method to determine if the Scheme should be instantiated.
        This method can be overridden by subclasses to provide custom functionality.

        Args:
            scheme: The Scheme to be checked
            broadcast_buffer: The NodeStructure in ProceduralMemory containing recent broadcast

        Returns:
            True if the Scheme should be instantiated, False otherwise
        """
        return scheme.get_total_activation() >= self.scheme_selection_threshold

    def create_instantiation(self, scheme: Scheme) -> Behavior:
        """
        Instantiates specified Scheme.

        Args:
            scheme: A Scheme over instantiation threshold

        Returns:
            A Behavior, an instantiation of the specified Scheme
        """
        self.logger.debug(f"Instantiating scheme: {scheme} in ProceduralMemory at tick {TaskManager.get_current_tick()}")
        behavior = BehaviorImpl()
        behavior.set_scheme(scheme)
        behavior.set_broadcost_id(self.broadcast_buffer.get_broad_scene_count())

        for listener in self.procedural_memory_listeners:
            listener.receive_behavior(behavior)

        return behavior

    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.

        Args:
            ticks: The number of ticks to decay by
        """
        self.broadcast_buffer.decay_node_structure(ticks)

        schemes_to_remove = []
        for scheme in self.scheme_set:
            try:
                # 检查is_innate是属性还是方法
                is_innate = False
                if hasattr(scheme, 'is_innate'):
                    if callable(scheme.is_innate):
                        is_innate = scheme.is_innate()
                    else:
                        is_innate = scheme.is_innate

                if not is_innate:
                    scheme.decay(ticks)
                    if scheme.get_activation() <= self.scheme_removal_threshold:
                        schemes_to_remove.append(scheme)
            except Exception as e:
                self.logger.warning(f"Error decaying scheme {scheme}: {e}")

        for scheme in schemes_to_remove:
            self.remove_scheme(scheme)

    def get_module_content(self, *params: Any) -> Any:
        """
        Get the content of this module.

        Args:
            params: Parameters specifying what content to return

        Returns:
            The content of this module
        """
        if params and params[0] == "schemes":
            return self.scheme_set
        return None
