"""
NARS系统的答案处理器
用于处理问题解答相关逻辑
"""
from abc import ABC, abstractmethod
from typing import List, Any, Optional, Type

from linars.org.opennars.io.events.event_emitter import EventEmitter
from linars.org.opennars.io.events.events import Answer
from linars.org.opennars.entity.task import Task
from linars.org.opennars.entity.sentence import Sentence

class AnswerHandler(EventEmitter.EventObserver, ABC):
    """
    NARS答案处理器
    继承自事件观察者接口，用于处理问题解答事件
    """

    def __init__(self):
        """构造函数"""
        self.question = None
        self.nar = None
        self.events = [Answer]

    def start(self, question: Task, n):
        """
        启动答案处理器

        参数:
            question: 待解答的问题任务
            n: NARS系统实例
        """
        self.nar = n
        self.question = question

        self.nar.event(self, True, *self.events)

    def off(self):
        """关闭答案处理器"""
        self.nar.event(self, False, *self.events)

    def event(self, event, args):
        """
        处理事件

        参数:
            event: 事件类型
            args: 事件参数
        """
        if event == Answer:
            task = args[0]
            belief = args[1]
            if task == self.question:
                self.on_solution(belief)

    @abstractmethod
    def on_solution(self, belief: Sentence):
        """
        当问题被直接解答时调用

        参数:
            belief: 解答问题的信念
        """
        pass
