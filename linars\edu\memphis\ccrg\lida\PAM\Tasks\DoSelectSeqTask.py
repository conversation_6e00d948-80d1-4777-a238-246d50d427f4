#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to handle selection sequence processing.
"""

from typing import Optional
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory

class DoSelectSeqTask(FrameworkTaskImpl):
    """
    A task to handle selection sequence processing.
    """

    def __init__(self, link: Link, pam: PAMemory, ticks_per_run: int, act_stamp: Optional[str] = None):
        """
        Initialize a DoSelectSeqTask.

        Args:
            link: The link
            pam: The PAMemory
            ticks_per_run: The ticks per run
            act_stamp: The action stamp
        """
        super().__init__(1, "tact")
        self.pam = pam
        self.link = link
        self.act_stamp = act_stamp

    def run_this_framework_task(self):
        """
        Run the task.
        """
        try:
            # 直接调用 get_act_root 方法
            self.pam.get_act_root(self.link, False, False, self.act_stamp)
        except Exception as e:
            print(f"Error in DoSelectSeqTask: {e}")

        self.cancel()
