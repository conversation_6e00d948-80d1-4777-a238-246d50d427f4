"""
NARS符号定义
包含NARS系统中使用的各种符号常量
"""
from enum import Enum
from typing import Dict, Optional

# Sentence type and delimiters
JUDGMENT_MARK = '.'
QUESTION_MARK = '?'
GOAL_MARK = '!'
QUEST_MARK = '@'
TERM_NORMALIZING_WORKAROUND_MARK = 'T'

# Tense markers
TENSE_MARK = ":"
TENSE_PAST = ":\\:"
TENSE_PRESENT = ":|:"
TENSE_FUTURE = ":/:";

# Variable type
VAR_INDEPENDENT = '$'
VAR_DEPENDENT = '#'
VAR_QUERY = '?'

# Numerical value delimiters, must be different from the Term delimiters
BUDGET_VALUE_MARK = '$'
TRUTH_VALUE_MARK = '%'
VALUE_SEPARATOR = ';'

# Special characters in argument list
ARGUMENT_SEPARATOR = ','
IMAGE_PLACE_HOLDER = '_'

# Prefix of special Term name
INTERVAL_PREFIX = '+'
OPERATOR_PREFIX = '^'
TERM_PREFIX = 'T'
QUOTE = '\"'

# Experience line prefix
INPUT_LINE_PREFIX = "IN"
OUTPUT_LINE_PREFIX = "OUT"
ERROR_LINE_PREFIX = "ERR"

PREFIX_MARK = ':'
COMMENT_MARK = '/'
ECHO_MARK = '\''

# Control commands
RESET_COMMAND = "*reset"
REBOOT_COMMAND = "*reboot"
STOP_COMMAND = "*stop"
START_COMMAND = "*start"
SET_NOISE_LEVEL_COMMAND = "*volume"
SET_DECISION_LEVEL_COMMAND = "*decisionthreshold"

# Stamp, display only
STAMP_OPENER = '{'
STAMP_CLOSER = '}'
STAMP_SEPARATOR = ';'
COMPOUND_TERM_OPENER = '('
COMPOUND_TERM_CLOSER = ')'
SET_INT_OPENER = '['
SET_INT_CLOSER = ']'
SET_EXT_OPENER = '{'
SET_EXT_CLOSER = '}'
STATEMENT_OPENER = '<'
STATEMENT_CLOSER = '>'
STAMP_STARTER = ':'

# TermLink type, display only
TO_COMPONENT_1 = "@("
TO_COMPONENT_2 = ")_"
TO_COMPOUND_1 = "_@("
TO_COMPOUND_2 = ")"

SELF = "SELF"

class NativeOperator(Enum):
    """NARS原生操作符枚举"""

    # CompoundTerm operators, length = 1
    INTERSECTION_EXT = ("&", False, True)  # 外延交集关系
    INTERSECTION_INT = ("|", False, True)  # 内涵交集关系
    DIFFERENCE_EXT = ("-", False, True)    # 外延差异关系
    DIFFERENCE_INT = ("~", False, True)    # 内涵差异关系
    PRODUCT = ("*", False, True)
    IMAGE_EXT = ("/", False, True)         # 图像外延关系
    IMAGE_INT = ("\\", False, True)        # 图像内涵关系

    # CompoundStatement operators, length = 2
    NEGATION = ("--", False, True)         # 否定关系
    DISJUNCTION = ("||", False, True)      # 或关系
    CONJUNCTION = ("&&", False, True)      # 与关系
    SEQUENCE = ("&/", False, True)
    PARALLEL = ("&|", False, True)
    SPATIAL = ("#", False, True)           # 空间关系，如序列关系、并行关系等
    SPATIAL_AFTER = ("#/", False, True)    # 前向序列关系
    SPATIAL_PARALLEL = ("#|", False, True) # 并列关系，无序
    SPATIAL_BEFORE = ("#\\", False, True)  # 后向序列关系
    TEMPORAL = ("@", False, True)          # 时间关系，如序列关系、并行关系等

    # CompoundTerm delimiters, must use 4 different pairs
    SET_INT_OPENER = ("[", False, True)
    SET_INT_CLOSER = ("]", False, False)
    SET_EXT_OPENER = ("{", False, True)
    SET_EXT_CLOSER = ("}", False, False)

    # Syntactical, so is neither relation or isNative
    COMPOUND_TERM_OPENER = ("(", False, False)
    COMPOUND_TERM_CLOSER = (")", False, False)
    STATEMENT_OPENER = ("<", False, False)
    STATEMENT_CLOSER = (">", False, False)

    # Relations
    INHERITANCE = ("-->", True)
    SIMILARITY = ("<->", True)             # 相似关系
    INSTANCE = ("{--", True)
    PROPERTY = ("--]", True)
    INSTANCE_PROPERTY = ("{-]", True)
    IMPLICATION = ("==>", True)

    # Temporal Relations
    IMPLICATION_AFTER = ("=/>", True)
    IMPLICATION_WHEN = ("=|>", True)
    IMPLICATION_BEFORE = ("=\\>", True)
    EQUIVALENCE = ("<=>", True)            # 对等关系
    EQUIVALENCE_AFTER = ("</>", True)
    EQUIVALENCE_WHEN = ("<|>", True)

    INHERITANCE_NEO = ("-=>", True)

    # An atomic term; this value is set if not a compound term
    ATOM = (".", False)

    def __init__(self, symbol, relation=False, is_native=None):
        """
        初始化原生操作符

        参数:
            symbol: 操作符的符号表示
            relation: 是否为关系操作符?
            is_native: 是否为原生操作符?
        """
        self.symbol = symbol
        self.relation = relation
        self.is_native = is_native if is_native is not None else not relation
        self.ch = symbol[0] if len(symbol) == 1 else None
        self.opener = self.name.endswith("_OPENER")
        self.closer = self.name.endswith("_CLOSER")

    def __str__(self):
        return self.symbol


# Setup NativeOperator String index dictionary
string_to_operator: Dict[str, NativeOperator] = {}
for op in NativeOperator:
    string_to_operator[op.symbol] = op

# Setup NativeOperator Character index dictionary
char_to_operator: Dict[str, NativeOperator] = {}
for op in NativeOperator:
    if op.ch is not None:
        char_to_operator[op.ch] = op


def get_operator(s: str) -> Optional[NativeOperator]:
    """
    根据符号获取操作符

    参数:
        s: 符号字符串

    返回:
        NativeOperator: 对应的操作符，找不到则返回None
    """
    # 注意: 这里原来有一个硬编码的测试值 s = "&"
    if s is None:
        print(f"警告: get_operator 收到了 None 值")
        return None

    try:
        if len(s) == 1:
            result = char_to_operator.get(s)
            return result
        else:
            result = string_to_operator.get(s)

            # 确保返回的是 NativeOperator 枚举值
            if result is not None and not isinstance(result, NativeOperator):
                # 如果不是枚举值，尝试找到对应的枚举值
                for op in NativeOperator:
                    if op.symbol == s:
                        return op

            return result
    except Exception as e:
        print(f"错误: 在获取操作符 '{s}' 时发生异常: {e}")
        return None


def get_relation(s: str) -> Optional[NativeOperator]:
    """
    根据符号获取关系操作符

    参数:
        s: 符号字符串

    返回:
        NativeOperator: 对应的关系操作符，找不到则返回None
    """
    op = get_operator(s)

    # 确保返回有效的关系操作符

    # 确保返回的是 NativeOperator 枚举值
    if op is not None:
        if isinstance(op, NativeOperator):
            if op.relation:
                return op
        elif isinstance(op, tuple) and len(op) > 1 and op[1]:  # 如果是元组且第二个元素为 True（表示是关系）
            # 尝试找到对应的枚举值
            symbol = op[0] if len(op) > 0 else s
            for enum_op in NativeOperator:
                if enum_op.symbol == symbol:
                    return enum_op

    return None


def get_opener(c: str) -> Optional[NativeOperator]:
    """
    根据字符获取开括号操作符

    参数:
        c: 字符

    返回:
        NativeOperator: 对应的开括号操作符，找不到则返回None
    """
    op = get_operator(c)
    if op is not None and op.opener:
        return op
    return None


def get_closer(c: str) -> Optional[NativeOperator]:
    """
    根据字符获取闭括号操作符

    参数:
        c: 字符

    返回:
        NativeOperator: 对应的闭括号操作符，找不到则返回None
    """
    op = get_operator(c)
    if op is not None and op.closer:
        return op
    return None


def is_relation(s: str) -> bool:
    """
    检查字符串是否为关系操作符

    参数:
        s: 待检查的字符串

    返回:
        bool: 如果是关系操作符则返回True
    """
    return get_relation(s) is not None


def debug_operators():
    """
    输出所有操作符信息，用于调试
    """
    print("\n=== 字符串操作符字典 ===\n")
    for symbol, op in string_to_operator.items():
        print(f"'{symbol}' -> {op.name}")

    print("\n=== 单字符操作符字典 ===\n")
    for char, op in char_to_operator.items():
        print(f"'{char}' -> {op.name}")
