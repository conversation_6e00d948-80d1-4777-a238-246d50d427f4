# LIDA Cognitive Framework
"""
Abstract implementation of FrameworkModule.
"""

import logging
from typing import Dict, Any, Optional, List
from concurrent.futures import ThreadPoolExecutor
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawner import TaskSpawner
from linars.edu.memphis.ccrg.lida.Framework.Initialization.InitializableImpl import InitializableImpl

class FrameworkModuleImpl(InitializableImpl, FrameworkModule):
    """
    Abstract implementation of FrameworkModule.
    Implementations should add themselves to the agent.xml configuration file.
    """
    
    # Class variable for task spawner
    task_spawner = None
    
    def __init__(self, name: ModuleName = None):
        """
        Initialize a FrameworkModuleImpl.
        
        Args:
            name: The name of this module
        """
        super().__init__()
        self.module_name = name if name is not None else ModuleName.UnnamedModule
        self.submodules: Dict[ModuleName, FrameworkModule] = {}
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def set_assisting_task_spawner(self, ts: TaskSpawner) -> None:
        """
        Specify the TaskSpawner which this FrameworkModule will use to spawn tasks.
        
        Args:
            ts: The TaskSpawner
        """
        FrameworkModuleImpl.task_spawner = ts
    
    def get_assisting_task_spawner(self) -> TaskSpawner:
        """
        Returns the TaskSpawner which this FrameworkModule uses to spawn tasks.
        
        Returns:
            The assisting task spawner
        """
        return FrameworkModuleImpl.task_spawner
    
    def get_submodule(self, name: ModuleName) -> Optional[FrameworkModule]:
        """
        Gets specified submodule.
        
        Args:
            name: Name of the desired submodule.
            
        Returns:
            The submodule.
        """
        if name is None:
            return None
        return self.submodules.get(name)
    
    def get_submodule_by_string(self, name: str) -> Optional[FrameworkModule]:
        """
        Gets specified submodule.
        
        Args:
            name: Name of the desired submodule.
            
        Returns:
            The submodule.
        """
        if name is None:
            return None
        return self.get_submodule(ModuleName.get_module_name(name))
    
    def contains_submodule(self, name: ModuleName) -> bool:
        """
        Returns whether this FrameworkModule contains a submodule with 
        specified ModuleName.
        
        Args:
            name: ModuleName of submodule
            
        Returns:
            True if there is a FrameworkModule with specified ModuleName
            in this FrameworkModule
        """
        return self.get_submodule(name) is not None
    
    def contains_submodule_by_string(self, name: str) -> bool:
        """
        Returns whether this FrameworkModule contains a submodule with 
        specified name.
        
        Args:
            name: Name of submodule
            
        Returns:
            True if there is a FrameworkModule with specified name
            in this FrameworkModule
        """
        return self.get_submodule_by_string(name) is not None
    
    def add_submodule(self, module: FrameworkModule) -> None:
        """
        Adds submodule as a component of this FrameworkModule.
        
        Args:
            module: Submodule to add
        """
        from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
        
        if module is None:
            self.logger.warning(f"Cannot add null submodule at tick {TaskManager.get_current_tick()}")
        elif module.get_module_name() is None:
            self.logger.warning(f"Cannot add a submodule with null ModuleName at tick {TaskManager.get_current_tick()}")
        else:
            self.submodules[module.get_module_name()] = module
    
    def task_manager_decay_module(self, ticks: int) -> None:
        """
        Framework users should not call this method.
        It will be called by the TaskManager. Decays this module and all its submodules.
        Subclasses overriding this method must call this method first in order to have all
        submodules decayed.
        
        Args:
            ticks: Number of ticks to decay.
        """
        from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
        
        try:
            self.decay_module(ticks)  # First call this FrameworkModule's decayModule method.
        except Exception as e:
            self.logger.warning(f"Exception occurred during the execution of the 'decay_module(ticks)' method in module: {self.module_name} at tick {TaskManager.get_current_tick()}. \n{e}")
            import traceback
            traceback.print_exc()
        
        for module in self.submodules.values():
            try:
                module.task_manager_decay_module(ticks)  # Then call all submodule's taskManagerDecayModule.
            except Exception as e:
                self.logger.warning(f"Exception occurred during the execution of the 'task_manager_decay_module(ticks)' method in module: {self.module_name} at tick {TaskManager.get_current_tick()}. \n{e}")
                import traceback
                traceback.print_exc()
    
    def decay_module(self, ticks: int) -> None:
        """
        Decay only this Module.
        
        Args:
            ticks: Number of ticks to decay.
        """
        pass
    
    def set_module_name(self, module_name: ModuleName) -> None:
        """
        Sets ModuleName.
        
        Args:
            module_name: ModuleName of this FrameworkModule
        """
        self.module_name = module_name
    
    def get_module_name(self) -> ModuleName:
        """
        Gets moduleName.
        
        Returns:
            ModuleName of this FrameworkModule
        """
        return self.module_name
    
    def __str__(self) -> str:
        """
        Return the string representation of this module.
        
        Returns:
            The name of this module
        """
        return None if self.module_name is None else self.module_name.name
    
    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module for this module.
        
        Args:
            module: The module to associate with this module
            module_usage: How this module will use the module
        """
        pass
    
    def add_listener(self, listener: ModuleListener) -> None:
        """
        Override this method to add a listener to the module.
        
        Args:
            listener: Listener of this FrameworkModule
        """
        pass
    
    def get_module_content(self, *params: Any) -> Any:
        """
        Intended to be called from the GUI. Override this method to return
        particular module content based on params.
        
        Args:
            params: Parameters specifying what content will be returned
            
        Returns:
            Parameter-specified content of this module.
        """
        return None
    
    def get_submodules(self) -> Dict[ModuleName, FrameworkModule]:
        """
        Convenience method to get submodules.
        
        Returns:
            Map of submodules by ModuleName
        """
        return self.submodules
