# LIDA Cognitive Framework
"""
A condition that is satisfied when a node with a specific ID is present in the content.
"""

from typing import Dict, Any
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.ProceduralMemory.ConditionImpl import ConditionImpl

class NodeCondition(ConditionImpl):
    """
    A condition that is satisfied when a node with a specific ID is present in the content.
    """
    
    def __init__(self, node: Node = None):
        """
        Initialize a NodeCondition.
        
        Args:
            node: The node that this condition represents
        """
        super().__init__()
        self.node = node
        if node is not None:
            self.set_condition_name(node.get_node_name())
    
    def is_satisfied(self, content: NodeStructure) -> bool:
        """
        Check if this condition is satisfied by the given content.
        
        Args:
            content: The content to check
            
        Returns:
            True if this condition is satisfied, False otherwise
        """
        if content is None or self.node is None:
            return False
        
        for node in content.get_nodes():
            if node.get_id() == self.node.get_id():
                return True
        
        return False
    
    def get_node(self) -> Node:
        """
        Get the node that this condition represents.
        
        Returns:
            The node that this condition represents
        """
        return self.node
    
    def set_node(self, node: Node) -> None:
        """
        Set the node that this condition represents.
        
        Args:
            node: The node to set
        """
        self.node = node
        if node is not None:
            self.set_condition_name(node.get_node_name())
    
    def init(self, params: Dict[str, Any]) -> None:
        """
        Initialize this condition with the given parameters.
        
        Args:
            params: Parameters for initialization
        """
        super().init(params)
        if "node" in params:
            self.set_node(params["node"])
    
    def __eq__(self, other) -> bool:
        """
        Check if this condition is equal to another.
        
        Args:
            other: The other condition to compare with
            
        Returns:
            True if the conditions have the same node, False otherwise
        """
        if isinstance(other, NodeCondition):
            if self.node is None or other.get_node() is None:
                return False
            return self.node.get_id() == other.get_node().get_id()
        return False
    
    def __hash__(self) -> int:
        """
        Return the hash code of this condition.
        
        Returns:
            The hash code of the node ID
        """
        return hash(self.node.get_id() if self.node is not None else 0)
    
    def __str__(self) -> str:
        """
        Return the string representation of this condition.
        
        Returns:
            The label of the node
        """
        return self.node.get_node_name() if self.node is not None else "NodeCondition"
