"""
LocalRules的辅助函数，用于避免循环导入问题。
"""
from typing import List, Optional
import math

from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.entity.truth_value import TruthValue
from linars.org.opennars.inference.truth_functions import TruthFunctions
from linars.org.opennars.inference.utility_functions import UtilityFunctions

def calc_task_achievement(t1: Optional[TruthValue], t2: TruthValue) -> float:
    """
    计算任务的达成度。

    参数:
        t1: 第一个真值
        t2: 第二个真值

    返回:
        任务的达成度
    """
    if t1 is None:
        return t2.get_expectation()

    return abs(t1.get_expectation() - t2.get_expectation())

def interval_projection(nal, new_belief_term: Term, old_belief_term: Term, recent_ivals: List[float], new_truth: TruthValue) -> bool:
    """
    间隔投影。

    根据哪个更常用来决定使用旧术语还是新术语，
    同时根据间隔差异调整真值置信度。

    参数:
        nal: 派生上下文
        new_belief_term: 新信念术语
        old_belief_term: 旧信念术语
        recent_ivals: 最近的间隔
        new_truth: 新真值

    返回:
        如果应该使用新信念术语返回True，否则返回False
    """
    use_new_belief_term = False

    if new_belief_term.has_interval():
        ival_old = CompoundTerm.extract_intervals(nal.memory, old_belief_term)
        ival_new = CompoundTerm.extract_intervals(nal.memory, new_belief_term)
        abs_diff_sum_new = 0
        abs_diff_sum_old = 0

        with recent_ivals:
            if not recent_ivals:
                for l in ival_old:
                    recent_ivals.append(float(l))

            for i in range(len(ival_new)):
                inbetween = (recent_ivals[i] + ival_new[i]) / 2.0  # vote as one new entry, turtle style
                speed = 1.0 / (nal.nar.narParameters.INTERVAL_ADAPT_SPEED * (1.0 - new_truth.get_expectation()))  # less truth expectation, slower
                recent_ivals[i] = recent_ivals[i] + speed * (inbetween - recent_ivals[i])

            for i in range(len(ival_new)):
                abs_diff_sum_new += abs(ival_new[i] - recent_ivals[i])

            for i in range(len(ival_old)):
                abs_diff_sum_old += abs(ival_old[i] - recent_ivals[i])

            if abs_diff_sum_new < abs_diff_sum_old:
                use_new_belief_term = True

            # Truth confidence proportional to how well the interval fits to the previous ones
            if abs_diff_sum_new > 0:
                new_truth.confidence = new_truth.confidence / (1.0 + abs_diff_sum_new)

    return use_new_belief_term

def solution_quality(rate_by_confidence: bool, prob_t, solution, memory, time) -> float:
    """
    评估判断作为问题解决方案的质量。

    参数:
        rate_by_confidence: 是否按置信度评分
        prob_t: 目标或问题
        solution: 要评估的解决方案
        memory: 内存
        time: 时间

    返回:
        判断作为解决方案的质量，如果无法计算则返回0.0
    """
    import logging
    import traceback

    logger = logging.getLogger("LocalRulesHelper")

    try:
        from linars.org.opennars.inference.temporal_rules import TemporalRules

        # 检查solution是否为None
        if solution is None:
            logger.warning("solution_quality: solution为None")
            return 0.0

        # 检查prob_t是否为None
        if prob_t is None or prob_t.sentence is None:
            logger.warning("solution_quality: prob_t或prob_t.sentence为None")
            return 0.0

        problem = prob_t.sentence

        # 检查solution.term是否为None
        if solution.term is None:
            logger.warning("solution_quality: solution.term为None")
            return 0.0

        if ((prob_t.sentence.punctuation != solution.punctuation and solution.term.has_var_query()) or
            not TemporalRules.matching_order_int(problem.get_temporal_order(), solution.get_temporal_order())):
            return 0.0

        # 检查solution.truth是否为None
        # if solution.truth is None:
        #     logger.warning("solution_quality: solution.truth为None")
        #     return 0.0

        truth = solution.truth

        if problem.get_occurrence_time() != solution.get_occurrence_time():
            try:
                projected_truth = solution.projection_truth(problem.get_occurrence_time(), time.time(), memory)
                # 检查projection_truth返回的结果是否为None
                if projected_truth is not None:
                    truth = projected_truth
                else:
                    logger.debug("solution_quality: projection_truth返回None，使用原始真值")
            except Exception as e:
                logger.warning(f"solution_quality: 投射真值时出错: {str(e)}")
                logger.debug(traceback.format_exc())
                # 出错时继续使用原始真值

        # 再次检查truth是否为None
        if truth is None:
            logger.warning("solution_quality: 最终使用的truth为None")
            return 0.0

        try:
            if not rate_by_confidence:
                complexity = solution.term.get_complexity() * memory.narParameters.COMPLEXITY_UNIT
                if complexity <= 0:
                    logger.warning(f"solution_quality: 复杂度异常: {complexity}")
                    complexity = 1.0  # 防止除以0
                return float(truth.get_expectation() / math.sqrt(math.sqrt(math.sqrt(complexity))))
            else:
                return float(truth.get_confidence())
        except Exception as e:
            logger.error(f"solution_quality: 计算质量时出错: {str(e)}")
            logger.debug(traceback.format_exc())
            return 0.0
    except Exception as e:
        logger.error(f"solution_quality: 未处理的异常: {str(e)}")
        logger.debug(traceback.format_exc())
        return 0.0
