"""
回应触发器 - 检测到回应目标时自动触发搜索操作
"""
import threading
import time
from typing import Optional

class RespondTrigger:
    """
    回应触发器类，用于监控和触发回应操作
    """
    
    def __init__(self):
        """初始化回应触发器"""
        self.running = False
        self.thread = None
        self.last_trigger_time = 0
        self.trigger_interval = 5  # 5秒间隔，避免重复触发
        
    def start(self):
        """启动回应触发器"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.thread.start()
            print("回应触发器已启动")
    
    def stop(self):
        """停止回应触发器"""
        self.running = False
        if self.thread:
            self.thread.join()
        print("回应触发器已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                # 检查是否应该触发回应
                if self._should_trigger_respond():
                    self._trigger_respond()
                    self.last_trigger_time = time.time()
                
                time.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                print(f"回应触发器监控循环中发生错误: {e}")
                time.sleep(5)  # 出错时等待5秒再继续
    
    def _should_trigger_respond(self) -> bool:
        """
        检查是否应该触发回应操作
        
        Returns:
            bool: 如果应该触发则返回True
        """
        current_time = time.time()
        
        # 避免频繁触发
        if current_time - self.last_trigger_time < self.trigger_interval:
            return False
        
        try:
            # 检查是否有回应相关的目标或任务
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            
            if AgentStarter.nar is None:
                return False
            
            # 这里可以添加更复杂的逻辑来检测是否需要回应
            # 目前禁用自动触发，避免干扰运算测试
            return False
            
        except Exception as e:
            print(f"检查回应触发条件时发生错误: {e}")
            return False
    
    def _trigger_respond(self):
        """触发回应操作"""
        try:
            print("\n=== 触发回应操作 ===")
            
            # 导入回应动作处理器
            from linars.org.opennars.operator.misc.respond_action import handle_respond_action
            
            # 执行回应操作
            result = handle_respond_action("回应")
            
            print(f"回应操作完成，结果: {result}")
            print("=== 回应操作结束 ===\n")
            
        except Exception as e:
            print(f"触发回应操作时发生错误: {e}")
            import traceback
            traceback.print_exc()

# 全局回应触发器实例
_respond_trigger = None

def start_respond_trigger():
    """启动全局回应触发器"""
    global _respond_trigger
    if _respond_trigger is None:
        _respond_trigger = RespondTrigger()
    _respond_trigger.start()

def stop_respond_trigger():
    """停止全局回应触发器"""
    global _respond_trigger
    if _respond_trigger is not None:
        _respond_trigger.stop()

def get_respond_trigger() -> Optional[RespondTrigger]:
    """获取全局回应触发器实例"""
    return _respond_trigger
