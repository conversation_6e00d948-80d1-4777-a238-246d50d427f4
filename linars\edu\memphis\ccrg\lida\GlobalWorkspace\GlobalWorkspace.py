# LIDA Cognitive Framework
"""
Interface for the Global Workspace module which contains Coalition objects
and implements a global conscious broadcast. It receives Coalition
objects generated by AttentionCodelet objects from Workspace.
Different BroadcastTrigger tasks can be registered dynamically. When
a TriggerTask fires, all registered BroadcastListener modules
receive the content of the winning Coalition. Modules that receive
the broadcast must register themselves to this module and implement the
BroadcastListener interface.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener import BroadcastListener
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.BroadcastTrigger import BroadcastTrigger
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.TriggerListener import TriggerListener
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.RefractoryPeriod import RefractoryPeriod

class GlobalWorkspace(FrameworkModule, TriggerListener, RefractoryPeriod):
    """
    Interface for the Global Workspace module which contains Coalition objects
    and implements a global conscious broadcast. It receives Coalition
    objects generated by AttentionCodelet objects from Workspace.
    Different BroadcastTrigger tasks can be registered dynamically. When
    a TriggerTask fires, all registered BroadcastListener modules
    receive the content of the winning Coalition. Modules that receive
    the broadcast must register themselves to this module and implement the
    BroadcastListener interface.
    """
    
    @abstractmethod
    def add_coalition(self, coalition: Coalition) -> bool:
        """
        Adds specified Coalition.
        
        Args:
            coalition: The Coalition to be added to the GlobalWorkspace
            
        Returns:
            True if coalition was added, False otherwise
        """
        pass
    
    @abstractmethod
    def add_broadcast_trigger(self, trigger: BroadcastTrigger) -> None:
        """
        Adds and registers specified BroadcastTrigger.
        
        Args:
            trigger: A BroadcastTrigger which can initiate a broadcast
        """
        pass
    
    @abstractmethod
    def add_broadcast_listener(self, listener: BroadcastListener) -> None:
        """
        Adds and registers a BroadcastListener. Each registered
        BroadcastListener receives each conscious broadcast.
        
        Args:
            listener: The BroadcastListener that will be registered
        """
        pass
    
    @abstractmethod
    def get_coalition_removal_threshold(self) -> float:
        """
        Gets the coalition removal threshold.
        
        Returns:
            The coalition removal threshold
        """
        pass
    
    @abstractmethod
    def set_coalition_removal_threshold(self, threshold: float) -> None:
        """
        Sets the coalition removal threshold.
        
        Args:
            threshold: The coalition removal threshold to set
        """
        pass
    
    @abstractmethod
    def get_broadcast_sent_count(self) -> int:
        """
        Gets the number of broadcasts sent.
        
        Returns:
            The number of broadcasts sent
        """
        pass
