<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
	xmlns:layout="http://www.ultraq.net.nz/web/thymeleaf/layout">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>首页010</title>
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="viewport" content="width=device-width, initial-scale=1" />

<style type="text/css">
        [v-cloak] {
        	/*display: none !important;*/
        }
        .pl-20{
            padding-left:21px
        }
        text{
            cursor:pointer;
            max-width: 25px;
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: middle;
        }
        circle{
            cursor:pointer;
        }
        #graphcontainerdiv{
            background:#fff;
        }
        .el-color-picker__panel{
            left: 812px !important;
        }
        .wange-toolbar{
            border: 1px solid #ccc;
        }
        .wangeditor-form{
            border: 1px solid #ccc;
            height: 350px;
            min-height: 340px
        }
        .mind-fj-box {
            display: inline-block;
            width: 290px;
            padding: 5px;
            border: 1px solid #e6e6e6;
            box-shadow: 0 0 8px rgba(206,205,201,.38);
        }
        .mind-fj-p {
            color: #666;
            line-height: 24px;
            padding: 5px;
            background: rgba(255,255,255,.85);
        }
        .mind-carousel+.mind-fj-p .el-scrollbar__wrap {
            height: auto;
            max-height: 220px;
            min-height: 0;
        }
        .carous-img {
            height: 100%;
            background: rgba(0,0,0,.10);
            line-height: 197px;
            text-align: center;
        }
        .carous-img img {
            max-width: 100%;
            max-height: 100%;
            line-height: 197px;
            vertical-align: middle;
        }
        .circle_opreate{
            display: none;
        }
        .node_detail {
            top: 20px;
            position: absolute;
            width: 350px;
            /*line-height: 35px;*/
            -webkit-border-radius: 10px;
            -moz-border-radius: 10px;
            border-radius: 10px;
            font-size: 12px;
            padding-bottom: 10px;
            background: rgba(198, 226, 255,0.2);
            display:none;
        }
        .node_pd{
            padding:4px;
            font-size: 13px;
            font-family: -webkit-body;
            font-weight: 600;
        }
        .operatetips{
            position: absolute;
            right: 10px;
            float: right;
            top: 0;
            width: 220px;
            padding: 30px;
            border: 2px #EE7942 solid;
            border-radius: 4px;
        }

        .el-dialog__body{
            /*padding: 0;*/
        }

        .jsoncontainer{
            position: absolute;
            right: 0;
            float: right;
            top: 0;
            /*width: 60%;*/
            height: 100%;
            padding: 20px;
            border: 2px #EE7942 solid;
            border-radius: 4px;
            background: #fff;
        }
        .cypher_toolbar{
            line-height: 65px;
            height: 130px;
            /*padding: 0 22px 0 300px;*/
            padding: 30px 22px 0 100px;
            border-bottom: 1px solid #ededed;
        }
    </style>

<script type="text/javascript">
	var contextRoot = "{{ url_for('static', filename='') }}";
</script>
<!-- import css -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/index0.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/x-index.css') }}">


<script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/iconfont.js') }}"></script>
<script src="{{ url_for('static', filename='js/vue.js') }}"></script>
<script src="{{ url_for('static', filename='js/index.js') }}"></script>
</head>

<body>
	<div>
		<!-- <div class="index"> -->
		<!-- 页头 -->
        <div id='appheader' v-cloak>{% include 'share/header.html' %}</div>
        <!-- 正文内容 -->
        <div id='app' v-cloak=''>
            {% block content %} {% endblock %}
        </div>
		<!-- 页脚 -->
		<!-- <div>{% include 'share/footer.html' %}</div> -->
		<!-- </div> -->
	</div>
	<script type="text/javascript">
	 // var appheader =  new Vue({
		//     el: '#appheader',
		//     data:{
		//     	currentUrl: window.location.pathname, //当前路由
		//     	navList:[
		//     		{
		//     			title:'首页',icon:'glyphicon glyphicon-cog',linkUrl:'http://www.miaoleyan.com',active:false,childrens:[]
		//     		},
		//     		{
		//     			title:'分享',icon:'glyphicon glyphicon-th-list',linkUrl:'javascript:void(0);',active:false,childrens:[
		//     				{title:'杨青博客',icon:'',linkUrl:'http://www.yangqq.com/',active:false,childrens:[]},
		//     				{title:'程序猿DD',icon:'',linkUrl:'http://blog.didispace.com/',active:false,childrens:[]},
		//     				{title:'hAdmin',icon:'',linkUrl:'http://demo.mycodes.net/houtai/hAdmin',active:false,childrens:[]},
		//     			]
		//     		}
		//     	],
		//     	isOpen:false,
		//     	search_active:false,
		//     	searchkeyword:'',
		//       },
		//     methods: {
		//     	selectStyle(nav){
		//     		var _this=this;
		//             this.$nextTick(function () {
		//             	_this.navList.forEach(function (item) {
		//             		item.active=false;
		//               });
		//             	nav.active=true;
		//             });
		//     	},
		//     	outStyle(nav) {
		//     		nav.active=false;
	 //            },
	 //            changeIcon(){
	 //        	  this.isOpen=!this.isOpen;
	 //            },
	 //            searchActive() {
	 //            	 this.search_active=!this.search_active;
	 //            },
	 //            clickNav(nav){
		//           nav.active=!nav.active;
		//     	}
	 //        }
		//  })

     </script>
</body>
<!-- JS内容 -->
<!--<div>-->
    {% block jscontent %}{% endblock %}
<!--</div>-->
</html>