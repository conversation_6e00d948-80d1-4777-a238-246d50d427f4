"""
Excel Utility

This module provides utility functions for working with Excel files.
"""
import pandas as pd
import logging

class ExcelUtil:
    """Excel Utility class for working with Excel files."""
    
    logger = logging.getLogger(__name__)
    
    @staticmethod
    def is_excel2007(filename):
        """
        Check if a file is an Excel 2007 file.
        
        Args:
            filename: The name of the file
            
        Returns:
            True if the file is an Excel 2007 file, False otherwise
        """
        return filename.endswith('.xlsx')
    
    @staticmethod
    def read_excel_file(file):
        """
        Read an Excel file.
        
        Args:
            file: The file to read
            
        Returns:
            A list of lists containing the Excel data
        """
        data = []
        try:
            # 使用pandas读取Excel文件
            df = pd.read_excel(file)
            
            # 将DataFrame转换为列表
            data = df.values.tolist()
            
            # 添加列名作为第一行
            data.insert(0, df.columns.tolist())
        except Exception as e:
            ExcelUtil.logger.error(f"Error reading Excel file: {e}")
        
        return data
    
    @staticmethod
    def create_excel_file(data, save_path, filename):
        """
        Create an Excel file.
        
        Args:
            data: The data to write
            save_path: The path to save the file
            filename: The name of the file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            import os
            
            # 确保目录存在
            if not os.path.exists(save_path):
                os.makedirs(save_path)
            
            # 构建完整的文件路径
            file_path = os.path.join(save_path, filename)
            
            # 假设第一行是列名
            headers = data[0]
            values = data[1:]
            
            # 创建DataFrame
            df = pd.DataFrame(values, columns=headers)
            
            # 写入Excel文件
            df.to_excel(file_path, index=False)
            
            return True
        except Exception as e:
            ExcelUtil.logger.error(f"Error creating Excel file: {e}")
            return False
