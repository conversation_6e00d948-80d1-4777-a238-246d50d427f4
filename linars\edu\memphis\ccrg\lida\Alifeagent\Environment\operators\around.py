"""
Around operator implementation.

This module provides the implementation of the around operator for the ALife agent.
"""
from typing import List, Optional, TYPE_CHECKING

from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.entity.task import Task
from linars.org.opennars.interfaces.timable import Timable
from linars.org.opennars.operator.operation import Operation
from linars.org.opennars.operator.operator import Operator

if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.linars.memory import Memory

class Around(Operator):
    """
    Around operator implementation.

    This class implements the around operator, which makes the agent turn around.
    """

    def __init__(self):
        """Initialize the around operator."""
        super().__init__("^around")

    def execute(self, operation: Operation, args: List[Term], memory, time: Timable) -> Optional[List[Task]]:
        """
        Execute the around operator.

        Args:
            operation: The operation to execute
            args: Arguments for the operation
            memory: The memory in which the operation is executed
            time: The time

        Returns:
            None, as this operator doesn't produce tasks
        """
        # Execute the around operation in the environment
        memory.emit_operator("around")

        return None
