"""
实例属性关系(InstanceProperty)是NARS中用于输入/输出的特殊关系，内部会转换为继承关系

实例属性关系定义:
- 语法结构: {A -] B}
- 表示A具有属性B
- 内部处理时会转换为{A} --> [B]的继承关系
"""
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.language.inheritance import Inheritance
from linars.org.opennars.language.set_ext import SetExt
from linars.org.opennars.language.set_int import SetInt

class InstanceProperty:
    """
    实例属性关系类，处理NARS中的实例属性关系

    主要功能:
    1. 表示实例属性关系(外部语法)
    2. 内部处理时转换为继承关系
    3. 主要用于输入/输出转换
    """

    @staticmethod
    def make(subject, predicate):
        """
        创建实例属性关系(由推理规则调用)

        转换规则:
        {A -] B} 转换为 {{A} --> [B]}

        参数:
            subject: 主语项(实例)
            predicate: 谓语项(属性)

        返回:
            Inheritance: 转换后的继承关系，如果发生异常则返回None

        注意:
        1. 实例属性关系仅在输入/输出时使用
        2. 内部处理时自动转换为继承关系
        3. 属性会被转换为内涵集合[B]
        """
        try:
            # 参数验证
            if subject is None:
                print(f"警告: InstanceProperty.make 收到了空的主语")
                return None

            if predicate is None:
                print(f"警告: InstanceProperty.make 收到了空的谓语")
                return None

            # 创建外延集合
            try:
                set_ext = SetExt([subject])
            except Exception as e:
                print(f"错误: 在创建外延集合时发生异常: {e}")
                return None

            # 创建内涵集合
            try:
                set_int = SetInt([predicate])
            except Exception as e:
                print(f"错误: 在创建内涵集合时发生异常: {e}")
                return None

            # 创建继承关系
            try:
                return Inheritance.make(set_ext, set_int)
            except Exception as e:
                print(f"错误: 在创建继承关系时发生异常: {e}")
                return None

        except Exception as e:
            print(f"错误: InstanceProperty.make方法发生未处理的异常: {e}")
            return None
