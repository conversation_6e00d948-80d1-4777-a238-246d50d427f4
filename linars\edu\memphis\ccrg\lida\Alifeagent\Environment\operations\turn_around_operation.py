"""
转身操作实现

本模块提供ALife环境中转身操作的实现
"""
from typing import List, Optional, Any

from linars.edu.memphis.ccrg.alife.elements.alife_object import ALifeObject
from linars.edu.memphis.ccrg.alife.opreations.world_operation import WorldOperation

class TurnAroundOperation(WorldOperation):
    """
    转身操作实现

    该类实现ALife环境中的转身操作
    """

    def __init__(self):
        """初始化转身操作"""
        super().__init__("around")

    def execute(self, actor: ALifeObject, target: Optional[ALifeObject], *params) -> bool:
        """
        执行转身操作

        参数:
            actor: 执行转身行为的对象
            target: 转身目标(未使用)
            params: 附加参数

        返回:
            操作成功返回True，否则返回False
        """
        # Get the actor's current direction
        direction = actor.get_attribute("direction")
        if direction is None:
            return False

        # Turn around (180 degrees)
        new_direction = None
        if direction == 'N':
            new_direction = 'S'
        elif direction == 'S':
            new_direction = 'N'
        elif direction == 'E':
            new_direction = 'W'
        elif direction == 'W':
            new_direction = 'E'

        # Set the new direction
        actor.set_attribute("direction", new_direction)

        return True
