# LIDA Cognitive Framework
"""
This class implements module of WorkspaceBuffer. WorkspaceBuffer is a submodule of workspace and 
it contains nodeStructures. Also this class maintains activation lower bound of its nodeStructures.
"""

import logging
from typing import Dict, Any, Optional

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.UnmodifiableNodeStructureImpl import UnmodifiableNodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Nlanguage.SubGraphSet import SubGraphSet
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WorkspaceBuffer import WorkspaceBuffer
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceContent import WorkspaceContent


# Try to import SubGraphSet if available
# try:
#     from linars.edu.memphis.ccrg.lida.Nlanguage.SubGraphSet import SubGraphSet
#     SUBGRAPHSET_AVAILABLE = True
# except ImportError:
#     SUBGRAPHSET_AVAILABLE = False

class WSBufferImpl_graph(FrameworkModuleImpl, WorkspaceBuffer):
    """
    This class implements module of WorkspaceBuffer. WorkspaceBuffer is a submodule of workspace and 
    it contains nodeStructures. Also this class maintains activation lower bound of its nodeStructures.
    """
    
    def __init__(self):
        """
        Initialize a WSBufferImpl_graph.
        """
        super().__init__()
        # Create buffer based on availability of SubGraphSet class
        # if SUBGRAPHSET_AVAILABLE:
        #     self.buffer = SubGraphSet()
        # else:
        #     self.buffer = NodeStructureImpl()
        self.buffer = SubGraphSet()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def add_buffer_content(self, content: WorkspaceContent) -> None:
        """
        Adds specified content to this workspace buffer.
        Note that this method merges the specified content into the buffer.
        
        Args:
            content: The WorkspaceContent to add
        """
        self.buffer.merge_with(content)
    
    def get_buffer_content(self, params: Optional[Dict[str, Any]] = None) -> WorkspaceContent:
        """
        Gets buffer content based on specified parameters.
        
        Args:
            params: Optional parameters to specify what content is returned
            
        Returns:
            The WorkspaceContent
        """
        return self.buffer
    
    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.
        
        Args:
            ticks: The number of ticks to decay by
        """
        self.logger.debug(f"Decaying buffer at tick {TaskManager.get_current_tick()}")
        self.buffer.decay_node_structure(ticks)
    
    def get_module_content(self, *params: Any) -> WorkspaceContent:
        """
        Get the content of this module.
        
        Args:
            params: Parameters specifying what content to return
            
        Returns:
            The content of this module
        """
        return UnmodifiableNodeStructureImpl(self.buffer)
