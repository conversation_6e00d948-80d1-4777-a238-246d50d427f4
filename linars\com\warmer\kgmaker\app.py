"""
Knowledge Graph Maker Application

This module provides the main entry point for the Knowledge Graph Maker application.
"""
import os
import sys
from flask import Flask, render_template, request, jsonify, send_from_directory
from neo4j import GraphDatabase
from py2neo import Graph

from linars.com.warmer.kgmaker.config.web_app_config import WebAppConfig
from linars.com.warmer.kgmaker.controllers import (
    kg_blueprint, file_blueprint, init_controller,
    webim_blueprint, nlp_blueprint, question_blueprint,
    xr0_blueprint, xr_blueprint
)
from linars.com.warmer.kgmaker.utils.neo4j_util import Neo4jUtil

app = Flask(__name__,
            template_folder='templates',
            static_folder='static',
            static_url_path='/')

# 加载配置
app.config.from_object(WebAppConfig)

# 注册蓝图
app.register_blueprint(kg_blueprint)
app.register_blueprint(file_blueprint)
app.register_blueprint(webim_blueprint)
app.register_blueprint(nlp_blueprint)
app.register_blueprint(question_blueprint)
app.register_blueprint(xr0_blueprint)
app.register_blueprint(xr_blueprint)

# 初始化Neo4j连接
neo4j_uri = app.config.get('NEO4J_URI', 'bolt://localhost:7687')
neo4j_user = app.config.get('NEO4J_USER', 'neo4j')
neo4j_password = app.config.get('NEO4J_PASSWORD', '12345678')

# 创建全局Neo4j连接
graph_db = None
neo4j_util = None

try:
    app.logger.info(f"Connecting to Neo4j at {neo4j_uri} with user {neo4j_user}")
    graph_db = Graph(neo4j_uri, auth=(neo4j_user, neo4j_password))
    neo4j_util = Neo4jUtil(graph_db)
    app.logger.info("Successfully connected to Neo4j")

    # 获取标签列表
    labels = neo4j_util.get_all_labels()
    app.logger.debug(f"Available labels in Neo4j: {labels}")

    # 获取关系类型列表
    relationship_types = neo4j_util.get_all_relationship_types()
    app.logger.debug(f"Available relationship types in Neo4j: {relationship_types}")
except Exception as e:
    app.logger.error(f"Failed to connect to Neo4j: {e}")

# 导入服务层
from linars.com.warmer.kgmaker.service.impl.kg_graph_service_impl import KGGraphServiceImpl
from linars.com.warmer.kgmaker.dal.impl.kg_repository_impl import KGRepositoryImpl

# 创建仓库实例
kg_repository = KGRepositoryImpl(graph_db, neo4j_util) if graph_db else None

# 创建服务实例
kg_graph_service = KGGraphServiceImpl(kg_repository) if kg_repository else None

# 初始化控制器
init_controller(neo4j_util, kg_graph_service, WebAppConfig)

# 初始化其他控制器
from linars.com.warmer.kgmaker.controllers.im_control_detector import init_controller as init_im_controller
from linars.com.warmer.kgmaker.controllers.xr_controller import init_controller as init_xr_controller

init_im_controller(neo4j_util, kg_graph_service, WebAppConfig)
init_xr_controller(neo4j_util, kg_graph_service, WebAppConfig)

# 注册关闭钩子
def shutdown_hook():
    """关闭Neo4j连接的钩子函数"""
    if graph_db:
        app.logger.info("Shutting down Neo4j connection...")

# 注册关闭钩子
import atexit
atexit.register(shutdown_hook)

@app.route('/')
def home():
    """首页路由"""
    # 传递一个包含pageModel属性的对象给模板
    # 这里的pageModel将被传递给Vue.js作为初始数据
    return render_template('kg/home.html', pageModel={'nodeList': [], 'pageIndex': 1, 'totalPage': 1})

@app.route('/2d')
def index_2d():
    """2D视图路由"""
    # 传递一个包含pageModel属性的对象给模板
    return render_template('kg/home.html', pageModel={'nodeList': [], 'pageIndex': 1, 'totalPage': 1})

@app.route('/3d')
def index_3d():
    """3D视图路由"""
    # 传递一个包含pageModel属性的对象给模板
    return render_template('kg/index.html', pageModel={'nodeList': [], 'pageIndex': 1, 'totalPage': 1})

if __name__ == '__main__':
    # 设置为非无头模式
    os.environ['JAVA_AWT_HEADLESS'] = 'false'

    # 启动应用
    app.run(debug=True, host='0.0.0.0', port=5000)
