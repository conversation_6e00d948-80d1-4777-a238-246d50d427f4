"""
CompoundTerm is a Term with internal structure
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, Iterator, Collection
import copy
import array
import logging
import random
from collections import defaultdict

from linars.edu.memphis.ccrg.linars.term import Term, NativeOperator

# 定义RuntimeException类
class RuntimeException(Exception):
    """Java风格的RuntimeException"""
    pass

# 尝试导入NARS相关类
try:
    from linars.org.opennars.inference.temporal_rules import TemporalRules
    from linars.org.opennars.language.equivalence import Equivalence
    from linars.org.opennars.language.implication import Implication
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False
    # 创建占位符
    class TemporalRules:
        ORDER_FORWARD = 1
        ORDER_CONCURRENT = 0
        ORDER_BACKWARD = -1

class CompoundTerm(Term):
    """
    A CompoundTerm is a Term with internal structure
    """

    class ConvRectangle:
        """
        卷积矩形类，用于空间项的索引和位置信息
        """
        def __init__(self):
            """
            初始化卷积矩形
            """
            self.index_variable = None  # 索引变量
            self.term_indices = None    # 术语索引数组 [sizeX, sizeY, posX, posY, minSizeX, minSizeY]

    def __init__(self, name: str = None, terms: List[Term] = None):
        """
        Constructor with a given name
        Args:
            name: A string as the name of the Term
            terms: List of component terms
        """
        # print("CompoundTerm---__init__----")
        super().__init__(name)

        # 基本属性
        self.term: List[Term] = [] if terms is None else terms
        self.complexity = 1
        self._commutative = False  # 使用下划线前缀避免与方法名冲突
        self.is_constant_term = True
        self.has_var_dep_term = False
        self.has_var_indep_term = False
        self.has_var_query_term = False
        self.has_interval_term = False
        self.hash_code = 0

        # 其他属性
        self.contained_temporal_relations_count = -1
        self.normalized = False
        self.logger = logging.getLogger(self.__class__.__name__)

        # 如果提供了组件项，初始化
        if terms is not None and len(terms) > 0:
            self.init(terms)

    def operator(self) -> str:
        """
        Get the operator type

        Returns:
            str: The operator type
        """
        return NativeOperator.COMPOUND

    def to_compound_term(self) -> 'CompoundTerm':
        """
        尝试转换为复合项

        返回:
            CompoundTerm: 当前复合项
        """
        return self

    def recurse_terms(self, visitor, parent=None):
        """
        递归访问项

        参数:
            visitor: 访问者函数
            parent: 父项
        """
        visitor(self, parent)
        if hasattr(self, 'term'):
            for t in self.term:
                t.recurse_terms(visitor, self)

    def recurse_subterms_containing_variables(self, visitor, parent=None):
        """
        递归访问包含变量的子项

        参数:
            visitor: 访问者函数
            parent: 父项
        """
        if not self.has_var():
            return

        visitor(self, parent)
        if hasattr(self, 'term'):
            for t in self.term:
                t.recurse_subterms_containing_variables(visitor, self)

    def subject_or_predicate_is_independent_var(self) -> bool:
        """
        检查主语或谓语是否是独立变量

        返回:
            bool: 如果主语或谓语是独立变量则为True
        """
        if hasattr(self, 'get_subject') and hasattr(self, 'get_predicate'):
            # 检查主语
            subject = self.get_subject()
            if hasattr(subject, 'has_var_indep') and callable(subject.has_var_indep) and subject.has_var_indep():
                return True

            # 检查谓语
            predicate = self.get_predicate()
            if hasattr(predicate, 'has_var_indep') and callable(predicate.has_var_indep) and predicate.has_var_indep():
                return True

        return False

    def get_complexity(self) -> int:
        """
        Get the complexity of the term

        Returns:
            int: The complexity
        """
        # 返回复杂度值
        return self.complexity

    def is_constant(self) -> bool:
        """
        Check if the term is constant

        Returns:
            bool: True if constant
        """
        # 检查项是否为常量
        return self.is_constant_term

    def has_var(self) -> bool:
        """
        Check if the term has variables

        Returns:
            bool: True if it has variables
        """
        # 检查项是否有变量
        return not self.is_constant_term

    def has_var_dep(self) -> bool:
        """
        Check if the term has dependent variables

        Returns:
            bool: True if it has dependent variables
        """
        # 检查项是否有依赖变量
        return self.has_var_dep_term

    def has_var_indep(self) -> bool:
        """
        Check if the term has independent variables

        Returns:
            bool: True if it has independent variables
        """
        # 检查项是否有独立变量
        return self.has_var_indep_term

    def has_var_query(self) -> bool:
        """
        Check if the term has query variables

        Returns:
            bool: True if it has query variables
        """
        # 检查项是否有查询变量
        return self.has_var_query_term

    def has_interval(self) -> bool:
        """
        Check if the term has intervals

        Returns:
            bool: True if it has intervals
        """
        # 检查项是否有间隔
        return self.has_interval_term

    def contains_term(self, target: Term) -> bool:
        """
        Check if the term contains another term

        Args:
            target: The term to check for

        Returns:
            bool: True if it contains the target
        """
        if self == target:
            return True

        for t in self.term:
            if t == target:
                return True

        return False

    def to_task(self, punctuation):
        """
        转换为任务
        Args:
            punctuation: 标点符号，表示任务类型（判断、目标、问题等）
        Returns:
            Task: 创建的任务
        """
        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        from linars.org.opennars.entity.sentence import Sentence
        from linars.org.opennars.entity.truth_value import TruthValue
        from linars.org.opennars.entity.stamp import Stamp
        from linars.org.opennars.entity.budget_value import BudgetValue
        from linars.org.opennars.entity.task import Task
        try:
            # 创建真值、印记和句子
            truth = TruthValue(1.0, 0.9, False, AgentStarter.nar.narParameters)
            stamp = Stamp(AgentStarter.nar, AgentStarter.nar.memory)
            print(f"DEBUG: Created stamp from CompoundTerm---: {stamp}")
            sentence = Sentence(self.term, punctuation, truth, stamp)
        except Exception as e:
            print("ERROR: Failed to create Sentence from CompoundTerm: ", e)
            # 打印跟踪
            import traceback
            traceback.print_exc()
            return None
        try:
            # 创建预算值和任务
            budget = BudgetValue(0.8, 0.5, 1.0, AgentStarter.nar.narParameters)
            from linars.org.opennars.entity.task import EnumType
            task = Task(sentence, budget, EnumType.INPUT)
            print(f"DEBUG: Created task from CompoundTerm----: {task.sentence.__str__()}")
            return task
        except Exception as e:
            print("ERROR: Failed to create Task from CompoundTerm: ", e)

    def contains_term_recursively(self, target: Term) -> bool:
        """
        Check if the term contains another term recursively

        Args:
            target: The term to check for

        Returns:
            bool: True if it contains the target
        """
        if target is None:
            return False

        if self == target:
            return True

        for t in self.term:
            if t.contains_term_recursively(target):
                return True

        return False

    def count_term_recursively(self, term_map=None) -> Dict[Term, int]:
        """
        Count terms recursively

        Args:
            term_map: The map to store counts

        Returns:
            Dict: The term count map
        """
        if term_map is None:
            term_map = {}

        term_map[self] = term_map.get(self, 0) + 1

        for t in self.term:
            t.count_term_recursively(term_map)

        return term_map

    def clone(self, replaced: List[Term] = None) -> 'CompoundTerm':
        """
        克隆当前复合项

        参数:
            replaced: 替换的组件项列表，如果为None则使用原始组件项

        返回:
            CompoundTerm: 克隆的复合项
        """
        try:
            # 如果提供了替换的组件项，使用它们创建新的复合项
            if replaced is not None:
                try:
                    return self.__class__(replaced)
                except Exception as e:
                    self.logger.error(f"使用替换的组件项创建复合项失败: {e}")
                    # 如果创建失败，尝试不使用组件项创建
                    clone = self.__class__()
                    clone.term = replaced
                    clone.set_term_name(clone.make_name())
                    return clone

            # 如果没有提供替换的组件项，创建一个空的复合项并复制属性
            clone = self.__class__()
            clone.set_term_name(self.name())
            clone.complexity = self.complexity
            clone._commutative = self._commutative
            clone.is_constant_term = self.is_constant_term
            clone.has_var_dep_term = self.has_var_dep_term
            clone.has_var_indep_term = self.has_var_indep_term
            clone.has_var_query_term = self.has_var_query_term
            clone.has_interval_term = self.has_interval_term
            clone.contained_temporal_relations_count = self.contained_temporal_relations_count
            clone.normalized = self.normalized

            # 复制变量索引
            if hasattr(self, 'term_indices') and self.term_indices is not None:
                clone.term_indices = self.term_indices.copy() if isinstance(self.term_indices, dict) else self.term_indices

            if hasattr(self, 'index_variable') and self.index_variable is not None:
                clone.index_variable = self.index_variable.copy() if isinstance(self.index_variable, dict) else self.index_variable

            # 克隆组件项
            clone.term = self.term.copy()

            return clone
        except Exception as e:
            self.logger.error(f"克隆复合项失败: {e}")
            # 定义一个无法克隆异常
            class UnableToCloneException(RuntimeException):
                def __init__(self, message):
                    super().__init__(message)

            raise UnableToCloneException(f"无法克隆 {self.__class__.__name__}: {e}")

    def clone_with_terms(self, terms: List[Term]) -> 'CompoundTerm':
        """
        使用新的组件项克隆复合项

        参数:
            terms: 新的组件项列表

        返回:
            CompoundTerm: 克隆的复合项

        注意:
            这是一个抽象方法，子类应该重写它
        """
        try:
            # 默认实现，子类应该重写这个方法
            return self.__class__(terms)
        except Exception as e:
            self.logger.error(f"使用新的组件项克隆复合项失败: {e}")
            return None

    def clone_terms(self, *additional: Term) -> List[Term]:
        """
        克隆组件项列表

        参数:
            *additional: 要添加的额外项

        返回:
            List[Term]: 克隆的组件项列表
        """
        return self.clone_terms_append(self.term, list(additional))

    @staticmethod
    def clone_terms_append(original: List[Term], additional: List[Term]) -> List[Term]:
        """
        克隆项列表并添加额外项

        参数:
            original: 原始项列表
            additional: 要添加的项列表

        返回:
            List[Term]: 克隆并添加后的项列表
        """
        if original is None:
            return None

        L = len(original) + len(additional)
        if L == 0:
            return original

        arr = [None] * L

        i = 0
        j = 0
        src_array = original
        for i in range(L):
            if i == len(original):
                src_array = additional
                j = 0
            arr[i] = src_array[j]
            j += 1

        return arr

    def clone_deep(self) -> 'CompoundTerm':
        """
        深度克隆当前复合项

        返回:
            CompoundTerm: 深度克隆的复合项
        """
        try:
            # 先克隆组件项
            cloned_terms = self.clone_terms_deep()
            if cloned_terms is None:
                return None

            # 使用克隆的组件项创建新的复合项
            c = self.clone(cloned_terms)

            if c is None:
                return None

            if c.__class__ != self.__class__:
                self.logger.debug(f"cloneDeep resulted in different class: {c} from {self}")

            if self.is_normalized():
                c.set_normalized(True)

            if not isinstance(c, CompoundTerm):
                return None

            return c
        except Exception as e:
            self.logger.error(f"Error cloning term: {e}")
            return None

    def clone_terms_deep(self) -> List[Term]:
        """
        深度克隆组件项列表

        返回:
            List[Term]: 深度克隆的组件项列表
        """
        l = [None] * len(self.term)
        for i in range(len(l)):
            l[i] = self.term[i].clone_deep()
            if l[i] is None:
                return None
        return l

    def contained_temporal_relations(self) -> int:
        """
        获取包含的时间关系数

        返回:
            int: 时间关系数
        """
        if self.contained_temporal_relations_count == -1:
            self.contained_temporal_relations_count = 0

            # 检查当前项是否为等价或蕴含关系
            if NARS_AVAILABLE and (isinstance(self, Equivalence) or isinstance(self, Implication)):
                if hasattr(self, 'get_temporal_order'):
                    temporal_order = self.get_temporal_order()
                    if temporal_order in [TemporalRules.ORDER_FORWARD, TemporalRules.ORDER_CONCURRENT, TemporalRules.ORDER_BACKWARD]:
                        self.contained_temporal_relations_count = 1

            # 递归检查组件项
            for t in self.term:
                self.contained_temporal_relations_count += t.contained_temporal_relations()

        return self.contained_temporal_relations_count

    def is_commutative(self) -> bool:
        """
        检查项的顺序是否重要

        可交换的复合项：集合、交集
        可交换的语句：相似性、等价性（除了具有时间顺序的）
        可交换的复合语句：析取、合取（除了具有时间顺序的）

        返回:
            bool: 默认值为False
        """
        # 检查项是否可交换
        # 子类应该重写这个方法
        return self._commutative

    def is_normalized(self) -> bool:
        """
        检查项是否已经标准化

        返回:
            bool: 如果项已经标准化则返回True
        """
        # 检查项是否已经标准化
        return self.normalized

    def set_normalized(self, normalized: bool) -> None:
        """
        设置项的标准化状态

        参数:
            normalized: 新的标准化状态
        """
        self.normalized = normalized

    def make_name(self) -> str:
        """
        从现有字段中生成当前项的名称

        返回:
            str: 项的名称
        """
        op = self.operator()
        if op is None:
            return "null_neo"
        return self.make_compound_name(op, self.term)

    @staticmethod
    def make_compound_name(op: str, args: List[Term]) -> str:
        """
        从给定字段中生成复合项的名称

        参数:
            op: 项的操作符
            args: 项的参数列表

        返回:
            str: 项的名称
        """
        # 计算所需的大小
        size = 2  # 开始和结束符号
        op_string = str(op)
        size += len(op_string)

        # 添加参数的大小
        for t in args:
            tname = t.name()
            if tname is None:
                tname = t.name()
            size += 1 + len(str(tname))  # 参数分隔符和参数名称

        # 构建名称
        result = "(" + op_string
        for t in args:
            result += "," + str(t.name())
        result += ")"

        return result

    def invalidate_name(self):
        """
        使名称无效，强制重新生成
        """
        self.set_term_name(None)
        self.hash_code = 0

    def name(self) -> str:
        """
        获取项的名称

        返回:
            str: 项的名称
        """
        if self.name_internal() is None:
            self.set_term_name(self.make_name())
        return self.name_internal()

    def __hash__(self) -> int:
        """
        Hash code for the term

        Returns:
            int: The hash code
        """
        if self.hash_code == 0:
            self.hash_code = hash(self.name())
        return self.hash_code

    @staticmethod
    def replace_intervals(t: Term) -> Term:
        """
        Replace intervals in a term

        Args:
            t: The term to process

        Returns:
            Term: The processed term
        """
        if not isinstance(t, CompoundTerm):
            return t

        if not t.has_interval():
            return t

        # 在实际实现中，我们需要替换间隔
        # 这里的实现是一个简化版本
        try:
            # 先克隆一份
            t_clone = t.clone_deep()
            if t_clone is None:
                return t

            # 递归替换间隔
            CompoundTerm._replace_intervals_recursive(t_clone)
            return t_clone
        except Exception as e:
            logging.error(f"Error replacing intervals: {e}")
            return t

    @staticmethod
    def _replace_intervals_recursive(comp: 'CompoundTerm') -> None:
        """
        递归替换复合项中的间隔

        参数:
            comp: 要处理的复合项
        """
        if not comp.has_interval():
            return

        # 在实际实现中，我们需要替换间隔为概念间隔
        # 这里的实现是一个简化版本
        for i in range(len(comp.term)):
            t = comp.term[i]
            if hasattr(t, '__class__') and t.__class__.__name__ == "Interval":
                # 在实际实现中，我们需要替换为概念间隔
                # 这里的实现是一个简化版本
                from linars.org.opennars.language.interval import Interval
                comp.term[i] = Interval(1)
            elif isinstance(t, CompoundTerm):
                CompoundTerm._replace_intervals_recursive(t)

    def get_terms(self) -> List[Term]:
        """
        获取组件项列表

        返回:
            List[Term]: 组件项列表
        """
        return self.term

    def clone_variable_terms_deep(self) -> List[Term]:
        """
        深度克隆变量项

        返回:
            List[Term]: 深度克隆的变量项列表
        """
        l = [None] * len(self.term)
        for i in range(len(l)):
            t = self.term[i]
            if t.has_var():
                if isinstance(t, CompoundTerm):
                    t = t.clone_deep_variables()
                else:  # 它是一个变量
                    t = t.clone()
            l[i] = t
        return l

    def clone_deep_variables(self) -> 'CompoundTerm':
        """
        深度克隆变量

        返回:
            CompoundTerm: 深度克隆变量后的复合项
        """
        try:
            cloned_terms = self.clone_variable_terms_deep()
            if cloned_terms is None:
                return None

            # 使用克隆的组件项创建新的复合项
            c = self.clone(cloned_terms)

            if c is None:
                return None

            if c.__class__ != self.__class__:
                self.logger.debug(f"cloneDeepVariables resulted in different class: {c} from {self}")

            c.set_normalized(self.is_normalized())
            return c
        except Exception as e:
            self.logger.error(f"Error cloning term variables: {e}")
            return None

    def get_term_names(self, term_names: Set[str] = None) -> Set[str]:
        """
        获取所有组件项的名称

        参数:
            term_names: 已有的名称集合，如果为None则创建新的集合

        返回:
            Set[str]: 组件项名称集合
        """
        if term_names is None:
            term_names = set()

        for t in self.term:
            if isinstance(t, CompoundTerm):
                t.get_term_names(term_names)
            else:
                term_names.add(str(t.name()))

        return term_names

    def get_term_names_with_index(self, term_names: Dict[str, List[int]] = None, level: int = 0, index: int = 0) -> Dict[str, List[int]]:
        """
        获取所有组件项的名称及其层级和索引

        参数:
            term_names: 已有的名称字典，如果为None则创建新的字典
            level: 当前层级
            index: 当前索引

        返回:
            Dict[str, List[int]]: 组件项名称及其层级和索引的字典
        """
        if term_names is None:
            term_names = {}

        current_index = index
        for t in self.term:
            if isinstance(t, CompoundTerm):
                t.get_term_names_with_index(term_names, level + 1, current_index)
            else:
                name = str(t.name())
                if name not in term_names:
                    term_names[name] = []
                term_names[name].extend([level, current_index])
            current_index += 1

        return term_names

    def get_contained_terms(self) -> Set[Term]:
        """
        递归获取所有包含的项

        返回:
            Set[Term]: 包含的项集合
        """
        s = set()
        for t in self.term:
            s.add(t)
            if isinstance(t, CompoundTerm):
                s.update(t.get_contained_terms())
        return s

    def size(self) -> int:
        """
        获取组件项数量

        返回:
            int: 组件项数量
        """
        return len(self.term)

    def as_term_list(self) -> List[Term]:
        """
        将组件项转换为列表

        返回:
            List[Term]: 组件项列表
        """
        l = []
        self.add_terms_to(l)
        return l

    def add_terms_to(self, c: Collection[Term]) -> None:
        """
        将组件项添加到集合中

        参数:
            c: 要添加到的集合
        """
        for t in self.term:
            c.append(t)

    @staticmethod
    def add_components_recursively(t: Term, components: Set[Term] = None) -> Set[Term]:
        """
        递归地将项及其所有组件添加到集合中

        参数:
            t: 要处理的项
            components: 已有的组件集合，如果为None则创建新的集合

        返回:
            Set[Term]: 包含项及其所有组件的集合
        """
        if components is None:
            components = set()

        components.add(t)

        if isinstance(t, CompoundTerm):
            for component in t.term:
                CompoundTerm.add_components_recursively(component, components)

        return components

    def prepare_component_links(self) -> List['TermLink']:
        """
        Prepare component links for the term

        Returns:
            List: The term links
        """
        # This is a placeholder - actual implementation would depend on term link handling
        from linars.org.opennars.entity.term_link import TermLink

        links = []
        for i, component in enumerate(self.term):
            # Create a term link for each component
            link = TermLink(component, TermLink.COMPONENT, i)
            links.append(link)

            # If the component is a compound term, add compound links
            if isinstance(component, CompoundTerm):
                for compound_link in component.prepare_component_links():
                    links.append(compound_link)

        return links


    @staticmethod
    def update_conv_rectangle(terms: List[Term]) -> 'CompoundTerm.ConvRectangle':
        """
        更新卷积矩形，根据索引处理复合词项

        Args:
            terms: 要处理的术语列表

        Returns:
            ConvRectangle: 更新后的卷积矩形
        """
        index_last_var = None
        min_x = float('inf')
        min_y = float('inf')
        max_x = 0
        max_y = 0
        mins_x = float('inf')
        mins_y = float('inf')
        has_term_indices = False
        calculate_term_indices = True

        for t in terms:
            if t is not None and hasattr(t, 'term_indices') and t.term_indices is not None:
                if not calculate_term_indices or \
                   (hasattr(t, 'index_variable') and t.index_variable is not None and \
                    index_last_var is not None and t.index_variable != index_last_var):
                    calculate_term_indices = False
                    has_term_indices = False
                    # 不同的"通道"，不计算词项索引
                    continue

                has_term_indices = True
                if index_last_var is None and hasattr(t, 'index_variable'):
                    index_last_var = t.index_variable

                # 更新最小/最大坐标
                if hasattr(t, 'term_indices') and t.term_indices is not None:
                    if len(t.term_indices) >= 6:
                        size_x = t.term_indices[0]
                        size_y = t.term_indices[1]
                        pos_x = t.term_indices[2]
                        pos_y = t.term_indices[3]
                        min_size_x = t.term_indices[4]
                        min_size_y = t.term_indices[5]

                        min_x = min(min_x, pos_x)
                        min_y = min(min_y, pos_y)
                        max_x = max(max_x, pos_x + size_x)
                        max_y = max(max_y, pos_y + size_y)
                        mins_x = min(mins_x, min_size_x)
                        mins_y = min(mins_y, min_size_y)

        rect = CompoundTerm.ConvRectangle()
        if has_term_indices:
            rect.term_indices = [0] * 6
            rect.term_indices[0] = max_x - min_x
            rect.term_indices[1] = max_y - min_y
            rect.term_indices[2] = min_x
            rect.term_indices[3] = min_y
            rect.term_indices[4] = mins_x
            rect.term_indices[5] = mins_y
            rect.index_variable = index_last_var

        return rect

    def init(self, terms: List[Term]):
        """
        初始化复合项，设置复杂度和变量标志

        Args:
            terms: 组成复合项的术语列表
        """
        if isinstance(terms, Tuple):
            terms = terms[0]
        elif isinstance(terms, list) and len(terms) == 1:
            # 如果terms是列表且只有一个元素，且该元素也是列表，那么将terms赋值为该元素
            # 包了两层
            if isinstance(terms[0], list):
                terms = terms[0]
        self.term = terms

        # 初始化基本属性
        self.complexity = 1
        self.is_constant_term = True
        self.has_var_dep_term = False
        self.has_var_indep_term = False
        self.has_var_query_term = False
        self.has_interval_term = False

        # 如果term_indices为空，使用ConvRectangle更新
        if hasattr(self, 'term_indices') and self.term_indices is None:
            rect = self.update_conv_rectangle(terms)
            if rect:
                self.index_variable = rect.index_variable
                self.term_indices = rect.term_indices

        # 计算复杂度并检查变量
        for t in terms:
            if t is not None:  # 防止None值
                self.complexity += t.get_complexity()
                if hasattr(t, 'has_var') and callable(getattr(t, 'has_var')):
                    if t.has_var():
                        self.is_constant_term = False
                if hasattr(t, 'has_var_dep') and callable(getattr(t, 'has_var_dep')):
                    if t.has_var_dep():
                        self.has_var_dep_term = True
                if hasattr(t, 'has_var_indep') and callable(getattr(t, 'has_var_indep')):
                    if t.has_var_indep():
                        self.has_var_indep_term = True
                if hasattr(t, 'has_var_query') and callable(getattr(t, 'has_var_query')):
                    if t.has_var_query():
                        self.has_var_query_term = True
                if hasattr(t, 'has_interval') and callable(getattr(t, 'has_interval')):
                    if t.has_interval():
                        self.has_interval_term = True

        # 使名称无效，强制重新生成
        self.invalidate_name()

        # 如果没有变量，设置为已标准化
        if not self.has_var():
            self.set_normalized(True)

    def clone_terms_replacing(self, from_term: Term, to_term: Term) -> List[Term]:
        """
        克隆项列表，替换特定项

        参数:
            from_term: 要替换的项
            to_term: 替换为的项

        返回:
            List[Term]: 替换后的项列表
        """
        y = [None] * len(self.term)
        for i, x in enumerate(self.term):
            if x == from_term:
                x = to_term
            y[i] = x
        return y

    def contains_all_terms_of(self, t: Term) -> bool:
        """
        检查复合项是否包含另一个项的所有组件项

        参数:
            t: 要检查的项

        返回:
            bool: 如果包含所有组件项则返回True
        """
        if self.__class__ == t.__class__:
            # 检查是否包含所有组件项
            if isinstance(t, CompoundTerm):
                for component in t.term:
                    if component not in self.term:
                        return False
                return True
        else:
            # 检查是否包含整个项
            return t in self.term
        return False

    def set_component(self, index: int, t: Term, memory=None) -> Term:
        """
        尝试在给定索引处替换复合项中的组件

        参数:
            index: 替换位置
            t: 新的组件
            memory: 内存引用

        返回:
            Term: 新的复合项
        """
        # 创建组件列表的副本
        term_list = self.as_term_list()
        term_list.pop(index)

        if t is not None:
            if self.__class__ != t.__class__:
                # 如果类型不同，直接添加
                term_list.insert(index, t)
            else:
                # 如果类型相同，展开组件
                if isinstance(t, CompoundTerm):
                    for i, component in enumerate(t.term):
                        term_list.insert(index + i, component)
                else:
                    term_list.insert(index, t)

        # 如果是可交换的，排序组件
        if self.is_commutative():
            term_list.sort()

        # 创建新的复合项
        try:
            return self.__class__(None, term_list)
        except Exception as e:
            self.logger.error(f"Error creating new compound term: {e}")
            return self

    def __iter__(self) -> Iterator[Term]:
        """
        返回项的迭代器

        返回:
            Iterator[Term]: 项的迭代器
        """
        return iter(self.term)

    def deep_equals(self, term1: 'CompoundTerm') -> bool:
        """
        递归深度对比两个复合项，精确对应两个复合项

        参数:
            term1: 要比较的复合项

        返回:
            bool: 如果深度相等则返回True
        """
        try:
            # 基本检查
            if term1 is None:
                return False

            # 检查类型
            if self.__class__ != term1.__class__:
                return False

            # 检查组件项数量
            if len(self.term) != len(term1.term):
                return False

            # 递归检查每个组件项
            for i in range(len(self.term)):
                t = self.term[i]
                t1 = term1.term[i]

                if t is None and t1 is None:
                    continue
                elif t is None or t1 is None:
                    return False

                if isinstance(t, CompoundTerm) and isinstance(t1, CompoundTerm):
                    if not t.deep_equals(t1):
                        return False
                elif isinstance(t, CompoundTerm) or isinstance(t1, CompoundTerm):
                    return False
                elif t != t1:
                    return False

            return True
        except Exception as e:
            self.logger.error(f"在深度比较项时发生异常: {e}")
            return False

    def get_components_strs(self) -> str:
        """
        获取所有组件的字符串表示

        返回:
            str: 组件字符串
        """
        sb = ""
        for t in self.term:
            if isinstance(t, CompoundTerm):
                sb += t.get_components_strs()
            else:
                sb += str(t.name())
        return sb

    def deep_replace_var(self, var_term: Term, term: Term) -> None:
        """
        递归替换变量

        参数:
            var_term: 要替换的变量
            term: 替换为的项
        """
        if term is None or var_term is None:
            return

        replaced = False
        for i in range(len(self.term)):
            t = self.term[i]
            if isinstance(t, CompoundTerm):
                t.deep_replace_var(var_term, term)
            elif t == var_term:
                self.term[i] = term
                replaced = True

        if replaced:
            # 更新项名称
            self.set_term_name(self.make_name())

    def deep_replace_term(self, array: List[Term]) -> None:
        """
        递归替换项

        参数:
            array: 替换项的数组
        """
        if array is None:
            return

        # 替换组件项
        for i in range(min(len(array), len(self.term))):
            # 如果类型一致，直接替换，否则先克隆再替换
            if self.term[i].__class__ == array[i].__class__:
                self.term[i] = array[i]
            else:
                self.term[i] = array[i].clone()

        # 更新项名称
        self.set_term_name(self.make_name())

    def __eq__(self, other: object) -> bool:
        """
        检查两个项是否相等

        参数:
            other: 要比较的项

        返回:
            bool: 如果相等则返回True
        """
        if other is self:
            return True
        if not isinstance(other, Term):
            return False

        # 比较名称
        n = self.name()
        if n is None:
            n = self.name()

        n2 = other.name()
        if n2 is None:
            n2 = other.name()

        return n == n2

    def __lt__(self, other: Term) -> bool:
        """
        比较两个项的大小

        参数:
            other: 要比较的项

        返回:
            bool: 如果当前项小于另一个项则返回True
        """
        return self.compare_to(other) < 0

    def apply_substitute(self, subs: Dict[Term, Term]) -> Optional[Term]:
        """
        递归地将替代项应用于当前的复合词

        参数:
            subs: 替代映射

        返回:
            Term: 应用替代后的项，如果无法创建则返回None
        """
        if subs is None or not subs:
            return self

        tt = [None] * len(self.term)
        modified = False

        for i in range(len(tt)):
            t1 = tt[i] = self.term[i]

            if t1 in subs:
                t2 = subs[t1]
                # 处理链式替换
                while t2 in subs:
                    t2 = subs[t2]
                # 防止无限递归
                if not t2.contains_term(t1):
                    tt[i] = t2
                    modified = True
            elif isinstance(t1, CompoundTerm):
                ss = t1.apply_substitute(subs)
                if ss is not None:
                    tt[i] = ss
                    if not tt[i] == self.term[i]:
                        modified = True

        if not modified:
            return self

        # 如果是可交换的，排序组件
        if self.is_commutative():
            tt.sort()

        return self.clone(tt)

    def apply_substitute_to_compound(self, substitute: Dict[Term, Term]) -> Optional['CompoundTerm']:
        """
        当且仅当结果为复合词时，返回apply_substitute的结果

        参数:
            substitute: 替代映射

        返回:
            CompoundTerm: 应用替代后的复合词，如果结果不是复合词则返回None
        """
        t = self.apply_substitute(substitute)
        if isinstance(t, CompoundTerm):
            return t
        return None

    def clone_terms_except(self, require_modification: bool, to_remove: List[Term]) -> Optional[List[Term]]:
        """
        克隆词项数组，除了一个或多个词项

        参数:
            require_modification: 如果为True，则要求至少有一个词项被移除
            to_remove: 要移除的词项列表

        返回:
            List[Term]: 克隆并移除指定词项后的列表，如果require_modification为True且没有词项被移除则返回None
        """
        l = self.as_term_list()
        removed = False

        for t in to_remove:
            if t in l:
                l.remove(t)
                removed = True

        if require_modification and not removed:
            return None

        return l

    def as_term_list(self) -> List[Term]:
        """
        将组件项转换为列表

        返回:
            List[Term]: 组件项列表
        """
        l = []
        self.add_terms_to(l)
        return l

    @staticmethod
    def transform_independent_variable_to_dependent(T: 'CompoundTerm') -> None:
        """
        将自变量转换为因变量

        参数:
            T: 要转换的复合词
        """
        from linars.org.opennars.language.variables import Variable
        from linars.org.opennars.io.symbols import Symbols

        term = T.term
        for i in range(len(term)):
            t = term[i]
            if t.has_var():
                if isinstance(t, CompoundTerm):
                    CompoundTerm.transform_independent_variable_to_dependent(t)
                elif isinstance(t, Variable) and t.is_independent_variable():
                    # 将独立变量转换为依赖变量
                    term[i] = Variable(Symbols.VAR_DEPENDENT + t.name()[1:])

    @staticmethod
    def shuffle(ar: List[Term], random_number: random.Random) -> None:
        """
        随机打乱数组中的元素

        参数:
            ar: 要打乱的数组
            random_number: 随机数生成器
        """
        if len(ar) < 2:
            return

        for i in range(len(ar) - 1, 0, -1):
            index = random_number.randint(0, i)
            # 简单交换
            a = ar[index]
            ar[index] = ar[i]
            ar[i] = a

# Import at the end to avoid circular imports
from linars.org.opennars.entity.term_link import TermLink
