# LIDA Cognitive Framework
"""
Interface for listeners of PAMemory.
"""

from abc import ABC, abstractmethod
from typing import Any

from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName


class PamListener(ModuleListener, ABC):
    """
    Interface for listeners of PAMemory.
    """
    
    @abstractmethod
    def receive_percept(self, percept: Any, module_name: ModuleName = None) -> None:
        """
        Receive a percept from PAMemory.
        
        Args:
            percept: The percept to receive
            module_name: The name of the module that sent the percept
        """
        pass
