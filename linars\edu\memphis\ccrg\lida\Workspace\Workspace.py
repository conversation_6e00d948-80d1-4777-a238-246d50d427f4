# LIDA Cognitive Framework
"""
The workspace collection of submodules where Cues from episodic memories, the recent contents
of conscious, the perceptual buffer, and the current situational model are stored.
A workspace should be interfaceable with codelets whose job is to
operate on the contents of these submodules.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.EpisodicMemory.CueListener import CueListener
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceListener import WorkspaceListener

class Workspace(FrameworkModule):
    """
    The workspace collection of submodules where Cues from episodic memories, the recent contents
    of conscious, the perceptual buffer, and the current situational model are stored.
    A workspace should be interfaceable with codelets whose job is to
    operate on the contents of these submodules.
    """
    
    @abstractmethod
    def add_cue_listener(self, listener: <PERSON>ueListener) -> None:
        """
        Add episodic memory that will listen for cues from the Workspace.
        
        Args:
            listener: The listener to add
        """
        pass
    
    @abstractmethod
    def add_workspace_listener(self, listener: WorkspaceListener) -> None:
        """
        Adds specified WorkspaceListener.
        
        Args:
            listener: The listener of this Workspace
        """
        pass
    
    @abstractmethod
    def cue_episodic_memories(self, content: NodeStructure) -> None:
        """
        Cue episodic memories with the specified content.
        
        Args:
            content: The content to cue with
        """
        pass
