"""
事件观察接口
定义可观察和触发事件的对象接口
"""
from abc import ABC, abstractmethod
from typing import Callable, List, Any

class Eventable(ABC):
    """
    事件观察接口
    定义可观察和触发事件的对象必须实现的方法
    """

    @abstractmethod
    def on(self, c: type, o: Callable):
        """
        注册事件观察器

        参数:
            c: 事件类型
            o: 事件观察器回调函数
        """
        pass

    @abstractmethod
    def off(self, c: type, o: Callable):
        """
        注销事件观察器

        参数:
            c: 事件类型
            o: 事件观察器回调函数
        """
        pass

    @abstractmethod
    def event(self, e: Callable, enabled: bool, *events: List[type]):
        """
        批量注册/注销事件观察器

        参数:
            e: 事件观察器回调函数
            enabled: 是否启用观察器(True注册/False注销)
            events: 要操作的事件类型列表
        """
        pass

    @abstractmethod
    def emit(self, c: type, *o: List[Any]):
        """
        触发事件

        参数:
            c: 事件类型
            o: 事件参数列表
        """
        pass
