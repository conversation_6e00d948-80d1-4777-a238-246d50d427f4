# LIDA Cognitive Framework
"""
Basic PAM Initializer which reads String parameters beginning with 'pam.'
and initializes the PAM module.
"""

import logging
from typing import Dict, Any, Optional, TypeVar, Generic, Type
from linars.edu.memphis.ccrg.lida.Framework.Initialization.initializer import Initializer

T = TypeVar('T')

from linars.edu.memphis.ccrg.lida.Framework.Initialization.FullyInitializable import FullyInitializable
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategoryImpl import LinkCategoryImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Framework.agent import Agent
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter


class BasicPamInitializer(Initializer):
    """
    Basic PAM Initializer which reads String parameters beginning with 'pam.'
    and initializes the PAM module.
    """

    def __init__(self):
        """
        Initialize a BasicPamInitializer.
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        #AgentStarter.pam = None

    def initModule(self, module: FullyInitializable, agent: Agent, params: Dict[str, Any]) -> None:
        """
        Initialize the module.

        Args:
            module: The module to initialize
            agent: The agent
            params: The parameters for initialization
        """
        try:
            # print(f"BasicPamInitializer.initModule() called with module: {module}, agent: {agent}, params: {params}")
            print(f"BasicPamInitializer.initModule() called with module-----AgentStarter.pam = module------: {module}")
            AgentStarter.pam = module

            if not isinstance(AgentStarter.pam, PAMemory):
                self.logger.warning(f"Module is not a PAMemory at tick {TaskManager.get_current_tick()}")
                return

            # Initialize PAM with default nodes and links
            self.init_default_nodes()
            self.init_default_links()
            print("PAM initialization successful!")
        except Exception as e:
            print(f"Error initializing PAM: {e}")
            import traceback
            traceback.print_exc()

    # 删除不需要的方法，因为我们已经有了initModule方法

    def init_default_nodes(self) -> None:
        """
        Initialize default nodes in PAM.
        """
        # Add some default nodes
        AgentStarter.pam.add_default_node("self")
        AgentStarter.pam.add_default_node("food")
        AgentStarter.pam.add_default_node("origin")
        AgentStarter.pam.add_default_node("front")
        AgentStarter.pam.add_default_node("left")
        AgentStarter.pam.add_default_node("right")
        AgentStarter.pam.add_default_node("around")
        AgentStarter.pam.add_default_node("get")
        AgentStarter.pam.add_default_node("goodHealth")
        AgentStarter.pam.add_default_node("fairHealth")
        AgentStarter.pam.add_default_node("badHealth")

    def init_default_links(self) -> None:
        """
        Initialize default links in PAM.
        """
        # Add some default links
        # This is just an example, actual implementation would depend on the specific requirements
        self_node = AgentStarter.pam.get_node("self")
        food_node = AgentStarter.pam.get_node("food")

        if self_node is not None and food_node is not None:
            # Create a link between self and food
            category = LinkCategoryImpl("association")
            AgentStarter.pam.add_default_link(self_node, food_node, category)

    # 删除不需要的initialize方法，因为我们已经有了initModule方法
