#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to add a PamLink and its sink to the percept.
"""

from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory

class AddLinkToPerceptTask(FrameworkTaskImpl):
    """
    A task to add a PamLink and its sink to the percept.
    
    See Also:
        ExcitationTask creates this task
        PropagationTask creates this task
    """
    
    def __init__(self, link: Link, pam: PAMemory):
        """
        Initialize an AddLinkToPerceptTask.
        
        Args:
            link: The link to add to the percept
            pam: The PAMemory
        """
        super().__init__()
        self.pam = pam
        self.link = link
        
    def run_this_framework_task(self):
        """
        Adds link's sink to the percept and tries to add the link as well then finishes.
        """
        self.pam.add_to_percept(self.link.get_sink())
        self.pam.add_to_percept(self.link)
        self.cancel()
