"""
ALifeObject implementation.

This module provides a simplified implementation of the ALifeObject class from the original Java alife package.
"""

from typing import Dict, Any, Optional, List, Set, Tuple


class ALifeObject:
    """
    A simplified implementation of the ALifeObject class.
    
    This class represents an object in the ALife environment.
    """
    
    def __init__(self, name: str = None, object_type: str = None, position: Tuple[int, int] = None):
        """
        Initialize an ALifeObject.
        
        Args:
            name: The name of the object
            object_type: The type of the object
            position: The position of the object as (x, y)
        """
        self.name = name
        self.object_type = object_type
        self.position = position
        self.properties = {}
        
    def get_name(self) -> str:
        """
        Get the name of this object.
        
        Returns:
            The name of this object
        """
        return self.name
        
    def set_name(self, name: str) -> None:
        """
        Set the name of this object.
        
        Args:
            name: The name to set
        """
        self.name = name
        
    def get_type(self) -> str:
        """
        Get the type of this object.
        
        Returns:
            The type of this object
        """
        return self.object_type
        
    def set_type(self, object_type: str) -> None:
        """
        Set the type of this object.
        
        Args:
            object_type: The type to set
        """
        self.object_type = object_type
        
    def get_position(self) -> Tuple[int, int]:
        """
        Get the position of this object.
        
        Returns:
            The position of this object as (x, y)
        """
        return self.position
        
    def set_position(self, position: Tuple[int, int]) -> None:
        """
        Set the position of this object.
        
        Args:
            position: The position to set as (x, y)
        """
        self.position = position
        
    def get_property(self, key: str) -> Any:
        """
        Get a property of this object.
        
        Args:
            key: The key of the property
            
        Returns:
            The value of the property, or None if not found
        """
        return self.properties.get(key)
        
    def set_property(self, key: str, value: Any) -> None:
        """
        Set a property of this object.
        
        Args:
            key: The key of the property
            value: The value to set
        """
        self.properties[key] = value
        
    def __str__(self) -> str:
        """
        Return the string representation of this object.
        
        Returns:
            The string representation of this object
        """
        return f"ALifeObject[name={self.name}, type={self.object_type}, position={self.position}]"
        
    def __eq__(self, other) -> bool:
        """
        Check if this object is equal to another.
        
        Args:
            other: The other object to compare with
            
        Returns:
            True if the objects have the same name, type, and position, False otherwise
        """
        if isinstance(other, ALifeObject):
            return (self.name == other.name and 
                    self.object_type == other.object_type and 
                    self.position == other.position)
        return False
        
    def __hash__(self) -> int:
        """
        Return the hash code of this object.
        
        Returns:
            The hash code of this object
        """
        return hash((self.name, self.object_type, self.position))
