# LIDA Cognitive Framework
"""
A PamNode is a Node which resides in PAMemory and represents a feature or a concept.
PamNodes are involved in activation passing where Nodes are not.
They can represent the LinkCategory of a Link.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategory import LinkCategory
from linars.edu.memphis.ccrg.lida.PAM.PamLinkable import PamLinkable

class PamNode(Node, PamLinkable, LinkCategory):
    """
    A PamNode is a Node which resides in PAMemory and represents a feature or a concept.
    PamNodes are involved in activation passing where Nodes are not.
    They can represent the LinkCategory of a Link.
    """
    pass
