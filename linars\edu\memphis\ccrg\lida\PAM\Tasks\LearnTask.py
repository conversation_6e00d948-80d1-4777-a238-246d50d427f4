#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task for learning.
"""

from typing import List, Optional
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.Data.neo_util import NeoUtil

# 导入 linars 组件
try:
    from linars.edu.memphis.ccrg.linars.memory import Memory
    from linars.edu.memphis.ccrg.linars.term import Term
    from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
    from linars.org.opennars.entity.task import Task
    from linars.org.opennars.control.derivation_context import DerivationContext
    from linars.org.opennars.io.parser import Parser
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False

class LearnTask(FrameworkTaskImpl):
    """
    A task for learning.
    """

    def __init__(self, terms: Optional[List[Term]] = None, root_node: Optional[str] = None,
                source: Optional[str] = None, act_stamp: Optional[str] = None):
        """
        Initialize a LearnTask.

        Args:
            terms: The terms
            root_node: The root node
            source: The source
            act_stamp: The action stamp
        """
        super().__init__(1, "tact")
        self.terms = terms
        self.root_node = root_node
        self.source = source
        self.act_stamp = act_stamp

        self.pam = None  # PAMemory
        self.link = None  # Link
        self.task = None  # Task
        self.mem = None  # Memory
        self.nal = None  # DerivationContext

        self.is_daemon = False  # 是否是守护线程，守护线程不会阻止程序的结束
        self.is_root = False  # 是否是时序根节点

    def run_this_framework_task(self):
        """
        Run the task.
        """
        # 多种大框架，分别对应各自构建方案，关注特定标志词，细节差异，后天习得的时序构建时序
        # 纯大步骤高度概括时序，小步骤解释时序，形式抽象时序，实例时序，带参数表等时序，混合时序
        # 如方法论：方法名，可能无。方法体，可能无。参数，可能无。返回值，可能无。异常，可能无
        # 各种标志词，连词，可能无：首先，然后，最后，因为，所以，但是，如果，否则等
        # 各步骤，各模态，各类型：赋值，判断，循环，调用，返回，异常等

        # 可能有插入其他类型内容，解释性，甚至无关内容，有时需要拓展补充
        # 结合构建目标判断筛选，每一句判断：这样的可作为时序。不一定是操作，可能是陈述、概念等
        # 礼貌需求等同理。可事后判断，可直接结合最短路约搜。被动输入构建=只能事后辨别
        # 标点符号，语音无，但有停顿。在理解阶段辅助分句，这里直接拿到分句后结构

        # 直接按分句构建即可？嵌套、循环等需序号，结合变量句，理解阶段做好嵌套等准备，这里直接构建
        # 嵌套标志，$次序标志-类型1-次序1，$次序标志-类型2-次序1。同类型为同层，不同类型不同层，同层次序递增
        # nars先后现在时态即构建时序，适合具身操作，心理、语言待定，即使有后天学习图程，也无法很好执行

        # 原始输入队列，用以获取标志词后续信息，限制形式、模态、内容、对象等，如谁+说的+方法论，理解队列不一定是原始顺序
        # 结合理解池和原始队列，按原始排序。义法歧义可能造成排序混乱，各种歧义情况需附加推理
        # 次序认知也是理解一部分，时间次序、空间次序、逻辑次序等。总之分清次序并通达即可

        mark = self.get_mark()

        # 拼接标志词后信息，原始输入队列形式是：教你26加8+其他
        from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
        input_queue_str = AgentStarter.input_queue_str

        for node in AgentStarter.nar.memory.get_nodes():
            if mark in node.get_tn_name() and node.get_tn_name() != mark:
                # todo 方法名等也适用变量句，后续再优化
                # 识别到关注的标志词，如果标志后信息在原始输入队列中，可开始构建
                # 标志词形式是（*，教你（*，26，加，8）），需要将标志词后信息拼接为：教你26加8，然后判断
                attent_str = node.get_strs()
                if attent_str in input_queue_str:
                    print(f"conceptNodes: {node}")
                    # 截取标志词后信息，作为方法名，需实时理解哪些是方法名，哪些是其他，过滤掉其他
                    # todo 识别哪些是参数，构建参数表，带符变量式描述，直接构建即可
                    method_name = ""
                    compound_term = None
                    try:
                        compound_term = AgentStarter.narsese.parse_term(node.get_tn_name())
                    except Exception as e:
                        raise RuntimeError(e)

                    terms = compound_term.get_terms()
                    method_name = str(terms[1])

                    # method_name = input_queue_str[input_queue_str.index(mark):input_queue_str.index(mark) + len(attent_str)]
                    # todo 新建两个节点，一是方法名，二是时序根节点，并新建相似边
                    link = NeoUtil.merge_link(None, "相似", method_name, "nars1111", "Verb", "Verb")

                    # 如果有可能的后续嵌套时序构建，则将上位时序存入主路线，以便回溯执行。与执行分开
                    from linars.edu.memphis.ccrg.lida.PAM.PAMemoryImpl import PAMemoryImpl
                    PAMemoryImpl.seq_ns.get_create_main_path().append(link)

                    if link is not None:
                        # 新建时序根节点，作为时序首，需前后两标志词截取，后天+理解=得出嵌套，标志词出自构建线程内部，而不是参数输入
                        from linars.edu.memphis.ccrg.lida.PAM.Tasks.CreateActRootTask0 import CreateActRootTask0
                        from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl

                        create_act_root_task = CreateActRootTask0("nars1111",
                                                               input_queue_str.index(attent_str) + len(attent_str),
                                                               node.get_tn_name(), True, self.act_stamp)
                        FrameworkModuleImpl.task_spawner.add_task(create_act_root_task)
                        # todo 结束当前线程，还要维护一个嵌套栈，用以判断当前线程是否结束，结束后再继续执行上一线程
                        self.cancel()

    def get_mark(self) -> str:
        """
        Get the mark.

        Returns:
            The mark
        """
        mark = ""

        # 如（*，听到，教你）
        mark = self.terms[0].get_source().get_tn_name().split(",")[2].replace(")", "")
        return mark
