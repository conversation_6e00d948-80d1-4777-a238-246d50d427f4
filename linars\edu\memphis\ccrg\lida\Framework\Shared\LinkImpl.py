# LIDA认知框架
"""
链接的默认实现
"""

import logging
from typing import Dict, Any, Optional, List
import traceback

from linars.edu.memphis.ccrg.lida.Framework.Shared.ExtendedId import ExtendedId
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategory import LinkCategory
from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Framework.Strategies.DecayStrategy import DecayStrategy
from linars.edu.memphis.ccrg.lida.Framework.Strategies.ExciteStrategy import ExciteStrategy

from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.term import Term

# Try to import NARS components if available
try:
    from linars.org.opennars.io.parser import Parser
    NARS_AVAILABLE = True
except ImportError:
    NARS_AVAILABLE = False


class LinkImpl(CompoundTerm, Link):
    """
    链接的默认实现
    """

    # Class variable for ID generator
    _id_generator = 0

    def __init__(self, source: Node = None, sink: Linkable = None, category: LinkCategory = None):
        """
        初始化链接实现
        参数:
            source: 源节点
            sink: 汇端可链接对象
            category: 链接类别
        """
        # print("LinkImpl---__init__-----")
        super().__init__()

        # 基本属性
        self.link_id = LinkImpl._id_generator
        LinkImpl._id_generator += 1
        self.source = source
        self.sink = sink
        self.category = category
        self.grounding_pam_link = self
        self.logger = logging.getLogger(self.__class__.__name__)
        self.extended_id = None

        # 激活和衰减相关属性
        self.excite_strategy = None
        self.decay_strategy = None
        self.incentive_salience_decay_strategy = None
        self.activation = 0.0
        self.removal_threshold0 = 0.0
        self.incentive_salience = 0.0

        # 其他属性
        self.parameters = None
        self.relationship_proxy = None
        self.last_from = None
        self.properties: Dict[str, Any] = {}
        self.now_order = 0
        self.last_order = 0
        self.k_order = 0

        # 初始化term数组
        self.term: List[Term] = [None, None, None]

        # 如果提供了源、汇和类别，则设置它们
        if source is not None:
            self.set_source(source)
        if category is not None:
            self.set_category(category)
        if sink is not None:
            self.set_sink(sink)

        # 初始化激活
        self.init_active()

    def is_simple_link(self) -> bool:
        """
        返回链接是否为简单链接(连接两个节点)

        返回:
            如果是简单链接返回True，如果是复杂链接(节点和简单链接之间)返回False
        """
        return self.sink is not None and self.sink.is_node()

    def get_source(self) -> Node:
        """
        获取链接的源端(向汇端提供激活)

        返回:
            源端可链接对象
        """
        return self.source

    def get_sink(self) -> Linkable:
        """
        获取链接的汇端(从源端接收激活)
        返回:
            汇端可链接对象
        """
        return self.sink

    def getSink(self) -> Linkable:
        """
        获取链接的汇端(从源端接收激活)(get_sink的别名)
        返回:
            汇端可链接对象
        """
        return self.get_sink()

    def init_active(self) -> None:
        """
        初始化激活相关属性
        """
        self.activation = 0.0
        self.incentive_salience = 0.0
        self.removal_threshold0 = 0.0

    def set_source(self, source: Node) -> None:
        """
        设置源端可链接对象

        参数:
            source: 新的源端
        """
        if source is None:
            self.logger.warning(f"Cannot set source to null at tick {TaskManager.get_current_tick()}")
            return
        elif self.sink is not None and source == self.sink:
            self.logger.warning(f"Cannot set link's source to the same Linkable as its sink at tick {TaskManager.get_current_tick()}")
            return

        self.source = source
        # 更新term数组
        # if hasattr(source, 'to_term'):
        #     self.term[0] = source.to_term() if callable(source.to_term) else source
        # else:
        self.term[0] = source

        # 更新扩展ID
        self._update_extended_id()

    def getSource(self) -> Node:
        """
        获取链接的源端(向汇端提供激活)(get_source的别名)

        返回:
            源端可链接对象
        """
        return self.get_source()

    def set_sink(self, sink: Linkable) -> None:
        """
        设置汇端可链接对象
        参数:
            sink: 新的汇端
        """
        # 在Java版本中有更多的检查，这里简化了
        self.sink = sink

        # 更新term数组
        # if hasattr(sink, 'to_term'):
        #     self.term[2] = sink.to_term() if callable(sink.to_term) else sink
        # else:
        self.term[2] = sink

        # 更新扩展ID
        self._update_extended_id()

    def get_category(self) -> LinkCategory:
        """
        获取此链接的类别

        返回:
            链接类别
        """
        return self.category

    def getCategory(self) -> LinkCategory:
        """
        获取此链接的类别(get_category的别名)
        返回:
            链接类别
        """
        return self.get_category()

    def set_category(self, category: LinkCategory) -> None:
        """
        设置链接类别

        参数:
            category: 新的链接类别
        """
        if category is None:
            self.logger.warning(f"Cannot set a Link's category to null at tick {TaskManager.get_current_tick()}")
            return

        self.category = category

        # 更新term数组
        # if hasattr(category, 'to_term'):
        #     self.term[1] = category.to_term() if callable(category.to_term) else category
        # else:
        self.term[1] = category

        # 更新扩展ID
        self._update_extended_id()

    def _update_extended_id(self) -> None:
        """
        根据类别、源和汇更新此链接的ExtendedId
        """
        if self.category is not None and self.source is not None and self.sink is not None:
            if self.logger.isEnabledFor(logging.DEBUG):
                self.logger.debug(f"ExtendedID updated at tick {TaskManager.get_current_tick()}")

            self.extended_id = ExtendedId(self.source.get_node_id(), self.sink.get_extended_id(), self.category.get_id())

    def get_tn_name(self) -> str:
        """
        获取链接的TN名称

        返回:
            链接的TN名称
        """
        if self.category is None:
            return ""
        return self.category.get_tn_name()

    def set_grounding_pam_link(self, link: 'PamLink') -> None:
        """
        设置此链接的基础PamLink

        参数:
            link: 新的基础PamLink
        """
        self.grounding_pam_link = link

    def get_grounding_pam_link(self) -> 'PamLink':
        """
        获取此链接的基础PamLink

        返回:
            基础PamLink
        """
        return self.grounding_pam_link

    def update_link_values(self, link: Link) -> None:
        """
        Link的子类应重写此方法，使用指定Link的值设置其所有类型特定的成员数据
        指定的Link必须是相同的子类类型

        参数:
            link: 用于更新值的链接
        """
        # This method should be overridden by subclasses
        pass

    def get_id(self) -> int:
        """
        获取此链接的ID
        返回:
            链接ID
        """
        return self.link_id

    def set_id(self, id: int) -> None:
        """
        设置此链接的ID
        参数:
            id: 要设置的ID
        """
        self.link_id = id

    def get_extended_id(self) -> ExtendedId:
        """
        获取扩展ID
        返回:
            可链接对象的通用ID
        """
        if self.source is None or self.sink is None or self.category is None:
            return ExtendedId()

        return ExtendedId(self.source.get_node_id(), self.sink.get_extended_id(), self.category.get_id())

    def is_node(self) -> bool:
        """
        返回此可链接对象是否为节点
        返回:
            如果是节点返回True，否则返回False
        """
        return False

    def __eq__(self, other) -> bool:
        """
        检查此链接是否与另一个相等
        参数:
            other: 要比较的另一个链接
        返回:
            如果链接具有相同的源端、汇端和类别则返回True，否则返回False
        """
        if isinstance(other, Link):
            if self.source is None or self.sink is None or self.category is None:
                return False

            other_source = other.get_source()
            other_sink = other.get_sink()
            other_category = other.get_category()

            if other_source is None or other_sink is None or other_category is None:
                return False

            return (self.source.get_node_id() == other_source.get_node_id() and
                    self.sink.get_extended_id() == other_sink.get_extended_id() and
                    self.category.get_id() == other_category.get_id())

        return False

    def __hash__(self) -> int:
        """
        返回此链接的哈希码
        返回:
            链接的哈希码
        """
        if self.source is None or self.sink is None or self.category is None:
            return 0

        result = 17
        result = 37 * result + self.source.get_node_id()
        result = 37 * result + hash(self.sink.get_extended_id())
        result = 37 * result + self.category.get_id()
        return result

    def to_term(self) -> Term:
        """
        将链接转换为术语

        返回:
            Term: 转换后的术语
        """
        if not NARS_AVAILABLE:
            return None

        try:
            # 尝试使用WorkspaceImpl的getLinkTerm方法
            from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceImpl import WorkspaceImpl
            term = WorkspaceImpl.get_link_term(self)

            # 如果有可用的narsese解析器，尝试解析术语字符串
            if term is not None and hasattr(self, 'nar') and hasattr(self.nar, 'narsese'):
                try:
                    term = self.nar.narsese.parse_term(str(term))
                except Exception as e:
                    self.logger.error(f"Error parsing term: {e}")

            return term
        except Exception as e:
            self.logger.error(f"Error converting link to term: {e}")
            traceback.print_exc()
            return None

    # 激活和衰减相关方法
    def get_activation(self) -> float:
        """
        获取激活值

        返回:
            float: 激活值
        """
        return self.activation

    def set_activation(self, a: float) -> None:
        """
        设置激活值

        参数:
            a: 新的激活值
        """
        if a > 1.0:
            self.activation = 1.0
        elif a < 0.0:
            self.activation = 0.0
        else:
            self.activation = a

    def get_incentive_salience(self) -> float:
        """
        获取激励显著性

        返回:
            float: 激励显著性
        """
        return self.incentive_salience

    def set_incentive_salience(self, s: float) -> None:
        """
        设置激励显著性

        参数:
            s: 新的激励显著性
        """
        if s > 1.0:
            self.incentive_salience = 1.0
        elif s < -1.0:
            self.incentive_salience = -1.0
        else:
            self.incentive_salience = s

    def get_total_incentive_salience(self) -> float:
        """
        获取总激励显著性

        返回:
            float: 总激励显著性
        """
        return self.get_incentive_salience()

    def set_activatible_removal_threshold(self, t: float) -> None:
        """
        设置可激活对象的移除阈值

        参数:
            t: 新的移除阈值
        """
        self.removal_threshold0 = t

    def set_decay_strategy(self, s: DecayStrategy) -> None:
        """
        设置衰减策略

        参数:
            s: 新的衰减策略
        """
        self.decay_strategy = s

    def set_excite_strategy(self, s: ExciteStrategy) -> None:
        """
        设置激发策略

        参数:
            s: 新的激发策略
        """
        self.excite_strategy = s

    def is_removable(self) -> bool:
        """
        检查此链接是否可移除

        返回:
            bool: 如果激活值和激励显著性均低于阈值则返回True
        """
        return (self.get_activation() <= self.removal_threshold0 and
                abs(self.get_incentive_salience()) <= self.removal_threshold0)

    def decay(self, ticks: int = 1) -> None:
        """
        使链接衰减

        参数:
            ticks: 衰减的tick数
        """
        if self.decay_strategy is not None:
            self.set_activation(self.decay_strategy.decay(self.get_activation()))

        if self.incentive_salience_decay_strategy is not None:
            self.set_incentive_salience(self.incentive_salience_decay_strategy.decay(self.get_incentive_salience()))

    def excite(self, amount: float) -> None:
        """
        激发链接

        参数:
            amount: 激发量
        """
        if self.excite_strategy is not None:
            self.set_activation(self.excite_strategy.excite(self.get_activation(), amount))

    def get_properties(self) -> Dict[str, Any]:
        """
        获取属性字典

        返回:
            Dict[str, Any]: 属性字典
        """
        return self.properties

    def set_properties(self, properties: Dict[str, Any]) -> None:
        """
        设置属性字典

        参数:
            properties: 新的属性字典
        """
        self.properties = properties

    def __str__(self) -> str:
        """
        返回此链接的字符串表示
        返回:
            链接的字符串表示
        """
        if self.source is None or self.sink is None or self.category is None:
            return "Link[null]"

        # 格式化激活值
        try:
            from decimal import Decimal, ROUND_HALF_UP
            activation_str = str(Decimal(str(self.activation)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
        except:
            activation_str = f"{self.activation:.2f}"

        return f"{self.source}-{self.category.get_tn_name()}-{self.sink}"
