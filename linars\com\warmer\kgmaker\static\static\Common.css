@import '/static/bootstrap.min.css';
@import '/static/font-awesome.min.css';
@import '/static/iconfont.css';
@import '/static/icons.css';

* {
  outline: none !important;
}

body {
  font-family: "Lato", "Helvetica", sans-serif;
 }

/**
The Page Dom Main struct 
#react-dom
└──#react-body
   ├──#header 
   ├──#main 
   |   ├── Others
   |   └── Maybe need has fixed css div
   └──#footer
*/

html,
body,
#react-dom {
  margin: 0;
  padding: 0;
  height: 100%;
  min-width: 768px;
}


#react-body {
  min-height: 100%;
  height: auto !important;
  position: relative;
  background-color: #303030;
  padding-top: 76px;
}

#react-body>#header {
  position: fixed;
  top: 0;
  z-index: 99999;
  width: 100%;
}

#react-body>#main {
  width: 100%;
  margin: 0 auto;
  padding-bottom: 160px;
}

#react-body>#footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 120px;
  background-color: #141414;
  color: #DDD;
  /*footer's height*/
  clear: both;
}

/**
 2. #main  please refer the page
*/

/**
 0. header footer common
*/

#footer,
#header {
  font-family: "Lato", "Helvetica", sans-serif;
  text-align: center;
  color: #ACACAC;
  background-color: #1D1D1D;
  font-size: 1rem;
  text-decoration: none;
  letter-spacing: 0;
  font-style: normal;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
/*
#footer ul,
#header ul {
  text-transform: uppercase;
}*/

#header ul .default-text {
  text-transform: capitalize;
}

/**
 1 #header 
 */

#header {
  border-radius: 0;
}

#header .collapse {
  /* max-width: 1280px; */
  /* margin: 0 auto; */
}

#header .collapse .nav-item {
  margin: 0 16px;
}

#header .nav-item .nav-link {
  color: #ACACAC;
}

#header .nav-item .nav-link:hover {
  color: #DBDBDB;
}

#header .navbar-brand>img {
  margin-left: 10px;
  margin-top: -4px;
  height: 40px;
  width: 40px;
}


/*Me Dropdown-menu*/
#header .dropdown-menu.show {
  background-color: #434343;
  margin: 12px 0 0 0;
}

#header .dropdown-menu.show .dropdown-item {
  color: #ACACAC;
  height: 35px;
}

#header .dropdown-menu.show .dropdown-item:focus,
#header .dropdown-menu.show .dropdown-item:hover {
  transition: 0.1s;
  background-color: #434343;
  color: #DBDBDB;
}

#header .dropdown-menu.show .dropdown-divider {
  border-color: #262626;
}


/**
 2 #footer 
 */

#footer .footer-menu {
  padding-top: 32px;
  width: 100%;
  color: #dddddd;
}

#footer .footer-menu ul {
  padding: 0;
  margin: 0 auto;
}

#footer .footer-menu ul li {
  letter-spacing: 0;
  list-style-type: none;
  cursor: pointer;
  text-align: center;
  padding: 0;
  display: inline;
  margin: 0 42px;
}

#footer .footer-menu li.menu-item a {
  text-transform: none;
  font-size: 1rem;
  padding: 4px 8px;
  background-repeat: no-repeat;
  text-decoration: none;
  color: #ACACAC;
}

#footer .footer-menu li.menu-item a:hover {
  color: #DBDBDB;
}

#footer .footer-menu li.menu-item.menu-linkedin a:hover {
  background-image: url("/static/svg/linkedin-w.svg");
  background-size: contain;
}

#footer .footer-menu li.menu-item.menu-linkedin a {
  background-image: url("/static/svg/linkedin.svg");
  background-size: contain;
}

#footer .footer-menu li.menu-item.menu-twitter a:hover {
  background-image: url("/static/svg/twitter-w.svg");
  background-size: contain;
}

#footer .footer-menu li.menu-item.menu-twitter a {
  background-image: url("/static/svg/twitter.svg");
  background-size: contain;
}

#footer .copy-right {
  font-family: "Lato", "Helvetica", sans-serif;
  margin: 20px auto auto auto;
  color: #7D7D7D;
  letter-spacing: 0px;
  -webkit-font-smoothing: antialiased;
  font-size: .85rem;
}

#footer .copy-right .version-name {
  padding-right: 12px;
  cursor: pointer;
}

#footer .copy-right .version-name:hover {
  color: #fff;
}

/*#feedback-dialog*/
#feedback-dialog.modal-dialog {
  margin-top: 40px;
  padding-bottom: 0px;
  color: #080808;
  font-family: Georgia, "Times New Roman", Times, serif !important;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

#feedback-dialog .form-group label {
  font-size: 16px;
}

#feedback-dialog input.form-control {
  min-width: 300px !important;
  border: 1px solid #ced4da;
  background-color: #fff;
}

#feedback-dialog .modal-content .feedback-tool,
#feedback-dialog .modal-content .feedback-editor {
  border: solid 1px #dddddd;
}

#feedback-dialog .modal-content .feedback-editor {
  height: 200px;
  border-top: none;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

#feedback-dialog .modal-content .modal-body {
  overflow-y: auto;
}

#feedback-dialog .full-version {
  font-size: 14px;
  color: #888;
}

/**
 Globle Style
 */
 label {
   margin-bottom: 0;
 }
/*fixed bootstrap4 button style*/
[type=reset],
[type=submit],
button,
html [type=button] {
  -webkit-appearance: none;
  cursor: pointer;
}

.modal.show {
  display: block;
  background-color: rgba(0, 0, 0, 0.6) !important;
}

.modal.show .modal-dialog {
  border-radius: 4px;
  background-color: transparent;
  position: relative;
  top: 0;
  margin-top: 0;
  z-index: 100000;
}

.modal.show .modal-dialog .modal-content{
  max-height: 100%;
}

.modal-content {
  border-radius: 0 0 15px 15px;
}

#confirm-modal {
  z-index: 200000;
}

.modal.show#confirm-modal .modal-dialog .modal-content{
  height: auto;
}

.modal.show .modal-dialog form{
  height: 100%;
  padding-bottom: 130px;
 }

input.form-control {
  font-size: 14px;
}

.btn{
  box-shadow: none !important;
}
.btn-primary {
  color: #fff;
  background-color: #337ab7;
  border-color: #2e6da4;
}

.form-group.has-error input {
  border-color: #dc3545;
}


/*fixed jquery resizable style*/
.ui-resizable-e {
  cursor: e-resize;
  width: 14px;
  right: -14px;
}

.ui-resizable-handle.ui-resizable-se.ui-icon.ui-icon-gripsmall-diagonal-se {
  background-image: url(/static/images/resize-bottom-right.png);
  height: 16px;
  width: 16px;
}

.rc-slider-tooltip{
 z-index: 99999;
 opacity: 1.0;
 padding:3px 8px !important;
}

.search-highlight{
  padding: 2px;
  margin: -2px;
  border-radius: 4px;
  border: 1px solid #F7E633;
  text-shadow: 1px 1px 0 #666;
  background: linear-gradient(to top left, #F7E633 0%, #F1D32F 100%);
}


.clicker{
  cursor: pointer;
}

.clicker:hover,
.clicker:focus{
  color: #ACACAC;
}

.fa.check-box{
  font-size: 18px;
}

.icon-whitespace:before {
  content: "\00a0";
}

.tooltip{
  z-index:1490;
}


#about-main {
  width: 100%;
  height: 100%;
  position: fixed;
  text-align: left;
  letter-spacing: 0ch;
  top: 0;
  left: 0;
  z-index: 99999;
  font-family: Georgia, 'Times New Roman', Times, serif !important;
  background-color: rgba(0, 0, 0, 0.6);
}

#about-main #about-page {
  width: 320px;
  height: 360px;
  border: 1px solid gray;
  color: #ddd;
  border-radius: 10px;
  position: absolute;
  left: 50%;
  margin-left: -160px;
  top: 140px;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.8)
}

#about-main #about-page .about-close {
  width: 94%;
  padding: 4px;
  text-align: end;
}

#about-main #about-page .about-close i {
  cursor: pointer;
}

#about-main #about-page .about-close i:hover {
  color: #fff;
}

#about-main #about-page .about-head {
  /* border-bottom: 1px #ddd solid; */
  width: 80%;
  display: flex;
  flex-direction: row;
  justify-items: center;
  align-items: center;
  font-size: 32px;

}

#about-main #about-page .about-head img {
  width: 60px;
  height: 60px;
  margin: 16px;
}

#about-main #about-page .about-body {
  height: 190px;
  width: 220px;
  font-size: 16px;
}

#about-main #about-page .about-body .about-item {
  margin-top: 16px;
  cursor: pointer;
}

#about-main #about-page .about-body .about-item:hover {
  color: #fff;
}

#about-main #about-page .about-body .about-item i{
  margin-right: 8px;
  width: 20px;
  text-align: center;
}

#about-main #about-page .about-foot {
  font-size: 10px;
  color: #888;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

#about-main #about-page .about-foot div {
  cursor: pointer;
}

#about-main #about-page .about-foot div:hover {
  color: #ddd;
}

#switch-dbs {
  position:fixed;
  width: 300px;
  margin: 136px -1px 0 -1px;
  color: #ddd;
  background-color: rgb(44,44,44);
  border: 1px solid grey;
}

#switch-dbs .form-group{
  margin-top:1rem;
  margin-bottom:1rem;
}

#switch-dbs select{
    width: 100%;
    padding: 5px 5px;
    margin: 8px 0;
    height: 38px;
    display: inline-block;
    border: 1px solid #ccc;
    box-sizing: border-box;
    border-radius: 4px;
}