"""
内涵/外延集合基类，为SetInt(内涵集合)和SetExt(外延集合)提供公共实现

集合特性:
1. 元素必须唯一且已排序
2. 是可交换的(顺序不影响语义)
"""
from typing import List
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.io.symbols import ARGUMENT_SEPARATOR

class SetTensional(CompoundTerm):
    """
    内涵/外延集合基类

    主要功能:
    1. 提供集合的公共实现
    2. 处理集合元素的唯一性和排序
    3. 提供集合名称生成方法
    """

    def __init__(self, arg):
        """
        构造函数(由make方法调用)

        参数:
            arg: 项的组成部分列表

        注意:
        1. 不允许空集合
        2. 元素必须唯一且已排序
        """
        super().__init__(arg)
        if len(arg) == 0:
            raise ValueError("0-arg empty set")
        # In Python implementation, we'll verify sorted and unique in the make method
        self.init(arg)

    @staticmethod
    def make_set_name(opener, arg, closer):
        """
        生成集合的名称

        参数:
            opener: 集合开始符号(如'{'或'[')
            arg: 项列表
            closer: 集合结束符号(如'}'或']')

        返回:
            str: 集合的完整名称

        示例:
        - 外延集合: {A,B}
        - 内涵集合: [A,B]
        """
        result = opener
        for i, t in enumerate(arg):
            if i != 0:
                result += ARGUMENT_SEPARATOR
            if isinstance(t, Term):
                result += t.name()
            else:
                result += str(t)
            # result += t.name()
        result += closer
        return result

    def is_commutative(self):
        """
        Check if the compound is commutative.

        Returns:
            True for commutative
        """
        return True
