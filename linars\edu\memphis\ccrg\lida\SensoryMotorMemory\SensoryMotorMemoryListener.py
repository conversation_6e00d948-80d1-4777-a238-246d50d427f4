# LIDA Cognitive Framework
"""
Listener of SensoryMotorMemory.
"""

from abc import abstractmethod
from typing import Any
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener

class SensoryMotorMemoryListener(ModuleListener):
    """
    Listener of SensoryMotorMemory.
    """
    
    @abstractmethod
    def receive_actuator_command(self, command: Any) -> None:
        """
        Receive an actuator command from SensoryMotorMemory.
        
        Args:
            command: Current command being executed
        """
        pass
