"""
框架基础元素的标准工厂

本模块提供了创建框架基础元素的工厂，
如Node(节点)、Link(链接)、FrameworkTask(框架任务)和NodeStructure(节点结构)。
"""
import logging
from typing import Dict, Any, Optional, List, Type

from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.LinkCategory import LinkCategory
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTask import FrameworkTask
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule

class ElementFactory:
    """
    框架基础元素的标准工厂

    该类提供了创建框架基础元素的方法，
    如Node(节点)、<PERSON>(链接)、FrameworkTask(框架任务)和NodeStructure(节点结构)。
    """

    # Singleton instance
    _instance = None

    # Used to assign unique IDs to nodes
    _node_id_count = 0

    # Used to assign unique IDs to behaviors
    _behavior_id_count = 0

    # Default decay type
    _default_decay_type = "defaultDecay"

    # Default excite type
    _default_excite_type = "defaultExcite"

    # Default incentive salience decay type
    _default_incentive_salience_decay = "defaultDecay"

    # Default link type
    _default_link_type = "LinkImpl"

    # Default link class name
    _default_link_class_name = "linars.edu.memphis.ccrg.lida.Framework.Shared.LinkImpl"

    # Default node class name
    _default_node_class_name = "linars.edu.memphis.ccrg.lida.PAM.PamNodeImpl"

    # Default node type
    _default_node_type = "PamNodeImpl"

    # Default behavior class name
    _default_behavior_class_name = "linars.edu.memphis.ccrg.lida.ActionSelection.BehaviorImpl"

    def __init__(self):
        """
        初始化工厂
        """
        self.logger = logging.getLogger(__name__)
        self.excite_strategies = {}
        self.decay_strategies = {}
        self.is_decay_strategies = {}
        self.node_classes = {}
        self.link_classes = {}
        self.tasks = {}

    @classmethod
    def get_instance(cls) -> 'ElementFactory':
        """
        获取该工厂的单例实例

        返回:
            该工厂的单例实例
        """
        if cls._instance is None:
            cls._instance = ElementFactory()
        return cls._instance

    def get_framework_task(self, task_type: str, params: Dict[str, Any], modules: Dict[ModuleName, FrameworkModule]) -> Optional[FrameworkTask]:
        """
        创建指定类型的框架任务

        参数:
            task_type: 要创建的任务类型
            params: 任务参数
            modules: 按ModuleName索引的模块映射

        返回:
            创建的框架任务，如果创建失败则返回None
        """
        # 根据任务类型创建任务
        # print(f"ElementFactory.get_framework_task: {task_type}")

        if task_type == "ListenDetector":
            try:
                from linars.edu.memphis.ccrg.lida.Alifeagent.Featuredetectors.listen_detector import ListenDetector
                task = ListenDetector()
                print(f"Created ListenDetector task: {task}")

                # 设置任务参数
                if params:
                    task.init()

                # 关联模块
                if modules:
                    from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
                    pam_memory = modules.get(ModuleName.PAMemory)
                    if pam_memory:
                        task.set_associated_module(pam_memory, "PAM")

                    sensory_memory = modules.get(ModuleName.SensoryMemory)
                    if sensory_memory:
                        task.set_associated_module(sensory_memory, "SensoryMemory")

                    environment = modules.get(ModuleName.Environment)
                    if environment:
                        task.set_associated_module(environment, "Environment")

                return task
            except Exception as e:
                self.logger.error(f"Error creating ListenDetector task: {e}")
                import traceback
                traceback.print_exc()
                return None

        # 其他任务类型的处理...
        # 这里可以添加其他任务类型的处理

        elif task_type == "HealthDetector":
            try:
                from linars.edu.memphis.ccrg.lida.Alifeagent.Featuredetectors.health_detector import HealthDetector
                task = HealthDetector()
                # print(f"Created HealthDetector task: {task}")

                # 设置任务参数
                if params:
                    task.init(params)

                # 关联模块
                if modules:
                    from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
                    pam_memory = modules.get(ModuleName.PAMemory)
                    if pam_memory:
                        task.set_associated_module(pam_memory, "PAM")

                    sensory_memory = modules.get(ModuleName.SensoryMemory)
                    if sensory_memory:
                        task.set_associated_module(sensory_memory, "SensoryMemory")

                    environment = modules.get(ModuleName.Environment)
                    if environment:
                        task.set_associated_module(environment, "Environment")

                return task
            except Exception as e:
                # self.logger.error(f"Error creating HealthDetector task: {e}")
                import traceback
                traceback.print_exc()
                return None

        elif task_type == "ObjectDetector":
            try:
                from linars.edu.memphis.ccrg.lida.Alifeagent.Featuredetectors.object_detector import ObjectDetector
                task = ObjectDetector()
                # print(f"Created ObjectDetector task: {task}")

                # 设置任务参数
                if params:
                    task.init(params)

                # 关联模块
                if modules:
                    from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
                    pam_memory = modules.get(ModuleName.PAMemory)
                    if pam_memory:
                        task.set_associated_module(pam_memory, "PAM")

                    sensory_memory = modules.get(ModuleName.SensoryMemory)
                    if sensory_memory:
                        task.set_associated_module(sensory_memory, "SensoryMemory")

                    environment = modules.get(ModuleName.Environment)
                    if environment:
                        task.set_associated_module(environment, "Environment")

                return task
            except Exception as e:
                # self.logger.error(f"Error creating ObjectDetector task: {e}")
                import traceback
                traceback.print_exc()
                return None

        elif task_type == "VarManagerTask":
            try:
                from linars.edu.memphis.ccrg.lida.PAM.Tasks.VarManagerTask import VarManagerTask
                task = VarManagerTask()
                # print(f"Created VarManagerTask task: {task}")

                # 设置任务参数
                if params:
                    task.init(params)

                # 关联模块
                if modules:
                    from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
                    pam_memory = modules.get(ModuleName.PAMemory)
                    if pam_memory:
                        task.set_associated_module(pam_memory, "PAM")

                return task
            except Exception as e:
                self.logger.error(f"Error creating VarManagerTask task: {e}")
                import traceback
                traceback.print_exc()
                return None

        elif task_type == "GoalBackgroundTask":
            try:
                from linars.edu.memphis.ccrg.lida.PAM.Tasks.GoalBackgroundTask import GoalBackgroundTask
                task = GoalBackgroundTask()
                print(f"Created GoalBackgroundTask task: {task}")

                # 设置任务参数
                if params:
                    task.init(params)

                # 关联模块
                if modules:
                    from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
                    pam_memory = modules.get(ModuleName.PAMemory)
                    if pam_memory:
                        task.set_associated_module(pam_memory, "PAM")

                return task
            except Exception as e:
                self.logger.error(f"Error creating GoalBackgroundTask task: {e}")
                import traceback
                traceback.print_exc()
                return None

        elif task_type == "UpdateCsmBackgroundTask":
            try:
                from linars.edu.memphis.ccrg.lida.Workspace.UpdateCsmBackgroundTask import UpdateCsmBackgroundTask
                task = UpdateCsmBackgroundTask()
                # print(f"Created UpdateCsmBackgroundTask task: {task}")

                # 设置任务参数
                if params:
                    task.init(params)

                # 关联模块
                if modules:
                    from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
                    workspace = modules.get(ModuleName.Workspace)
                    if workspace:
                        task.set_associated_module(workspace, "Workspace")

                return task
            except Exception as e:
                self.logger.error(f"Error creating UpdateCsmBackgroundTask task: {e}")
                import traceback
                traceback.print_exc()
                return None

        elif task_type == "SensoryMemoryBackgroundTask":
            try:
                from linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemoryBackgroundTask import SensoryMemoryBackgroundTask
                task = SensoryMemoryBackgroundTask()
                # print(f"Created SensoryMemoryBackgroundTask task: {task}")

                # 设置任务参数
                if params:
                    task.init()

                # 关联模块
                if modules:
                    from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
                    sensory_memory = modules.get(ModuleName.SensoryMemory)
                    if sensory_memory:
                        task.set_associated_module(sensory_memory, "SensoryMemory")

                return task
            except Exception as e:
                self.logger.error(f"Error creating SensoryMemoryBackgroundTask task: {e}")
                import traceback
                traceback.print_exc()
                return None

        # 如果没有匹配的任务类型，返回空
        self.logger.warning(f"Unknown task type: {task_type}")
        return None

    def get_node(self, node_type: str = None, name: str = "Node") -> Optional[Node]:
        """
        创建指定类型的节点

        参数:
            node_type: 要创建的节点类型
            name: 节点名称

        返回:
            创建的节点，如果创建失败则返回None
        """
        # This is a simplified implementation
        # In a real implementation, we would create the node based on the node type
        return None

    def get_link(self, link_type: str, source: Node, sink: Any, category: LinkCategory) -> Optional[Link]:
        """
        创建指定类型的链接

        参数:
            link_type: 要创建的链接类型
            source: 链接的源节点
            sink: 链接的目标
            category: 链接类别

        返回:
            创建的链接，如果创建失败则返回None
        """
        # This is a simplified implementation
        # In a real implementation, we would create the link based on the link type
        return None
