# LIDA认知框架
"""
基础行为选择模块，选择激活度最高的行为。
BehaviorNetwork的实现比这个更复杂，是更优的选择。
"""

import logging
from typing import List, Set, Dict, Any, Collection, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.Framework.Strategies.DecayStrategy import DecayStrategy
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener import BroadcastListener
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.ProceduralMemory.ProceduralMemoryListener import ProceduralMemoryListener
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Scheme import Scheme
from linars.edu.memphis.ccrg.lida.ActionSelection.ActionSelection import ActionSelection
from linars.edu.memphis.ccrg.lida.ActionSelection.ActionSelectionListener import ActionSelectionListener
from linars.edu.memphis.ccrg.lida.ActionSelection.PreafferenceListener import PreafferenceListener
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action
from linars.edu.memphis.ccrg.lida.ActionSelection.Behavior import Behavior

class BasicActionSelection(FrameworkModuleImpl, ActionSelection, ProceduralMemoryListener, BroadcastListener):
    """
    基础行为选择模块，选择激活度最高的行为。
    BehaviorNetwork的实现比这个更复杂，是更优的选择。
    """

    # 默认值
    DEFAULT_REFRACTORY_PERIOD = 80
    DEFAULT_CANDIDATE_THRESHOLD = 0.8
    DEFAULT_REMOVAL_THRESHOLD = 0.1
    DEFAULT_TICKS_PER_RUN = 10
    DEFAULT_THRESHOLD_DECAY_RATE = 0.1

    def __init__(self):
        """
        初始化基础行为选择模块
        """
        super().__init__()
        self.listeners: List[ActionSelectionListener] = []
        self.preafference_listeners: List[PreafferenceListener] = []
        self.behaviors: Set[Behavior] = set()
        self.max_activation_threshold = 0.0
        self.behavior_decay_strategy = None
        self.refractory_period_ticks = self.DEFAULT_REFRACTORY_PERIOD
        self.candidate_threshold = self.DEFAULT_CANDIDATE_THRESHOLD
        self.default_removal_threshold = self.DEFAULT_REMOVAL_THRESHOLD
        self.ticks_per_run = self.DEFAULT_TICKS_PER_RUN
        self.threshold_decay_rate = self.DEFAULT_THRESHOLD_DECAY_RATE
        self.logger = logging.getLogger(self.__class__.__name__)

    def init(self, params=None) -> None:
        """
        初始化基础行为选择模块
        参数:
            params: 初始化参数，默认为None
        """
        super().init(params)
        print("BasicActionSelection.init()")  # 打印初始化调试信息
        self.refractory_period_ticks = self.get_param("actionSelection.refractoryPeriodTicks", self.DEFAULT_REFRACTORY_PERIOD)
        self.candidate_threshold = self.get_param("actionSelection.candidateThreshold", self.DEFAULT_CANDIDATE_THRESHOLD)
        self.max_activation_threshold = self.candidate_threshold
        self.default_removal_threshold = self.get_param("actionSelection.removalThreshold", self.DEFAULT_REMOVAL_THRESHOLD)
        self.ticks_per_run = self.get_param("actionSelection.backgroundTaskTicksPerRun", self.DEFAULT_TICKS_PER_RUN)

        from linars.edu.memphis.ccrg.lida.ActionSelection.ActionSelectionBackgroundTask import ActionSelectionBackgroundTask
        self.get_assisting_task_spawner().add_task(ActionSelectionBackgroundTask(self.ticks_per_run, self))

        decay_type = self.get_param("actionSelection.behaviorDecayStrategy", "defaultDecay")
        self.behavior_decay_strategy = self.get_decay_strategy(decay_type)
        self.threshold_decay_rate = self.get_param("actionSelection.thresholdDecayRate", self.DEFAULT_THRESHOLD_DECAY_RATE)

    def get_param(self, name: str, default_value: Any) -> Any:
        """
        获取带默认值的参数

        参数:
            name: 参数名称
            default_value: 默认值

        返回:
            参数值或默认值
        """
        parameters = getattr(self, "parameters", {})
        if parameters and name in parameters:
            return parameters[name]
        return default_value

    def get_decay_strategy(self, name: str) -> DecayStrategy:
        """
        根据名称获取衰减策略
        参数:
            name: 衰减策略名称
        返回:
            衰减策略实例
        """
        # 实际实现会使用工厂获取衰减策略，目前返回默认策略
        from linars.edu.memphis.ccrg.lida.Framework.Strategies.DefaultDecayStrategy import DefaultDecayStrategy
        return DefaultDecayStrategy()

    def add_listener(self, listener: ModuleListener) -> None:
        """
        添加监听器到基础行为选择模块
        参数:
            listener: 要添加的监听器
        """
        if isinstance(listener, ActionSelectionListener):
            self.add_action_selection_listener(listener)
        elif isinstance(listener, PreafferenceListener):
            self.add_preafference_listener(listener)
        else:
            self.logger.warning(f"无法在tick {TaskManager.get_current_tick()}添加监听器 {listener}")

    def add_action_selection_listener(self, listener: ActionSelectionListener) -> None:
        """
        添加指定的行为选择监听器

        参数:
            listener: 接收来自行为选择模块的选定行为的模块
        """
        self.listeners.append(listener)

    def add_preafference_listener(self, listener: PreafferenceListener) -> None:
        """
        添加指定的预偏好监听器

        参数:
            listener: 接收来自行为选择模块的预偏好的模块
        """
        self.preafference_listeners.append(listener)

    def receive_behavior(self, behavior: Behavior) -> None:
        """
        从程序性记忆接收行为

        参数:
            behavior: 要接收的行为
        """
        if behavior is not None:
            behavior.set_decay_strategy(self.behavior_decay_strategy)
            behavior.set_removal_threshold(self.default_removal_threshold)
            scheme = behavior.get_scheme()

            is_exist = False
            for existing_behavior in self.behaviors:
                if existing_behavior.get_name() == behavior.get_name():
                    is_exist = True
                    existing_behavior.set_incentive_salience(scheme.get_incentive_salience())
                    existing_behavior.set_activation(scheme.get_activation())

            if not is_exist:
                activation = scheme.get_total_activation()
                behavior.set_activation(activation)
                behavior.set_incentive_salience(scheme.get_incentive_salience())
                self.behaviors.add(behavior)

    def attempt_action_selection(self) -> Optional[Action]:
        """
        尝试选择行为
        返回:
            选定的行为，如果没有选择则返回None
        """
        behavior = self.select_behavior(self.behaviors, self.candidate_threshold)
        if behavior is not None:
            action = behavior.get_action()
            self.logger.debug(f"Action Selected: {action} at tick {TaskManager.get_current_tick()}")  # 记录行为选择日志

            # 将动作发送给所有监听器
            for listener in self.listeners:
                listener.receive_action(action)

            # 向所有预偏好监听器发送预偏好信息
            self.send_preafference(behavior)
            return action
        return None

    def select_behavior(self, behaviors: Collection[Behavior], candidate_threshold: float) -> Optional[Behavior]:
        """
        选择要执行的行为(包含一个动作)
        参数:
            behaviors: 模块当前可用的行为集合
            candidate_threshold: 行为成为候选的阈值
        返回:
            获胜的行为，如果没有选择则返回None
        """
        selected = None
        highest_activation = -1.0
        highest_incentive = -1.0

        for behavior in behaviors:
            behavior_activation = behavior.get_activation()
            behavior_incentive = behavior.get_incentive_salience()

            # 选择激活度最高且激励值最大的行为
            if (behavior_activation >= candidate_threshold * 0.08 and
                behavior_activation >= highest_activation and
                behavior_incentive > highest_incentive):
                selected = behavior
                highest_activation = behavior_activation
                highest_incentive = behavior_incentive

        return selected

    def get_behaviors(self) -> Collection[Behavior]:
        """
        返回当前行为选择模块中的行为视图

        返回:
            行为对象的集合
        """
        return self.behaviors

    def get_module_content(self, *params: Any) -> Any:
        """
        获取此模块的内容

        参数:
            params: 指定返回内容的参数

        返回:
            此模块的内容
        """
        if params and params[0] == "behaviors":
            return self.behaviors
        return None

    def learn(self, coalition: Coalition) -> None:
        """
        从联盟中学习

        参数:
            coalition: 要学习的联盟
        """
        # 本类未实现此功能
        pass

    def receive_broadcast(self, coalition: Coalition) -> None:
        """
        接收来自全局工作空间的广播

        参数:
            coalition: 赢得意识竞争的联盟
        """
        # 本类未实现此功能
        pass

    def send_preafference(self, behavior: Behavior) -> None:
        """
        向所有预偏好监听器发送预偏好信息
        参数:
            behavior: 要发送预偏好信息的行为
        """
        if behavior is None or not self.preafference_listeners:
            return

        # 从行为的添加和删除列表创建节点集合
        from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl

        add_set = NodeStructureImpl()
        for condition in behavior.get_adding_list():
            if hasattr(condition, 'get_node') and condition.get_node() is not None:
                add_set.add_default_node(condition.get_node())

        delete_set = NodeStructureImpl()
        for condition in behavior.get_deleting_list():
            if hasattr(condition, 'get_node') and condition.get_node() is not None:
                delete_set.add_default_node(condition.get_node())

        # 向所有预偏好监听器发送节点集合
        for listener in self.preafference_listeners:
            listener.receive_preafference(add_set, delete_set)

    def decay_module(self, ticks: int) -> None:
        """
        衰减此模块

        参数:
            ticks: 衰减的tick数
        """
        behaviors_to_remove = []
        for behavior in self.behaviors:
            behavior.decay(ticks)
            if behavior.is_removable():
                behaviors_to_remove.append(behavior)

        for behavior in behaviors_to_remove:
            self.behaviors.remove(behavior)


class ActionSelectionBackgroundTask(FrameworkTaskImpl):
    """
    行为选择的后台任务
    """

    def __init__(self, ticks_per_run: int, action_selection: BasicActionSelection):
        """
        初始化行为选择后台任务

        参数:
            ticks_per_run: 此任务每次运行间隔的tick数
            action_selection: 拥有此任务的基础行为选择模块
        """
        super().__init__(ticks_per_run)
        self.action_selection = action_selection

    def run_this_framework_task(self) -> None:
        """
        运行此任务
        """
        if self.action_selection.attempt_action_selection() is not None:
            self.set_next_ticks_per_run(self.action_selection.refractory_period_ticks)
            self.action_selection.candidate_threshold = self.action_selection.max_activation_threshold
        else:
            self.action_selection.candidate_threshold -= self.action_selection.threshold_decay_rate
            if self.action_selection.candidate_threshold < 0.0:
                self.action_selection.candidate_threshold = 0.0
