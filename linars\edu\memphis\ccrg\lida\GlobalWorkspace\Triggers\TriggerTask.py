# LIDA Cognitive Framework
"""
A task that triggers a broadcast.
"""

import logging
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.GlobalWorkspace import GlobalWorkspace
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.BroadcastTrigger import BroadcastTrigger

class TriggerTask(FrameworkTaskImpl):
    """
    A task that triggers a broadcast.
    """
    
    def __init__(self, ticks_per_run: int, gw: GlobalWorkspace, name: str, trigger: BroadcastTrigger):
        """
        Initialize a TriggerTask.
        
        Args:
            ticks_per_run: The number of ticks between runs of this task
            gw: The GlobalWorkspace
            name: The name of this task
            trigger: The BroadcastTrigger that created this task
        """
        super().__init__(ticks_per_run)
        self.gw = gw
        self.name = name
        self.trigger = trigger
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def run_this_framework_task(self) -> None:
        """
        Run this task.
        """
        self.gw.trigger_broadcast(self.trigger)
