# LIDA Cognitive Framework
"""
A workspace listener receives content from the workspace.
The prime example is PAM.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceContent import WorkspaceContent

class WorkspaceListener(ModuleListener):
    """
    A workspace listener receives content from the workspace.
    The prime example is PAM.
    """
    
    @abstractmethod
    def receive_workspace_content(self, originating_buffer: ModuleName, content: WorkspaceContent) -> None:
        """
        Receive NodeStructure content from ModuleType originating_buffer.
        
        Args:
            originating_buffer: Source of content
            content: Sent content
        """
        pass
