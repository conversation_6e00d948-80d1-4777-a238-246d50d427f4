# LIDA Cognitive Framework
"""
Interface for the BroadcastQueue, a WorkspaceBuffer that stores recent conscious broadcasts.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WorkspaceBuffer import WorkspaceBuffer
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceContent import WorkspaceContent

class BroadcastQueue(WorkspaceBuffer):
    """
    Interface for the BroadcastQueue, a WorkspaceBuffer that stores recent conscious broadcasts.
    """
    
    @abstractmethod
    def get_position_content(self, position: int) -> WorkspaceContent:
        """
        Get the content at the specified position in the queue.
        
        Args:
            position: The position in the queue
            
        Returns:
            The content at the specified position
        """
        pass
