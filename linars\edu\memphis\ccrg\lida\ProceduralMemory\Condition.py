# LIDA Cognitive Framework
"""
A condition that must be satisfied for a Scheme to be instantiated.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, TYPE_CHECKING
from linars.edu.memphis.ccrg.lida.Framework.Initialization.Initializable import Initializable
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.Activatible import Activatible

# 避免循环导入
if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure

class Condition(Initializable, Activatible, ABC):
    """
    A condition that must be satisfied for a Scheme to be instantiated.
    """

    @abstractmethod
    def is_satisfied(self, content: 'NodeStructure') -> bool:
        """
        Check if this condition is satisfied by the given content.

        Args:
            content: The content to check

        Returns:
            True if this condition is satisfied, False otherwise
        """
        pass

    @abstractmethod
    def get_condition_name(self) -> str:
        """
        Get the name of this condition.

        Returns:
            The name of this condition
        """
        pass

    @abstractmethod
    def set_condition_name(self, name: str) -> None:
        """
        Set the name of this condition.

        Args:
            name: The name to set
        """
        pass

    @abstractmethod
    def get_condition_id(self) -> Any:
        """
        Get the ID of this condition.

        Returns:
            The ID of this condition
        """
        pass
