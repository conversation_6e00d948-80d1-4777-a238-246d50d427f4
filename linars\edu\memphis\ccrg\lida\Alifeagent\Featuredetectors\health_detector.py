"""
Health detector implementation.

This module provides the implementation of the health detector for the ALife agent.
"""
import logging
from typing import Dict, Any, Optional

from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructureImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeImpl import NodeImpl
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.PAM.Tasks.BasicDetectionAlgorithm import BasicDetectionAlgorithm
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemory import SensoryMemory
from linars.edu.memphis.ccrg.lida.Environment.Environment import Environment

class HealthDetector(BasicDetectionAlgorithm):
    """
    Health detector implementation.

    This class implements a detector that monitors the agent's health level.
    """

    def __init__(self):
        """Initialize the health detector."""
        super().__init__()
        self.logger = logging.getLogger("HealthDetector")
        self.detector_params = {}
        self.health_node = None
        self.low_health_node = None
        self.high_health_node = None
        self.sensory_memory = None
        self.pam_memory = None

    def init(self, params=None):
        """
        Initialize the detector.

        Args:
            params: Optional parameters for initialization
        """
        super().init(params)

        # Store parameters if provided
        if params:
            for key, value in params.items():
                self.detector_params[key] = value

    def detect_linkable(self) -> Optional[NodeStructure]:
        """
        Detect the agent's health level.

        Returns:
            A node structure containing the detected health state
        """
        # Get the agent's health from sensory memory
        params = {"mode": "health"}
        health = self.sensory_memory.get_state(params)

        if health is None:
            return None

        # Create a node structure for the detected health state
        result = NodeStructureImpl()

        # Add the appropriate node based on health level
        if health < 0.3:
            # Low health
            self.low_health_node.set_activation(1.0)
            result.add_node(self.low_health_node)
        elif health > 0.7:
            # High health
            self.high_health_node.set_activation(1.0)
            result.add_node(self.high_health_node)
        else:
            # Normal health
            self.health_node.set_activation(health)
            result.add_node(self.health_node)

        return result

    def detect(self) -> float:
        """
        Detect the agent's health level.

        Returns:
            A value from 0.0 to 1.0 representing the degree to which the feature occurs.
        """
        # Get the agent's health from sensory memory
        params = {"mode": "health"}
        health = 0.5  # Default health value

        try:
            if self.sensory_memory is not None:
                state = self.sensory_memory.get_state(params)
                if state is not None and isinstance(state, (int, float)):
                    health = float(state)
        except Exception as e:
            self.logger.warning(f"Error getting health state: {e}")

        # Ensure health is between 0 and 1
        health = max(0.0, min(1.0, health))

        # Excite the appropriate node based on health level
        try:
            if health < 0.3:
                # Low health
                if self.pam_memory is not None and self.low_health_node is not None:
                    self.pam_memory.excite(self.low_health_node.get_id(), 1.0, "HealthDetector")
            elif health > 0.7:
                # High health
                if self.pam_memory is not None and self.high_health_node is not None:
                    self.pam_memory.excite(self.high_health_node.get_id(), 1.0, "HealthDetector")
            else:
                # Normal health
                if self.pam_memory is not None and self.health_node is not None:
                    self.pam_memory.excite(self.health_node.get_id(), health, "HealthDetector")
        except Exception as e:
            self.logger.warning(f"Error exciting health nodes: {e}")

        return health

    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module.

        Args:
            module: The module to associate
            module_usage: The usage of the module
        """
        if module_usage == "PAMemory" or module_usage == "PAM":
            self.pam_memory = module
            self.logger.info(f"Set PAM memory: {module}")
        elif module_usage == "SensoryMemory":
            self.sensory_memory = module
            self.logger.info(f"Set sensory memory: {module}")
        elif module_usage == "Environment":
            self.environment = module
            self.logger.info(f"Set environment: {module}")
        else:
            self.logger.warning(f"Cannot set associated module {module}, type not recognized")
