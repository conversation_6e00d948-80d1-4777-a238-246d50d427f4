# LIDA认知框架
"""
Action的默认实现
"""

from typing import Dict, Any
from linars.edu.memphis.ccrg.lida.Framework.Initialization.InitializableImpl import InitializableImpl
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action

class ActionImpl(InitializableImpl, Action):
    """
    Action的默认实现
    """

    # ID生成器的类变量
    _id_generator = 0

    def __init__(self, name: str = None):
        """
        初始化ActionImpl

        参数:
            name: 此行为的名称
        """
        super().__init__()
        self.id = ActionImpl._id_generator
        ActionImpl._id_generator += 1
        self.label = None
        self.name = name

    def get_name(self) -> str:
        """
        获取此行为的名称

        返回:
            此行为的名称
        """
        return self.name

    def set_name(self, name: str) -> None:
        """
        设置此行为的名称

        参数:
            name: 要设置的名称
        """
        self.name = name

    def get_label(self) -> str:
        """
        获取此行为的标签

        返回:
            此行为的标签
        """
        return self.label

    def set_label(self, label: str) -> None:
        """
        设置此行为的标签

        参数:
            label: 要设置的标签
        """
        self.label = label

    def get_id(self) -> int:
        """
        获取此行为的ID

        返回:
            此行为的ID
        """
        return self.id

    def set_id(self, id: int) -> None:
        """
        设置此行为的ID

        参数:
            id: 要设置的ID
        """
        self.id = id

    def __str__(self) -> str:
        """
        返回此行为的字符串表示

        返回:
            此行为的名称
        """
        return self.name

    def init(self, params: Dict[str, Any]) -> None:
        """
        使用给定参数初始化此行为

        参数:
            params: 初始化参数
        """
        super().init(params)
        if "name" in params:
            self.name = params["name"]
        if "label" in params:
            self.label = params["label"]
        if "id" in params:
            self.id = int(params["id"])
