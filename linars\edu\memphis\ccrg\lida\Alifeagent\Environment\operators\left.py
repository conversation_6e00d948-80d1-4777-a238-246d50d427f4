"""
Left operator implementation.

This module provides the implementation of the left operator for the ALife agent.
"""
from typing import List, Optional, TYPE_CHECKING

from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.entity.task import Task
from linars.org.opennars.interfaces.timable import Timable
from linars.org.opennars.operator.operation import Operation
from linars.org.opennars.operator.operator import Operator

if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.linars.memory import Memory

class Left(Operator):
    """
    Left operator implementation.

    This class implements the left operator, which makes the agent turn left.
    """

    def __init__(self):
        """Initialize the left operator."""
        super().__init__("^left")

    def execute(self, operation: Operation, args: List[Term], memory, time: Timable) -> Optional[List[Task]]:
        """
        Execute the left operator.

        Args:
            operation: The operation to execute
            args: Arguments for the operation
            memory: The memory in which the operation is executed
            time: The time

        Returns:
            None, as this operator doesn't produce tasks
        """
        # Execute the left operation in the environment
        memory.emit_operator("left")

        return None
