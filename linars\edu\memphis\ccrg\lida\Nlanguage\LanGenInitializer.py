#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
LanGenInitializer class for the LIDA framework.
"""

import logging
from typing import Dict, Any, Optional, TypeVar, Generic, Type

from linars.edu.memphis.ccrg.lida.Framework.Initialization.FullyInitializable import FullyInitializable
from linars.edu.memphis.ccrg.lida.Framework.Initialization.global_initializer import GlobalInitializer
from linars.edu.memphis.ccrg.lida.Framework.Initialization.initializer import Initializer
from linars.edu.memphis.ccrg.lida.Framework.agent import Agent

T = TypeVar('T')

class LanGenInitializer(Initializer):
    """
    Initializer for the LanGen module.
    """

    def __init__(self):
        """
        Initialize a LanGenInitializer.
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.langen = None

    def initModule(self, m: FullyInitializable, a: Agent, params: Dict[str, Any]) -> None:
        """
        Initialize a module.

        Args:
            m: The module to initialize
            a: The agent
            params: The parameters for initialization
        """
        self.langen = m
        label = params.get("linkCategory")
        if label:
            o = GlobalInitializer.get_instance().get_attribute(label.strip())

    # 删除不需要的方法，因为我们已经有了initModule方法
