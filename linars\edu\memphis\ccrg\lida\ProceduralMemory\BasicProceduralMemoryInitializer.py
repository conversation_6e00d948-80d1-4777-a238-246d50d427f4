# LIDA Cognitive Framework
"""
Basic ProceduralMemory Initializer which reads String parameters beginning with 'scheme.'
and creates a scheme based on the parameter.
"""

import logging
from typing import Dict, Any, Optional, TypeVar, Generic, Type

from linars.edu.memphis.ccrg.lida.Framework.Initialization.initializer import Initializer
from linars.edu.memphis.ccrg.lida.Framework.Initialization.FullyInitializable import FullyInitializable
from linars.edu.memphis.ccrg.lida.Framework.agent import Agent
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.ProceduralMemory.ProceduralMemory import ProceduralMemory
from linars.edu.memphis.ccrg.lida.ProceduralMemory.SchemeImpl import SchemeImpl

T = TypeVar('T')

class BasicProceduralMemoryInitializer(Initializer):
    """
    Basic ProceduralMemory Initializer which reads String parameters beginning with 'scheme.'
    and creates a scheme based on the parameter.
    The definition is: schemeLabel|(contextNode1, contextNode2,...)(contextLink1, contextLink2,...)|actionName|(resultNode1, resultNode2,...)(resultLink1, resultLink2,...)|baseLevelActivation
    """

    def __init__(self):
        """
        Initialize a BasicProceduralMemoryInitializer.
        """
        self.logger = logging.getLogger(self.__class__.__name__)

    def initModule(self, module: FullyInitializable, agent: Agent, params: Dict[str, Any]) -> None:
        """
        Initialize the module.

        Args:
            module: The module to initialize
            agent: The agent
            params: The parameters for initialization
        """
        # print(f"BasicProceduralMemoryInitializer.initModule() called with module: {module}, agent: {agent}, params: {params}")
        # print(f"BasicProceduralMemoryInitializer.initModule() called with module: {module}")
        self.init(module, params)

    def init(self, module: ProceduralMemory, params: Dict[str, Any]) -> None:
        """
        Initialize the module.

        Args:
            module: The module to initialize
            params: The parameters for initialization
        """
        for key in params.keys():
            if key.startswith("scheme."):
                value = params.get(key)
                scheme_def = ""
                if isinstance(value, str):
                    scheme_def = value
                else:
                    self.logger.warning(f"Parameter name: {key} started with scheme. but did not contain a valid def at tick {TaskManager.get_current_tick()}")
                    continue

                self.logger.info(f"Loading scheme: {scheme_def} at tick {TaskManager.get_current_tick()}")

                try:
                    # Parse the scheme definition
                    # Format: schemeLabel|(contextNode1, contextNode2,...)(contextLink1, contextLink2,...)|actionName|(resultNode1, resultNode2,...)(resultLink1, resultLink2,...)|baseLevelActivation
                    parts = scheme_def.split("|")
                    if len(parts) < 5:
                        self.logger.warning(f"Scheme definition {scheme_def} does not have enough parts at tick {TaskManager.get_current_tick()}")
                        continue

                    scheme_label = parts[0].strip()
                    context_nodes_str = parts[1].strip()
                    action_name = parts[2].strip()
                    result_nodes_str = parts[3].strip()
                    base_level_activation_str = parts[4].strip()

                    # Parse base level activation
                    try:
                        base_level_activation = float(base_level_activation_str)
                    except ValueError:
                        # self.logger.warning(f"Invalid base level activation {base_level_activation_str} for scheme {scheme_label} at tick {TaskManager.get_current_tick()}")
                        base_level_activation = 0.0

                    # Create the scheme
                    scheme = SchemeImpl()
                    scheme.set_label(scheme_label)
                    scheme.set_action_name(action_name)
                    scheme.set_base_activation(base_level_activation)

                    # Add the scheme to the procedural memory
                    module.add_scheme(scheme)

                    self.logger.info(f"Added scheme {scheme_label} to procedural memory at tick {TaskManager.get_current_tick()}")
                except Exception as e:
                    self.logger.error(f"Error creating scheme from definition {scheme_def}: {e} at tick {TaskManager.get_current_tick()}")

    # 删除不需要的方法，因为我们已经有了initModule方法
