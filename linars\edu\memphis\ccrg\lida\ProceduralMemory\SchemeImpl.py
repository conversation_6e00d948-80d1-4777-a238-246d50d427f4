# LIDA Cognitive Framework
"""
Default implementation of Scheme.
"""

import logging
from typing import Dict, Any, List, Collection, Optional
from enum import Enum
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.LearnableImpl import LearnableImpl
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Scheme import Scheme
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Condition import Condition

class ConditionType(Enum):
    """
    The possible type of usage for a condition inside a Scheme.
    """
    CONTEXT = 0
    ADDINGLIST = 1
    DELETINGLIST = 2
    NEGATEDCONTEXT = 3

class SchemeImpl(LearnableImpl, Scheme):
    """
    Default implementation of Scheme.
    """

    # Class variable for ID generator
    _id_generator = 0

    # Default values
    DEFAULT_RELIABILITY_THRESHOLD = 0.5

    def __init__(self, name: str = None):
        """
        Initialize a SchemeImpl.

        Args:
            name: The name of this scheme
        """
        super().__init__()
        self.scheme_id = SchemeImpl._id_generator
        SchemeImpl._id_generator += 1
        self.name = name
        self.label = name
        self.action = None
        self.action_name = None  # 添加action_name属性
        self.context_conditions: List[Condition] = []
        self.adding_list: List[Condition] = []
        self.deleting_list: List[Condition] = []
        self.num_executions = 0
        self.num_successful_executions = 0
        self.is_innate = False
        self.reliability_threshold = self.DEFAULT_RELIABILITY_THRESHOLD
        self.logger = logging.getLogger(self.__class__.__name__)

    def get_id(self) -> int:
        """
        Get the ID of this scheme.

        Returns:
            The ID of this scheme
        """
        return self.scheme_id

    def set_id(self, id: int) -> None:
        """
        Set the ID of this scheme.

        Args:
            id: The ID to set
        """
        self.scheme_id = id

    def get_name(self) -> str:
        """
        Get the name of this scheme.

        Returns:
            The name of this scheme
        """
        return self.name

    def set_name(self, name: str) -> None:
        """
        Set the name of this scheme.

        Args:
            name: The name to set
        """
        self.name = name

    def get_action(self) -> Action:
        """
        Get the action of this scheme.

        Returns:
            The action of this scheme
        """
        return self.action

    def set_action(self, action: Action) -> None:
        """
        Set the action of this scheme.

        Args:
            action: The action to set
        """
        self.action = action

    def get_action_name(self) -> str:
        """
        Get the action name of this scheme.

        Returns:
            The action name of this scheme
        """
        return self.action_name

    def set_action_name(self, action_name: str) -> None:
        """
        Set the action name of this scheme.

        Args:
            action_name: The action name to set
        """
        self.action_name = action_name

    def get_base_activation(self) -> float:
        """
        Get the base activation of this scheme.

        Returns:
            The base activation of this scheme
        """
        return self.base_activation if hasattr(self, 'base_activation') else 0.0

    def set_base_activation(self, base_activation: float) -> None:
        """
        Set the base activation of this scheme.

        Args:
            base_activation: The base activation to set
        """
        self.base_activation = base_activation

    def get_context_conditions(self) -> Collection[Condition]:
        """
        Get the context conditions of this scheme.

        Returns:
            The context conditions of this scheme
        """
        return self.context_conditions

    def add_context_condition(self, condition: Condition) -> None:
        """
        Add a context condition to this scheme.

        Args:
            condition: The condition to add
        """
        self.context_conditions.append(condition)

    def get_adding_list(self) -> Collection[Condition]:
        """
        Get the adding list of this scheme.

        Returns:
            The adding list of this scheme
        """
        return self.adding_list

    def add_to_adding_list(self, condition: Condition) -> None:
        """
        Add a condition to the adding list of this scheme.

        Args:
            condition: The condition to add
        """
        self.adding_list.append(condition)

    def get_deleting_list(self) -> Collection[Condition]:
        """
        Get the deleting list of this scheme.

        Returns:
            The deleting list of this scheme
        """
        return self.deleting_list

    def add_to_deleting_list(self, condition: Condition) -> None:
        """
        Add a condition to the deleting list of this scheme.

        Args:
            condition: The condition to add
        """
        self.deleting_list.append(condition)

    def get_label(self) -> str:
        """
        Get the label of this scheme.

        Returns:
            The label of this scheme
        """
        return self.label

    def set_label(self, label: str) -> None:
        """
        Set the label of this scheme.

        Args:
            label: The label to set
        """
        self.label = label

    def add_condition(self, condition: Condition, condition_type: ConditionType) -> bool:
        """
        Add a condition to this scheme.

        Args:
            condition: The condition to add
            condition_type: The type of the condition

        Returns:
            True if the condition was added, False otherwise
        """
        if condition is None:
            return False

        if condition_type == ConditionType.CONTEXT:
            self.add_context_condition(condition)
            return True
        elif condition_type == ConditionType.ADDINGLIST:
            self.add_to_adding_list(condition)
            return True
        elif condition_type == ConditionType.DELETINGLIST:
            self.add_to_deleting_list(condition)
            return True

        return False

    def action_executed(self) -> None:
        """
        Called when Scheme's action is executed.
        Scheme should update the number of times its action has been executed in order to calculate
        reliability.
        """
        self.num_executions += 1

    def get_num_executions(self) -> int:
        """
        Get the number of executions.

        Returns:
            How many times this scheme's action has been executed
        """
        return self.num_executions

    def action_successful(self) -> None:
        """
        Called when Scheme's action produces expected result.
        """
        self.num_successful_executions += 1

    def get_reliability(self) -> float:
        """
        Get the reliability of this scheme.

        Returns:
            Frequency that result is observed after scheme's Action is taken
        """
        if self.num_executions == 0:
            return 0.0
        return self.num_successful_executions / self.num_executions

    def is_reliable(self) -> bool:
        """
        Check if this scheme is reliable.

        Returns:
            True if reliability is over threshold, False otherwise
        """
        return self.get_reliability() >= self.reliability_threshold

    def set_innate(self, innate: bool) -> None:
        """
        Set whether this scheme is innate.

        Args:
            innate: Whether this Scheme is hard-wired and cannot be decayed
        """
        self.is_innate = innate

    def is_innate(self) -> bool:
        """
        Check if this scheme is innate.

        Returns:
            True if this scheme should not be decayed, False otherwise
        """
        return self.is_innate

    def __eq__(self, other) -> bool:
        """
        Check if this scheme is equal to another.

        Args:
            other: The other scheme to compare with

        Returns:
            True if the schemes have the same ID, False otherwise
        """
        if isinstance(other, SchemeImpl):
            return self.scheme_id == other.get_id()
        return False

    def __hash__(self) -> int:
        """
        Return the hash code of this scheme.

        Returns:
            The hash code of the scheme ID
        """
        return self.scheme_id

    def __str__(self) -> str:
        """
        Return the string representation of this scheme.

        Returns:
            The name of this scheme with its ID
        """
        if self.name is None:
            return f"Scheme-{self.scheme_id}"
        return f"{self.name}-{self.scheme_id}"
