# LIDA认知框架
"""
可链接对象的通用ID。可以表示节点(Node)或链接(Link)。
"""

from typing import Optional

class ExtendedId:
    """
    可链接对象的通用ID。可以表示节点(Node)或链接(Link)。
    """

    def __init__(self, source_id: int = 0, sink_id: Optional['ExtendedId'] = None, category_id: int = 0):
        """
        初始化扩展ID

        参数:
            source_id: 源ID
            sink_id: 目标ID
            category_id: 类别ID
        """
        self.source_id = source_id
        self.sink_id = sink_id
        self.category_id = category_id

    def is_node_id(self) -> bool:
        """
        返回此扩展ID是否表示节点

        返回:
            如果表示节点返回True，否则返回False
        """
        return self.sink_id is None

    def get_source_node_id(self) -> int:
        """
        获取源节点ID

        返回:
            源节点ID
        """
        return self.source_id

    def get_sink_id(self) -> Optional['ExtendedId']:
        """
        获取目标ID

        返回:
            目标ID
        """
        return self.sink_id

    def get_category_id(self) -> int:
        """
        获取类别ID

        返回:
            类别ID
        """
        return self.category_id

    def __eq__(self, other) -> bool:
        """
        检查此扩展ID是否与另一个相等

        参数:
            other: 要比较的另一个扩展ID

        返回:
            如果相等返回True，否则返回False
        """
        if not isinstance(other, ExtendedId):
            return False

        if self.is_node_id() and other.is_node_id():
            return self.source_id == other.source_id

        if not self.is_node_id() and not other.is_node_id():
            return (self.source_id == other.source_id and
                    self.sink_id == other.sink_id and
                    self.category_id == other.category_id)

        return False

    def __hash__(self) -> int:
        """
        返回此扩展ID的哈希码

        返回:
            哈希码
        """
        if self.is_node_id():
            return self.source_id

        result = 17
        result = 37 * result + self.source_id
        result = 37 * result + hash(self.sink_id)
        result = 37 * result + self.category_id
        return result

    def __str__(self) -> str:
        """
        返回此扩展ID的字符串表示

        返回:
            字符串表示
        """
        if self.is_node_id():
            return f"Node[{self.source_id}]"

        return f"Link[{self.source_id}, {self.sink_id}, {self.category_id}]"
