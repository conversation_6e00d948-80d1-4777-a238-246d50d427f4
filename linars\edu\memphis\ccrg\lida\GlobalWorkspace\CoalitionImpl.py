# LIDA Cognitive Framework
"""
The default implementation of Coalition. Wraps content entering the
GlobalWorkspace to compete for consciousness. Extends
ActivatibleImpl. Contains reference to the AttentionCodelet
that created it.
"""

import logging
from typing import Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.ActivatibleImpl import ActivatibleImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.AttentionCodelets.AttentionCodelet import AttentionCodelet

class CoalitionImpl(ActivatibleImpl, Coalition):
    """
    The default implementation of Coalition. Wraps content entering the
    GlobalWorkspace to compete for consciousness. Extends
    ActivatibleImpl. Contains reference to the AttentionCodelet
    that created it.
    """

    # Class variable for ID counter
    _id_counter = 0
    
    def __init__(self, content: Any = None, codelet: Optional[AttentionCodelet] = None):
        """
        Initialize a CoalitionImpl.
        
        Args:
            content: The content of this Coalition
            codelet: The AttentionCodelet that created this Coalition
        """
        super().__init__()
        self.id = CoalitionImpl._id_counter
        CoalitionImpl._id_counter += 1
        self.broadcast_content = content
        self.codelet = codelet
        self.logger = logging.getLogger(self.__class__.__name__)
        
        if content is not None and codelet is not None:
            self.set_coalition_activation()
    
    def get_content(self) -> Any:
        """
        Get the content of this Coalition.
        
        Returns:
            The content of this Coalition
        """
        return self.broadcast_content
    
    def set_content(self, content: Any) -> None:
        """
        Set the content of this Coalition.
        
        Args:
            content: The content to set
        """
        self.broadcast_content = content
        if self.codelet is not None:
            self.set_coalition_activation()
    
    def get_creating_attention_codelet(self) -> Optional[AttentionCodelet]:
        """
        Get the AttentionCodelet that created this Coalition.
        
        Returns:
            The AttentionCodelet that created this Coalition
        """
        return self.codelet
    
    def set_creating_attention_codelet(self, codelet: AttentionCodelet) -> None:
        """
        Set the AttentionCodelet that created this Coalition.
        
        Args:
            codelet: The AttentionCodelet that created this Coalition
        """
        self.codelet = codelet
        if self.broadcast_content is not None:
            self.set_coalition_activation()
    
    def get_id(self) -> int:
        """
        Get the ID of this Coalition.
        
        Returns:
            The ID of this Coalition
        """
        return self.id
    
    def set_coalition_activation(self) -> None:
        """
        Sets the coalition's activation to the average total activation and
        total incentive salience of the broadcast content
        multiplied by the attention codelet's base-level activation.
        """
        activation = 0.0
        if isinstance(self.broadcast_content, NodeStructure):
            linkable_count = self.broadcast_content.get_linkable_count()
            if linkable_count != 0:
                salience_sum = 0.0
                for linkable in self.broadcast_content.get_linkables():
                    if isinstance(linkable, Linkable):
                        salience_sum += linkable.get_total_activation() + abs(linkable.get_incentive_salience())
                # Divide by 2 since each Linkable could contribute at most 2.
                # activation = self.codelet.get_base_level_activation() * salience_sum / linkable_count
                activation = 0.5 * salience_sum
        self.set_activation(activation)
    
    def __eq__(self, other) -> bool:
        """
        Check if this Coalition is equal to another.
        
        Args:
            other: The other Coalition to compare with
            
        Returns:
            True if the Coalitions have the same ID, False otherwise
        """
        if isinstance(other, CoalitionImpl):
            return self.id == other.get_id()
        return False
    
    def __hash__(self) -> int:
        """
        Return the hash code of this Coalition.
        
        Returns:
            The hash code of the Coalition ID
        """
        return self.id
