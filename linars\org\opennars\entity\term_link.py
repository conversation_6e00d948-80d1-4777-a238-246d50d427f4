"""
TermLink将复合术语连接到组件或其他复合术语

这个类主要用于inference.RuleTable中，将前提分派给推理规则
"""
import array
import numpy as np
from typing import Optional, List, Any, Tuple, Union, TypeVar

from linars.org.opennars.entity.item import Item
from linars.org.opennars.entity.t_link import TLink
from linars.org.opennars.io.symbols import TO_COMPONENT_1, TO_COMPONENT_2, TO_COMPOUND_1, TO_COMPOUND_2

T = TypeVar('T')

class TermLink(Item, TLink['Term']):
    """
    复合术语与组件或其他复合术语之间的链接

    TermLink将当前术语链接到目标术语，目标术语要么是当前术语的组件，要么是由当前术语组成的复合术语。
    这两个术语都不包含与其他术语共享的变量。
    索引值指示复合术语中组件的位置。
    """
    # 链接类型常量
    SELF = 0                    # 在C处，指向C；仅TaskLink
    COMPONENT = 1               # 在(&&, A, C)处，指向C
    COMPOUND = 2                # 在C处，指向(&&, A, C)
    COMPONENT_STATEMENT = 3     # 在<C --> A>处，指向C
    COMPOUND_STATEMENT = 4      # 在C处，指向<C --> A>
    COMPONENT_CONDITION = 5     # 在<(&&, C, B) ==> A>处，指向C
    COMPOUND_CONDITION = 6      # 在C处，指向<(&&, C, B) ==> A>
    TRANSFORM = 8               # 在C处，指向<(*, C, B) --> A>；仅TaskLink
    TEMPORAL = 9                # 在C处，指向B，可能没有共同的子术语

    def __init__(self, target=None, link_type=SELF, indices=None, budget=None):
        """
        构造函数

        参数:
            target: 目标术语
            link_type: 链接类型
            indices: 在复合术语中的索引列表，可以是1到4个
            budget: 预算值
        """
        super().__init__(budget)
        self.target = target    # 目标术语
        self.type = link_type   # 链接类型

        # 处理索引
        if indices is None:
            self.index = None
        elif isinstance(indices, (int, np.int16, np.int32, np.int64)):
            # 单个索引值
            self.index = [indices]
        elif isinstance(indices, (list, tuple)):
            # 索引列表
            self.index = list(indices)
        else:
            # 其他情况，设为None
            self.index = None

        # 如果是COMPOUND_CONDITION类型，第一个索引默认为0
        if self.type == self.COMPOUND_CONDITION and self.index is not None:
            self.index = [0] + self.index

        # 计算哈希值
        self.hash = self.init_hash()

    @classmethod
    def create_from_template(cls, target, template, budget_value):
        """
        从模板创建实际的TermLink

        参数:
            target: 目标术语
            template: 之前准备的TermLink模板
            budget_value: 链接的预算值

        返回:
            TermLink: 新创建的TermLink
        """
        link_type = (template.type - 1) if template.target == target else template.type
        return cls(target, link_type, template.index, budget_value)

    def get_target(self):
        """
        获取目标术语

        返回:
            Term: 目标术语
        """
        return self.target

    def get_type(self) -> int:
        """
        获取链接类型

        返回:
            int: 链接类型
        """
        return self.type

    def get_index(self, i: int) -> int:
        """
        按级别获取一个索引

        参数:
            i: 索引级别

        返回:
            int: 索引值，如果不存在则返回-1
        """
        if self.index is not None and i < len(self.index):
            return self.index[i]
        return -1

    def get_priority(self) -> float:
        """
        获取优先级

        返回:
            float: 优先级
        """
        return self.budget.get_priority() if hasattr(self, 'budget') and self.budget else 0.0

    def name(self):
        """
        获取链接名称

        返回:
            TermLink: 返回自身
        """
        return self

    def new_key_prefix(self) -> str:
        """
        生成新的键前缀

        返回:
            str: 键前缀
        """
        if (self.type % 2) == 1:  # 指向组件
            at1 = TO_COMPONENT_1
            at2 = TO_COMPONENT_2
        else:  # 指向复合
            at1 = TO_COMPOUND_1
            at2 = TO_COMPOUND_2

        prefix = f"{at1}T{self.type}"

        if self.index is not None:
            for i in self.index:
                # 使用十六进制表示索引值
                prefix += f"-{format(i + 1, 'x')}"

        prefix += at2
        return prefix

    def __str__(self) -> str:
        """
        字符串表示

        返回:
            str: 字符串表示
        """
        return f"{self.new_key_prefix()}{self.target.name() if self.target else ''}"

    def type_to_string(self) -> str:
        """
        将链接类型转换为字符串

        返回:
            str: 链接类型的字符串表示
        """
        if self.type == self.SELF:
            return "自引用"
        elif self.type == self.COMPONENT:
            return "组件"
        elif self.type == self.COMPOUND:
            return "复合"
        elif self.type == self.COMPONENT_STATEMENT:
            return "组件语句"
        elif self.type == self.COMPOUND_STATEMENT:
            return "复合语句"
        elif self.type == self.COMPONENT_CONDITION:
            return "组件条件"
        elif self.type == self.COMPOUND_CONDITION:
            return "复合条件"
        elif self.type == self.TRANSFORM:
            return "转换"
        elif self.type == self.TEMPORAL:
            return "时间"
        else:
            return "未知"

    def __eq__(self, other: Any) -> bool:
        """
        检查相等性

        参数:
            other: 要比较的对象

        返回:
            bool: 如果相等则为True
        """
        if other is self:
            return True

        if self.hash != hash(other):
            return False

        if not isinstance(other, TermLink):
            return False

        if self.type != other.type:
            return False

        if self.index != other.index:
            return False

        if self.target is None:
            return other.target is None
        elif other.target is None:
            return self.target is None
        else:
            return self.target == other.target

    def init_hash(self) -> int:
        """
        初始化哈希值

        返回:
            int: 哈希值
        """
        import hashlib
        target_hash = hash(self.target) if self.target else 0
        type_hash = hash(self.type)
        index_hash = hash(tuple(self.index)) if self.index else 0

        # 组合哈希值
        combined_hash = ((target_hash * 31) + type_hash) * 31 + index_hash
        return combined_hash

    def __hash__(self) -> int:
        """
        哈希码

        返回:
            int: 哈希码
        """
        return self.hash

    def get_term(self):
        """
        获取术语

        返回:
            Term: 目标术语
        """
        return self.get_target()
