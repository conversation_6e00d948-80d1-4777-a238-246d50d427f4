"""
插件接口
定义支持插件的对象接口
"""
from abc import ABC, abstractmethod
from typing import List, Any

class Pluggable(ABC):
    """
    插件接口
    定义支持插件的对象必须实现的方法
    """

    @abstractmethod
    def add_plugin(self, plugin):
        """
        添加/注册插件

        参数:
            plugin: 要注册的插件对象
        """
        pass

    @abstractmethod
    def remove_plugin(self, plugin_state):
        """
        移除插件

        参数:
            plugin_state: 要移除的插件状态
        """
        pass

    @abstractmethod
    def get_plugins(self) -> List[Any]:
        """
        获取所有已添加的插件

        返回:
            List[Any]: 插件列表
        """
        pass
