# LIDA Cognitive Framework
"""
Interface for objects that can be fully initialized.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule

class FullyInitializable(ABC):
    """
    Interface for objects that can be fully initialized.
    """

    @abstractmethod
    def init(self, params: Dict[str, Any]) -> None:
        """
        Initialize this object with the given parameters.

        Args:
            params: Parameters for initialization
        """
        pass

    @abstractmethod
    def set_associated_module(self, module: 'FrameworkModule', module_usage: str) -> None:
        """
        Set an associated module for this object.

        Args:
            module: The module to associate with this object
            module_usage: How this object will use the module
        """
        pass
