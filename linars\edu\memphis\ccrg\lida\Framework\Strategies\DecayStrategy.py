# LIDA认知框架
"""
LIDA框架中衰减策略的接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
from linars.edu.memphis.ccrg.lida.Framework.Strategies.Strategy import Strategy

class DecayStrategy(Strategy, ABC):
    """
    LIDA框架中衰减策略的接口
    """

    @abstractmethod
    def decay(self, current_activation: float, base_level_activation: float, ticks: int) -> float:
        """
        使用给定参数衰减对象

        参数:
            current_activation: 对象的当前激活值
            base_level_activation: 对象的基础激活水平
            ticks: 衰减的ticks数

        返回:
            新的激活值
        """
        pass
