# LIDA Cognitive Framework
"""
A strategy for propagating activation upwards in PAMemory.
"""

from typing import Dict, Any
from linars.edu.memphis.ccrg.lida.PAM.PropagationStrategy import PropagationStrategy

class UpscalePropagationStrategy(PropagationStrategy):
    """
    A strategy for propagating activation upwards in PAMemory.
    """
    
    def __init__(self):
        """
        Initialize an UpscalePropagationStrategy.
        """
        self.upscale_factor = 0.6
    
    def get_activation_to_propagate(self, params: Dict[str, Any]) -> float:
        """
        Get the amount of activation to propagate.
        
        Args:
            params: Parameters for calculating the amount of activation to propagate
            
        Returns:
            The amount of activation to propagate
        """
        upscale = params.get("upscale", self.upscale_factor)
        total_activation = params.get("totalActivation", 0.0)
        return upscale * total_activation
    
    def set_upscale_factor(self, factor: float) -> None:
        """
        Set the upscale factor.
        
        Args:
            factor: The upscale factor to set
        """
        self.upscale_factor = factor
    
    def get_upscale_factor(self) -> float:
        """
        Get the upscale factor.
        
        Returns:
            The upscale factor
        """
        return self.upscale_factor
