"""
常用数学工具函数
主要用于处理[0,1]范围内的实数运算
"""
import math

class UtilityFunctions:
    """
    实用数学函数工具类
    提供NARS系统中常用的数学运算方法，包括：
    - 逻辑运算(AND/OR)
    - 平均值计算
    - 权重与置信度转换
    """

    @staticmethod
    def AND(*arr) -> float:
        """
        多值逻辑与运算(AND)
        输出结果由输入值共同决定(取各输入值的乘积)

        参数:
            *arr: 输入值数组，每个值在[0,1]范围内

        返回:
            float: 运算结果，不大于任一输入值
        """
        product = 1.0
        for f in arr:
            product *= f
        return product

    @staticmethod
    def OR(*arr) -> float:
        """
        多值逻辑或运算(OR)
        输出结果由输入值共同决定(1减去各输入值补数的乘积)

        参数:
            *arr: 输入值数组，每个值在[0,1]范围内

        返回:
            float: 运算结果，不小于任一输入值
        """
        product = 1.0
        for f in arr:
            product *= (1 - f)
        return 1 - product

    @staticmethod
    def ave_ari(*arr) -> float:
        """
        计算算术平均值

        参数:
            *arr: 输入值数组，每个值在[0,1]范围内

        返回:
            float: 输入值的算术平均值
        """
        if not arr:
            return 0.0

        sum_val = 0.0
        for f in arr:
            sum_val += f
        return sum_val / len(arr)

    @staticmethod
    def ave_geo(*arr) -> float:
        """
        计算几何平均值

        参数:
            *arr: 输入值数组，每个值在[0,1]范围内

        返回:
            float: 输入值的几何平均值
        """
        if not arr:
            return 0.0

        product = 1.0
        for f in arr:
            product *= f

        if len(arr) == 2:
            return math.sqrt(arr[0] * arr[1])

        return math.pow(product, 1.0 / len(arr))

    @staticmethod
    def w2c(w: float, narParameters) -> float:
        """
        权重(weight)转置信度(confidence)

        参数:
            w: 证据权重，非负实数
            narParameters: 推理机参数

        返回:
            float: 对应的置信度值，范围[0,1)
        """
        return w / (w + narParameters.HORIZON)

    @staticmethod
    def c2w(c: float, narParameters) -> float:
        """
        置信度(confidence)转权重(weight)

        参数:
            c: 置信度值，范围[0,1)
            narParameters: 推理机参数

        返回:
            float: 对应的证据权重，非负实数
        """
        return narParameters.HORIZON * c / (1 - c)
