#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task which performs the excitation of a single PamNode.
"""

import logging
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTask import FrameworkTask
from linars.edu.memphis.ccrg.lida.PAM.PamNode import PamN<PERSON>
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory
from linars.edu.memphis.ccrg.lida.PAM.Tasks.AddNodeToPerceptTask import AddNodeToPerceptTask

class ExcitationTask(FrameworkTaskImpl):
    """
    A task which performs the excitation of a single PamNode.

    See Also:
        PAMemory.receive_excitation(PamLinkable, double)
    """

    def __init__(self, ticks_per_run: int, node: PamNode, excitation: float, pam: PAMemory, from_source: str = ""):
        """
        Initialize an ExcitationTask.

        Args:
            ticks_per_run: The ticks per run
            node: The node to be excited
            excitation: The amount to excite
            pam: The PAMemory
            from_source: The source of the excitation
        """
        super().__init__(ticks_per_run)
        self.node = node
        self.excitation_amount = excitation
        self.pam = pam
        self.from_source = from_source
        self.logger = logging.getLogger("ExcitationTask")

    def run_this_framework_task(self):
        """
        This method first excites the PamNode, if this puts the PamNode
        over the percept threshold it creates an AddNodeToPerceptTask to
        add it to the percept. In either case it calls
        to pass the node's activation, then the tasks finishes.
        """
        # 外感知激活=点基础值*乘积，累积在pam联动里，点边累积统一
        # self.node.set_activation(self.node.get_activation() * self.excitation_amount)
        # self.node.excite_activation(self.excitation_amount)

        # print(f"{self.node.get_name()} +++++ act ++++++++{self.node.get_activation()}")

        # 不用过阈值，直接加入睡前buffer，展示done。这里加入的是pamnode
        task = AddNodeToPerceptTask(self.node, self.pam)
        self.pam.get_assisting_task_spawner().add_task(task)

        # 这里只要node就行，实际是pamnode（中期记忆），而接下来的联动是node
        self.pam.propagate_activation_to_parents(self.node, 0, self.from_source)
        self.cancel()
