"""
推理规则表，按任务和信念的TermLinks索引。
用于任务的间接处理，将推理案例分派给相关的推理规则。
"""
from linars.org.opennars.entity.term_link import TermLink
from linars.org.opennars.entity.stamp import Stamp
from linars.org.opennars.language.inheritance import Inheritance
from linars.org.opennars.language.similarity import Similarity
from linars.org.opennars.language.implication import Implication
from linars.org.opennars.language.equivalence import Equivalence
from linars.org.opennars.language.conjunction import Conjunction
from linars.org.opennars.language.negation import Negation
from linars.org.opennars.language.variable import Variable
from linars.org.opennars.language.terms import Terms
from linars.org.opennars.language.set_ext import SetExt
from linars.org.opennars.language.set_int import SetInt
from linars.org.opennars.operator.operation import Operation
from linars.org.opennars.inference.local_rules_stub import LocalRules
from linars.org.opennars.inference.syllogistic_rules import SyllogisticRules
from linars.org.opennars.inference.compositional_rules import CompositionalRules
from linars.org.opennars.inference.structural_rules import StructuralRules
from linars.org.opennars.io.symbols import GOAL_MARK

class RuleTables:
    """
    推理规则表，按任务和信念的TermLinks索引。
    用于任务的间接处理，将推理案例分派给相关的推理规则。
    """

    @staticmethod
    def reason(tLink, bLink, nal):
        """
        推理引擎的入口点。

        参数:
            tLink: 选中的TaskLink，将提供任务
            bLink: 选中的TermLink，可能提供信念
            nal: 派生上下文
        """
        memory = nal.mem()
        task = nal.get_current_task()
        task_sentence = task.sentence
        task_term = task_sentence.term  # cloning for substitution
        belief_term = bLink.target  # cloning for substitution

        belief_concept = memory.concept(belief_term)

        belief = None
        if belief_concept is not None:
            # We only need the target concept to select a belief
            belief = belief_concept.get_belief(nal, task)

        nal.set_current_belief(belief)
        if belief is not None:
            # Because interval handling differs on conceptual level
            belief_term = belief.term
            # Too restrictive, it's checked for non-deductive inference rules in derived_task (also for single prem)
            nal.evidential_overlap = Stamp.base_overlap(task.sentence.stamp, belief.stamp)
            # Only allow for eternal reasoning for now to prevent derived event floods
            if nal.evidential_overlap and (not task.sentence.is_eternal() or not belief.is_eternal()):
                return

            nal.emit("BeliefReason", belief, belief_term, task_term, nal)
            # New tasks resulted from the match, so return
            if LocalRules.match(task, belief, belief_concept, nal):
                return

        # Current belief and task may have changed, so set again
        nal.set_current_belief(belief)
        nal.set_current_task(task)

        # Put here since LocalRules match should be possible even if the belief is foreign
        if Terms.equal_sub_terms_in_respect_to_image_and_product(task_term, belief_term):
            return

        RuleTables.apply_rule_table(tLink, bLink, nal, task, task_sentence, task_term, belief_term, belief)

    @staticmethod
    def apply_rule_table(tLink, bLink, nal, task, task_sentence, task_term, belief_term, belief):
        """
        应用规则表派生新任务。

        参数:
            tLink: 任务链接
            bLink: 信念链接
            nal: 派生上下文
            task: 任务
            task_sentence: 任务句子
            task_term: 任务术语
            belief_term: 信念术语
            belief: 信念句子
        """
        tIndex = tLink.get_index(0)
        bIndex = bLink.get_index(0)

        # Dispatch first by TaskLink type
        if tLink.type == TermLink.SELF:
            if bLink.type == TermLink.COMPONENT:
                # Reasoning between a compound and its component
                RuleTables.compound_and_self(task_term, belief_term, True, bIndex, nal)
            elif bLink.type == TermLink.COMPOUND:
                # Reasoning between a compound and its component
                RuleTables.compound_and_self(belief_term, task_term, False, bIndex, nal)
            elif bLink.type == TermLink.COMPONENT_STATEMENT:
                if belief is not None:
                    RuleTables.component_statement_and_statement(task_term, belief_term, bIndex, nal)
            elif bLink.type == TermLink.COMPOUND_STATEMENT:
                if belief is not None:
                    RuleTables.compound_statement_and_statement(task_term, belief_term, bIndex, nal)
            elif bLink.type == TermLink.COMPONENT_CONDITION:
                if belief is not None:
                    RuleTables.component_condition_and_statement(task_term, belief_term, bIndex, nal)
            elif bLink.type == TermLink.COMPOUND_CONDITION:
                if belief is not None:
                    RuleTables.compound_condition_and_statement(task_term, belief_term, bIndex, nal)
            elif bLink.type == TermLink.TRANSFORM:
                RuleTables.transform_task(bLink, nal)
        elif tLink.type == TermLink.COMPOUND:
            if bLink.type == TermLink.COMPOUND:
                RuleTables.compound_and_compound(task_term, belief_term, tIndex, bIndex, nal)
            elif bLink.type == TermLink.COMPONENT:
                RuleTables.compound_and_component(task_term, belief_term, tIndex, bIndex, nal)
            elif bLink.type == TermLink.COMPOUND_STATEMENT:
                RuleTables.compound_and_statement(task_term, belief_term, tIndex, bIndex, nal, True)
            elif bLink.type == TermLink.COMPOUND_CONDITION:
                RuleTables.compound_and_statement(task_term, belief_term, tIndex, bIndex, nal, False)
        elif tLink.type == TermLink.COMPONENT:
            if bLink.type == TermLink.COMPOUND:
                RuleTables.component_and_compound(task_term, belief_term, tIndex, bIndex, nal)
            elif bLink.type == TermLink.COMPONENT:
                RuleTables.component_and_component(task_term, belief_term, tIndex, bIndex, nal)
            elif bLink.type == TermLink.COMPOUND_STATEMENT:
                RuleTables.component_and_statement(task_term, belief_term, tIndex, bIndex, nal, True)
            elif bLink.type == TermLink.COMPOUND_CONDITION:
                RuleTables.component_and_statement(task_term, belief_term, tIndex, bIndex, nal, False)
        elif tLink.type == TermLink.COMPOUND_STATEMENT:
            if bLink.type == TermLink.COMPOUND_STATEMENT:
                if belief is not None:
                    RuleTables.syllogisms(tLink, bLink, task_term, belief_term, nal)
            elif bLink.type == TermLink.COMPOUND_CONDITION:
                if belief is not None:
                    RuleTables.syllogisms(tLink, bLink, task_term, belief_term, nal)
            elif bLink.type == TermLink.COMPONENT:
                RuleTables.statement_and_component(task_term, belief_term, tIndex, bIndex, nal, true)
            elif bLink.type == TermLink.COMPOUND:
                RuleTables.statement_and_compound(task_term, belief_term, tIndex, bIndex, nal, true)
        elif tLink.type == TermLink.COMPOUND_CONDITION:
            if bLink.type == TermLink.COMPOUND_CONDITION:
                if belief is not None:
                    RuleTables.syllogisms(tLink, bLink, task_term, belief_term, nal)
            elif bLink.type == TermLink.COMPOUND_STATEMENT:
                if belief is not None:
                    RuleTables.syllogisms(tLink, bLink, task_term, belief_term, nal)
            elif bLink.type == TermLink.COMPONENT:
                RuleTables.statement_and_component(task_term, belief_term, tIndex, bIndex, nal, False)
            elif bLink.type == TermLink.COMPOUND:
                RuleTables.statement_and_compound(task_term, belief_term, tIndex, bIndex, nal, False)
        elif tLink.type == TermLink.COMPONENT_STATEMENT:
            if bLink.type == TermLink.COMPONENT_STATEMENT:
                if belief is not None:
                    RuleTables.syllogisms(tLink, bLink, task_term, belief_term, nal)
            elif bLink.type == TermLink.COMPONENT_CONDITION:
                if belief is not None:
                    RuleTables.syllogisms(tLink, bLink, task_term, belief_term, nal)
        elif tLink.type == TermLink.COMPONENT_CONDITION:
            if bLink.type == TermLink.COMPONENT_CONDITION:
                if belief is not None:
                    RuleTables.syllogisms(tLink, bLink, task_term, belief_term, nal)
            elif bLink.type == TermLink.COMPONENT_STATEMENT:
                if belief is not None:
                    RuleTables.syllogisms(tLink, bLink, task_term, belief_term, nal)
        elif tLink.type == TermLink.TRANSFORM:
            RuleTables.transform_task(tLink, nal)

        RuleTables.goal_from_want_belief(task, tIndex, bIndex, task_term, nal, belief_term)
        RuleTables.desire_from_goal_belief(task, tIndex, bIndex, task_term, nal, belief_term)

    @staticmethod
    def goal_from_want_belief(task, tIndex, bIndex, task_term, nal, belief_term):
        """
        从want信念派生目标。

        参数:
            task: 任务
            tIndex: 任务索引
            bIndex: 信念索引
            task_term: 任务术语
            nal: 派生上下文
            belief_term: 信念术语
        """
        if (task.sentence.is_judgment() and tIndex == 0 and bIndex == 1 and
            isinstance(task_term, Operation)):
            op = task_term
            if op.get_predicate() == nal.nar.memory.get_operator("^want"):
                new_truth = TruthFunctions.deduction(task.sentence.truth, nal.nar.narParameters.reliance, nal.nar.narParameters)
                nal.single_premise_task(op.get_arguments().term[1], GOAL_MARK, new_truth, BudgetFunctions.forward(new_truth, nal))

    @staticmethod
    def desire_from_goal_belief(task, tIndex, bIndex, task_term, nal, belief_term):
        """
        从goal信念派生desire。

        参数:
            task: 任务
            tIndex: 任务索引
            bIndex: 信念索引
            task_term: 任务术语
            nal: 派生上下文
            belief_term: 信念术语
        """
        # Implementation details omitted for brevity
        pass

    @staticmethod
    def syllogisms(tLink, bLink, task_term, belief_term, nal):
        """
        三段论规则的元表，按任务句子和信念的内容类索引。

        参数:
            tLink: 任务链接
            bLink: 信念链接
            task_term: 任务内容
            belief_term: 信念内容
            nal: 内存引用
        """
        task_sentence = nal.get_current_task().sentence
        belief = nal.get_current_belief()
        figure = None

        if isinstance(task_term, Inheritance):
            if isinstance(belief_term, Inheritance):
                figure = RuleTables.index_to_figure(tLink, bLink)
                RuleTables.asymmetric_asymmetric(task_sentence, belief, figure, nal)
            elif isinstance(belief_term, Similarity):
                figure = RuleTables.index_to_figure(tLink, bLink)
                RuleTables.asymmetric_symmetric(task_sentence, belief, figure, nal)
            elif isinstance(belief_term, Implication):
                RuleTables.statement_to_statement(task_sentence, belief, figure, nal)
            elif isinstance(belief_term, Equivalence):
                RuleTables.statement_to_statement(task_sentence, belief, figure, nal)
        elif isinstance(task_term, Similarity):
            if isinstance(belief_term, Inheritance):
                figure = RuleTables.index_to_figure(bLink, tLink)
                RuleTables.asymmetric_symmetric(belief, task_sentence, figure, nal)
            elif isinstance(belief_term, Similarity):
                figure = RuleTables.index_to_figure(tLink, bLink)
                RuleTables.symmetric_symmetric(task_sentence, belief, figure, nal)
            elif isinstance(belief_term, Implication):
                RuleTables.statement_to_statement(task_sentence, belief, figure, nal)
            elif isinstance(belief_term, Equivalence):
                RuleTables.statement_to_statement(task_sentence, belief, figure, nal)
        elif isinstance(task_term, Implication):
            if isinstance(belief_term, Implication):
                figure = RuleTables.index_to_figure(tLink, bLink)
                RuleTables.asymmetric_asymmetric(task_sentence, belief, figure, nal)
            elif isinstance(belief_term, Equivalence):
                figure = RuleTables.index_to_figure(tLink, bLink)
                RuleTables.asymmetric_symmetric(task_sentence, belief, figure, nal)
            elif isinstance(belief_term, Inheritance):
                RuleTables.detachment_with_var(task_sentence, belief, tLink.get_index(0), nal)
            elif isinstance(belief_term, Similarity):
                # Bridge to higher order statements
                figure = RuleTables.index_to_figure(tLink, bLink)
                RuleTables.asymmetric_symmetric(task_sentence, belief, figure, nal)
        elif isinstance(task_term, Equivalence):
            if isinstance(belief_term, Implication):
                figure = RuleTables.index_to_figure(bLink, tLink)
                RuleTables.asymmetric_symmetric(belief, task_sentence, figure, nal)
            elif isinstance(belief_term, Equivalence):
                figure = RuleTables.index_to_figure(bLink, tLink)
                RuleTables.symmetric_symmetric(belief, task_sentence, figure, nal)
            elif isinstance(belief_term, Inheritance):
                RuleTables.detachment_with_var(task_sentence, belief, tLink.get_index(0), nal)
            elif isinstance(belief_term, Similarity):
                # Bridge to higher order statements
                figure = RuleTables.index_to_figure(tLink, bLink)
                RuleTables.symmetric_symmetric(belief, task_sentence, figure, nal)

    @staticmethod
    def index_to_figure(link1, link2):
        """
        根据前提中共同术语的位置决定三段论的图形。

        参数:
            link1: 第一个前提的链接
            link2: 第二个前提的链接

        返回:
            三段论的图形，四种之一: 11, 12, 21 或 22
        """
        return (link1.get_index(0) + 1) * 10 + (link2.get_index(0) + 1)

    @staticmethod
    def compound_and_self(compound, component, compound_is_task, index, nal):
        """
        复合术语与其组件之间的推理。

        参数:
            compound: 复合术语
            component: 组件术语
            compound_is_task: 复合术语是否来自任务
            index: 组件在复合术语中的索引
            nal: 内存引用
        """
        if compound_is_task:
            if compound.is_commutative() and (compound.term[index] == component):
                CompositionalRules.decompose(compound, index, nal)
            if isinstance(compound, Conjunction) and (index > 0) and component.is_statement() and not component.is_constant():
                CompositionalRules.decompose_statement_to_image(compound, index, nal)
        else:
            if compound.is_commutative() and (compound.term[index] == component):
                CompositionalRules.compose(compound, component, index, nal)

    @staticmethod
    def compound_and_component(compound, component, compound_index, component_index, nal):
        """
        复合术语与另一个复合术语的组件之间的推理。

        参数:
            compound: 复合术语
            component: 组件术语
            compound_index: 术语在复合术语中的索引
            component_index: 组件的索引
            nal: 内存引用
        """
        if component.is_compound():
            component_compound = component
            if compound.is_commutative() and component_compound.is_commutative():
                CompositionalRules.compose_compound(compound, component_compound, compound_index, component_index, nal)

    @staticmethod
    def component_and_compound(component, compound, component_index, compound_index, nal):
        """
        组件与包含另一个组件的复合术语之间的推理。

        参数:
            component: 组件术语
            compound: 复合术语
            component_index: 组件的索引
            compound_index: 术语在复合术语中的索引
            nal: 内存引用
        """
        if compound.is_compound() and compound.is_commutative():
            CompositionalRules.decompose_compound(component, compound, component_index, compound_index, nal)

    @staticmethod
    def component_and_component(component1, component2, index1, index2, nal):
        """
        两个组件之间的推理。

        参数:
            component1: 第一个组件
            component2: 第二个组件
            index1: 第一个组件的索引
            index2: 第二个组件的索引
            nal: 内存引用
        """
        if component1.is_compound() and component2.is_compound():
            if component1.is_commutative() and component2.is_commutative():
                CompositionalRules.decompose_compound_to_component(component1, component2, index1, index2, nal)

    @staticmethod
    def compound_and_statement(compound, statement, compound_index, statement_index, nal, compound_is_statement):
        """
        复合术语与陈述之间的推理。

        参数:
            compound: 复合术语
            statement: 陈述术语
            compound_index: 术语在复合术语中的索引
            statement_index: 术语在陈述中的索引
            nal: 内存引用
            compound_is_statement: 复合术语是否为陈述
        """
        if compound_is_statement:
            if statement.is_statement():
                CompositionalRules.decompose_statement_to_component(compound, statement, compound_index, statement_index, nal)
        else:
            if statement.is_statement():
                CompositionalRules.decompose_statement_to_component(statement, compound, statement_index, compound_index, nal)

    @staticmethod
    def component_and_statement(component, statement, component_index, statement_index, nal, component_is_statement):
        """
        组件与陈述之间的推理。

        参数:
            component: 组件术语
            statement: 陈述术语
            component_index: 组件的索引
            statement_index: 术语在陈述中的索引
            nal: 内存引用
            component_is_statement: 组件是否为陈述
        """
        if component_is_statement:
            if statement.is_statement():
                CompositionalRules.decompose_statement_to_component(component, statement, component_index, statement_index, nal)
        else:
            if statement.is_statement():
                CompositionalRules.decompose_statement_to_component(statement, component, statement_index, component_index, nal)

    @staticmethod
    def statement_and_component(statement, component, statement_index, component_index, nal, statement_is_belief):
        """
        陈述与组件之间的推理。

        参数:
            statement: 陈述术语
            component: 组件术语
            statement_index: 术语在陈述中的索引
            component_index: 组件的索引
            nal: 内存引用
            statement_is_belief: 陈述是否为信念
        """
        task = nal.get_current_task()
        if task.sentence.is_judgment():
            if statement.is_statement():
                if component.is_compound():
                    if statement_index == 0:
                        if component_index == 0:
                            CompositionalRules.decompose_statement_to_component1(statement, component, nal)
                        elif component_index > 0:
                            CompositionalRules.decompose_statement_to_component2(statement, component, component_index, nal)
                    elif statement_index == 1:
                        if component_index == 0:
                            CompositionalRules.decompose_statement_to_component3(statement, component, nal)
                        elif component_index > 0:
                            CompositionalRules.decompose_statement_to_component4(statement, component, component_index, nal)

    @staticmethod
    def statement_and_compound(statement, compound, statement_index, compound_index, nal, statement_is_belief):
        """
        陈述与复合术语之间的推理。

        参数:
            statement: 陈述术语
            compound: 复合术语
            statement_index: 术语在陈述中的索引
            compound_index: 术语在复合术语中的索引
            nal: 内存引用
            statement_is_belief: 陈述是否为信念
        """
        if statement.is_statement() and compound.is_compound():
            side = statement_index == 0
            if compound.is_commutative():
                if statement.is_inheritance():
                    StructuralRules.structural_compose1(compound, compound_index, statement, nal)
                    if not (isinstance(compound, SetExt) or isinstance(compound, SetInt) or isinstance(compound, Negation) or
                            isinstance(compound, Conjunction) or isinstance(compound, Disjunction)):
                        StructuralRules.structural_compose2(compound, compound_index, statement, side, nal)
                elif not (isinstance(compound, Negation) or isinstance(compound, Conjunction) or isinstance(compound, Disjunction)):
                    StructuralRules.structural_compose2(compound, compound_index, statement, side, nal)

    @staticmethod
    def component_statement_and_statement(compound, component, index, nal):
        """
        组件陈述与陈述之间的推理。

        参数:
            compound: 复合术语
            component: 组件术语
            index: 组件的索引
            nal: 内存引用
        """
        if compound.is_statement() and component.is_statement():
            if index == 0:
                CompositionalRules.decompose_statement1(compound, component, nal)
            elif index == 1:
                CompositionalRules.decompose_statement2(compound, component, nal)

    @staticmethod
    def compound_statement_and_statement(compound, component, index, nal):
        """
        复合陈述与陈述之间的推理。

        参数:
            compound: 复合术语
            component: 组件术语
            index: 组件的索引
            nal: 内存引用
        """
        if compound.is_statement() and component.is_statement():
            task = nal.get_current_task()
            if task.sentence.is_judgment():
                CompositionalRules.decompose_compound_statement(compound, index, nal)

    @staticmethod
    def component_condition_and_statement(compound, component, index, nal):
        """
        组件条件与陈述之间的推理。

        参数:
            compound: 复合术语
            component: 组件术语
            index: 组件的索引
            nal: 内存引用
        """
        if compound.is_statement() and component.is_statement():
            if index > 0:
                CompositionalRules.decompose_statement_to_condition1(compound, component, index, nal)

    @staticmethod
    def compound_condition_and_statement(compound, component, index, nal):
        """
        复合条件与陈述之间的推理。

        参数:
            compound: 复合术语
            component: 组件术语
            index: 组件的索引
            nal: 内存引用
        """
        if compound.is_statement() and component.is_statement():
            task = nal.get_current_task()
            if task.sentence.is_judgment():
                CompositionalRules.decompose_compound_condition(compound, index, nal)

    @staticmethod
    def asymmetric_asymmetric(asym1, asym2, figure, nal):
        """
        两个前提都在相同非对称关系上的三段论规则。

        参数:
            asym1: 第一个前提
            asym2: 第二个前提
            figure: 三段论的图形
            nal: 内存引用
        """
        task_sentence = nal.get_current_task().sentence
        belief = nal.get_current_belief()
        task_statement = task_sentence.term
        belief_statement = belief.term

        if isinstance(task_statement, Inheritance) and isinstance(belief_statement, Inheritance):
            if Variable.has_var_dep(task_statement.name()) or Variable.has_var_dep(belief_statement.name()):
                return

            is_deduction = False
            t1 = None
            t2 = None

            if figure == 11:  # induction
                sensational = SyllogisticRules.abd_ind_com(belief_statement.get_predicate(), task_statement.get_predicate(), task_sentence, belief, figure, nal)
                if sensational:
                    return
                CompositionalRules.compose_compound(task_statement, belief_statement, 0, nal)
                CompositionalRules.intro_var_outer(task_statement, belief_statement, 0, nal)
                CompositionalRules.eliminate_variable_of_condition_abductive(figure, task_sentence, belief, nal)
            elif figure == 22:  # abduction
                sensational = SyllogisticRules.abd_ind_com(task_statement.get_subject(), belief_statement.get_subject(), task_sentence, belief, figure, nal)
                if sensational:
                    return
                CompositionalRules.compose_compound(task_statement, belief_statement, 1, nal)
                CompositionalRules.intro_var_outer(task_statement, belief_statement, 1, nal)
                CompositionalRules.eliminate_variable_of_condition_abductive(figure, task_sentence, belief, nal)
            elif figure == 12 or figure == 21:  # deduction or exemplification
                is_deduction = figure == 12
                t1 = belief_statement.get_subject() if is_deduction else task_statement.get_subject()
                t2 = task_statement.get_predicate() if is_deduction else belief_statement.get_predicate()
                if Variable.unify(nal.memory.random_number, Variable.VAR_QUERY, t1, t2, [task_statement, belief_statement]):
                    LocalRules.match_reverse(nal)
                else:
                    SyllogisticRules.ded_exe(t1, t2, task_sentence, belief, nal)

    @staticmethod
    def asymmetric_symmetric(asym, sym, figure, nal):
        """
        第一个前提在非对称关系上，第二个前提在对称关系上的三段论规则。

        参数:
            asym: 非对称前提
            sym: 对称前提
            figure: 三段论的图形
            nal: 内存引用
        """
        task_sentence = nal.get_current_task().sentence
        belief = nal.get_current_belief()
        task_statement = task_sentence.term
        belief_statement = belief.term

        if isinstance(task_statement, Inheritance) and isinstance(belief_statement, Similarity):
            if Variable.has_var_dep(task_statement.name()) or Variable.has_var_dep(belief_statement.name()):
                return

            figure_left = figure % 10
            figure_right = figure // 10

            asym_st = task_statement
            sym_st = belief_statement

            u = [asym_st, sym_st]
            if not Variable.unify(nal.memory.random_number, Variable.VAR_INDEPENDENT,
                                asym_st.ret_by_side(figure_left), sym_st.ret_by_side(figure_right), u):
                return

            asym_st = u[0]
            sym_st = u[1]
            t1 = asym_st.ret_by_side(ret_opposite_side(figure_left))
            t2 = sym_st.ret_by_side(ret_opposite_side(figure_right))

            if Variable.unify(nal.memory.random_number, Variable.VAR_QUERY, t1, t2, u):
                LocalRules.match_asym_sym(asym, sym, figure, nal)
            else:
                if figure == 11 or figure == 12:
                    SyllogisticRules.analogy(t2, t1, asym, sym, figure, nal)
                elif figure == 21 or figure == 22:
                    SyllogisticRules.analogy(t1, t2, asym, sym, figure, nal)

    @staticmethod
    def symmetric_symmetric(sym1, sym2, figure, nal):
        """
        两个前提都在相同对称关系上的三段论规则。

        参数:
            sym1: 第一个前提
            sym2: 第二个前提
            figure: 三段论的图形
            nal: 内存引用
        """
        task_sentence = nal.get_current_task().sentence
        belief = nal.get_current_belief()
        task_statement = task_sentence.term
        belief_statement = belief.term

        if isinstance(task_statement, Similarity) and isinstance(belief_statement, Similarity):
            if Variable.has_var_dep(task_statement.name()) or Variable.has_var_dep(belief_statement.name()):
                return

            figure_left = figure % 10
            figure_right = figure // 10

            s1 = task_statement
            s2 = belief_statement

            ut1 = s1.ret_by_side(figure_left)
            ut2 = s2.ret_by_side(figure_right)
            rt1 = None
            rt2 = None

            if figure == 11:
                rt1 = s1.get_predicate()
                rt2 = s2.get_predicate()
            elif figure == 12:
                rt1 = s1.get_predicate()
                rt2 = s2.get_subject()
            elif figure == 21:
                rt1 = s1.get_subject()
                rt2 = s2.get_predicate()
            elif figure == 22:
                rt1 = s1.get_subject()
                rt2 = s2.get_subject()

            u = [s1, s2]
            if Variable.unify(nal.memory.random_number, Variable.VAR_INDEPENDENT, ut1, ut2, u):
                # Recalculate rt1, rt2 from above
                if figure == 11:
                    rt1 = s1.get_predicate()
                    rt2 = s2.get_predicate()
                elif figure == 12:
                    rt1 = s1.get_predicate()
                    rt2 = s2.get_subject()
                elif figure == 21:
                    rt1 = s1.get_subject()
                    rt2 = s2.get_predicate()
                elif figure == 22:
                    rt1 = s1.get_subject()
                    rt2 = s2.get_subject()

                SyllogisticRules.resemblance(rt1, rt2, belief, task_sentence, figure, nal)
                CompositionalRules.eliminate_variable_of_condition_abductive(figure, task_sentence, belief, nal)

    @staticmethod
    def statement_to_statement(premise1, premise2, figure, nal):
        """
        两个前提都是高阶陈述的三段论规则。

        参数:
            premise1: 第一个前提
            premise2: 第二个前提
            figure: 三段论的图形
            nal: 内存引用
        """
        # Implementation details would be added here
        pass

    @staticmethod
    def detachment_with_var(premise1, premise2, index, nal):
        """
        条件演绎。

        参数:
            premise1: 条件前提
            premise2: 匹配条件的另一个前提
            index: 共享术语的位置
            nal: 内存引用
        """
        # Implementation details would be added here
        pass

    @staticmethod
    def ret_opposite_side(side):
        """
        返回对称关系的另一侧。

        参数:
            side: 给定的一侧

        返回:
            另一侧
        """
        return 2 if side == 1 else 1

    @staticmethod
    def transform_task(tLink, nal):
        """
        TaskLink类型为TRANSFORM，结论是等价转换。

        参数:
            tLink: 任务链接
            nal: 内存引用
        """
        content = nal.get_current_task().get_term()
        indices = tLink.index
        expected_inheritance_term = None

        # This block "dereferences" the term by the address which we are storing in "indices"
        if (len(indices) == 2) or isinstance(content, Inheritance):
            expected_inheritance_term = content
        elif len(indices) == 3:
            expected_inheritance_term = content.term[indices[0]]
        elif len(indices) == 4:
            component = content.term[indices[0]]
            if (isinstance(component, Conjunction) and
                ((isinstance(content, Implication) and indices[0] == 0) or
                 isinstance(content, Equivalence))):
                cterms = component.term
                if indices[1] < len(cterms) - 1:
                    expected_inheritance_term = cterms[indices[1]]
                else:
                    return
            else:
                return

        # It is not a fatal error if it is not an inheritance, we just ignore it in this case
        if isinstance(expected_inheritance_term, Inheritance):
            StructuralRules.transform_product_image(expected_inheritance_term, content, indices, nal)
