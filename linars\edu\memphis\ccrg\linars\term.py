"""
项是Narsese的基本组成部分，也是NARS中处理的对象。

一个项可能关联一个概念(Concept)，其中包含与其他项的关系。
这种关联不直接存储在项中，因为概念可能会被遗忘而项仍然存在。
多个对象可能表示同一个项。
"""
import re
from collections import defaultdict
from typing import Dict, List, Optional, Set, Tuple, Union, Any

from linars.edu.memphis.ccrg.linars.term_node_impl import TermNodeImpl
from linars.edu.memphis.ccrg.linars.abstract_term import AbstractTerm

# Forward declaration to avoid circular imports
class CompoundTerm:
    """Forward declaration for CompoundTerm"""
    pass

class Variable:
    """Forward declaration for Variable"""
    pass

class NativeOperator:
    """Native operators enum"""
    ATOM = "ATOM"
    COMPOUND = "COMPOUND"
    STATEMENT = "STATEMENT"
    INTERVAL = "INTERVAL"
    VARIABLE = "VARIABLE"

class Term(TermNodeImpl, AbstractTerm):
    """
    项类
    Narsese的基本组成部分，也是NARS中处理的对象。
    """
    # Static fields
    atoms: Dict[str, 'Term'] = {}
    SELF = None  # Will be initialized later
    SEQ_SPATIAL = None  # Will be initialized later
    SEQ_TEMPORAL = None  # Will be initialized later

    def __init__(self, name: str = None):
        """
        构造函数
        参数:
            name: 项的名称字符串
        """
        # print("Term---__init__----")
        super().__init__()
        self.term_id = 0
        self.parent_id = 0
        self.children: List['Term'] = []
        self.imagination = None
        self.term_indices = None
        self.index_variable = None
        self.properties: Dict[str, Any] = {}
        self.nar = None  # 引用到NARS实例

        if name:
            self.set_term_name(name)
            self.set_node_name(name)

    @staticmethod
    def get(name: str):
        """
        根据名称获取原子项

        参数:
            name: 项的名称

        返回:
            Term: 具有给定名称的项
        """
        # Check if it's already in the atoms dictionary
        if name in Term.atoms and not Term.atoms[name].name().endswith("]"):
            return Term.atoms[name]

        # Process indices if present
        term_indices = None
        before_indices_str = None

        if name.endswith("]") and "[" in name:
            # Extract indices
            parts = name.split("[")
            before_indices_str = parts[0]
            indices_str = parts[1].split("]")[0]

            inds = indices_str.split(",")
            if len(inds) == 2:  # Only position info given
                indices_str = f"1,1,{indices_str}"
                inds = indices_str.split(",")

            # Convert to integers if all are numeric
            if all(ind.isdigit() for ind in inds):
                term_indices = [int(ind) for ind in inds]

        # Create the term
        name2 = name
        if term_indices is not None:
            name2 = f"{before_indices_str}[i,j,k,l]"

        x = Term(name2)
        x.term_indices = term_indices
        x.index_variable = before_indices_str if before_indices_str else ""
        Term.atoms[name2] = x

        return x

    @staticmethod
    def get_int(i: int) -> 'Term':
        """
        获取表示整数的原子项

        参数:
            i: 整数值

        返回:
            Term: 表示该整数的项
        """
        return Term.get(str(i))

    def name(self) -> str:
        """
        获取当前项的名称

        返回:
            str: 项的名称
        """
        if self.name_internal() is None:
            return ""
        return self.name_internal()

    def name_internal(self) -> str:
        """
        获取内部名称

        返回:
            str: 内部名称
        """
        return self.TNname

    def clone(self) -> 'Term':
        """
        克隆具有相同名称的新项

        返回:
            Term: 新克隆的项
        """
        t = Term()
        if self.term_indices is not None:
            t.term_indices = self.term_indices.copy()
            t.index_variable = self.index_variable
        t.set_term_name(self.name())
        t.imagination = self.imagination
        return t

    def clone_deep(self) -> 'Term':
        """
        深度克隆项

        返回:
            Term: 深度克隆后的项
        """
        return self.clone()

    def equals(self, that: Any) -> bool:
        """
        判断两个项是否相等(名称相同即可，不要求引用相同)

        参数:
            that: 要与当前项比较的项

        返回:
            bool: 两个项是否相等
        """
        if that is self:
            return True

        if not isinstance(that, Term) and not isinstance(that, str):
            return False

        this_name = self.name()
        if this_name is None:
            this_name = self.name()

        if isinstance(that, Term):
            that_name = that.name()
            if that_name is None:
                that_name = that.name()
            com = self.get_complexity()
            tcom = that.get_complexity()
            comeq = com == tcom
            nameq = this_name == that_name
            return (comeq and nameq)
        elif isinstance(that, str):
            return this_name == that

        return False

    def __eq__(self, other: Any) -> bool:
        """
        Equality operator

        Args:
            other: The object to compare with

        Returns:
            bool: True if equal
        """
        return self.equals(other)

    def __hash__(self) -> int:
        """
        Produce a hash code for the term

        Returns:
            int: An integer hash code
        """
        if self.name() is None:
            return self.nodeId
        return hash(self.name())

    def is_constant(self) -> bool:
        """
        检查当前项是否可以命名一个概念

        返回:
            bool: 默认情况下项是常量
        """
        return True

    def get_temporal_order(self) -> int:
        """
        获取时间顺序

        返回:
            int: 时间顺序值
        """
        return 0  # ORDER_NONE

    def get_is_spatial(self) -> bool:
        """
        检查项是否是空间性的

        返回:
            bool: 如果是空间性的则为True
        """
        return False

    def recurse_terms(self, visitor, parent=None):
        """
        递归访问项

        参数:
            visitor: 访问者函数
            parent: 父项
        """
        visitor(self, parent)
        # 基本项不需要递归处理子项
        # CompoundTerm类会重写这个方法

    def recurse_subterms_containing_variables(self, visitor, parent=None):
        """
        递归访问包含变量的子项

        参数:
            visitor: 访问者函数
            parent: 父项
        """
        if not self.has_var():
            return

        visitor(self, parent)
        # 基本项不需要递归处理子项
        # CompoundTerm类会重写这个方法

    def get_complexity(self) -> int:
        """
        获取项的复杂度

        返回:
            int: 复杂度(原子项的复杂度为1)
        """
        return 1

    def operator(self) -> str:
        """
        获取项的操作符

        返回:
            str: 项的操作符
        """
        return NativeOperator.ATOM

    def get_priority(self) -> float:
        """
        获取优先级

        返回:
            float: 优先级值，默认为0.0
        """
        # 如果有预算值，返回预算的优先级
        if hasattr(self, 'budget') and self.budget is not None:
            return self.budget.get_priority()
        # 否则返回默认优先级
        return 0.0

    def get_predicate(self):
        """
        获取谓词（对于基本Term，返回自身）

        返回:
            Term: 谓词项
        """
        # 对于基本Term，没有谓词概念，返回自身
        # Statement类会重写这个方法
        return self

    def get_subject(self):
        """
        获取主语（对于基本Term，返回自身）

        返回:
            Term: 主语项
        """
        # 对于基本Term，没有主语概念，返回自身
        # Statement类会重写这个方法
        return self

    def is_higher_order_statement(self) -> bool:
        """
        检查项是否为高阶语句

        返回:
            bool: 如果项是等价或蕴含关系则返回True
        """
        # 在Python中使用字符串类型检查
        return (self.__class__.__name__ == "Equivalence" or
                self.__class__.__name__ == "Implication")

    def is_executable(self, memory) -> bool:
        """
        检查项是否可执行

        参数:
            memory: 内存对象

        返回:
            bool: 如果项是操作则返回True
        """
        # 检查是否为操作
        is_op = self.__class__.__name__ == "Operation"

        if is_op and hasattr(self, 'get_operator'):
            op = self.get_operator()
            # 如果操作是^want或^believe，则不允许执行
            if (hasattr(op, 'equals') and
                (op.equals(memory.get_operator("^want")) or
                 op.equals(memory.get_operator("^believe")))):
                return False

        return is_op

    def get_terms(self) -> List['Term']:
        """
        获取项的组成部分

        返回:
            List[Term]: 项的组成部分
        """
        if hasattr(self, 'term'):
            return self.term
        return []

    def complexity(self) -> int:
        """
        Get the complexity of this term.
        Implementation of the abstract method from AbstractTerm.

        Returns:
            The complexity of this term
        """
        return self.get_complexity()

    def set_term_name(self, new_name: str):
        """
        设置项的名称

        参数:
            new_name: 新名称
        """
        self.TNname = new_name

    def compare_to(self, that: AbstractTerm) -> int:
        """
        比较当前项与另一个项

        参数:
            that: 要与当前项比较的项

        返回:
            int: 比较结果(-1, 0, 1)
        """
        if that is self:
            return 0

        # Previously: Orders among terms: variable < atomic < compound
        if isinstance(that, Variable) and not isinstance(self, Variable):
            return 1
        elif isinstance(self, Variable) and not isinstance(that, Variable):
            return -1

        # Compare names
        if self.name() < that.name():
            return -1
        elif self.name() > that.name():
            return 1
        else:
            return 0

    def __lt__(self, other: AbstractTerm) -> bool:
        """
        Less than operator

        Args:
            other: The term to compare with

        Returns:
            bool: True if this term is less than the other
        """
        return self.compare_to(other) < 0

    def contained_temporal_relations(self) -> int:
        """
        计算包含的时间关系数量

        返回:
            int: 时间关系数量
        """
        return 0

    def contains_term_recursively(self, target: 'Term') -> bool:
        """
        递归检查复合项是否包含目标项

        参数:
            target: 要搜索的目标项

        返回:
            bool: 是否包含目标项
        """
        if target is None:
            return False
        return self == target

    def count_term_recursively(self, term_map=None) -> Dict['Term', int]:
        """
        递归计算各项的出现次数

        参数:
            term_map: 要更新的计数映射

        返回:
            Dict: 各项的计数
        """
        if term_map is None:
            term_map = {}

        term_map[self] = term_map.get(self, 0) + 1
        return term_map

    def contains_term(self, target: 'Term') -> bool:
        """
        检查当前项是否在其组件中包含另一个项

        参数:
            target: 要检查的目标项

        返回:
            bool: 如果包含目标项则为True
        """
        return self == target

    def __str__(self) -> str:
        """
        String representation of the term

        Returns:
            str: The name of the term
        """
        return str(self.name())

    def to_string(self) -> str:
        """
        获取项的字符串表示（兼容Java版本的方法名）

        Returns:
            str: 项的名称
        """
        return self.__str__()

    @staticmethod
    def text(t: str) -> 'Term':
        """
        从字符串创建引用转义的项

        参数:
            t: 要转换的文本

        返回:
            Term: 带引号的文本项
        """
        return Term.get(f'"{t}"')

    def has_var(self) -> bool:
        """
        检查当前复合项是否包含任何变量项

        返回:
            bool: 基本项返回False
        """
        return False

    def has_var_type(self, type_char: str) -> bool:
        """
        检查项是否有特定类型的变量

        参数:
            type_char: 变量类型字符

        返回:
            bool: 如果项有该类型变量则为True
        """
        if type_char == '$':  # VAR_INDEPENDENT
            return self.has_var_indep()
        elif type_char == '#':  # VAR_DEPENDENT
            return self.has_var_dep()
        elif type_char == '?':  # VAR_QUERY
            return self.has_var_query()

        raise ValueError(f"Invalid variable type: {type_char}")

    def has_var_indep(self) -> bool:
        """
        检查项是否有独立变量

        返回:
            bool: 基本项返回False
        """
        return False

    def has_interval(self) -> bool:
        """
        检查项是否有间隔

        返回:
            bool: 基本项返回False
        """
        return False

    def has_var_dep(self) -> bool:
        """
        检查项是否有依赖变量

        返回:
            bool: 基本项返回False
        """
        return False

    def has_var_query(self) -> bool:
        """
        检查项是否有查询变量

        返回:
            bool: 基本项返回False
        """
        return False

    @staticmethod
    def to_sorted_set(*args: 'Term') -> Set['Term']:
        """
        将多个项转换为排序后的集合

        参数:
            *args: 要转换的项

        返回:
            Set: 排序后的项集合
        """
        return set(sorted(args))

    @staticmethod
    def to_sorted_set_array(*args: 'Term') -> List['Term']:
        """
        将多个项转换为排序后的数组

        参数:
            *args: 要转换的项

        返回:
            List: 排序后的项列表
        """
        if len(args) == 0:
            return []
        elif len(args) == 1:
            return [args[0]]
        elif len(args) == 2:
            a, b = args[0], args[1]
            c = -1 if a < b else 1 if a > b else 0

            if c < 0:
                return [a, b]
            elif c > 0:
                return [b, a]
            else:
                return [a]  # Equal terms

        # For more than 2 terms, use a set
        return sorted(set(args))

    @staticmethod
    def valid(content: 'Term') -> bool:
        """
        全面检查项的有效性

        参数:
            content: 要检查的项

        返回:
            bool: 如果项有效则为True
        """
        cloned = content.clone_deep()
        return cloned is not None

    def subject_or_predicate_is_independent_var(self) -> bool:
        """
        检查主语或谓语是否是独立变量

        返回:
            bool: 如果主语或谓语是独立变量则为True
        """
        # 基本项没有主语和谓语
        # 复合项会重写这个方法
        return False

    def to_compound_term(self) -> Optional['CompoundTerm']:
        """
        尝试转换为复合项

        返回:
            CompoundTerm or None: 复合项或None
        """
        # 基本项不是复合项
        return None

    def operator(self) -> str:
        """
        获取操作符类型

        返回:
            str: 操作符类型
        """
        return NativeOperator.ATOM

    def contained_temporal_relations(self) -> int:
        """
        获取包含的时间关系数

        返回:
            int: 时间关系数
        """
        return 0

    def get_tn_name(self) -> str:
        """
        获取TN名称

        返回:
            str: TN名称
        """
        return self.TNname

    def is_higher_order_statement(self) -> bool:
        """
        检查项是否为高阶语句

        返回:
            bool: 如果项是等价或蕴含关系则返回True
        """
        # 在Python中使用字符串类型检查
        return (self.__class__.__name__ == "Equivalence" or
                self.__class__.__name__ == "Implication")

    def is_executable(self, memory) -> bool:
        """
        检查项是否可执行

        参数:
            memory: 内存对象

        返回:
            bool: 如果项是操作则返回True
        """
        # 检查是否为操作
        is_op = self.__class__.__name__ == "Operation"

        if is_op and hasattr(self, 'get_operator'):
            op = self.get_operator()
            # 如果操作是^want或^believe，则不允许执行
            if (hasattr(op, 'equals') and
                (op.equals(memory.get_operator("^want")) or
                 op.equals(memory.get_operator("^believe")))):
                return False

        return is_op

    def get_children(self) -> List['Term']:
        """获取当前项的子项列表"""
        return self.children

    def set_children(self, children: List['Term']):
        """设置当前项的子项列表"""
        self.children = children

    def get_term_id(self) -> int:
        """获取项的ID"""
        return self.term_id

    def set_term_id(self, term_id: int):
        """设置项的ID"""
        self.term_id = term_id

    def get_parent_id(self) -> int:
        """获取父项ID"""
        return self.parent_id

    def set_parent_id(self, parent_id: int):
        """设置父项ID"""
        self.parent_id = parent_id

    def get_properties(self) -> Dict[str, Any]:
        """
        获取属性字典

        返回:
            Dict[str, Any]: 属性字典
        """
        return self.properties

    def set_properties(self, properties: Dict[str, Any]) -> None:
        """
        设置属性字典

        参数:
            properties: 新的属性字典
        """
        self.properties = properties


# Initialize static fields
Term.SELF = None  # Will be initialized later with SetExt.make(Term.get("SELF"))
Term.SEQ_SPATIAL = Term.get("#")
Term.SEQ_TEMPORAL = Term.get("&/")
