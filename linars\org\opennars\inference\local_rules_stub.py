"""
LocalRules的存根实现，用于避免循环导入问题。
"""
from typing import Optional

from linars.edu.memphis.ccrg.linars.concept import Concept
from linars.org.opennars.control.derivation_context import DerivationContext
from linars.org.opennars.entity.sentence import Sentence
from linars.org.opennars.entity.task import Task

class LocalRules:
    """
    直接通过信念处理任务，两者都只包含两个术语。
    这是一个存根实现，用于避免循环导入问题。
    """

    @staticmethod
    def match(task: Task, belief: Sentence, belief_concept: Concept, nal: DerivationContext) -> bool:
        """
        任务和信念具有相同的内容。
        这是一个存根实现，实际实现在local_rules.py中。

        参数:
            task: 任务
            belief: 信念
            belief_concept: 信念的概念
            nal: 派生上下文

        返回:
            如果任务被处理返回True，否则返回False
        """
        # 导入真正的实现
        from linars.org.opennars.inference.local_rules import LocalRules as RealLocalRules
        return RealLocalRules.match(task, belief, belief_concept, nal)
