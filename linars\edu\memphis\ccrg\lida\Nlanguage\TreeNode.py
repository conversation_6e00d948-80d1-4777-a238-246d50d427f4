#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
TreeNode class for the LIDA framework.
"""

from typing import List, Optional, Any

class TreeNode:
    """
    A tree node in the LIDA framework.
    """
    
    def __init__(self, value: Any):
        """
        Initialize a TreeNode with a value.
        
        Args:
            value: The value of the node
        """
        self.value = value
        self.children = []  # List of TreeNode
        # 别称、可替代、同义词、命名等。类似相似或对等词项。可多个，父子关系共享
        self.alias = []  # List of Any
        # 主题信息池，包括相关的概念、属性、时序等，供动机执行时检索，区分可能混杂的数据，参考程序执行，可能要垃圾回收
        # 人类只有一个共用的工作记忆，可能信息混淆，且容量有限，还要衰减，但计算机可有多个不同的工作记忆，属于优化处理
        self.topic_data_pool = []  # List of Any
        self.parent = None  # TreeNode or None
        
    def add_child(self, child: 'TreeNode') -> bool:
        """
        Add a child node to this node.
        
        Args:
            child: The child node to add
            
        Returns:
            True if the child was added, False otherwise
        """
        # 不能重复添加
        for node in self.children:
            if node == child:
                return False
                
        # 不能添加自己为子节点
        if self == child:
            return False
            
        # 不能添加父节点为子节点
        if self.parent == child:
            return False
            
        # 不能添加循环引用
        for node in self.children:
            if node in node.children:
                return False
                
        self.children.append(child)
        child.parent = self
        return True
        
    def find_child_by_value(self, value: Any) -> Optional['TreeNode']:
        """
        Find a child node by its value.
        
        Args:
            value: The value to search for
            
        Returns:
            The child node if found, None otherwise
        """
        for node in self.children:
            if node.value == value:
                return node
        return None
        
    def remove_child(self, child: 'TreeNode') -> None:
        """
        Remove a child node from this node.
        
        Args:
            child: The child node to remove
        """
        if child in self.children:
            self.children.remove(child)
            
    def add_alias(self, alias: Any) -> None:
        """
        Add an alias to this node.
        
        Args:
            alias: The alias to add
        """
        self.alias.append(alias)
        
    def remove_alias(self, alias: Any) -> None:
        """
        Remove an alias from this node.
        
        Args:
            alias: The alias to remove
        """
        if alias in self.alias:
            self.alias.remove(alias)
            
    def has_alias(self, alias: Any) -> bool:
        """
        Check if this node has a specific alias.
        
        Args:
            alias: The alias to check for
            
        Returns:
            True if the node has the alias, False otherwise
        """
        return alias in self.alias
        
    def get_aliases(self) -> List[Any]:
        """
        Get the list of aliases for this node.
        
        Returns:
            The list of aliases
        """
        return self.alias
        
    def add_topic_data(self, data: Any) -> None:
        """
        Add data to the topic data pool.
        
        Args:
            data: The data to add
        """
        self.topic_data_pool.append(data)
        
    def remove_topic_data(self, data: Any) -> None:
        """
        Remove data from the topic data pool.
        
        Args:
            data: The data to remove
        """
        if data in self.topic_data_pool:
            self.topic_data_pool.remove(data)
            
    def has_topic_data(self, data: Any) -> bool:
        """
        Check if the topic data pool contains specific data.
        
        Args:
            data: The data to check for
            
        Returns:
            True if the data is in the pool, False otherwise
        """
        return data in self.topic_data_pool
        
    def get_topic_data_pool(self) -> List[Any]:
        """
        Get the topic data pool.
        
        Returns:
            The topic data pool
        """
        return self.topic_data_pool
        
    def set_value(self, new_value: Any) -> None:
        """
        Set the value of this node.
        
        Args:
            new_value: The new value
        """
        self.value = new_value
        
    def get_value(self) -> Any:
        """
        Get the value of this node.
        
        Returns:
            The value of the node
        """
        return self.value
        
    def get_children(self) -> List['TreeNode']:
        """
        Get the children of this node.
        
        Returns:
            The list of children
        """
        return self.children
