"""
Neo4j Utility for LIDA framework.

This module provides utility functions for interacting with Neo4j in the LIDA framework.
"""
import logging
from typing import Dict, List, Set, Any, Optional
from py2neo import Node as Neo4jNode, Relationship

from linars.edu.memphis.ccrg.lida.ActionSelection.ActionImpl import ActionImpl
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.PAM.PamLinkImpl import PamLinkImpl
from linars.edu.memphis.ccrg.lida.PAM.PamNodeImpl import PamNodeImpl
from linars.edu.memphis.ccrg.lida.ProceduralMemory.ProceduralMemoryImpl import ProceduralMemoryImpl
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Scheme import Scheme
from linars.edu.memphis.ccrg.linars.term import Term

# 全局变量
parameters = {}
pnNode = None
reLink = None
isExcite = False
pam = None  # 将在初始化时设置
narsese = None  # 将在初始化时设置
arg = ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7", "arg8", "arg9"]
graph_db = None  # 将在初始化时设置

# 初始化函数
def init(graph, pam_instance, narsese_instance):
    """
    初始化Neo4j工具
    Args:
        graph: Neo4j图实例
        pam_instance: PAM实例
        narsese_instance: Narsese解析器实例
    """
    global graph_db, pam, narsese
    graph_db = graph
    pam = pam_instance
    narsese = narsese_instance

class NeoUtil:
    """Neo4j工具类，提供与Neo4j交互的方法"""

    logger = logging.getLogger(__name__)

    @staticmethod
    def add_nodes_and_rels_to_buffer(query: str, name: ModuleName) -> None:
        """
        将查询结果中的节点和关系添加到指定的缓冲区

        Args:
            query: Cypher查询
            name: 模块名称
        """
        try:
            # 使用非上下文管理器方式运行查询
            tx = graph_db.begin()
            result = tx.run(query, parameters)
            for record in result:
                for key in record.keys():
                    if key == "n":
                        n = record[key]
                        # 节点转换为pamNodeImpl，并加入到buffer
                        pamNode = NeoUtil.get_pam_node(n, n["name"])
                        pam.get_listener().receive_percept(pamNode, name)
                    elif key == "re":
                        re = record[key]
                        link0 = NeoUtil.cast_neo_to_lida_link(re, None)
                        # 加入到buffer
                        pam.get_listener().receive_percept(link0, name)

            # 提交事务
            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"添加节点和关系到缓冲区时发生错误：{e}")
            # 如果发生错误，回滚事务
            if 'tx' in locals():
                tx.rollback()

    @staticmethod
    def get_schemes(n: Node, pm: ProceduralMemoryImpl) -> Set[Scheme]:
        """
        获取与节点相关的方案

        Args:
            n: 节点
            pm: 程序性记忆实现

        Returns:
            方案集合
        """
        schemes = set()
        query = f"match (n:word{{name:'{n.get_tn_name()}'}})-[r:longE]->() return r"

        try:
            tx = graph_db.begin()
            result = tx.run(query, parameters)
            already_add = set()

            for record in result:
                for key in record.keys():
                    re = record[key]
                    end_name = re.end_node["name"]

                    if end_name in already_add:
                        continue  # 同类长链，一条就够

                    already_add.add(end_name)
                    query = f"match (n:Verb{{name:'{end_name}'}})-[r:action]->() return r"

                    result0 = tx.run(query, parameters)
                    for record0 in result0:
                        for key0 in record0.keys():
                            # 每次一个新的schema
                            sss = pm.get_new_scheme()
                            re0 = record0[key0]
                            sch_label = re0.start_node["name"] + re0.end_node["name"]
                            sss.set_name(sch_label)

                            action = ActionImpl(sch_label)
                            action.set_id(int(re0.id))
                            sss.set_action(action)

                            schemes.add(sss)
            # 提交事务
            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"获取动作时发生错误：{e}")
            # 如果发生错误，回滚事务
            if 'tx' in locals():
                tx.rollback()

        return schemes

    @staticmethod
    def get_node(name: str) -> Optional[Node]:
        """
        获取节点
        Args:
            name: 节点名称
        Returns:
            节点或None
        """
        if name is None:
            return None

        # 如果Neo4j数据库连接不可用，返回None
        if graph_db is None:
            NeoUtil.logger.warning(f"Neo4j数据库连接不可用，无法获取节点：{name}")
            return None

        try:
            node1 = PamNodeImpl(name)
            query = f"match (n{{name:'{name}'}}) return n"
            weight = None

            tx = None

            tx = graph_db.begin()
            result = tx.run(query, parameters)
            num = 0

            for record in result:
                num += 1
                node = record[result.keys()[0]]
                node1 = NeoUtil.get_pam_node(node, name)

                try:
                    weight = node["weight"]
                except KeyError:
                    weight = 0.88

                # 类型可能是字符串、整数、浮点数
                if isinstance(weight, str):
                    node1.set_activation(float(weight))
                elif isinstance(weight, int):
                    node1.set_activation(float(weight))
                elif isinstance(weight, float):
                    node1.set_activation(weight)
                break

            if num == 0:
                NeoUtil.logger.info(f"查不到这个：{name}")
                node1 = None

            # 提交事务
            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"获取节点时发生错误：{e}")
            # 如果发生错误，回滚事务
            if tx is not None:
                tx.rollback()
            node1 = None

        return node1

    @staticmethod
    def merge_node(name: str, label: str) -> Optional[Node]:
        """
        合并或创建节点

        Args:
            name: 节点名称
            label: 节点标签

        Returns:
            节点或None
        """
        if name is None:
            return None

        node1 = PamNodeImpl()
        query = f"merge (n:{label}{{name:'{name}'}}) return n"

        try:
            tx = graph_db.begin()
            result = tx.run(query, parameters)

            for record in result:
                node = record[result.keys()[0]]
                node1 = NeoUtil.get_pam_node(node, name)

            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"合并节点时发生错误：{e}")
            if 'tx' in locals():
                tx.rollback()

        return node1

    @staticmethod
    def merge_link(link_name: Optional[str], link_label: Optional[str],
                  from_node_name: str, to_node_name: str,
                  from_label: str, to_label: str) -> Optional[Link]:
        """
        合并或创建链接

        Args:
            link_name: 链接名称
            link_label: 链接标签
            from_node_name: 源节点名称
            to_node_name: 目标节点名称
            from_label: 源节点标签
            to_label: 目标节点标签

        Returns:
            链接或None
        """
        if link_name is None and link_label is None:
            return None

        link = PamLinkImpl()
        query = f"merge (n:{from_label}{{name:'{from_node_name}', weight:'0.87'}}) merge (m:{to_label}{{name:'{to_node_name}', weight:'0.87'}}) "

        if link_name is not None and link_label is not None:
            query += f"merge (n)-[r:{link_label}{{name:'{link_name}', weight:'0.87'}}]->(m) return r"
        elif link_name is None:
            query += f"merge (n)-[r:{link_label}{{weight:'0.87'}}]->(m) return r"
        else:
            query += f"merge (n)-[r{{name:'{link_name}', weight:'0.87'}}]->(m) return r"

        try:
            tx = graph_db.begin()
            result = tx.run(query, parameters)

            for record in result:
                re = record[result.keys()[0]]
                link = NeoUtil.cast_neo_to_lida_link(re, None)

            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"合并链接时发生错误：{e}")
            if 'tx' in locals():
                tx.rollback()

        return link

    @staticmethod
    def get_pam_node(node: Neo4jNode, name: str) -> Node:
        """
        从Neo4j节点创建PAM节点
        Args:
            node: Neo4j节点
            name: 节点名称
        Returns:
            PAM节点
        """
        node1 = PamNodeImpl(name)
        node1.set_node_id(int(node.identity))
        node1.set_node_name(name)

        labels = []
        for lb in node.labels:
            labels.append(lb)

        node1.set_labels(labels)
        node1.set_properties(dict(node))

        return node1

    @staticmethod
    def get_property(node: Neo4jNode, key: str) -> Any:
        """
        获取节点属性

        Args:
            node: Neo4j节点
            key: 属性键

        Returns:
            属性值或None
        """
        properties = dict(node)
        for k in properties:
            if k == key:
                return properties[k]

        return None

    @staticmethod
    def cast_neo_to_lida_link(re: Relationship, pn_node: Optional[Node]) -> PamLinkImpl:
        """
        从Neo4j关系创建PAM链接

        Args:
            re: Neo4j关系
            pn_node: 源节点

        Returns:
            PAM链接
        """
        link = PamLinkImpl()
        # print("------link = PamLinkImpl()--------")
        category = PamNodeImpl()

        # 尝试多种方式获取关系类型
        type_name = None
        if "label" in re:
            type_name = re["label"]
        elif "type" in re:
            type_name = re["type"]
        else:
            # 如果没有label或type属性，使用关系类型
            try:
                # 检查re.type是否为函数
                if callable(re.type):
                    # 如果re.type是函数，尝试获取实际的关系类型
                    # 使用py2neo的关系类型获取方法
                    try:
                        # 尝试直接使用关系的字符串表示
                        type_str = str(re)
                        # 从字符串表示中提取类型，格式通常是"(:Node)-[:TYPE{}]->(:Node)",
                        # 例如'(苹果)-[:warg_1 {}]->(words_562)'
                        import re as regex
                        # 尝试多种正则表达式模式
                        # 匹配格式如 [:warg_1 {}]
                        match = regex.search(r'\[:(\w+)\s*\{', type_str)
                        if not match:
                            # 匹配格式如 [:TYPE]
                            match = regex.search(r'\[:(\w+)\]', type_str)
                        if match:
                            type_name = match.group(1)
                            # print("----match=", match, "--type_name=", type_name)
                        else:
                            # 如果无法从字符串中提取，尝试其他方法
                            # 尝试获取关系的类型名称
                            if hasattr(re, '_type') and re._type is not None:
                                type_name = re._type
                            elif hasattr(re, 'relationships') and hasattr(re.relationships, 'types') and len(re.relationships.types) > 0:
                                type_name = str(re.relationships.types[0])
                            # else:
                                # 如果所有方法都失败，使用默认值
                                # type_name = re.start_node.get('name', '') + '_to_' + re.end_node.get('name', '')
                                # if not type_name or type_name == '_to_':
                                #     type_name = "relationship"


                    except Exception as inner_e:
                        # 不输出警告，使用默认值
                        type_name = "relationship"
                else:
                    type_name = str(re.type)
            except Exception as e:
                # 不输出警告，使用默认值
                type_name = "relationship"
        # tt = str(re)
        # print(tt,"-----re.type()------")

        # 确保type_name不为None
        if type_name is None:
            type_name = "unknown"
            NeoUtil.logger.warning(f"无法确定关系类型，使用默认值'unknown'，关系ID: {re.identity}")

        category.set_node_name(type_name)
        category.set_node_id(int(re.identity))

        name_f = NeoUtil.get_property(re.start_node, "name")
        from_node = NeoUtil.get_pam_node(re.start_node, name_f)

        name_t = NeoUtil.get_property(re.end_node, "name")
        to_node = NeoUtil.get_pam_node(re.end_node, name_t)

        link.set_category(category)
        act = 0
        weight0 = None
        wt0 = None
        weight = 0
        wt = 0

        if pn_node is not None:
            NeoUtil.logger.info(f"{name_f}--{type_name}--{name_t}")

            try:
                weight0 = re["weight"]
            except KeyError:
                weight0 = 0.88

            if isinstance(weight0, str):
                weight = float(weight0)
            elif isinstance(weight0, int):
                weight = float(weight0)

            if weight != 0:
                act = weight * pn_node.get_activation()
            else:
                act = pn_node.get_activation()

            from_node.set_activation(pn_node.get_activation())

            try:
                wt0 = re.end_node["weight"]
            except KeyError:
                wt0 = 0.88

            if isinstance(wt0, str):
                wt = float(wt0)
            elif isinstance(wt0, int):
                wt = float(wt0)

            to_node.set_activation(wt * act)
        else:
            act = 0.8
            from_node.set_activation(act)
            to_node.set_activation(act)

        link.set_activation(act)
        link.set_source(from_node)
        link.set_sink(to_node)
        link.set_id(int(re.identity))
        link.set_properties(dict(re))
        link.init(link.term)

        return link

    @staticmethod
    def get_links(n: Node) -> Optional[Set[Link]]:
        """
        获取节点的出链接

        Args:
            n: 节点

        Returns:
            链接集合或None
        """
        if n is None:
            return None

        # 如果Neo4j数据库连接不可用，返回空集合
        if graph_db is None:
            NeoUtil.logger.warning(f"Neo4j数据库连接不可用，无法获取链接：{n.get_tn_name()}")
            return set()

        try:
            links = set()
            query = f"match (n{{name:'{n.get_tn_name()}'}})-[r]->() return r"
            links = NeoUtil.get_links_cypher(query, n)

            return links
        except Exception as e:
            NeoUtil.logger.warning(f"获取链接时发生错误：{e}")
            return set()

    @staticmethod
    def execute_only(query: str) -> None:
        """
        仅执行查询，不返回结果

        Args:
            query: Cypher查询
        """
        try:
            tx = graph_db.begin()
            tx.run(query, parameters)
            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"执行查询时发生错误0：{e}")
            if 'tx' in locals():
                tx.rollback()

    @staticmethod
    def get_node_cypher(query: str) -> Optional[Node]:
        """
        执行查询，返回节点

        Args:
            query: Cypher查询

        Returns:
            节点或None
        """
        node1 = None

        try:
            tx = graph_db.begin()
            result = tx.run(query, parameters)

            for record in result:
                node = record[result.keys()[0]]
                node1 = NeoUtil.get_pam_node(node, node["name"])
                break

            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"执行查询时发生错误1：{e}")
            if 'tx' in locals():
                tx.rollback()

        return node1

    @staticmethod
    def get_link_cypher(query: str) -> Optional[Link]:
        """
        执行查询，返回链接

        Args:
            query: Cypher查询

        Returns:
            链接或None
        """
        link = None

        try:
            tx = graph_db.begin()
            result = tx.run(query, parameters)

            for record in result:
                re = record[result.keys()[0]]
                link = NeoUtil.cast_neo_to_lida_link(re, None)
                break

            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"执行查询时发生错误2：{e}")
            if 'tx' in locals():
                tx.rollback()

        return link

    @staticmethod
    def get_links_cypher(query: str, n: Optional[Node] = None) -> Set[Link]:
        """
        执行查询，返回链接集合

        Args:
            query: Cypher查询
            n: 节点

        Returns:
            链接集合
        """
        links = set()

        # 如果Neo4j数据库连接不可用，返回空集合
        if graph_db is None:
            NeoUtil.logger.warning(f"Neo4j数据库连接不可用，无法执行查询：{query}")
            return links

        try:
            tx = graph_db.begin()
            result = tx.run(query, parameters)

            for record in result:
                for key in record.keys():
                    re = record[key]
                    link = NeoUtil.cast_neo_to_lida_link(re, n)
                    links.add(link)

            tx.commit()
            return links
        except Exception as e:
            NeoUtil.logger.warning(f"执行查询时发生错误3：{e}")
            if 'tx' in locals():
                tx.rollback()
            return links

    @staticmethod
    def get_nodes_cypher(query: str) -> Set[Node]:
        """
        执行查询，返回节点集合

        Args:
            query: Cypher查询

        Returns:
            节点集合
        """
        nodes = set()

        try:
            tx = graph_db.begin()
            result = tx.run(query, parameters)

            for record in result:
                node0 = record[result.keys()[0]]
                node = NeoUtil.get_pam_node(node0, node0["name"])
                nodes.add(node)

            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"执行查询时发生错误4：{e}")
            if 'tx' in locals():
                tx.rollback()

        return nodes

    @staticmethod
    def get_path_cypher(query: str) -> List[Node]:
        """
        执行查询，返回路径

        Args:
            query: Cypher查询

        Returns:
            节点列表
        """
        nodes = []

        try:
            tx = graph_db.begin()
            result = tx.run(query, parameters)

            for record in result:
                node0 = record[result.keys()[0]]
                node = NeoUtil.get_pam_node(node0, node0["name"])
                nodes.append(node)

            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"执行查询时发生错误5：{e}")
            if 'tx' in locals():
                tx.rollback()

        return nodes

    @staticmethod
    def get_one_link_tx(query: str, tx) -> Optional[Link]:
        """
        在事务内查询一条链接

        Args:
            query: Cypher查询
            tx: 事务

        Returns:
            链接或None
        """
        link0 = None
        result0 = tx.run(query, parameters)

        for record0 in result0:
            for key0 in record0.keys():
                actre = record0[key0]
                link0 = NeoUtil.cast_neo_to_lida_link(actre, None)
                to_node = link0.get_sink()

                # 每个时序分别加入计划，以备执行，头节点已有，不用加入
                pam.get_listener().receive_percept(to_node, ModuleName.SEQ_GRAPH)
                pam.get_listener().receive_percept(link0, ModuleName.SEQ_GRAPH)

                NeoUtil.logger.info(f"------时序执行------{link0}")

        return link0

    @staticmethod
    def get_one_link(n: Node, linktype: str, fto: str, n1type: str, n2type: str) -> Optional[Link]:
        """
        获取一条链接

        Args:
            n: 节点
            linktype: 链接类型
            fto: 方向
            n1type: 源节点类型
            n2type: 目标节点类型

        Returns:
            链接或None
        """
        link = None
        links = NeoUtil.get_some_links(n, linktype, fto, n1type, n2type)

        for l in links:
            link = l
            break

        return link

    @staticmethod
    def get_some_links(n: Optional[Node], linktype: Optional[str], fto: Optional[str],
                      n1type: Optional[str], n2type: Optional[str]) -> Set[Link]:
        """
        根据条件获取链接

        Args:
            n: 节点
            linktype: 链接类型
            fto: 方向
            n1type: 源节点类型
            n2type: 目标节点类型

        Returns:
            链接集合
        """
        links = set()
        sb = []

        sb.append("match (n")
        if n1type is not None and n1type != "":
            sb.append(f":{n1type}")

        if n is None:
            sb.append(")")
        else:
            sb.append(f"{{name:'{n.get_tn_name()}'}})")

        if fto is not None and fto == "<":
            sb.append(fto)

        sb.append("-[r")
        if linktype is not None and linktype != "":
            sb.append(f":{linktype}")

        sb.append("]-")
        if fto is not None and fto == ">":
            sb.append(fto)

        sb.append("(m")
        if n2type is not None and n2type != "":
            sb.append(f":{n2type}")

        sb.append(") return r")
        query = "".join(sb)

        links = NeoUtil.get_links_cypher(query, n)

        return links

    @staticmethod
    def get_ns_n2n(name1: str, name2: str, degree: int) -> Set[Node]:
        """
        获取两点间特定度数内的所有节点

        Args:
            name1: 起始节点名称
            name2: 终止节点名称
            degree: 度数

        Returns:
            节点集合
        """
        query = f'MATCH p=((n{{name:"{name1}"}})-[*{degree}]-(m{{name:"{name2}"}})) RETURN nodes(p)'
        NeoUtil.logger.info(f"query = {query}")
        nodes = set()

        try:
            tx = graph_db.begin()
            result0 = tx.run(query, parameters)

            for row0 in result0:
                for key0 in row0.keys():
                    # nodes(p)是一个list，里面是node
                    result_nodes = row0[key0]
                    for scene0 in result_nodes:
                        node1 = NeoUtil.get_pam_node(scene0, scene0["name"])
                        nodes.add(node1)

            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"执行查询时发生错误6：{e}")
            if 'tx' in locals():
                tx.rollback()

        return nodes

    @staticmethod
    def shortest(start_node_name: str, end_node_name: str) -> None:
        """
        查找最短路径

        Args:
            start_node_name: 起始节点名称
            end_node_name: 终止节点名称
        """
        try:
            tx = graph_db.begin()
            cypher_query = f"MATCH (n)-[r]-(m) WHERE n.name = '{start_node_name}' AND m.name = '{end_node_name}' RETURN shortestPath((n)-[*..10]-(m))"

            result = tx.run(cypher_query, parameters)
            for row in result:
                for key in row.keys():
                    shortest_paths = row[key]
                    NeoUtil.logger.info(f"Shortest path between {start_node_name} and {end_node_name}:")
                    NeoUtil.logger.info(shortest_paths)

            tx.commit()
        except Exception as e:
            NeoUtil.logger.error(f"Error finding shortest path: {e}")
            if 'tx' in locals():
                tx.rollback()

    @staticmethod
    def set_term_to_node(term: Term, node: Node, num: int) -> int:
        """
        将Term设置到节点

        Args:
            term: Term
            node: 节点
            num: 编号

        Returns:
            更新后的编号
        """
        final_term = narsese.parse_term(str(term))
        str_val = "linars"

        if final_term.get_complexity() > 12:
            NeoUtil.logger.info(f"复杂度过高，不更新到图谱---------- {term}")
            # 用其他代号代替
            str_val = f"nars_{num}"
            num += 1
        else:
            str_val = str(term)

        # 以node的id为准，不用name，因为name可能重复。cpstr意思是复合词的字符串
        query = f"match (n) where id(n) = {node.get_node_id()} set n.name = '{str_val}'"
        NeoUtil.execute_only(query)

        return num

    @staticmethod
    def get_scene_son(tnname: str) -> List[Node]:
        """
        获取场景子节点

        Args:
            tnname: 节点名称

        Returns:
            节点列表
        """
        nodes = []
        query = f"match (n:场景{{name:'{tnname}'}})<-[r]-(m) return r"

        try:
            tx = graph_db.begin()
            result = tx.run(query, parameters)
            links = set()

            for row in result:
                for key in row.keys():
                    re = row[key]
                    if "arg" in re.type:
                        links.add(re)

            # 对links排序
            link_list = []
            for s in arg:
                for link in links:
                    if link.type == s:
                        link_list.append(link)

            for re0 in link_list:
                node = NeoUtil.get_pam_node(re0.start_node, re0.start_node["name"])
                nodes.append(node)

            tx.commit()
        except Exception as e:
            NeoUtil.logger.warning(f"获取场景子节点时发生错误：{e}")
            if 'tx' in locals():
                tx.rollback()

        return nodes

    @staticmethod
    def sort_links(links: List[Link]) -> None:
        """
        将链接按类型排序

        Args:
            links: 链接列表
        """
        link_list = []
        for s in arg:
            for link in links:
                if link.get_tn_name() == s:
                    link_list.append(link)

        if not link_list:
            link_list.extend(links)
        elif len(link_list) < len(links):
            # 有arg开头的，但不全，按照原来的顺序排列
            for link in links:
                if link not in link_list:
                    link_list.append(link)

        links.clear()
        links.extend(link_list)

    @staticmethod
    def get_by_cypher(cypher: str, tx) -> List[Dict[str, Any]]:
        """
        执行任意查询

        Args:
            cypher: Cypher查询
            tx: 事务

        Returns:
            结果列表
        """
        list_result = []
        result = tx.run(cypher)

        for record in result:
            list_result.append(dict(record))

        return list_result

    @staticmethod
    def execute_query(query: str, parameters: Dict[str, Any] = None):
        """
        执行Cypher查询并返回结果

        Args:
            query: Cypher查询语句
            parameters: 查询参数

        Returns:
            查询结果对象，支持迭代和列访问
        """
        if graph_db is None:
            NeoUtil.logger.warning(f"Neo4j数据库连接不可用，无法执行查询：{query}")
            return None

        try:
            # 使用py2neo的Graph.run方法执行查询
            result = graph_db.run(query, parameters or {})
            return result
        except Exception as e:
            NeoUtil.logger.warning(f"执行查询时发生错误：{e}")
            return None

    @staticmethod
    def cast_neo_to_lida_node(neo_node) -> Optional[Node]:
        """
        将Neo4j节点转换为LIDA节点

        Args:
            neo_node: Neo4j节点对象

        Returns:
            LIDA节点对象或None
        """
        if neo_node is None:
            return None

        try:
            # 获取节点名称
            name = neo_node.get("name", str(neo_node.identity))
            return NeoUtil.get_pam_node(neo_node, name)
        except Exception as e:
            NeoUtil.logger.warning(f"转换Neo4j节点到LIDA节点时发生错误：{e}")
            return None
