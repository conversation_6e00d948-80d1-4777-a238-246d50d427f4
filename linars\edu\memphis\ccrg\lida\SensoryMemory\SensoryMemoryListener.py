# LIDA Cognitive Framework
"""
This interface should be implemented for receiving and using information coming from a SensoryMemory module.
"""

from abc import abstractmethod
from typing import Any
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener

class SensoryMemoryListener(ModuleListener):
    """
    This interface should be implemented for receiving and using information coming from a SensoryMemory module.
    """
    
    @abstractmethod
    def receive_sensory_memory_content(self, content: Any) -> None:
        """
        This method is used to receive information from sensory memory.
        Sensory-Motor Memory calls this method and receives the information of sensory memory.
        
        Args:
            content: An Object containing SensoryMemory content
        """
        pass
