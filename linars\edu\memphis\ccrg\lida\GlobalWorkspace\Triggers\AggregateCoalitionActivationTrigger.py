# LIDA Cognitive Framework
"""
A BroadcastTrigger that triggers a broadcast when the sum of all Coalition objects in GlobalWorkspace is greater than a threshold.
"""

import logging
from typing import Dict, Any, Collection
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.GlobalWorkspace import GlobalWorkspace
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Triggers.BroadcastTrigger import BroadcastTrigger

class AggregateCoalitionActivationTrigger(BroadcastTrigger):
    """
    A BroadcastTrigger that triggers a broadcast when the sum of all Coalition objects in GlobalWorkspace is greater than a threshold.
    """
    
    # Default values
    DEFAULT_THRESHOLD = 0.5
    
    def __init__(self):
        """
        Initialize an AggregateCoalitionActivationTrigger.
        """
        self.gw = None
        self.threshold = self.DEFAULT_THRESHOLD
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def init(self, params: Dict[str, Any], gw: GlobalWorkspace) -> None:
        """
        Initialize this trigger with parameters.
        
        Args:
            params: The parameters for initialization
            gw: The GlobalWorkspace
        """
        self.gw = gw
        threshold = params.get("threshold")
        if threshold is not None and isinstance(threshold, float):
            if threshold >= 0.0:
                self.threshold = threshold
            else:
                self.logger.warning(f"Invalid threshold parameter, using default at tick {TaskManager.get_current_tick()}")
        else:
            self.logger.warning(f"Failed to set threshold parameter, using default at tick {TaskManager.get_current_tick()}")
    
    def check_for_trigger_condition(self, coalitions: Collection[Coalition]) -> None:
        """
        Calculates the aggregate activation of all coalitions in the GlobalWorkspace and
        if it is over threshold a broadcast is triggered.
        This method is called each time a new Coalition enters the GlobalWorkspace.
        
        Args:
            coalitions: The coalitions in the GlobalWorkspace
        """
        aggregate_activation = 0.0
        for c in coalitions:
            aggregate_activation += c.get_activation()
        
        if aggregate_activation > self.threshold:
            self.logger.debug(f"Aggregate Activation trigger fires at tick {TaskManager.get_current_tick()}")
            self.gw.trigger_broadcast(self)
    
    def start(self) -> None:
        """
        Start this trigger.
        """
        # Not applicable
        pass
    
    def reset(self) -> None:
        """
        Reset this trigger.
        """
        # Not applicable
        pass
    
    def get_threshold(self) -> float:
        """
        Get the threshold.
        
        Returns:
            The threshold to activate the trigger
        """
        return self.threshold
