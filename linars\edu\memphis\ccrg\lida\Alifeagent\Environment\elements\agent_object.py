"""
ALife环境中的智能体对象实现

本模块提供ALife环境中智能体对象的实现
"""
from linars.edu.memphis.ccrg.alife.elements.alife_object_impl import ALifeObjectImpl

class AgentObject(ALifeObjectImpl):
    """
    ALife环境中的智能体对象

    该类表示ALife环境中的智能体
    """

    def get_icon_id(self) -> int:
        """
        根据智能体方向获取对应的图标ID

        返回:
            图标ID
        """
        direction = self.get_attribute("direction")
        icon = 3  # 默认图标(朝南)

        if direction == 'S':
            icon = 3
        elif direction == 'N':
            icon = 4
        elif direction == 'E':
            icon = 5
        elif direction == 'W':
            icon = 6

        return icon
