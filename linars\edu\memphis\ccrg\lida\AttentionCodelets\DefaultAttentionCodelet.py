# LIDA Cognitive Framework
"""
Default AttentionCodelet which seeks to create a Coalition
from the most activate content above a threshold.
"""

import logging
from typing import Dict, Any, List, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructureImpl import NodeStructure<PERSON>mpl
from linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WorkspaceBuffer import WorkspaceBuffer
from linars.edu.memphis.ccrg.lida.AttentionCodelets.AttentionCodeletImpl import AttentionCodeletImpl

class DefaultAttentionCodelet(AttentionCodeletImpl):
    """
    Default AttentionCodelet which seeks to create a Coalition
    from the most activate content above a threshold.
    """
    
    # Default values
    DEFAULT_ATTENTION_THRESHOLD = 0.0
    DEFAULT_RETRIEVAL_DEPTH = 0
    
    def __init__(self, ticks_per_run: int = 1):
        """
        Initialize a DefaultAttentionCodelet.
        
        Args:
            ticks_per_run: The number of ticks between runs of this codelet
        """
        super().__init__(ticks_per_run)
        self.attention_threshold = self.DEFAULT_ATTENTION_THRESHOLD
        self.retrieval_depth = self.DEFAULT_RETRIEVAL_DEPTH
        self.active_nodes: List[Node] = []
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def init(self, params: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize this codelet with the given parameters.
        If this method is overridden, this init() must be called first! i.e. use super().init()
        Will set parameters with the following names:
        
        attentionThreshold: Threshold content must have to be added to a Coalition
        retrievalDepth: Depth of neighboring nodes retrieved from most active content
        
        If any parameter is not specified its default value will be used.
        
        Args:
            params: Parameters for initialization
        """
        super().init(params)
        self.attention_threshold = self.get_param("attentionThreshold", self.DEFAULT_ATTENTION_THRESHOLD)
        self.retrieval_depth = self.get_param("retrievalDepth", self.DEFAULT_RETRIEVAL_DEPTH)
    
    def buffer_contains_sought_content(self, buffer: WorkspaceBuffer) -> bool:
        """
        Returns true if specified buffer contains at least one node above attention_threshold.
        Sets the most activated node as the codelet's new sought content.
        
        Args:
            buffer: The WorkspaceBuffer to be checked for content
            
        Returns:
            True if the buffer contains the sought content, False otherwise
        """
        max_activation = -1.0
        max_active_node = None
        ns = buffer.get_buffer_content(None)
        
        for node in ns.get_nodes():
            activation = node.get_activation()
            if activation >= self.attention_threshold and activation > max_activation:
                max_activation = activation
                max_active_node = node
        
        if max_active_node is not None:
            self.active_nodes.clear()
            self.active_nodes.append(max_active_node)
            return True
        
        return False
    
    def retrieve_workspace_content(self, buffer: WorkspaceBuffer) -> NodeStructure:
        """
        Retrieves content from the specified WorkspaceBuffer.
        
        Args:
            buffer: The WorkspaceBuffer to retrieve content from Returns:
            The retrieved content
        """
        result = NodeStructureImpl()
        
        if not self.active_nodes:
            return result
        
        # Add the active nodes to the result
        for node in self.active_nodes:
            result.add_default_node(node)
        
        # If retrieval depth is greater than 0, add neighboring nodes
        if self.retrieval_depth > 0:
            ns = buffer.get_buffer_content(None)
            
            for node in self.active_nodes:
                # Add links connected to the active node
                for link in ns.get_links_of_source(node):
                    result.add_default_link(link)
                
                for link in ns.get_links_of_sink(node):
                    result.add_default_link(link)
        
        return result
