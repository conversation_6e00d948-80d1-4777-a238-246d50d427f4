# LIDA Cognitive Framework
"""
Class representing the name of a module in the LIDA framework.
"""

from typing import Dict, Optional

class ModuleName:
    """
    Class representing the name of a module in the LIDA framework.
    """

    # Static dictionary to store module names
    _module_names: Dict[str, 'ModuleName'] = {}

    # Predefined module names
    UnnamedModule = None  # Will be initialized below
    Agent = None
    Environment = None
    SensoryMemory = None
    SensoryMotorMemory = None
    PerceptualAssociativeMemory = None
    PAM = None
    PAMemory = None
    TransientEpisodicMemory = None
    DeclarativeMemory = None
    ProceduralMemory = None
    SpatialMemory = None
    Workspace = None
    PerceptualBuffer = None
    EpisodicBuffer = None
    ConsciousContentsQueue = None
    BroadcastQueue = None
    CurrentSituationalModel = None
    CurrentSM = None  # Alias for CurrentSituationalModel
    GlobalWorkspace = None
    AttentionModule = None
    ActionSelection = None
    SensoryMotorSystem = None
    StructureBuildingCodeletModule = None

    # Additional buffers from Java source
    NonGraph = None
    FeelGraph = None
    GoalGraph = None
    SeqGraph = None
    ConcentGraph = None
    VisionGraph = None
    ListenGraph = None
    TextGraph = None
    VVGraph = None
    VListenGraph = None
    WordGraph = None
    SceneGraph = None
    GrammarGraph = None
    UnderstandGraph = None
    narmemory = None

    def __init__(self, name: str):
        """
        Initialize a ModuleName with the given name.

        Args:
            name: The name of the module
        """
        self.name = name
        ModuleName._module_names[name] = self

    @staticmethod
    def get_module_name(name: str) -> Optional['ModuleName']:
        """
        Get a ModuleName by its string name.

        Args:
            name: The string name of the module

        Returns:
            The ModuleName object, or None if not found
        """
        return ModuleName._module_names.get(name)

    @staticmethod
    def add_module_name(name: str) -> 'ModuleName':
        """
        Creates and adds a new module name if name is not already defined.
        Returns new ModuleName or existing ModuleName associated with the name.

        Args:
            name: The string name of the module

        Returns:
            The ModuleName object
        """
        if name not in ModuleName._module_names:
            return ModuleName(name)
        return ModuleName._module_names[name]

    def __str__(self) -> str:
        """
        Return the string representation of this ModuleName.

        Returns:
            The name of this ModuleName
        """
        return self.name

    def __eq__(self, other) -> bool:
        """
        Check if this ModuleName is equal to another.

        Args:
            other: The other ModuleName to compare with

        Returns:
            True if the names are equal, False otherwise
        """
        if isinstance(other, ModuleName):
            return self.name == other.name
        return False

    def __hash__(self) -> int:
        """
        Return the hash code of this ModuleName.

        Returns:
            The hash code of the name
        """
        return hash(self.name)

# Initialize predefined module names
ModuleName.UnnamedModule = ModuleName("UnnamedModule")
ModuleName.Agent = ModuleName("Agent")
ModuleName.Environment = ModuleName("Environment")
ModuleName.SensoryMemory = ModuleName("SensoryMemory")
ModuleName.SensoryMotorMemory = ModuleName("SensoryMotorMemory")
ModuleName.PerceptualAssociativeMemory = ModuleName("PerceptualAssociativeMemory")
ModuleName.PAM = ModuleName("PAM")
ModuleName.PAMemory = ModuleName("PAMemory")

ModuleName.TransientEpisodicMemory = ModuleName("TransientEpisodicMemory")
ModuleName.DeclarativeMemory = ModuleName("DeclarativeMemory")
ModuleName.ProceduralMemory = ModuleName("ProceduralMemory")
ModuleName.SpatialMemory = ModuleName("SpatialMemory")
ModuleName.Workspace = ModuleName("Workspace")
ModuleName.PerceptualBuffer = ModuleName("PerceptualBuffer")
ModuleName.EpisodicBuffer = ModuleName("EpisodicBuffer")
ModuleName.ConsciousContentsQueue = ModuleName("ConsciousContentsQueue")
ModuleName.BroadcastQueue = ModuleName("BroadcastQueue")
ModuleName.CurrentSituationalModel = ModuleName("CurrentSituationalModel")
ModuleName.CurrentSM = ModuleName("CurrentSituationalModel")  # Alias for CurrentSituationalModel
ModuleName.GlobalWorkspace = ModuleName("GlobalWorkspace")
ModuleName.AttentionModule = ModuleName("AttentionModule")
ModuleName.ActionSelection = ModuleName("ActionSelection")
ModuleName.SensoryMotorSystem = ModuleName("SensoryMotorSystem")
ModuleName.StructureBuildingCodeletModule = ModuleName("StructureBuildingCodeletModule")

# Initialize additional buffers
ModuleName.NonGraph = ModuleName("NonGraph")
ModuleName.FeelGraph = ModuleName("FeelGraph")
ModuleName.GoalGraph = ModuleName("GoalGraph")
ModuleName.SeqGraph = ModuleName("SeqGraph")
ModuleName.ConcentGraph = ModuleName("ConcentGraph")
ModuleName.VisionGraph = ModuleName("VisionGraph")
ModuleName.ListenGraph = ModuleName("ListenGraph")
ModuleName.TextGraph = ModuleName("TextGraph")
ModuleName.VVGraph = ModuleName("VVGraph")
ModuleName.VListenGraph = ModuleName("VListenGraph")
ModuleName.WordGraph = ModuleName("WordGraph")
ModuleName.SceneGraph = ModuleName("SceneGraph")
ModuleName.GrammarGraph = ModuleName("GrammarGraph")
ModuleName.UnderstandGraph = ModuleName("UnderstandGraph")
ModuleName.narmemory = ModuleName("narmemory")
