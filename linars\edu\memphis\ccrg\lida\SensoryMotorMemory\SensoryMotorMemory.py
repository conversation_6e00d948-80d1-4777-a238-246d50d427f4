# LIDA Cognitive Framework
"""
Sensory Motor Memory is a module which receives selected actions from ActionSelection and
content from SensoryMemory. It contains the algorithm for a selected action. When it executes an algorithm it
directly calls a method in the environment (doesn't use a listener).
"""

from abc import abstractmethod
from typing import Any
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.SensoryMotorMemory.SensoryMotorMemoryListener import SensoryMotorMemoryListener

class SensoryMotorMemory(FrameworkModule):
    """
    Sensory Motor Memory is a module which receives selected actions from ActionSelection and
    content from SensoryMemory. It contains the algorithm for a selected action. When it executes an algorithm it
    directly calls a method in the environment (doesn't use a listener).
    """
    
    @abstractmethod
    def add_sensory_motor_memory_listener(self, listener: SensoryMotorMemoryListener) -> None:
        """
        Any non-environment communication should use listeners.
        
        Args:
            listener: The SensoryMotorMemoryListener to add
        """
        pass
    
    @abstractmethod
    def send_actuator_command(self, command: Any) -> None:
        """
        Executes specified action algorithm.
        
        Args:
            command: Algorithm to execute in the agent's actuators or directly in the environment
        """
        pass
