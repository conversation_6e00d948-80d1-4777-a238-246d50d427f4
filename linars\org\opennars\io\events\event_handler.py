"""
NARS事件处理器
提供事件处理的基础功能
"""
from typing import List, Any, Optional, Type

from linars.org.opennars.io.events.event_emitter import EventEmitter

class EventHandler(EventEmitter.EventObserver):
    """
    NARS事件处理器
    继承自事件观察者，提供事件激活状态管理功能
    """

    def __init__(self, source, active: bool, *events: List[Type]):
        """
        构造函数

        参数:
            source: 事件源
            active: 初始激活状态
            events: 要处理的事件类型列表
        """
        self.source = source
        self.active = False
        self.events = events
        self.set_active(active)

    def set_active(self, b: bool):
        """
        设置处理器激活状态

        参数:
            b: 是否激活(True激活/False禁用)
        """
        if self.active == b:
            return

        self.active = b
        self.source.set(self, b, *self.events)

    def is_active(self) -> bool:
        """
        检查处理器是否激活

        返回:
            bool: 是否处于激活状态
        """
        return self.active
