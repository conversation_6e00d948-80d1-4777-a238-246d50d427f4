# LIDA Cognitive Framework
"""
Implementation of the Environment interface.
"""

import logging
from typing import Dict, Any, Optional, Set, List

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import Module<PERSON><PERSON>ener
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawner import TaskSpawner
from linars.edu.memphis.ccrg.lida.Framework.Initialization.InitializableImpl import InitializableImpl
from linars.edu.memphis.ccrg.lida.Environment.Environment import Environment

class EnvironmentImpl(InitializableImpl, Environment):
    """
    Implementation of the Environment interface.
    """

    def __init__(self):
        """Initialize the environment."""
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.task_spawner = None
        self.properties = {}
        self.module_name = None
        self.submodules = {}
        self.listeners = []

    def init(self, params=None):
        """Initialize this environment.

        Args:
            params: Parameters for initialization, defaults to None
        """
        super().init(params)

    def get_state(self, params: Dict[str, Any]) -> Any:
        """
        Get the current state of the environment.

        Args:
            params: Parameters for getting the state

        Returns:
            The current state of the environment
        """
        return None

    def process_action(self, action: Any) -> None:
        """
        Process an action in the environment.

        Args:
            action: The action to process
        """
        pass

    def reset_state(self) -> None:
        """Reset the environment state."""
        pass

    def get_module_content(self, *params) -> Any:
        """
        Get the module content.

        Args:
            params: Parameters for getting the content

        Returns:
            The module content
        """
        return None

    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.

        Args:
            ticks: The number of ticks to decay by
        """
        pass

    def get_module_name(self) -> ModuleName:
        """
        Gets moduleName.

        Returns:
            ModuleName of this FrameworkModule
        """
        return self.module_name

    def set_module_name(self, module_name: ModuleName) -> None:
        """
        Sets ModuleName.

        Args:
            module_name: ModuleName of this FrameworkModule
        """
        self.module_name = module_name

    def contains_submodule(self, name: ModuleName) -> bool:
        """
        Returns whether this FrameworkModule contains a submodule with
        specified ModuleName.

        Args:
            name: ModuleName of submodule

        Returns:
            True if there is a FrameworkModule with specified ModuleName
            in this FrameworkModule
        """
        return name in self.submodules

    def contains_submodule_by_string(self, name: str) -> bool:
        """
        Returns whether this FrameworkModule contains a submodule with
        specified name.

        Args:
            name: Name of submodule

        Returns:
            True if there is a FrameworkModule with specified name
            in this FrameworkModule
        """
        for module_name in self.submodules.keys():
            if module_name.name == name:
                return True
        return False

    def get_submodule(self, name: ModuleName) -> Optional[FrameworkModule]:
        """
        Gets specified submodule.

        Args:
            name: Name of the desired submodule.

        Returns:
            The submodule.
        """
        return self.submodules.get(name)

    def get_submodule_by_string(self, name: str) -> Optional[FrameworkModule]:
        """
        Gets specified submodule.

        Args:
            name: Name of the desired submodule.

        Returns:
            The submodule.
        """
        for module_name, module in self.submodules.items():
            if module_name.name == name:
                return module
        return None

    def add_submodule(self, module: FrameworkModule) -> None:
        """
        Adds submodule as a component of this FrameworkModule.

        Args:
            module: Submodule to add
        """
        self.submodules[module.get_module_name()] = module

    def task_manager_decay_module(self, ticks: int) -> None:
        """
        Decay this module and all its submodules.
        Framework users should not call this method. It will be called by the TaskManager.
        Decays this module and all its submodules.

        Args:
            ticks: Number of ticks to decay.
        """
        self.decay_module(ticks)
        for module in self.submodules.values():
            module.task_manager_decay_module(ticks)

    def add_listener(self, listener: ModuleListener) -> None:
        """
        Generic way to add various kinds of listeners.

        Args:
            listener: Listener of this FrameworkModule
        """
        self.listeners.append(listener)

    def set_assisting_task_spawner(self, ts: TaskSpawner) -> None:
        """
        Specify the TaskSpawner which this FrameworkModule will use to spawn tasks.

        Args:
            ts: The TaskSpawner
        """
        self.task_spawner = ts

    def get_assisting_task_spawner(self) -> TaskSpawner:
        """
        Returns the TaskSpawner which this FrameworkModule uses to spawn tasks.

        Returns:
            The assisting task spawner
        """
        return self.task_spawner

    def get_submodules(self) -> Dict[ModuleName, FrameworkModule]:
        """
        Convenience method to get submodules

        Returns:
            Map of submodules by ModuleName
        """
        return self.submodules

    def set_associated_module(self, module: FrameworkModule) -> None:
        """
        Sets an associated module.

        Args:
            module: The module to associate with this module
        """
        pass
