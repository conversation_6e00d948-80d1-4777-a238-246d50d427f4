"""
优先级(当前)、持久性(衰减)和质量(长期平均)的三元组。

用于控制资源分配。
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar
import math

from linars.org.opennars.entity.truth_value import TruthValue

# Symbols
BUDGET_VALUE_MARK = '$'
VALUE_SEPARATOR = ';'

class BudgetValue:
    """
    优先级(当前)、持久性(衰减)和质量(长期平均)的三元组。
    """

    def __init__(self, p: float, d: float, q_or_truth: Union[float, TruthValue], narParameters = None):
        """
        带初始化的构造函数

        参数:
            p: 初始优先级
            d: 初始持久性
            q_or_truth: 初始质量或真值
            narParameters: NAR参数
        """
        self.narParameters = narParameters

        # Convert TruthValue to quality if needed
        if isinstance(q_or_truth, TruthValue):
            from linars.org.opennars.inference.budget_functions import BudgetFunctions
            q = BudgetFunctions.truth_to_quality(q_or_truth)
        else:
            q = q_or_truth

        # Relative share of time resource to be allocated
        self.priority = p

        # The percent of priority to be kept in a constant period
        self.durability = d

        # Overall (context-independent) evaluation
        self.quality = q

        # Time at which this budget was last forgotten
        self.last_forget_time = -1

        # Ensure values are within valid ranges
        if self.narParameters:
            if d >= 1.0:
                self.durability = 1.0 - self.narParameters.TRUTH_EPSILON

            if p > 1.0:
                self.priority = 1.0

    @classmethod
    def from_budget(cls, v: 'BudgetValue') -> 'BudgetValue':
        """
        克隆构造函数

        参数:
            v: 要克隆的预算值

        返回:
            BudgetValue: 克隆后的预算值
        """
        return cls(v.get_priority(), v.get_durability(), v.get_quality(), v.narParameters)

    def clone(self) -> 'BudgetValue':
        """
        克隆方法

        返回:
            BudgetValue: 克隆后的预算值
        """
        return BudgetValue(self.get_priority(), self.get_durability(), self.get_quality(), self.narParameters)

    def get_priority(self) -> float:
        """
        获取优先级值

        返回:
            float: 当前优先级
        """
        return self.priority

    def set_priority(self, v: float) -> 'BudgetValue':
        """
        修改优先级值

        参数:
            v: 新的优先级值

        返回:
            BudgetValue: 自身
        """
        if v > 1.0:
            v = 1.0
        self.priority = v
        return self

    def inc_priority(self, v: float):
        """
        按剩余范围的百分比增加优先级值

        参数:
            v: 增加的百分比
        """
        self.set_priority(min(1.0, self.or_op(self.priority, v)))

    def and_priority(self, v: float):
        """
        将优先级与另一个值进行AND(乘法)运算

        参数:
            v: 要进行AND运算的值
        """
        self.set_priority(self.and_op(self.priority, v))

    def dec_priority(self, v: float):
        """
        按剩余范围的百分比减少优先级值

        参数:
            v: 减少的百分比
        """
        self.set_priority(self.and_op(self.priority, v))

    def get_durability(self) -> float:
        """
        获取持久性值

        返回:
            float: 当前持久性
        """
        return self.durability

    def set_durability(self, d: float) -> 'BudgetValue':
        """
        修改持久性值

        参数:
            d: 新的持久性值

        返回:
            BudgetValue: 自身
        """
        if d >= 1.0 and self.narParameters:
            d = 1.0 - self.narParameters.TRUTH_EPSILON
        self.durability = d
        return self

    def inc_durability(self, v: float):
        """
        按剩余范围的百分比增加持久性值

        参数:
            v: 增加的百分比
        """
        durability2 = self.or_op(self.durability, v)
        if durability2 >= 1.0 and self.narParameters:
            durability2 = 1.0 - self.narParameters.TRUTH_EPSILON  # Put into allowed range
        self.durability = durability2

    def dec_durability(self, v: float):
        """
        按剩余范围的百分比减少持久性值

        参数:
            v: 减少的百分比
        """
        self.durability = self.and_op(self.durability, v)

    def get_quality(self) -> float:
        """
        获取质量值

        返回:
            float: 当前质量
        """
        return self.quality

    def set_quality(self, v: float) -> 'BudgetValue':
        """
        修改质量值

        参数:
            v: 新的质量值

        返回:
            BudgetValue: 自身
        """
        self.quality = v
        return self

    def inc_quality(self, v: float):
        """
        按剩余范围的百分比增加质量值

        参数:
            v: 增加的百分比
        """
        self.quality = self.or_op(self.quality, v)

    def dec_quality(self, v: float):
        """
        按剩余范围的百分比减少质量值

        参数:
            v: 减少的百分比
        """
        self.quality = self.and_op(self.quality, v)

    def merge(self, that: 'BudgetValue'):
        """
        将一个BudgetValue合并到另一个

        参数:
            that: 另一个预算值
        """
        from linars.org.opennars.inference.budget_functions import BudgetFunctions
        BudgetFunctions.merge(self, that)

    def greater_than(self, rhs: 'BudgetValue') -> bool:
        """
        检查此预算在所有量上是否大于另一个预算

        参数:
            rhs: 要比较的预算值

        返回:
            bool: 如果更大则为True
        """
        if not self.narParameters:
            threshold = 0.01
        else:
            threshold = self.narParameters.BUDGET_THRESHOLD

        return ((self.get_priority() - rhs.get_priority() > threshold) and
                (self.get_durability() - rhs.get_durability() > threshold) and
                (self.get_quality() - rhs.get_quality() > threshold))

    def summary(self) -> float:
        """
        将BudgetValue汇总为[0,1]范围内的单个数字

        返回:
            float: 汇总值
        """
        return self.durability * (self.priority + self.quality) / 2.0

    def equals_by_precision(self, that: Any) -> bool:
        """
        检查两个预算值在精度上是否相等

        参数:
            that: 要比较的对象

        返回:
            bool: 如果精度相等则为True
        """
        if isinstance(that, BudgetValue):
            if not self.narParameters:
                epsilon = 0.01
            else:
                epsilon = self.narParameters.TRUTH_EPSILON

            d_prio = abs(self.get_priority() - that.get_priority())
            if d_prio >= epsilon:
                return False

            d_dura = abs(self.get_durability() - that.get_durability())
            if d_dura >= epsilon:
                return False

            d_qual = abs(self.get_quality() - that.get_quality())
            return d_qual < epsilon

        return False

    def above_threshold(self) -> bool:
        """
        预算是否应该被处理

        返回:
            bool: 是否处理该项目的决定
        """
        if not self.narParameters:
            threshold = 0.01
        else:
            threshold = self.narParameters.BUDGET_THRESHOLD

        return self.summary() >= threshold

    def __str__(self) -> str:
        """
        完整显示BudgetValue

        返回:
            str: 值的字符串表示
        """
        return f"{BUDGET_VALUE_MARK}{self.priority:.4f}{VALUE_SEPARATOR}{self.durability:.4f}{VALUE_SEPARATOR}{self.quality:.4f}{BUDGET_VALUE_MARK}"

    def to_string_external(self) -> str:
        """
        简要显示BudgetValue

        返回:
            str: 保留2位精度的值的字符串表示
        """
        return f"{BUDGET_VALUE_MARK}{self.priority:.2f}{VALUE_SEPARATOR}{self.durability:.2f}{VALUE_SEPARATOR}{self.quality:.2f}{BUDGET_VALUE_MARK}"

    def set_last_forget_time(self, current_time: int) -> int:
        """
        计算周期并将当前时间设置为周期

        参数:
            current_time: 当前时间

        返回:
            int: 时间周期: currentTime - lastForgetTime
        """
        if self.last_forget_time == -1:
            period = 0
        else:
            period = current_time - self.last_forget_time

        self.last_forget_time = current_time

        return period

    def get_last_forget_time(self) -> int:
        """
        获取上次遗忘时间

        返回:
            int: 上次遗忘时间
        """
        return self.last_forget_time

    @staticmethod
    def or_op(a: float, b: float) -> float:
        """
        两个值的逻辑OR运算

        参数:
            a: 第一个值
            b: 第二个值

        返回:
            float: OR运算结果
        """
        return a + b - (a * b)

    @staticmethod
    def and_op(a: float, b: float) -> float:
        """
        两个值的逻辑AND运算

        参数:
            a: 第一个值
            b: 第二个值

        返回:
            float: AND运算结果
        """
        return a * b
