"""
蕴含关系项定义
按照NARS理论定义的蕴含关系操作
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar

from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.language.statement import Statement, NativeOperator

# Temporal order constants
ORDER_NONE = 0
ORDER_FORWARD = 1
ORDER_CONCURRENT = 2
ORDER_BACKWARD = 3

class Implication(Statement):
    """
    蕴含关系项类
    按照NARS理论定义的蕴含关系操作
    """

    def __init__(self, arg: List[Term] = None, order: int = ORDER_NONE, counter: int = 1):
        """
        构造函数

        参数:
            arg: 项的组成部分列表
            order: 时间顺序(ORDER_NONE/ORDER_FORWARD/ORDER_CONCURRENT/ORDER_BACKWARD)
            counter: 用于证据追踪的计数器
        """
        self.temporal_order = order
        self.counter = counter
        super().__init__(arg)

    @classmethod
    def create(cls, subject: Term, predicate: Term, order: int) -> 'Implication':
        """
        创建蕴含关系语句

        参数:
            subject: 主语项
            predicate: 谓语项
            order: 时间顺序

        返回:
            Implication: 创建的语句
        """
        return cls([subject, predicate], order)

    def clone(self, replaced=None) -> 'Implication':
        """
        克隆对象

        参数:
            replaced: 可选的替换项列表

        返回:
            Implication: 新的蕴含关系对象
        """
        if replaced is None:
            return Implication(self.term, self.get_temporal_order(), self.counter)
        else:
            return self.clone_with_terms(replaced)

    def clone_with_terms(self, t: List[Term]) -> Optional['Implication']:
        """
        使用新项克隆对象

        参数:
            t: 新的项列表

        返回:
            Implication: 克隆后的对象
        """
        if t is None:
            return None

        if len(t) != 2:
            raise ValueError(f"Implication requires 2 components: {t}")

        return self.make(t[0], t[1], self.temporal_order)

    @staticmethod
    def make(subject: Term, predicate: Term, temporal_order: int = ORDER_NONE) -> Optional['Implication']:
        """
        尝试从两个项创建新的蕴含关系项

        参数:
            subject: 第一个组件(主语)
            predicate: 第二个组件(谓语)
            temporal_order: 时间顺序

        返回:
            Implication: 生成的复合项或None，如果发生异常则返回None
        """
        try:
            # 参数验证
            if subject is None:
                print(f"警告: Implication.make 收到了空的主语")
                return None

            if predicate is None:
                print(f"警告: Implication.make 收到了空的谓语")
                return None

            # 检查无效的语句
            try:
                check_temporal = temporal_order != ORDER_FORWARD and temporal_order != ORDER_CONCURRENT
                if Statement.invalid_statement(subject, predicate, check_temporal):
                    return None
            except Exception as e:
                print(f"警告: 在检查语句有效性时发生异常: {e}")
                # 继续执行，不要因为这个检查失败而中断整个方法

            # 检查无效的项类型
            try:
                from linars.org.opennars.language.equivalence import Equivalence

                # 检查subject是否为蕴含或等价关系
                if isinstance(subject, Implication) or isinstance(subject, Equivalence):
                    return None

                # 检查predicate是否为等价关系
                if isinstance(predicate, Equivalence):
                    return None
            except Exception as e:
                print(f"警告: 在检查项类型时发生异常: {e}")
                # 继续执行，不要因为这个检查失败而中断整个方法

            # 检查是否为间隔项
            try:
                if ((hasattr(subject, 'is_interval') and callable(getattr(subject, 'is_interval')) and subject.is_interval()) or
                    (hasattr(predicate, 'is_interval') and callable(getattr(predicate, 'is_interval')) and predicate.is_interval())):
                    return None
            except Exception as e:
                print(f"警告: 在检查是否为间隔项时发生异常: {e}")
                # 继续执行，不要因为这个检查失败而中断整个方法

            # 处理嵌套的蕴含关系
            if isinstance(predicate, Implication):
                try:
                    old_condition = predicate.get_subject()

                    # 检查连接词是否包含主语
                    try:
                        if (hasattr(old_condition, 'is_conjunction') and callable(getattr(old_condition, 'is_conjunction')) and
                            old_condition.is_conjunction()):
                            if (hasattr(old_condition, 'contains_term') and callable(getattr(old_condition, 'contains_term')) and
                                old_condition.contains_term(subject)):
                                return None
                    except Exception as e:
                        print(f"警告: 在检查连接词是否包含主语时发生异常: {e}")
                        # 继续执行，不要因为这个检查失败而中断整个方法

                    # 获取顺序和空间属性
                    order = temporal_order
                    spatial = False

                    # 如果主语是连接词，使用其时间顺序和空间属性
                    try:
                        if (hasattr(subject, 'is_conjunction') and callable(getattr(subject, 'is_conjunction')) and
                            subject.is_conjunction()):
                            conj = subject
                            if hasattr(conj, 'get_temporal_order') and callable(getattr(conj, 'get_temporal_order')):
                                order = conj.get_temporal_order()
                            if hasattr(conj, 'get_is_spatial') and callable(getattr(conj, 'get_is_spatial')):
                                spatial = conj.get_is_spatial()
                    except Exception as e:
                        print(f"警告: 在获取连接词属性时发生异常: {e}")
                        # 继续执行，不要因为这个检查失败而中断整个方法

                    # 创建新的条件
                    try:
                        from linars.org.opennars.language.conjunction import Conjunction
                        new_condition = Conjunction.make_term_with_order(subject, old_condition, order, spatial)
                        if new_condition is None:
                            print(f"警告: 创建新的条件失败")
                            return Implication([subject, predicate], temporal_order)
                    except Exception as e:
                        print(f"错误: 在创建新的条件时发生异常: {e}")
                        return Implication([subject, predicate], temporal_order)

                    # 递归创建新的蕴含关系
                    try:
                        pred_predicate = predicate.get_predicate()
                        return Implication.make(new_condition, pred_predicate, temporal_order)
                    except Exception as e:
                        print(f"错误: 在递归创建新的蕴含关系时发生异常: {e}")
                        return Implication([subject, predicate], temporal_order)
                except Exception as e:
                    print(f"错误: 在处理嵌套的蕴含关系时发生异常: {e}")
                    # 尝试创建普通的蕴含关系
                    try:
                        return Implication([subject, predicate], temporal_order)
                    except Exception as inner_e:
                        print(f"错误: 在创建普通的蕴含关系时发生异常0: {inner_e}")
                        return None
            else:
                # 创建普通的蕴含关系
                try:
                    return Implication([subject, predicate], temporal_order)
                except Exception as e:
                    print(f"错误: 在创建普通的蕴含关系时发生异常1: {e}")
                    Implication([subject, predicate], temporal_order)
                    return None

        except Exception as e:
            print(f"错误: Implication.make方法发生未处理的异常: {e}")
            return None

    def make_name(self) -> str:
        """
        重写默认方法，从现有字段创建当前项的名称

        返回:
            str: 项的名称
        """
        return self.make_implication_name(self.get_subject(), self.temporal_order, self.get_predicate())

    @staticmethod
    def make_implication_name(subject: Term, temporal_order: int, predicate: Term) -> str:
        """
        为蕴含关系创建名称

        参数:
            subject: 主语项
            temporal_order: 时间顺序
            predicate: 谓语项

        返回:
            str: 名称字符串
        """
        if temporal_order == ORDER_FORWARD:
            copula = NativeOperator.IMPLICATION_AFTER
        elif temporal_order == ORDER_CONCURRENT:
            copula = NativeOperator.IMPLICATION_WHEN
        elif temporal_order == ORDER_BACKWARD:
            copula = NativeOperator.IMPLICATION_BEFORE
        else:
            copula = NativeOperator.IMPLICATION

        return Statement.make_statement_name(subject, copula, predicate)

    def operator(self) -> NativeOperator:
        """
        获取项的操作符

        返回:
            NativeOperator: 项的操作符
        """
        if self.temporal_order == ORDER_FORWARD:
            return NativeOperator.IMPLICATION_AFTER
        elif self.temporal_order == ORDER_CONCURRENT:
            return NativeOperator.IMPLICATION_WHEN
        elif self.temporal_order == ORDER_BACKWARD:
            return NativeOperator.IMPLICATION_BEFORE

        return NativeOperator.IMPLICATION

    def get_temporal_order(self) -> int:
        """
        获取时间顺序

        返回:
            int: 时间顺序
        """
        return self.temporal_order

    def is_forward(self) -> bool:
        """
        检查是否为正向蕴含

        返回:
            bool: 如果是正向蕴含则返回True
        """
        return self.get_temporal_order() == ORDER_FORWARD

    def is_backward(self) -> bool:
        """
        检查是否为反向蕴含

        返回:
            bool: 如果是反向蕴含则返回True
        """
        return self.get_temporal_order() == ORDER_BACKWARD

    def is_concurrent(self) -> bool:
        """
        检查是否为并发蕴含

        返回:
            bool: 如果是并发蕴含则返回True
        """
        return self.get_temporal_order() == ORDER_CONCURRENT
