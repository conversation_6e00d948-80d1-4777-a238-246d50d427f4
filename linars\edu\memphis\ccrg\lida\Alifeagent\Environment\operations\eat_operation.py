"""
进食操作实现

本模块提供ALife环境中进食操作的实现
"""
from typing import List, Optional, Any

from linars.edu.memphis.ccrg.alife.elements.alife_object import ALifeObject
from linars.edu.memphis.ccrg.alife.elements.cell import Cell
from linars.edu.memphis.ccrg.alife.opreations.world_operation import WorldOperation

class EatOperation(WorldOperation):
    """
    进食操作实现

    该类实现ALife环境中的进食操作
    """

    def __init__(self):
        """初始化进食操作"""
        super().__init__("eat")

    def execute(self, actor: ALifeObject, target: Optional[ALifeObject], *params) -> bool:
        """
        执行进食操作

        参数:
            actor: 执行进食行为的对象
            target: 进食目标(未使用)
            params: 附加参数

        返回:
            操作成功返回True，否则返回False
        """
        # Get the actor's cell
        cell = actor.get_container()
        if not isinstance(cell, Cell):
            return False

        # Get objects in the cell
        objects = cell.get_objects()
        food_found = False

        # Look for food objects
        for obj in objects:
            if obj != actor and "food" in obj.get_name().lower():
                # Increase health when eating food
                actor.increase_health(0.2)
                # Remove the food from the cell
                cell.remove_object(obj)
                food_found = True
                break

        return food_found
