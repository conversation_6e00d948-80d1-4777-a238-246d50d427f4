# LIDA认知框架
"""
可链接对象的默认实现
"""

from typing import Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable
from linars.edu.memphis.ccrg.lida.Framework.Shared.ExtendedId import ExtendedId
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.ActivatibleImpl import ActivatibleImpl
from linars.edu.memphis.ccrg.lida.Framework.Initialization.InitializableImpl import InitializableImpl

class LinkableImpl(ActivatibleImpl, InitializableImpl, Linkable):
    """
    可链接对象的默认实现
    """

    def __init__(self):
        """
        初始化可链接对象实现
        """
        super().__init__()
        self.label = ""
        self.factory_type = ""
        self.properties: Dict[str, Any] = {}

    def get_label(self) -> str:
        """
        获取标签

        返回:
            可读标签
        """
        return self.label

    def set_label(self, label: str) -> None:
        """
        设置标签

        参数:
            label: 可读标签
        """
        self.label = label

    def get_extended_id(self) -> ExtendedId:
        """
        获取扩展ID

        返回:
            可链接对象的通用ID
        """
        # This method should be overridden by subclasses
        return ExtendedId()

    def get_factory_type(self) -> str:
        """
        获取工厂类型

        返回:
            可链接对象的工厂类型
        """
        return self.factory_type

    def set_factory_type(self, factory_type: str) -> None:
        """
        设置工厂类型

        参数:
            factory_type: 可链接对象的工厂类型
        """
        self.factory_type = factory_type

    def set_properties(self, properties: Dict[str, Any]) -> None:
        """
        设置属性

        参数:
            properties: 要设置的属性
        """
        self.properties = properties

    def get_properties(self) -> Dict[str, Any]:
        """
        获取属性

        返回:
            属性字典
        """
        return self.properties

    def get_property(self, key: str) -> Any:
        """
        获取指定属性

        参数:
            key: 属性键

        返回:
            属性值
        """
        return self.properties.get(key)

    def is_node(self) -> bool:
        """
        返回此可链接对象是否为节点

        返回:
            如果是节点返回True，否则返回False
        """
        # This method should be overridden by subclasses
        return False

    def init(self, params: Optional[Dict[str, Any]] = None) -> None:
        """
        使用给定参数初始化此可链接对象

        参数:
            params: 初始化参数
        """
        super().init(params)
        if params is not None:
            if "label" in params:
                self.label = params["label"]
            if "factoryType" in params:
                self.factory_type = params["factoryType"]
            if "properties" in params:
                self.properties = params["properties"]
