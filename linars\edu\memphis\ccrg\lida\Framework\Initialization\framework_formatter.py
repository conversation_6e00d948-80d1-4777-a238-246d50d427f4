"""
Framework formatter for formatting log messages.

This module provides a formatter for log messages in the framework.
"""
import logging
import time
from typing import Dict, Any, Optional

class FrameworkFormatter(logging.Formatter):
    """
    Framework formatter for formatting log messages.
    
    This class provides a formatter for log messages in the framework.
    """
    
    def __init__(self, format_str: str = None):
        """
        Initialize the framework formatter.
        
        Args:
            format_str: The format string for log messages
        """
        if format_str is None:
            format_str = "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        
        super().__init__(format_str)
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Format a log record.
        
        Args:
            record: The log record to format
            
        Returns:
            The formatted log message
        """
        # Add the current tick to the record
        record.tick = self.get_current_tick()
        
        # Format the record
        return super().format(record)
    
    def get_current_tick(self) -> int:
        """
        Get the current tick.
        
        Returns:
            The current tick or 0 if not available
        """
        try:
            # Import here to avoid circular imports
            from linars.edu.memphis.ccrg.lida.Framework.Tasks.task_manager import TaskManager
            return TaskManager.get_current_tick()
        except (ImportError, AttributeError):
            return 0
