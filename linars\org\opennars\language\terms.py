"""
静态工具类，提供与Term相关的静态方法
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, Collection, TypeVar, cast
import copy

from linars.edu.memphis.ccrg.linars.term import Term
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.org.opennars.io.symbols import NativeOperator
from linars.org.opennars.io.symbols import SET_EXT_OPENER, SET_INT_OPENER
from linars.org.opennars.entity.term_link import TermLink

T = TypeVar('T')

class Terms:
    """
    静态工具类，提供与Term相关的静态方法
    """

    @staticmethod
    def equal_sub_terms_in_respect_to_image_and_product(a: Term, b: Term) -> bool:
        """
        检查两个项在考虑图像和乘积的情况下是否具有相等的子项

        参数:
            a: 第一个项
            b: 第二个项

        返回:
            bool: 如果子项相等则返回True
        """
        if a is None or b is None:
            return False

        if not (isinstance(a, CompoundTerm) and isinstance(b, CompoundTerm)):
            return a == b

        # 检查是否为Inheritance或Similarity类型
        from linars.org.opennars.language.inheritance import Inheritance
        from linars.org.opennars.language.similarity import Similarity

        if isinstance(a, Inheritance) and isinstance(b, Inheritance):
            return Terms.equal_subject_predicate_in_respect_to_image_and_product(a, b)

        if isinstance(a, Similarity) and isinstance(b, Similarity):
            return (Terms.equal_subject_predicate_in_respect_to_image_and_product(a, b) or
                    Terms.equal_subject_predicate_in_respect_to_image_and_product(b, a))

        # 检查操作符和组件数量
        if len(a.term) != len(b.term) or a.operator() != b.operator():
            return False

        # 检查每个组件
        for i in range(len(a.term)):
            x = a.term[i]
            y = b.term[i]

            if x != y:
                # 递归检查子组件
                if isinstance(x, Inheritance) and isinstance(y, Inheritance):
                    if not Terms.equal_subject_predicate_in_respect_to_image_and_product(x, y):
                        return False
                    continue

                if isinstance(x, Similarity) and isinstance(y, Similarity):
                    if not (Terms.equal_subject_predicate_in_respect_to_image_and_product(x, y) or
                            Terms.equal_subject_predicate_in_respect_to_image_and_product(y, x)):
                        return False
                    continue

                return False

        return True

    @staticmethod
    def equal_subject_predicate_in_respect_to_image_and_product(a: Term, b: Term) -> bool:
        """
        检查两个语句的主语和谓语在考虑图像和乘积的情况下是否相等

        参数:
            a: 第一个语句
            b: 第二个语句

        返回:
            bool: 如果主语和谓语相等则返回True
        """
        if a is None or b is None:
            return False

        # 检查是否为语句类型
        from linars.org.opennars.language.statement import Statement
        from linars.org.opennars.language.similarity import Similarity
        from linars.org.opennars.language.inheritance import Inheritance

        if not (isinstance(a, Statement) and isinstance(b, Statement)):
            return False

        if a == b:
            return True

        # 确保类型匹配
        if not ((isinstance(a, Similarity) and isinstance(b, Similarity)) or
                (isinstance(a, Inheritance) and isinstance(b, Inheritance))):
            return False

        # 获取主语和谓语
        subj_a = a.get_subject()
        pred_a = a.get_predicate()
        subj_b = b.get_subject()
        pred_b = b.get_predicate()

        # 初始化变量
        ta = None
        tb = None
        sa = None
        sb = None

        # 检查各种组合情况
        from linars.org.opennars.language.product import Product
        from linars.org.opennars.language.image_ext import ImageExt
        from linars.org.opennars.language.image_int import ImageInt

        if isinstance(subj_a, Product) and isinstance(pred_b, ImageExt):
            ta = pred_a
            sa = subj_a
            tb = subj_b
            sb = pred_b
        elif isinstance(subj_b, Product) and isinstance(pred_a, ImageExt):
            ta = subj_a
            sa = pred_a
            tb = pred_b
            sb = subj_b
        elif isinstance(pred_a, ImageExt) and isinstance(pred_b, ImageExt):
            ta = subj_a
            sa = pred_a
            tb = subj_b
            sb = pred_b
        elif isinstance(subj_a, ImageInt) and isinstance(subj_b, ImageInt):
            ta = pred_a
            sa = subj_a
            tb = pred_b
            sb = subj_b
        elif isinstance(pred_a, Product) and isinstance(subj_b, ImageInt):
            ta = subj_a
            sa = pred_a
            tb = pred_b
            sb = subj_b
        elif isinstance(pred_b, Product) and isinstance(subj_a, ImageInt):
            ta = pred_a
            sa = subj_a
            tb = subj_b
            sb = pred_b

        if ta is None:
            return False

        # 获取组件
        sat = sa.term
        sbt = sb.term

        # 检查图像索引
        if isinstance(sa, ImageExt) or isinstance(sa, ImageInt):
            if isinstance(sb, ImageExt) or isinstance(sb, ImageInt):
                if hasattr(sa, 'relation_index') and hasattr(sb, 'relation_index'):
                    if sa.relation_index != sb.relation_index:
                        return False

        # 创建组件集合
        components_a = set([ta] + list(sat))
        components_b = set([tb] + list(sbt))

        # 检查每个组件
        for s_a in components_a:
            had = False
            for s_b in components_b:
                # 检查变量
                from linars.org.opennars.language.variable import Variable
                if isinstance(s_a, Variable) and isinstance(s_b, Variable):
                    if s_a.name() == s_b.name():
                        had = True
                        break
                elif s_a == s_b:
                    had = True
                    break

            if not had:
                return False

        return True

    @staticmethod
    def term(compound: Union[CompoundTerm, NativeOperator], components: Union[List[Term], Tuple[Term, ...]]) -> Optional[Term]:
        """
        尝试从模板和项列表创建复合项

        参数:
            compound: 模板或操作符
            components: 组件列表

        返回:
            Term: 复合项或None

        异常:
            ValueError: 当操作符未知或参数无效时抛出
            TypeError: 当类型不匹配时抛出
            Exception: 其他异常情况
        """
        try:
            # 检查参数
            if compound is None:
                raise ValueError("Compound parameter cannot be None")

            if components is None:
                components = []

            # 如果是复合项
            if isinstance(compound, CompoundTerm):
                try:
                    from linars.org.opennars.language.image_ext import ImageExt
                    from linars.org.opennars.language.image_int import ImageInt

                    if isinstance(compound, ImageExt):
                        if hasattr(compound, 'relation_index'):
                            try:
                                return ImageExt(list(components), compound.relation_index)
                            except Exception as e:
                                raise ValueError(f"Error creating ImageExt: {str(e)}")
                    elif isinstance(compound, ImageInt):
                        if hasattr(compound, 'relation_index'):
                            try:
                                return ImageInt.make(list(components), compound.relation_index)
                            except Exception as e:
                                raise ValueError(f"Error creating ImageInt: {str(e)}")
                    else:
                        # 使用操作符创建
                        try:
                            if not hasattr(compound, 'operator'):
                                raise ValueError(f"Compound term does not have operator method: {compound}")
                            operator = compound.operator()
                            if operator is None:
                                raise ValueError(f"Compound term returned None operator: {compound}")
                            return Terms.term(operator, components)
                        except Exception as e:
                            if isinstance(e, ValueError):
                                raise
                            raise ValueError(f"Error getting operator from compound term: {str(e)}")
                except ImportError as e:
                    raise ImportError(f"Error importing required classes: {str(e)}")
                except Exception as e:
                    if isinstance(e, (ValueError, ImportError)):
                        raise
                    raise ValueError(f"Error processing compound term: {str(e)}")

            # 如果是操作符
            if isinstance(compound, NativeOperator):
                try:
                    # 创建组件列表副本
                    try:
                        component_list = list(components)
                    except Exception as e:
                        raise TypeError(f"Cannot convert components to list: {str(e)}")

                    # 导入必要的类
                    try:
                        from linars.org.opennars.language.set_ext import SetExt
                        from linars.org.opennars.language.set_int import SetInt
                        from linars.org.opennars.language.intersection_ext import IntersectionExt
                        from linars.org.opennars.language.intersection_int import IntersectionInt
                        from linars.org.opennars.language.difference_ext import DifferenceExt
                        from linars.org.opennars.language.difference_int import DifferenceInt
                        from linars.org.opennars.language.inheritance import Inheritance
                        from linars.org.opennars.language.product import Product
                        from linars.org.opennars.language.image_ext import ImageExt
                        from linars.org.opennars.language.image_int import ImageInt
                        from linars.org.opennars.language.negation import Negation
                        from linars.org.opennars.language.disjunction import Disjunction
                        from linars.org.opennars.language.conjunction import Conjunction
                        from linars.org.opennars.language.implication import Implication
                        from linars.org.opennars.language.equivalence import Equivalence
                        from linars.org.opennars.inference.temporal_rules import TemporalRules
                    except ImportError as e:
                        raise ImportError(f"Error importing required classes: {str(e)}")

                    # 根据操作符创建不同类型的复合项
                    try:
                        if compound == NativeOperator.SET_EXT_OPENER:
                            try:
                                return SetExt.make(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating SetExt: {str(e)}")
                        elif compound == NativeOperator.SET_INT_OPENER:
                            try:
                                return SetInt.make(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating SetInt: {str(e)}")
                        elif compound == NativeOperator.INTERSECTION_EXT:
                            try:
                                return IntersectionExt.make(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating IntersectionExt: {str(e)}")
                        elif compound == NativeOperator.INTERSECTION_INT:
                            try:
                                return IntersectionInt.make(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating IntersectionInt: {str(e)}")
                        elif compound == NativeOperator.DIFFERENCE_EXT:
                            try:
                                return DifferenceExt.make(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating DifferenceExt: {str(e)}")
                        elif compound == NativeOperator.DIFFERENCE_INT:
                            try:
                                return DifferenceInt.make(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating DifferenceInt: {str(e)}")
                        elif compound == NativeOperator.INHERITANCE:
                            if len(component_list) >= 2:
                                try:
                                    return Inheritance.make(component_list[0], component_list[1])
                                except Exception as e:
                                    raise ValueError(f"Error creating Inheritance: {str(e)}")
                            else:
                                raise ValueError(f"Inheritance requires at least 2 components, got {len(component_list)}")
                        elif compound == NativeOperator.PRODUCT:
                            try:
                                return Product(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating Product: {str(e)}")
                        elif compound == NativeOperator.IMAGE_EXT:
                            try:
                                return ImageExt.make(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating ImageExt: {str(e)}")
                        elif compound == NativeOperator.IMAGE_INT:
                            try:
                                return ImageInt.make(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating ImageInt: {str(e)}")
                        elif compound == NativeOperator.NEGATION:
                            try:
                                return Negation.make(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating Negation: {str(e)}")
                        elif compound == NativeOperator.DISJUNCTION:
                            try:
                                return Disjunction.make(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating Disjunction: {str(e)}")
                        elif compound == NativeOperator.CONJUNCTION:
                            try:
                                return Conjunction.make(component_list)
                            except Exception as e:
                                raise ValueError(f"Error creating Conjunction: {str(e)}")
                        elif compound == NativeOperator.SEQUENCE:
                            try:
                                return Conjunction.make(component_list, TemporalRules.ORDER_FORWARD)
                            except Exception as e:
                                raise ValueError(f"Error creating Sequence: {str(e)}")
                        elif compound == NativeOperator.SPATIAL:
                            try:
                                return Conjunction.make(component_list, TemporalRules.ORDER_FORWARD, True)
                            except Exception as e:
                                raise ValueError(f"Error creating Spatial: {str(e)}")
                        elif compound == NativeOperator.PARALLEL:
                            try:
                                return Conjunction.make(component_list, TemporalRules.ORDER_CONCURRENT)
                            except Exception as e:
                                raise ValueError(f"Error creating Parallel: {str(e)}")
                        elif compound == NativeOperator.IMPLICATION:
                            if len(component_list) >= 2:
                                try:
                                    return Implication.make(component_list[0], component_list[1])
                                except Exception as e:
                                    raise ValueError(f"Error creating Implication: {str(e)}")
                            else:
                                raise ValueError(f"Implication requires at least 2 components, got {len(component_list)}")
                        elif compound == NativeOperator.IMPLICATION_AFTER:
                            if len(component_list) >= 2:
                                try:
                                    return Implication.make(component_list[0], component_list[1], TemporalRules.ORDER_FORWARD)
                                except Exception as e:
                                    raise ValueError(f"Error creating ImplicationAfter: {str(e)}")
                            else:
                                raise ValueError(f"ImplicationAfter requires at least 2 components, got {len(component_list)}")
                        elif compound == NativeOperator.IMPLICATION_BEFORE:
                            if len(component_list) >= 2:
                                try:
                                    return Implication.make(component_list[0], component_list[1], TemporalRules.ORDER_BACKWARD)
                                except Exception as e:
                                    raise ValueError(f"Error creating ImplicationBefore: {str(e)}")
                            else:
                                raise ValueError(f"ImplicationBefore requires at least 2 components, got {len(component_list)}")
                        elif compound == NativeOperator.IMPLICATION_WHEN:
                            if len(component_list) >= 2:
                                try:
                                    return Implication.make(component_list[0], component_list[1], TemporalRules.ORDER_CONCURRENT)
                                except Exception as e:
                                    raise ValueError(f"Error creating ImplicationWhen: {str(e)}")
                            else:
                                raise ValueError(f"ImplicationWhen requires at least 2 components, got {len(component_list)}")
                        elif compound == NativeOperator.EQUIVALENCE:
                            if len(component_list) >= 2:
                                try:
                                    return Equivalence.make(component_list[0], component_list[1])
                                except Exception as e:
                                    raise ValueError(f"Error creating Equivalence: {str(e)}")
                            else:
                                raise ValueError(f"Equivalence requires at least 2 components, got {len(component_list)}")
                        elif compound == NativeOperator.EQUIVALENCE_WHEN:
                            if len(component_list) >= 2:
                                try:
                                    return Equivalence.make(component_list[0], component_list[1], TemporalRules.ORDER_CONCURRENT)
                                except Exception as e:
                                    raise ValueError(f"Error creating EquivalenceWhen: {str(e)}")
                            else:
                                raise ValueError(f"EquivalenceWhen requires at least 2 components, got {len(component_list)}")
                        elif compound == NativeOperator.EQUIVALENCE_AFTER:
                            if len(component_list) >= 2:
                                try:
                                    return Equivalence.make(component_list[0], component_list[1], TemporalRules.ORDER_FORWARD)
                                except Exception as e:
                                    raise ValueError(f"Error creating EquivalenceAfter: {str(e)}")
                            else:
                                raise ValueError(f"EquivalenceAfter requires at least 2 components, got {len(component_list)}")
                        else:
                            raise ValueError(f"Unknown Term operator: {compound}")
                    except Exception as e:
                        if isinstance(e, (ValueError, ImportError)):
                            raise
                        raise ValueError(f"Error creating term with operator {compound}: {str(e)}")
                except Exception as e:
                    if isinstance(e, (ValueError, TypeError, ImportError)):
                        raise
                    raise ValueError(f"Unexpected error processing operator {compound}: {str(e)}")

            # 如果不是复合项或操作符
            if not isinstance(compound, (CompoundTerm, NativeOperator)):
                raise TypeError(f"Expected CompoundTerm or NativeOperator, got {type(compound).__name__}")

            return None
        except Exception as e:
            if isinstance(e, (ValueError, TypeError, ImportError)):
                raise
            raise ValueError(f"Unexpected error in term method: {str(e)}")

    @staticmethod
    def reduce_components(compound: CompoundTerm, component: Term, memory) -> Optional[Term]:
        """
        尝试从复合项中移除一个组件

        参数:
            compound: 复合项
            component: 要移除的组件
            memory: 内存引用

        返回:
            Term: 新的复合项
        """
        # 获取要保留的组件列表
        if compound.__class__ == component.__class__:
            # 如果类型相同，移除所有组件
            if hasattr(compound, 'clone_terms_except') and hasattr(component, 'term'):
                term_list = compound.clone_terms_except(True, component.term)
            else:
                return None
        else:
            # 如果类型不同，只移除指定组件
            if hasattr(compound, 'clone_terms_except'):
                term_list = compound.clone_terms_except(True, [component])
            else:
                return None

        # 如果没有组件列表，返回None
        if term_list is None:
            return None

        # 如果只有一个组件，返回该组件
        if len(term_list) == 1:
            return term_list[0]

        # 如果有多个组件，创建新的复合项
        return Terms.term(compound, term_list)

    @staticmethod
    def reduce_component_one_layer(compound: CompoundTerm, component: Term, memory) -> Optional[Term]:
        """
        尝试从复合项中移除一个组件，只处理一层

        参数:
            compound: 复合项
            component: 要移除的组件
            memory: 内存引用

        返回:
            Term: 新的复合项
        """
        # 获取要保留的组件列表
        if compound.__class__ == component.__class__:
            # 如果类型相同，移除所有组件
            if hasattr(compound, 'clone_terms_except') and hasattr(component, 'term'):
                term_list = compound.clone_terms_except(True, component.term)
            else:
                return compound
        else:
            # 如果类型不同，只移除指定组件
            if hasattr(compound, 'clone_terms_except'):
                term_list = compound.clone_terms_except(True, [component])
            else:
                return compound

        # 如果没有组件列表，返回原复合项
        if term_list is None:
            return compound

        # 如果有多个组件，创建新的复合项
        if len(term_list) > 1:
            return Terms.term(compound, term_list)
        # 如果只有一个组件，返回该组件
        elif len(term_list) == 1:
            return term_list[0]

        # 如果没有组件，返回原复合项
        return compound

    @staticmethod
    def unwrap_negation(t: Term) -> Term:
        """
        如果项是否定项，则返回其内部项

        参数:
            t: 要检查的项

        返回:
            Term: 解包后的项
        """
        if t is not None and hasattr(t, 'operator') and t.operator() == 'NEGATION':
            if isinstance(t, CompoundTerm) and len(t.term) > 0:
                return t.term[0]
        return t

    @staticmethod
    def prepare_component_links(component_links: List[TermLink], term_type: int, term: CompoundTerm) -> List[TermLink]:
        """
        收集TermLink模板到列表中

        参数:
            component_links: 已构建的TermLink模板列表
            term_type: 要构建的TermLink类型
            term: 为其构建链接的复合项

        返回:
            List[TermLink]: 更新后的组件链接列表
        """
        from linars.org.opennars.language.equivalence import Equivalence
        from linars.org.opennars.language.implication import Implication
        from linars.org.opennars.language.conjunction import Conjunction
        from linars.org.opennars.language.negation import Negation
        from linars.org.opennars.language.product import Product
        from linars.org.opennars.language.image_ext import ImageExt
        from linars.org.opennars.language.image_int import ImageInt
        from linars.org.opennars.language.variable import Variable
        from linars.org.opennars.entity.sentence import Sentence
        from linars.org.opennars.io.symbols import TERM_NORMALIZING_WORKAROUND_MARK

        # 检查特殊情况
        t_equivalence = isinstance(term, Equivalence)
        t_implication = isinstance(term, Implication)

        # 遍历每个组件
        for i in range(len(term.term)):
            # 获取组件
            t1 = term.term[i]

            # 创建无真值和时间戳的句子
            if hasattr(Sentence, '__init__'):
                t1 = Sentence(t1, TERM_NORMALIZING_WORKAROUND_MARK, None, None).term

            # 跳过变量
            if isinstance(t1, Variable):
                continue

            # 添加组件链接
            component_links.append(TermLink(term_type, t1, i))

            # 处理特殊情况
            if (t_equivalence or (t_implication and i == 0)) and (isinstance(t1, Conjunction) or isinstance(t1, Negation)):
                # 递归处理条件组件
                Terms.prepare_component_links(component_links, TermLink.COMPOUND_CONDITION, t1)
            elif isinstance(t1, CompoundTerm):
                ct1 = t1
                ct1_size = len(ct1.term)
                t1_product_or_image = isinstance(t1, Product) or isinstance(t1, ImageExt) or isinstance(t1, ImageInt)

                # 遍历子组件
                for j in range(ct1_size):
                    t2 = ct1.term[j]

                    # 创建无真值和时间戳的句子
                    if hasattr(Sentence, '__init__'):
                        t2 = Sentence(t2, TERM_NORMALIZING_WORKAROUND_MARK, None, None).term

                    # 跳过包含变量的项
                    if hasattr(t2, 'has_var') and t2.has_var():
                        continue

                    # 根据类型添加不同的链接
                    if t1_product_or_image:
                        if term_type == TermLink.COMPOUND_CONDITION:
                            component_links.append(TermLink(TermLink.TRANSFORM, t2, 0, i, j))
                        else:
                            component_links.append(TermLink(TermLink.TRANSFORM, t2, i, j))
                    else:
                        component_links.append(TermLink(term_type, t2, i, j))

                    # 处理特殊类型的子组件
                    if isinstance(t2, Product) or isinstance(t2, ImageExt) or isinstance(t2, ImageInt):
                        ct2 = t2
                        ct2_size = len(ct2.term)

                        # 遍历子子组件
                        for k in range(ct2_size):
                            t3 = ct2.term[k]

                            # 创建无真值和时间戳的句子
                            if hasattr(Sentence, '__init__'):
                                t3 = Sentence(t3, TERM_NORMALIZING_WORKAROUND_MARK, None, None).term

                            # 跳过包含变量的项
                            if hasattr(t3, 'has_var') and t3.has_var():
                                continue

                            # 添加变换链接
                            if term_type == TermLink.COMPOUND_CONDITION:
                                component_links.append(TermLink(TermLink.TRANSFORM, t3, 0, i, j, k))
                            else:
                                component_links.append(TermLink(TermLink.TRANSFORM, t3, i, j, k))

        return component_links

    @staticmethod
    def prepare_component_links_for_term(component_links: List[TermLink], term: CompoundTerm) -> List[TermLink]:
        """
        为复合项准备组件链接

        参数:
            component_links: 已构建的TermLink模板列表
            term: 为其构建链接的复合项

        返回:
            List[TermLink]: 更新后的组件链接列表
        """
        from linars.org.opennars.language.statement import Statement
        from linars.org.opennars.entity.term_link import TermLink

        # 确定链接类型
        term_type = TermLink.COMPOUND_STATEMENT if isinstance(term, Statement) else TermLink.COMPOUND

        # 调用主方法
        return Terms.prepare_component_links(component_links, term_type, term)

    @staticmethod
    def index_of(array: List[T], value: T) -> int:
        """
        查找值在数组中的索引

        参数:
            array: 要搜索的数组
            value: 要查找的值

        返回:
            int: 索引，如果不存在则返回-1
        """
        try:
            return array.index(value)
        except ValueError:
            return -1

    @staticmethod
    def contains_all(a: List[T], b: List[T]) -> bool:
        """
        检查一组项是否包含另一组项的所有元素

        参数:
            a: 第一组项
            b: 第二组项

        返回:
            bool: 如果a包含b的所有元素则返回True
        """
        for item in a:
            if item not in b:
                return False
        return True

    @staticmethod
    def contains_any(a: List[Term], b: Collection[Term]) -> bool:
        """
        检查a是否包含b中的任何元素

        参数:
            a: 第一组项
            b: 第二组项

        返回:
            bool: 如果a包含b中的任何元素则返回True
        """
        for item in b:
            if item in a:
                return True

        for item in a:
            if isinstance(item, CompoundTerm):
                if Terms.contains_any(item.term, b):
                    return True

        return False

    @staticmethod
    def contains(array: List[T], value: T) -> bool:
        """
        检查数组是否包含值

        参数:
            array: 要搜索的数组
            value: 要查找的值

        返回:
            bool: 如果数组包含值则返回True
        """
        return value in array

    @staticmethod
    def equals(a: List[Term], b: List[Term]) -> bool:
        """
        检查两个项数组是否相等

        参数:
            a: 第一个数组
            b: 第二个数组

        返回:
            bool: 如果数组相等则返回True
        """
        if len(a) != len(b):
            return False

        for i in range(len(a)):
            if a[i] != b[i]:
                return False

        return True

    @staticmethod
    def verify_non_null(collection: Collection) -> None:
        """
        验证集合中没有空元素

        参数:
            collection: 要验证的集合

        抛出:
            IllegalStateException: 如果集合包含空元素
        """
        for item in collection:
            if item is None:
                raise ValueError(f"Element null in: {collection}")

    @staticmethod
    def verify_sorted_and_unique(arg: List[Term], allow_singleton: bool) -> List[Term]:
        """
        验证项数组是否已排序且唯一

        参数:
            arg: 要验证的数组
            allow_singleton: 是否允许单元素数组

        返回:
            List[Term]: 排序后的数组

        抛出:
            ValueError: 如果数组不符合要求
        """
        if len(arg) == 0:
            raise ValueError("Needs >0 components")

        if not allow_singleton and len(arg) == 1:
            raise ValueError(f"Needs >1 components: {arg}")

        # 使用Term.to_sorted_set_array排序并去重
        s = sorted(set(arg))

        if len(arg) != len(s):
            raise ValueError(f"Contains duplicates: {arg}")

        for i, t in enumerate(s):
            if t != arg[i]:
                raise ValueError(f"Un-ordered: {arg}, correct order={s}")

        return s
