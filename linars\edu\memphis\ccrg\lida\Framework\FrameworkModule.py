# LIDA Cognitive Framework
"""
Interface for the modules of an agent.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.Initialization.FullyInitializable import FullyInitializable
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import ModuleName
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawner import TaskSpawner

class FrameworkModule(FullyInitializable, ABC):
    """
    Interface for the modules of an agent.
    """
    
    @abstractmethod
    def get_module_name(self) -> ModuleName:
        """
        Gets moduleName.
        
        Returns:
            ModuleName of this FrameworkModule
        """
        pass
    
    @abstractmethod
    def set_module_name(self, module_name: ModuleName) -> None:
        """
        Sets ModuleName.
        
        Args:
            module_name: ModuleName of this FrameworkModule
        """
        pass
    
    @abstractmethod
    def contains_submodule(self, name: <PERSON>du<PERSON><PERSON>ame) -> bool:
        """
        Returns whether this FrameworkModule contains a submodule with 
        specified ModuleName.
        
        Args:
            name: ModuleName of submodule
            
        Returns:
            True if there is a FrameworkModule with specified ModuleName
            in this FrameworkModule
        """
        pass
    
    @abstractmethod
    def contains_submodule_by_string(self, name: str) -> bool:
        """
        Returns whether this FrameworkModule contains a submodule with 
        specified name.
        
        Args:
            name: Name of submodule
            
        Returns:
            True if there is a FrameworkModule with specified name
            in this FrameworkModule
        """
        pass
    
    @abstractmethod
    def get_submodule(self, name: ModuleName) -> Optional['FrameworkModule']:
        """
        Gets specified submodule.
        
        Args:
            name: Name of the desired submodule.
            
        Returns:
            The submodule.
        """
        pass
    
    @abstractmethod
    def get_submodule_by_string(self, name: str) -> Optional['FrameworkModule']:
        """
        Gets specified submodule.
        
        Args:
            name: Name of the desired submodule.
            
        Returns:
            The submodule.
        """
        pass
    
    @abstractmethod
    def add_submodule(self, module: 'FrameworkModule') -> None:
        """
        Adds submodule as a component of this FrameworkModule.
        
        Args:
            module: Submodule to add
        """
        pass
    
    @abstractmethod
    def get_module_content(self, *params: Any) -> Any:
        """
        Returns module content specified by params. Intended for use by the GUI only.
        
        Args:
            params: Parameters specifying what content will be returned
            
        Returns:
            Parameter-specified content of this module.
        """
        pass
    
    @abstractmethod
    def task_manager_decay_module(self, ticks: int) -> None:
        """
        Decay this module and all its submodules. 
        Framework users should not call this method. It will be called by the TaskManager.
        Decays this module and all its submodules.
        
        Args:
            ticks: Number of ticks to decay.
        """
        pass
    
    @abstractmethod
    def decay_module(self, ticks: int) -> None:
        """
        Decay only this Module.
        
        Args:
            ticks: Number of ticks to decay.
        """
        pass
    
    @abstractmethod
    def add_listener(self, listener: ModuleListener) -> None:
        """
        Generic way to add various kinds of listeners.
        
        Args:
            listener: Listener of this FrameworkModule
        """
        pass
    
    @abstractmethod
    def set_assisting_task_spawner(self, ts: TaskSpawner) -> None:
        """
        Specify the TaskSpawner which this FrameworkModule will use to spawn tasks.
        
        Args:
            ts: The TaskSpawner
        """
        pass
    
    @abstractmethod
    def get_assisting_task_spawner(self) -> TaskSpawner:
        """
        Returns the TaskSpawner which this FrameworkModule uses to spawn tasks.
        
        Returns:
            The assisting task spawner
        """
        pass
    
    @abstractmethod
    def get_submodules(self) -> Dict[ModuleName, 'FrameworkModule']:
        """
        Convenience method to get submodules
        
        Returns:
            Map of submodules by ModuleName
        """
        pass
