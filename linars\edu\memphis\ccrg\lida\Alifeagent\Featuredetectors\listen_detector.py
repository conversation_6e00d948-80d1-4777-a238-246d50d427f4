from linars.edu.memphis.ccrg.lida.PAM.Tasks.MultipleDetectionAlgorithm import MultipleDetectionAlgorithm

class ListenDetector(MultipleDetectionAlgorithm):
    def __init__(self):
        super().__init__()
        self.message = None
        print("ListenDetector created")
    
    def init(self, params=None):
        super().init(params)
        print("ListenDetector initialized")
    
    def detect_linkables(self):
        print("ListenDetector.detect_linkables called")
        words = ["运算", "26", "加", "8"]
        ll = "听到"
        
        self.excite(ll, 0.95, "listen0")
        
        for word in words:
            print(f"Testing word: {word}")
            self.excite(word, 0.95, "listen0")
