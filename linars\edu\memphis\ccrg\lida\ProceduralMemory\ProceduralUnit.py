# LIDA Cognitive Framework
"""
An abstraction of the commonality between Scheme and Behavior.
"""

from abc import ABC, abstractmethod
from typing import Collection
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.Activatible import Activatible
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action
from linars.edu.memphis.ccrg.lida.ProceduralMemory.Condition import Condition

class ProceduralUnit(Activatible, ABC):
    """
    An abstraction of the commonality between Scheme and Behavior.
    """
    
    @abstractmethod
    def get_action(self) -> Action:
        """
        Gets action.
        
        Returns:
            The Action this unit contains
        """
        pass
    
    @abstractmethod
    def get_context_conditions(self) -> Collection[Condition]:
        """
        Gets context conditions.
        
        Returns:
            The context's conditions
        """
        pass
    
    @abstractmethod
    def get_adding_list(self) -> Collection[Condition]:
        """
        Gets adding list.
        
        Returns:
            The adding list
        """
        pass
    
    @abstractmethod
    def get_deleting_list(self) -> Collection[Condition]:
        """
        Gets deleting list.
        
        Returns:
            The deleting list
        """
        pass
    
    @abstractmethod
    def get_label(self) -> str:
        """
        Gets the label.
        
        Returns:
            Label of the unit
        """
        pass
