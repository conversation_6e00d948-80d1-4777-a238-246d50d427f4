# LIDA Cognitive Framework
"""
Interface for objects that can spawn tasks.
"""

from abc import ABC, abstractmethod
from typing import Optional
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTask import FrameworkTask

class TaskSpawner(ABC):
    """
    Interface for objects that can spawn tasks.
    """

    # 单例实例
    _instance = None

    @classmethod
    def get_instance(cls):
        """
        获取TaskSpawner的单例实例。
        如果实例不存在，则返回FrameworkModuleImpl中的task_spawner。

        Returns:
            TaskSpawner: TaskSpawner的单例实例
        """
        if cls._instance is None:
            # 从FrameworkModuleImpl获取task_spawner
            from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
            cls._instance = FrameworkModuleImpl.task_spawner
        return cls._instance

    @classmethod
    def set_instance(cls, instance):
        """
        设置TaskSpawner的单例实例。

        Args:
            instance: TaskSpawner的实例
        """
        cls._instance = instance

    @abstractmethod
    def add_task(self, task: FrameworkTask) -> None:
        """
        Add a task to be spawned.

        Args:
            task: The task to add
        """
        pass

    @abstractmethod
    def cancel_task(self, task: FrameworkTask) -> None:
        """
        Cancel a task.

        Args:
            task: The task to cancel
        """
        pass

    @abstractmethod
    def receive_finished_task(self, task: FrameworkTask) -> None:
        """
        Receive a finished task.

        Args:
            task: The finished task
        """
        pass
