"""
内涵图像项定义
按照NARS理论定义的内涵图像操作

(\,P,A,_) --> B 当且仅当 P --> (*,A,B)

内部实现实际上是(\,A,P)_1，带有一个索引
"""
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.io.symbols import NativeOperator
from linars.org.opennars.language.image import Image
from linars.org.opennars.language.product import Product

class ImageInt(Image):
    """
    内涵图像项类
    按照NARS理论定义的内涵图像操作

    (\,P,A,_) --> B 当且仅当 P --> (*,A,B)

    内部实现实际上是(\,A,P)_1，带有一个索引
    """

    def __init__(self, arg, index):
        """
        构造函数

        参数:
            arg: 项的组成部分列表
            index: 关系在组件列表中的索引位置
        """
        super().__init__(arg, index)

    def clone(self):
        """
        克隆对象

        返回:
            ImageInt: 新的内涵图像对象
        """
        return ImageInt(self.term, self.relation_index)

    def clone_with_terms(self, replaced):
        """
        使用替换项克隆对象

        参数:
            replaced: 被替换的项列表

        返回:
            Term: 包含替换组件的新项
        """
        if replaced is None:
            return None
        if len(replaced) != len(self.term):
            raise ValueError(f"Replaced terms not the same amount as existing terms ({len(self.term)}): {replaced}")

        return ImageInt(replaced, self.relation_index)

    @staticmethod
    def make(arg_list):
        """
        尝试创建新的内涵图像项

        参数:
            arg_list: 项列表

        返回:
            Term: 从参数生成的项
        """
        if len(arg_list) < 2:
            return arg_list[0]

        relation = arg_list[0]
        argument = [None] * (len(arg_list) - 1)
        index = 0
        n = 0

        for j in range(1, len(arg_list)):
            if Image.is_place_holder(arg_list[j]):
                index = j - 1
                argument[n] = relation
            else:
                argument[n] = arg_list[j]
            n += 1

        return ImageInt.make_from_components(argument, index)

    @staticmethod
    def make_from_product(product, relation, index):
        """
        尝试从乘积项和关系创建内涵图像项

        参数:
            product: 乘积项
            relation: 关系项
            index: 占位符的索引位置

        返回:
            Term: 生成的复合项或简化后的项
        """
        if isinstance(relation, Product):
            p2 = relation
            if (product.size() == 2) and (p2.size() == 2):
                if (index == 0) and product.term[1] == p2.term[1]:  # (\,_,(*,a,b),b) is reduced to a
                    return p2.term[0]
                if (index == 1) and product.term[0] == p2.term[0]:  # (\,(*,a,b),a,_) is reduced to b
                    return p2.term[1]

        argument = product.clone_terms()
        argument[index] = relation
        return ImageInt.make_from_components(argument, index)

    @staticmethod
    def make_from_image(old_image, component, index):
        """
        尝试从现有图像项和组件创建新图像项

        参数:
            old_image: 现有图像项
            component: 要添加到组件列表中的组件
            index: 新图像项中占位符的索引位置

        返回:
            Term: 生成的复合项或简化后的项
        """
        arg_list = old_image.clone_terms()
        old_index = old_image.relation_index
        relation = arg_list[old_index]
        arg_list[old_index] = component
        arg_list[index] = relation
        return ImageInt.make_from_components(arg_list, index)

    @staticmethod
    def make_from_components(argument, index):
        """
        尝试从一组项创建新的复合项

        参数:
            argument: 参数列表
            index: 新图像项中占位符的索引位置

        返回:
            ImageInt: 从参数生成的项
        """
        return ImageInt(argument, index)

    def operator(self):
        """
        获取项的操作符

        返回:
            NativeOperator: 项的操作符
        """
        return NativeOperator.IMAGE_INT
