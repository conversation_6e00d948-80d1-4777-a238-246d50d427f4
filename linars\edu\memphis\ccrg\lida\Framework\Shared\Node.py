# LIDA认知框架
"""
节点(Node)表示LIDA中的概念。它可以针对系统的不同部分以不同方式实现。
例如在PAM中是pamNodes，在工作区中是WorkspaceNodes。具有相同ID的节点
表示相同的概念，因此即使对象属于不同类，equals也必须返回true。
"""

from abc import abstractmethod, ABC
from typing import Optional, TYPE_CHECKING
from linars.edu.memphis.ccrg.lida.Framework.Shared.Linkable import Linkable

# 避免循环导入
if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.lida.ProceduralMemory.Condition import Condition
    from linars.edu.memphis.ccrg.lida.PAM.PamNode import PamNode

# 暂时移除Condition继承，只继承Linkable
class Node(Linkable, ABC):
    """
    节点(Node)表示LIDA中的概念。它可以针对系统的不同部分以不同方式实现。
    例如在PAM中是pamNodes，在工作区中是WorkspaceNodes。具有相同ID的节点
    表示相同的概念，因此即使对象属于不同类，equals也必须返回true。
    """

    @abstractmethod
    def get_node_id(self) -> int:
        """
        获取此节点的ID
        返回:
            此节点的ID
        """
        pass

    @abstractmethod
    def set_node_id(self, id: int) -> None:
        """
        设置此节点的ID

        参数:
            id: 要设置的ID
        """
        pass

    @abstractmethod
    def get_node_name(self) -> str:
        """
        获取此节点的名称

        返回:
            此节点的名称
        """
        pass

    @abstractmethod
    def set_node_name(self, name: str) -> None:
        """
        设置此节点的名称

        参数:
            name: 要设置的名称
        """
        pass

    @abstractmethod
    def get_location(self) -> str:
        """
        获取此节点的位置

        返回:
            此节点的位置
        """
        pass

    @abstractmethod
    def set_location(self, location: str) -> None:
        """
        设置此节点的位置

        参数:
            location: 要设置的位置
        """
        pass

    @abstractmethod
    def get_grounding_pam_node(self) -> 'PamNode':
        """
        获取此节点的基础PamNode

        返回:
            基础PamNode
        """
        pass

    @abstractmethod
    def set_grounding_pam_node(self, node: 'PamNode') -> None:
        """
        设置此节点的基础PamNode

        参数:
            node: 要设置的基础PamNode
        """
        pass

    @abstractmethod
    def update_node_values(self, node: 'Node') -> None:
        """
        Node的子类应重写此方法，使用指定节点的值设置其特定类型的成员数据。
        指定的节点必须是相同子类类型。

        参数:
            node: 用于更新值的节点
        """
        pass

    @abstractmethod
    def get_truth(self) -> int:
        """
        获取此节点的真值

        返回:
            此节点的真值
        """
        pass

    @abstractmethod
    def set_truth(self, truth: int) -> None:
        """
        设置此节点的真值

        参数:
            truth: 要设置的真值
        """
        pass

    @abstractmethod
    def get_bcastid(self) -> str:
        """
        获取此节点的广播ID

        返回:
            此节点的广播ID
        """
        pass

    @abstractmethod
    def set_bcastid(self, bcastid: str) -> None:
        """
        设置此节点的广播ID

        参数:
            bcastid: 要设置的广播ID
        """
        pass

    @abstractmethod
    def get_last_act(self) -> Optional[str]:
        """
        获取此节点的最后激活

        返回:
            此节点的最后激活
        """
        pass

    @abstractmethod
    def set_last_act(self, last_act: str) -> None:
        """
        设置此节点的最后激活

        参数:
            last_act: 要设置的最后激活
        """
        pass
