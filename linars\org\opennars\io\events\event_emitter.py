"""
NARS事件发射器
实现事件发布-订阅模式的核心组件
"""
from typing import Dict, List, Callable, Any, Set, Optional, Type
from collections import deque

class EventEmitter:
    """
    NARS事件发射器
    负责事件的注册、注销和触发
    """

    class EventObserver:
        """
        事件观察者接口
        定义事件处理回调方法
        """

        def event(self, event_class: Type, args: List[Any]):
            """
            处理事件回调方法

            参数:
                event_class: 事件类型
                args: 事件参数列表
            """
            pass

    def __init__(self, known_event_classes: List[Type] = None):
        """
        构造函数

        参数:
            known_event_classes: 预定义的事件类型列表
        """
        if known_event_classes:
            self.events = {}
            for c in known_event_classes:
                self.events[c] = self._new_observer_list()
        else:
            self.events = {}

        self.pending_ops = deque()

    def _new_observer_list(self) -> List[EventObserver]:
        """
        创建新的观察者列表

        返回:
            List[EventObserver]: 新建的观察者列表
        """
        return []

    def is_active(self, event: Type) -> bool:
        """
        检查事件是否活跃(有观察者)

        参数:
            event: 事件类型

        返回:
            bool: 是否有活跃观察者
        """
        if event in self.events:
            return len(self.events[event]) > 0
        return False

    def synch(self):
        """同步待处理操作"""
        if self.pending_ops:
            for op in self.pending_ops:
                enable, c, d = op
                if enable:
                    self.on(c, d)
                else:
                    self.off(c, d)
            self.pending_ops.clear()

    def on(self, event: Type, observer: EventObserver):
        """
        注册事件观察者

        参数:
            event: 事件类型
            observer: 观察者对象
        """
        if event in self.events:
            self.events[event].append(observer)
        else:
            a = self._new_observer_list()
            a.append(observer)
            self.events[event] = a

    def off(self, event: Type, observer: EventObserver):
        """
        注销事件观察者

        参数:
            event: 事件类型
            observer: 观察者对象

        异常:
            ValueError: 当事件或观察者无效时抛出
        """
        if event is None or observer is None:
            raise ValueError("Invalid parameter")
        if event not in self.events:
            raise ValueError(f"Unknown event: {event}")
        self.events[event].remove(observer)

    def set(self, observer: EventObserver, enable: bool, *events: List[Type]):
        """
        批量设置事件观察状态

        参数:
            observer: 观察者对象
            enable: 是否启用(True注册/False注销)
            events: 要操作的事件类型列表
        """
        for c in events:
            if enable:
                self.on(c, observer)
            else:
                self.off(c, observer)

    def emit(self, event_class: Type, *params: List[Any]):
        """
        触发事件

        参数:
            event_class: 事件类型
            params: 事件参数列表
        """
        observers = self.events.get(event_class)
        if not observers:
            return

        for m in observers:
            m.event(event_class, params)
