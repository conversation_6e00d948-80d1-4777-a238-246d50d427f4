"""
变量工具类，提供与变量相关的静态方法

主要功能:
1. 查找替代
2. 统一术语
3. 应用替代并重命名变量
4. 检查是否包含变量
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, ClassVar, cast
import random
from collections import OrderedDict

from linars.edu.memphis.ccrg.linars.term import Term
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.org.opennars.language.variable import Variable
from linars.org.opennars.io.symbols import VAR_INDEPENDENT, VAR_DEPENDENT, VAR_QUERY
from linars.org.opennars.language.inheritance import Inheritance
from linars.org.opennars.language.similarity import Similarity
from linars.org.opennars.language.conjunction import Conjunction
from linars.org.opennars.language.disjunction import Disjunction


class Variables:
    """
    静态工具类，用于与变量相关的静态方法
    """

    @staticmethod
    def find_substitute(rnd: random.Random, type_char: str, term1: Term, term2: Term,
                        map1: Dict[Term, Term], map2: Dict[Term, Term]) -> bool:
        """
        查找替代

        参数:
            rnd: 随机数生成器
            type_char: 变量类型
            term1: 第一个术语
            term2: 第二个术语
            map1: 第一个映射
            map2: 第二个映射

        返回:
            bool: 是否找到替代
        """
        return Variables.find_substitute_with_maps(rnd, type_char, term1, term2, [map1, map2])

    @staticmethod
    def find_substitute_with_maps(rnd: random.Random, type_char: str, term1: Term, term2: Term,
                                 maps: List[Dict[Term, Term]], allow_partial: bool = False) -> bool:
        """
        使用映射列表查找替代

        参数:
            rnd: 随机数生成器
            type_char: 变量类型
            term1: 第一个术语
            term2: 第二个术语
            maps: 映射列表
            allow_partial: 是否允许部分匹配

        返回:
            bool: 是否找到替代
        """
        # 添加异常处理以提高健壮性
        try:
            # 检查term1和term2是否为None
            if term1 is None or term2 is None:
                return False

            # 检查term1是否包含变量
            try:
                term1_has_var = term1.has_var_type(type_char) if hasattr(term1, 'has_var_type') else False
                if type_char == VAR_INDEPENDENT:
                    term1_has_var |= term1.has_var_dep() if hasattr(term1, 'has_var_dep') else False
                    term1_has_var |= term1.has_var_query() if hasattr(term1, 'has_var_query') else False
                if type_char == VAR_DEPENDENT:
                    term1_has_var |= term1.has_var_query() if hasattr(term1, 'has_var_query') else False
                term2_has_var = term2.has_var_type(type_char) if hasattr(term2, 'has_var_type') else False
            except Exception as e:
                print(f"Error checking variable types: {e}")
                term1_has_var = False
                term2_has_var = False

            # 检查是否为变量类型
            term1_var = isinstance(term1, Variable)
            term2_var = isinstance(term2, Variable)

            # 如果两个术语都没有变量，直接比较
            if not term1_has_var and not term2_has_var:
                # todo 暂时简单比较字符串名字。后期全面比较
                # 前面概念copy时，统一新建term，不分类型，复杂度不一样
                try:
                    # 参考Java源码，使用equals方法进行比较
                    terms_equal = term1.equals(term2)
                    if terms_equal:
                        return True
                    return term1.TNname == term2.TNname
                except Exception as e:
                    print(f"Error comparing terms: {e}")
                    # 如果比较失败，尝试比较名称
                    try:
                        return term1.name() == term2.name()
                    except Exception as e2:
                        print(f"Error comparing term names: {e2}")
                        return False

            # 变量“重命名”为相同类型的变量始终有效
            if term1_var and term2_var:
                try:
                    v1 = cast(Variable, term1)
                    v2 = cast(Variable, term2)
                    if v1.get_type() == v2.get_type():
                        common_var = Variables.make_common_variable(term1, term2)
                        if maps[0] is None:
                            maps[0] = {}
                            maps[1] = {}
                        maps[0][v1] = common_var
                        maps[1][v2] = common_var
                        return True
                except Exception as e:
                    print(f"Error handling variable renaming: {e}")
                    # 继续处理其他情况

            # 检查变量统一是否允许
            try:
                term1_var_unify_allowed = term1_var and Variables.allow_unification(type_char, term1.get_type())
                term2_var_unify_allowed = term2_var and Variables.allow_unification(type_char, term2.get_type())
            except Exception as e:
                print(f"Error checking unification allowance: {e}")
                term1_var_unify_allowed = False
                term2_var_unify_allowed = False

            if term1_var_unify_allowed or term2_var_unify_allowed:
                try:
                    term_a = term1 if term1_var_unify_allowed else term2
                    term_b = term2 if term1_var_unify_allowed else term1
                    term_a_as_variable = cast(Variable, term_a)

                    map_idx = 0 if term1_var_unify_allowed else 1

                    # 检查maps和maps[map_idx]是否为None
                    if maps is None or map_idx >= len(maps):
                        return False

                    t = None
                    if maps[map_idx] is not None:
                        t = maps[map_idx].get(term_a_as_variable)

                    if t is not None:
                        return Variables.find_substitute_with_maps(rnd, type_char, t, term_b, maps)
                except (IndexError, TypeError, KeyError) as e:
                    print(f"Error accessing map in find_substitute_with_maps: {e}")
                    return False

                try:
                    if maps[0] is None:
                        maps[0] = {}
                        maps[1] = {}

                    # 检查变量是否已经绑定到其他值
                    if term_a_as_variable.is_common() and maps[map_idx] is not None:
                        for key, value in maps[map_idx].items():
                            if isinstance(key, Variable) and key.is_common() and key != term_a_as_variable:
                                if value == term_b:
                                    return False
                except (AttributeError, TypeError, IndexError) as e:
                    print(f"Error checking variable bindings: {e}")
                    return False

                # 检查是否有循环引用
                try:
                    if hasattr(term_b, 'contains_term') and term_b.contains_term(term_a_as_variable):
                        return False
                except Exception as e:
                    print(f"Error checking circular reference: {e}")
                    return False

                # 添加映射
                try:
                    if term1_var_unify_allowed:
                        if isinstance(term_b, Variable) and Variables.allow_unification(type_char, cast(Variable, term_b).get_type()):
                            common_var = Variables.make_common_variable(term_a, term_b)
                            maps[0][term_a_as_variable] = common_var
                            maps[1][term_b] = common_var
                        else:
                            # 检查变量类型是否兼容
                            if isinstance(term_b, Variable) and (
                                (cast(Variable, term_b).get_type() == VAR_QUERY and term_a_as_variable.get_type() != VAR_QUERY) or
                                (cast(Variable, term_b).get_type() != VAR_QUERY and term_a_as_variable.get_type() == VAR_QUERY)
                            ):
                                return False
                            maps[0][term_a_as_variable] = term_b
                            if term_a_as_variable.is_common():
                                maps[1][term_a_as_variable] = term_b
                    else:
                        maps[1][term_a_as_variable] = term_b
                        if term_a_as_variable.is_common():
                            maps[0][term_a_as_variable] = term_b
                    return True
                except Exception as e:
                    print(f"Error adding mappings: {e}")
                    return False

            # 处理复合术语
            try:
                if isinstance(term1, CompoundTerm) and isinstance(term2, CompoundTerm):
                    # 检查是否为相同类型的复合术语
                    if term1.__class__ != term2.__class__:
                        return False

                    # 如果名称相同，直接返回成功
                    if term1.name() == term2.name():
                        return True

                    # 检查是否为带有时间关系的语句
                    are_both_conjunctions = isinstance(term1, Conjunction) and isinstance(term2, Conjunction)
                    are_both_equivalence = False
                    are_both_implication = False

                    try:
                        from linars.org.opennars.language.equivalence import Equivalence
                        from linars.org.opennars.language.implication import Implication
                        are_both_equivalence = isinstance(term1, Equivalence) and isinstance(term2, Equivalence)
                        are_both_implication = isinstance(term1, Implication) and isinstance(term2, Implication)
                    except Exception as e:
                        print(f"Error checking temporal order: {e}")
                        are_both_equivalence = False
                        are_both_implication = False

                    # 检查时间顺序是否相同
                    is_same_order = True
                    is_same_order_and_same_spatial = True

                    try:
                        if are_both_conjunctions or are_both_equivalence or are_both_implication:
                            # 检查时间顺序
                            if hasattr(term1, 'get_temporal_order') and hasattr(term2, 'get_temporal_order'):
                                is_same_order = term1.get_temporal_order() == term2.get_temporal_order()

                            # 检查空间关系
                            if are_both_conjunctions:
                                # 检查是否有is_spatial属性
                                if hasattr(term1, 'is_spatial') and hasattr(term2, 'is_spatial'):
                                    # 检查is_spatial是属性还是方法
                                    if callable(getattr(term1, 'is_spatial')) and callable(getattr(term2, 'is_spatial')):
                                        # 如果是方法，调用它
                                        is_same_order_and_same_spatial = is_same_order and term1.is_spatial() == term2.is_spatial()
                                    else:
                                        # 如果是属性，直接访问
                                        is_same_order_and_same_spatial = is_same_order and term1.is_spatial == term2.is_spatial
                    except Exception as e:
                        print(f"Error checking temporal/spatial properties: {e}")
                        # 出错时保持默认值True

                    # 如果时间顺序或空间关系不同，返回失败
                    if (are_both_conjunctions and not is_same_order_and_same_spatial) or \
                       ((are_both_equivalence or are_both_implication) and not is_same_order):
                        return False

                    # 检查复合术语的组件数量是否相同
                    if term1.size() != term2.size():
                        return False

                    # 检查图像索引
                    try:
                        from linars.org.opennars.language.image_ext import ImageExt
                        from linars.org.opennars.language.image_int import ImageInt

                        if (isinstance(term1, ImageExt) and isinstance(term2, ImageExt) and \
                            term1.relation_index != term2.relation_index) or \
                           (isinstance(term1, ImageInt) and isinstance(term2, ImageInt) and \
                            term1.relation_index != term2.relation_index):
                            return False
                    except Exception as e:
                        print(f"Error checking image index: {e}")
            except Exception as e:
                print(f"Error processing compound terms: {e}")
                return False

            # 如果是可交换的，尝试随机排列
            try:
                if term1.is_commutative():
                    # 获取组件并随机排序
                    components_list = term1.clone_terms()
                    if components_list is not None and hasattr(CompoundTerm, 'shuffle'):
                        CompoundTerm.shuffle(components_list, rnd)
            except Exception as e:
                print(f"Error handling commutative terms: {e}")
                # 继续处理，不返回错误

            # 检查组件
            try:
                t1_components = getattr(term1, 'term', None)
                t2_components = getattr(term2, 'term', None)

                if t1_components is None or t2_components is None or len(t1_components) != len(t2_components):
                    return False
            except (AttributeError, TypeError) as e:
                print(f"Error accessing term components: {e}")
                return False

            # 复制映射以避免修改原始映射
            try:
                maps_copy = Variables.copy_map_from(maps)

                # 递归检查每个组件
                for i in range(len(t1_components)):
                    if i >= len(t2_components):
                        return False
                    if not Variables.find_substitute_with_maps(rnd, type_char, t1_components[i], t2_components[i], maps_copy, allow_partial):
                        return False

                # 更新原始映射
                Variables.append_to_map(maps_copy[0], maps[0])
                Variables.append_to_map(maps_copy[1], maps[1])
                return True
            except Exception as e:
                print(f"Error during recursive component check: {e}")
                return False
        except Exception as e:
            # 最外层异常处理
            print(f"Unexpected error in find_substitute_with_maps: {e}")
            return False

    @staticmethod
    def copy_map_from(source: List[Dict[Term, Term]]) -> List[Dict[Term, Term]]:
        """
        复制映射

        参数:
            source: 源映射列表

        返回:
            List[Dict[Term, Term]]: 复制的映射列表
        """
        destination = [OrderedDict(), OrderedDict()]

        if source[0] is not None:
            Variables.append_to_map(source[0], destination[0])

        if source[1] is not None:
            Variables.append_to_map(source[1], destination[1])

        return destination

    @staticmethod
    def append_to_map(source: Dict[Term, Term], target: Dict[Term, Term]) -> None:
        """
        将源映射添加到目标映射

        参数:
            source: 源映射
            target: 目标映射
        """
        for key, value in source.items():
            target[key] = value

    @staticmethod
    def allow_unification(type_char: str, uni_type: str) -> bool:
        """
        检查是否允许统一

        参数:
            type_char: 变量类型
            uni_type: 统一类型

        返回:
            bool: 是否允许统一
        """
        # 相同类型总是允许统一
        if uni_type == type_char:
            return True

        # 独立变量可以统一依赖变量和查询变量
        if uni_type == VAR_INDEPENDENT:
            return type_char == VAR_DEPENDENT or type_char == VAR_QUERY

        # 依赖变量可以统一查询变量
        if uni_type == VAR_DEPENDENT:
            return type_char == VAR_QUERY

        return False

    @staticmethod
    def contain_var(n: str) -> bool:
        """
        检查字符串是否表示包含变量的术语的名称
        参数:
            n: 要检查的字符串名称
        返回:
            bool: 名称是否包含变量
        """
        if n is None:
            return False

        for i in range(len(n)):
            if n[i] in [VAR_INDEPENDENT, VAR_DEPENDENT, VAR_QUERY]:
                # 检查前一个字符是否是分隔符
                if i > 0 and n[i-1] not in [',', '(', '{', '[']:
                    return False
                return True

        return False

    @staticmethod
    def contain_var_terms(terms: List[Term]) -> bool:
        """
        检查术语列表是否包含变量

        参数:
            terms: 术语列表

        返回:
            bool: 是否包含变量
        """
        for term in terms:
            if isinstance(term, Variable):
                return True
        return False

    @staticmethod
    def unify(rnd: random.Random, type_char: str, terms: List[Term]) -> bool:
        """
        统一两个术语

        参数:
            rnd: 随机数生成器
            type_char: 变量类型
            terms: 术语列表(包含两个术语)

        返回:
            bool: 统一是否可能
        """
        return Variables.unify_terms(rnd, type_char, terms[0], terms[1], terms)

    @staticmethod
    def unify_terms(rnd: random.Random, type_char: str, t1: Term, t2: Term,
                   compound: List[Term], allow_partial: bool = False) -> bool:
        """
        统一两个术语

        参数:
            rnd: 随机数生成器
            type_char: 变量类型
            t1: 第一个术语
            t2: 第二个术语
            compound: 复合术语列表
            allow_partial: 是否允许部分匹配

        返回:
            bool: 统一是否可能
        """
        maps = [None, None]  # 初始为空

        # 查找替代
        has_subs = Variables.find_substitute_with_maps(rnd, type_char, t1, t2, maps, allow_partial)

        if has_subs:
            # 应用替代
            if isinstance(compound[0], Variable) and maps[0] is not None and compound[0] in maps[0]:
                a = maps[0][compound[0]]
            elif isinstance(compound[0], CompoundTerm):
                a = Variables.apply_substitute_and_rename_variables(cast(CompoundTerm, compound[0]), maps[0])
            else:
                a = compound[0]

            if a is None:
                return False

            if isinstance(compound[1], Variable) and maps[1] is not None and compound[1] in maps[1]:
                b = maps[1][compound[1]]
            elif isinstance(compound[1], CompoundTerm):
                b = Variables.apply_substitute_and_rename_variables(cast(CompoundTerm, compound[1]), maps[1])
            else:
                b = compound[1]

            if b is None:
                return False

            # 更新复合术语
            compound[0] = a
            compound[1] = b

            return True

        return False

    @staticmethod
    def apply_substitute_and_rename_variables(t: CompoundTerm, subs: Optional[Dict[Term, Term]]) -> Optional[Term]:
        """
        应用替代并重命名变量

        参数:
            t: 复合术语
            subs: 替代映射

        返回:
            Term: 应用替代后的术语
        """
        if subs is None or not subs:
            # 无需更改
            return t

        # 应用替代
        r = t.apply_substitute(subs)

        if r is None:
            return None

        if r.equals(t):
            return t

        return r

    @staticmethod
    def indep_var_used_invalid(t: Term) -> bool:
        """
        检查独立变量使用是否无效

        参数:
            t: 术语

        返回:
            bool: 是否无效
        """
        if isinstance(t, Conjunction) or isinstance(t, Disjunction):
            components = t.get_components()
            for component in components:
                if Variables.indep_var_used_invalid(component):
                    return True

        if not isinstance(t, Inheritance) and not isinstance(t, Similarity):
            return False

        return t.has_var_indep()

    @staticmethod
    def make_common_variable(v1: Term, v2: Term) -> Variable:
        """
        创建公共变量

        参数:
            v1: 第一个变量
            v2: 第二个变量

        返回:
            Variable: 公共变量
        """
        # v2先于当类型不匹配时，但它是允许的重命名，如$1 -> #1，则应该使用第二个类型
        return Variable(str(v2) + str(v1) + '$')

    @staticmethod
    def has_substitute(rnd: random.Random, type_char: str, term1: Term, term2: Term) -> bool:
        """
        检查两个术语是否可以统一

        参数:
            rnd: 随机数生成器
            type_char: 变量类型
            term1: 第一个术语
            term2: 第二个术语

        返回:
            bool: 是否有替代
        """
        return Variables.find_substitute(rnd, type_char, term1, term2, OrderedDict(), OrderedDict())
