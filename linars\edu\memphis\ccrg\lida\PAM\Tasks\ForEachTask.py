#LIDA Cognitive Framework
#Pennsylvania State University, Course : SWENG480
#Authors: <AUTHORS>

"""
A task to add a PamLink and its sink to the percept.
"""

from typing import Dict, List, Optional
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTaskImpl import FrameworkTaskImpl
from linars.edu.memphis.ccrg.lida.Framework.Shared.Link import Link
from linars.edu.memphis.ccrg.lida.Framework.Shared.Node import Node
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure
from linars.edu.memphis.ccrg.lida.PAM.PAMemory import PAMemory

class ForEachTask(FrameworkTaskImpl):
    """
    A task to add a PamLink and its sink to the percept.
    
    See Also:
        ExcitationTask creates this task
        PropagationTask creates this task
    """
    
    def __init__(self, link: Link, pam: PAMemory, seq_ns: Optional[NodeStructure] = None, 
                scene_ns: Optional[NodeStructure] = None, act_stamp: Optional[str] = None):
        """
        Initialize a ForEachTask.
        
        Args:
            link: The link to process
            pam: The PAMemory
            seq_ns: The sequence NodeStructure
            scene_ns: The scene NodeStructure
            act_stamp: The action stamp
        """
        super().__init__(1, "tact")
        self.link = link
        self.pam = pam
        self.seq_ns = seq_ns
        self.scene_ns = scene_ns
        self.act_stamp = act_stamp
        
        self.sinks = None
        self.sink = None
        self.source = None
        self.goal_ns = None
        self.yufa_ns = None
        self.else_size = 0
        self.else_links = None
        self.else_map = None
        self.cosc_list = None
        
    def run_this_framework_task(self):
        """
        Adds link's sink to the percept and tries to add the link as well then finishes.
        """
        self.seq_ns = self.pam.get_workspace_buffer("seq").get_buffer_content(None)
        self.yufa_ns = self.pam.get_workspace_buffer("yufa").get_buffer_content(None)
        
        # 进入循环结构体内，需要存入上位时序，以便回溯，遍历也只需一条上位边？
        self.seq_ns.get_do_main_path_map().get(self.act_stamp).append(self.link)
        
        # todo 判断哪种循环，这里先实现do while。另外维护循环变量和层级，以便回溯
        
        # 循环体执行跟普通时序一致。do的部分
        self.pam.get_act_root(self.link, False, True, self.act_stamp)
        
        # 循环体执行完，判断是否继续循环，while的部分
        # todo 不能直接在这里执行最后步骤，因为都是子线程，很难判断他们是否都执行完毕，
        # 所以需要在外面再加一个任务，来判断是否执行完毕？
        
        # String query = "match (m)-[r:循环条件]->(i) where id(m) = " + sink.getNodeId() + " return r";
        
        self.cancel()
