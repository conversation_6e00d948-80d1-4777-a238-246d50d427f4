# LIDA Cognitive Framework
"""
The interface for LIDA's episodic memory. Episodic memory in LIDA
communicates with the workspace, receiving memory cues, and returning local
associations.

Specific implementations of episodic memories must implement
this interface. Every implementation of this interface must also implement
BroadcastListener.
"""

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule

class EpisodicMemory(FrameworkModule):
    """
    The interface for LIDA's episodic memory. Episodic memory in LIDA
    communicates with the workspace, receiving memory cues, and returning local
    associations.
    
    Specific implementations of episodic memories must implement
    this interface. Every implementation of this interface must also implement
    BroadcastListener.
    """
    pass
