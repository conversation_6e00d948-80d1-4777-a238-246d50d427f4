"""
乘积项定义
按照NARS理论定义的项的序列乘积操作
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar, Collection
import copy

from linars.edu.memphis.ccrg.linars.term import Term
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm

class NativeOperator:
    """Native operators enum"""
    PRODUCT = "*"

class Product(CompoundTerm):
    """
    乘积项类
    按照NARS理论定义的项的序列乘积操作
    """

    def __init__(self, *arg: Term, finding_list: Optional[Collection[Term]] = None):
        """
        构造函数

        参数:
            *arg: 项的组成部分列表
            finding_list: 查找列表(可选)
        """
        super().__init__()
        if finding_list:
            self.finding_list = list(finding_list)

        # 将参数转换为列表形式
        if arg:
            # 如果 arg 是一个元组，将其转换为列表
            term_list = list(arg)
            self.init(term_list)
        else:
            # 如果没有参数，初始化为空列表
            self.init([])

    @classmethod
    def from_list(cls, x: List[Term]) -> 'Product':
        """
        从项列表创建乘积项

        参数:
            x: 项列表

        返回:
            Product: 创建的乘积项
        """
        return cls(*x)

    @staticmethod
    def make(*arg: Term) -> 'Product':
        """
        创建乘积项

        参数:
            *arg: 项的组成部分列表

        返回:
            Product: 创建的乘积项
        """
        return Product(*arg)

    def clone(self, replaced: List[Term] = None) -> 'Product':
        """
        克隆乘积项

        参数:
            replaced: 替换的组件项列表，如果为None则使用原始组件项

        返回:
            Product: 新的乘积项对象
        """
        if replaced is not None:
            return Product(*replaced)
        return Product(*self.term)

    def clone_with_terms(self, replaced: List[Term]) -> Optional[CompoundTerm]:
        """
        使用新项克隆对象

        参数:
            replaced: 被替换的项列表

        返回:
            CompoundTerm: 克隆后的对象
        """
        if replaced is None:
            return None
        return Product(*replaced)

    @staticmethod
    def make_from_image(image: CompoundTerm, component: Term, index: int) -> Term:
        """
        尝试从图像项和组件创建乘积项

        参数:
            image: 现有图像项
            component: 要添加到组件列表中的组件
            index: 新图像项中占位符的索引位置

        返回:
            Term: 生成的复合项或简化后的项
        """
        argument = image.clone_terms()
        argument[index] = component
        return Product(*argument)

    def operator(self) -> str:
        """
        获取项的操作符

        返回:
            str: 项的操作符
        """
        return NativeOperator.PRODUCT

    def is_product(self) -> bool:
        """
        检查是否为乘积项

        返回:
            bool: 如果是乘积项则返回True
        """
        return True

    def __len__(self) -> int:
        """
        实现 len() 方法，返回组件项的数量

        返回:
            int: 组件项的数量
        """
        return self.size()

    def __getitem__(self, index: int) -> Term:
        """
        实现下标访问操作，允许使用 product[index] 访问组件项

        参数:
            index: 要访问的组件项的索引

        返回:
            Term: 指定索引处的组件项

        异常:
            IndexError: 如果索引超出范围
        """
        if not isinstance(self.term, list):
            raise TypeError(f"Term attribute is not a list: {type(self.term)}")

        if index < 0 or index >= len(self.term):
            raise IndexError(f"Index {index} out of range for Product with {len(self.term)} components")

        return self.term[index]
