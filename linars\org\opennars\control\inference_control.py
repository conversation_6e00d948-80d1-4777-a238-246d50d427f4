"""
概念推理上下文控制模块。

通过应用推理机来"触发"或激活概念。
"""
from typing import Optional

from linars.edu.memphis.ccrg.linars.memory import Memory
from linars.org.opennars.control.concept.process_anticipation import ProcessAnticipation
from linars.org.opennars.control.derivation_context import DerivationContext
from linars.org.opennars.entity.term_link import TermLink
from linars.org.opennars.inference.budget_functions import BudgetFunctions
from linars.org.opennars.inference.rule_tables import RuleTables

class InferenceControl:
    """
    概念推理控制类。

    负责管理概念的推理过程，通过触发(激活)概念来执行推理。
    """

    @staticmethod
    def select_concept_for_inference(mem: Memory, narParameters, nar):
        """
        选择概念进行推理。

        参数:
            mem: 内存对象
            narParameters: NAR参数配置
            nar: NAR推理机实例
        """
        # Select a concept from the concept bag
        current_concept = None
        with mem.concepts_lock:
            current_concept = mem.concepts.take_out()
            if current_concept is None:
                return

        # Create derivation context
        nal = DerivationContext(mem, narParameters, nar)
        put_back_concept = False
        forget_cycles = 0.0

        # Process the concept
        with current_concept.lock:
            # Maintain disappointed anticipations
            ProcessAnticipation.maintain_disappointed_anticipations(narParameters, current_concept, nar, mem)

            # Remove concepts without tasklinks or termlinks
            if current_concept.task_links.name_size() == 0:
                mem.concepts.pick_out(current_concept.get_term().to_string())
                mem.concept_removed(current_concept)
                return
            # term_links是set，不是list
            if current_concept.term_links.__len__() == 0:
                mem.concepts.pick_out(current_concept.get_term().to_string())
                mem.concept_removed(current_concept)
                return

            # Set current concept and fire it
            nal.set_current_concept(current_concept)
            put_back_concept = InferenceControl.fire_concept(nal, 1)

            # Update concept quality and forget cycles
            if put_back_concept:
                forget_cycles = nar.memory.cycles(2.0)  # CONCEPT_FORGET_DURATIONS
                if nar.memory.emotion is not None:
                    nal.current_concept.set_quality(BudgetFunctions.or_op(
                        nal.current_concept.get_quality(),
                        nar.memory.emotion.happy()
                    ))

        # Put the concept back into the bag
        if put_back_concept:
            with mem.concepts_lock:
                mem.concepts.put_back(nal.current_concept, forget_cycles, nar.memory)

    @staticmethod
    def fire_concept(nal: DerivationContext, num_task_links: int) -> bool:
        """
        触发(激活)一个概念。

        参数:
            nal: 派生上下文
            num_task_links: 要处理的任务链接数量

        返回:
            bool: 如果概念需要放回内存则返回True
        """
        for i in range(num_task_links):
            # Check if there are task links
            if nal.current_concept.task_links.name_size() == 0:
                return False

            # Take out a task link
            nal.current_task_link = nal.current_concept.task_links.take_out()
            if nal.current_task_link is None:
                return False

            # Fire the task link if it's above threshold
            if nal.current_task_link.budget.above_threshold():
                InferenceControl.fire_task_link(nal, nal.narParameters.TERMLINK_MAX_REASONED)

            # Put the task link back
            nal.current_concept.task_links.put_back(
                nal.current_task_link,
                nal.memory.cycles(nal.narParameters.TASKLINK_FORGET_DURATIONS),
                nal.memory
            )

        return True

    @staticmethod
    def fire_task_link(nal: DerivationContext, term_links: int):
        """
        触发一个任务链接。

        参数:
            nal: 派生上下文
            term_links: 要处理的术语链接数量
        """
        # Get the task from the task link
        task = nal.current_task_link.get_target()

        # Set current variables
        nal.set_current_term(nal.current_concept.term)
        nal.set_current_task_link(nal.current_task_link)
        nal.set_current_belief_link(None)
        nal.set_current_task(task)

        # Adjust busy emotion
        if nal.memory.emotion is not None:
            nal.memory.emotion.adjust_busy(
                nal.current_task_link.get_priority(),
                nal.current_task_link.get_durability(),
                nal
            )

        # Process transform task link
        if nal.current_task_link.type == TermLink.TRANSFORM:
            nal.set_current_belief(None)
            RuleTables.transform_task(nal.current_task_link, nal)
        else:
            # Process term links
            while term_links > 0:
                # Select a term link
                term_link = nal.current_concept.select_term_link(
                    nal.current_task_link,
                    nal.time.time(),
                    nal.narParameters
                )

                if term_link is None:
                    break

                # Fire the term link
                InferenceControl.fire_term_link(term_link, nal)

                # Return the term link
                nal.current_concept.return_term_link(term_link)

                term_links -= 1

        # Emit concept fire event
        nal.memory.emit("ConceptFire", nal)

    @staticmethod
    def fire_term_link(term_link: TermLink, nal: DerivationContext) -> bool:
        """
        触发一个术语链接。

        参数:
            term_link: 术语链接对象
            nal: 派生上下文

        返回:
            bool: 成功则返回True
        """
        # Set current belief link
        nal.set_current_belief_link(term_link)

        # Reason with the term link
        RuleTables.reason(nal.current_task_link, term_link, nal)

        # Emit term link select event
        nal.memory.emit("TermLinkSelect", term_link, nal.current_concept, nal)

        return True
