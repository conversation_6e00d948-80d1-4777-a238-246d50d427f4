# LIDA Cognitive Framework
"""
A Codelet is a specialized type of FrameworkTask that has activation.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.Tasks.FrameworkTask import FrameworkTask
from linars.edu.memphis.ccrg.lida.Framework.Shared.NodeStructure import NodeStructure

class Codelet(FrameworkTask):
    """
    A Codelet is a specialized type of FrameworkTask that has activation.
    """
    
    @abstractmethod
    def get_sought_content(self) -> NodeStructure:
        """
        Get the content this codelet is looking for.
        
        Returns:
            The content this codelet is looking for
        """
        pass
    
    @abstractmethod
    def set_sought_content(self, content: NodeStructure) -> None:
        """
        Set the content this codelet is looking for.
        
        Args:
            content: The content this codelet is looking for
        """
        pass
