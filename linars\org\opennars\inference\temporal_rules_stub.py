"""
TemporalRules的存根实现，用于避免循环导入问题。
"""
from typing import Optional

from linars.org.opennars.entity.sentence import Sentence

class TemporalRules:
    """
    时间推理规则的存根实现，用于避免循环导入问题。
    """

    @staticmethod
    def matching_order(s1: Sentence, s2: Sentence) -> bool:
        """
        检查两个句子的时间顺序是否匹配。
        这是一个存根实现，实际实现在temporal_rules.py中。
        参数:
            s1: 第一个句子
            s2: 第二个句子
        返回:
            如果时间顺序匹配返回True，否则返回False
        """
        # 导入真正的实现
        from linars.org.opennars.inference.temporal_rules import TemporalRules as RealTemporalRules
        return RealTemporalRules.matching_order(s1, s2)
    
    @staticmethod
    def reverse_order(order: int) -> int:
        """
        反转时间顺序。
        这是一个存根实现，实际实现在temporal_rules.py中。
        参数:
            order: 要反转的时间顺序
        返回:
            反转后的时间顺序
        """
        # 导入真正的实现
        from linars.org.opennars.inference.temporal_rules import TemporalRules as RealTemporalRules
        return RealTemporalRules.reverse_order(order)
