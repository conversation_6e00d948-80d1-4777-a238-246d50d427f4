"""
Agent implementation.

This module provides an implementation of the Agent interface.
"""
import logging
from typing import Dict, Any, Optional, List, TYPE_CHECKING

from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.ModuleName import Module<PERSON><PERSON>
from linars.edu.memphis.ccrg.lida.Framework.agent import Agent

if TYPE_CHECKING:
    from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager

class AgentImpl(FrameworkModuleImpl, Agent):
    """
    Basic Agent Implementation.
    """

    def __init__(self, task_manager=None):
        """
        Initialize the agent.

        Args:
            task_manager: The TaskManager to use
        """
        super().__init__(ModuleName.Agent)
        self.task_manager = task_manager
        self.running = False
        self.logger = logging.getLogger(__name__)
        self.logger.debug(f"AgentImpl created with TaskManager: {task_manager}")

    def init(self, params=None):
        """
        Initialize the agent.

        Args:
            params: Parameters for initialization
        """
        if self.task_manager:
            self.task_manager.set_decaying_modules(self.get_submodules().values())
            self.logger.info("FrameworkModules have been started")

    def get_task_manager(self) -> 'TaskManager':
        """
        Returns the Task Manager.

        Returns:
            The TaskManager in charge of all tasks
        """
        return self.task_manager

    def set_task_manager(self, task_manager: 'TaskManager') -> None:
        """
        Set the Task Manager.

        Args:
            task_manager: The TaskManager to set
        """
        self.task_manager = task_manager

    def is_running(self) -> bool:
        """
        Check if the agent is running.

        Returns:
            True if the agent is running, False otherwise
        """
        return self.running

    def start(self) -> None:
        """
        Start the agent.
        """
        if self.task_manager:
            self.task_manager.resume_tasks()
            self.running = True

    def stop(self) -> None:
        """
        Stop the agent.
        """
        if self.task_manager:
            self.task_manager.pause_tasks()
            self.running = False
