"""
时间间隔(Interval)表示NARS中的时间差量级

时间间隔定义:
- 存储时间差的对数值(以D=duration为底)
- 显示值 = 存储值 + 1
- 示例: magnitude=0 显示为"+1", magnitude=1 显示为"+2"
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar

from linars.edu.memphis.ccrg.linars.term import Term

# Symbols
INTERVAL_PREFIX = "+"

class Interval(Term):
    """
    时间间隔类，表示NARS中的时间差量级

    主要功能:
    1. 存储时间差的对数值
    2. 处理时间间隔的创建和转换
    3. 提供时间间隔的克隆方法
    """

    @staticmethod
    def interval(i: str) -> 'Interval':
        """
        从字符串创建时间间隔

        参数:
            i: 时间间隔字符串(如"+1")

        返回:
            Interval: 创建的时间间隔对象

        注意:
        1. 字符串格式必须为"+数字"
        2. 会自动转换为内部存储值
        """
        return Interval(int(i[1:]))

    def has_interval(self) -> bool:
        """
        Check if the term has intervals

        Returns:
            bool: True for intervals
        """
        return True

    def is_interval(self) -> bool:
        """
        Check if the term is an interval

        Returns:
            bool: True for intervals
        """
        return True

    def __init__(self, time: int):
        """
        直接指定时间量级的构造函数

        参数:
            time: 时间值(内部存储值)

        注意:
        1. 显示值 = 存储值 + 1
        2. 自动设置term_name为"+数字"格式
        """
        super().__init__()
        self.time = time
        self.set_term_name(f"{INTERVAL_PREFIX}{time}")

    @classmethod
    def from_string(cls, i: str) -> 'Interval':
        """
        Create an interval from a string

        Args:
            i: The interval string

        Returns:
            Interval: The created interval
        """
        return cls(int(i[1:]) - 1)

    def clone(self) -> 'Interval':
        """
        Clone the interval

        Returns:
            Interval: The cloned interval
        """
        # Can return this as its own clone since it's immutable
        return self

    def clone_deep(self) -> 'Interval':
        """
        Deep clone of the interval

        Returns:
            Interval: The cloned interval
        """
        return self
