"""
查看单元格内对象操作实现

本模块提供ALife环境中查看单元格内对象操作的实现
"""
from typing import List, Optional, Any, Set

from linars.edu.memphis.ccrg.alife.elements.alife_object import ALifeObject
from linars.edu.memphis.ccrg.alife.elements.cell import Cell
from linars.edu.memphis.ccrg.alife.opreations.world_operation import WorldOperation

class SeeObjectsInCell(WorldOperation):
    """
    查看单元格内对象操作实现

    该类实现ALife环境中查看单元格内对象的操作
    """

    def __init__(self):
        """初始化查看单元格内对象操作"""
        super().__init__("seeobjects")

    def execute(self, actor: ALifeObject, target: Optional[ALifeObject], *params) -> Set[ALifeObject]:
        """
        执行查看单元格内对象操作

        参数:
            actor: 执行查看行为的对象
            target: 查看目标(未使用)
            params: 附加参数，包括x和y坐标

        返回:
            指定单元格内的对象集合
        """
        if len(params) < 2:
            return set()

        # Get coordinates from parameters
        try:
            x = int(params[0])
            y = int(params[1])
        except (ValueError, TypeError):
            return set()

        # Get the world from the actor's cell
        cell = actor.get_container()
        if not isinstance(cell, Cell):
            return set()

        world = cell.get_world()

        # Check if coordinates are valid
        if not (0 <= x < world.get_width() and 0 <= y < world.get_height()):
            return set()

        # Get the cell at the specified coordinates
        target_cell = world.get_cell(x, y)

        # Return the objects in the cell
        return set(target_cell.get_objects())
