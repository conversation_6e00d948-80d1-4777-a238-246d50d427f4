"""
Utilities for loading configuration properties.

This module provides utilities for loading properties files and configuring loggers.
"""
import logging
import os
from typing import Dict, Any, Optional

class ConfigUtils:
    """
    Utilities for loading properties files and configuring loggers.
    """
    
    logger = logging.getLogger(__name__)
    
    @staticmethod
    def load_properties(file_name: str) -> Optional[Dict[str, str]]:
        """
        Load properties from a file.
        
        Args:
            file_name: The name of the properties file
            
        Returns:
            A dictionary of properties or None if the file is invalid
        """
        properties = {}
        
        if file_name is None:
            ConfigUtils.logger.warning("Properties file not specified")
            return None
        
        try:
            with open(file_name, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            properties[key.strip()] = value.strip()
        except FileNotFoundError as e:
            ConfigUtils.logger.warning(f"{e}")
            return None
        except IOError as e:
            ConfigUtils.logger.warning(f"{e}")
            return None
        
        return properties
    
    @staticmethod
    def config_loggers(path: str) -> None:
        """
        Configure the logger manager with the specified config file.
        
        Args:
            path: The path to the logging configuration file
        """
        try:
            # In Python, we can use the logging.config module to configure loggers
            import logging.config
            logging.config.fileConfig(path)
        except FileNotFoundError as e:
            ConfigUtils.logger.warning(f"Exception: {e} occurred loading Logging Properties File from path: {path}")
        except PermissionError as e:
            ConfigUtils.logger.warning(f"Exception: {e} occurred loading Logging Properties File from path: {path}")
        except IOError as e:
            ConfigUtils.logger.warning(f"Exception: {e} occurred loading Logging Properties File from path: {path}")
