# LIDA Cognitive Framework
"""
Modules that receive the conscious broadcast must implement this interface.
Implementers will receive each winning Coalition from the GlobalWorkspace.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.GlobalWorkspace.Coalition import Coalition

class BroadcastListener(ModuleListener):
    """
    Modules that receive the conscious broadcast must implement this interface.
    Implementers will receive each winning Coalition from the GlobalWorkspace.
    """
    
    @abstractmethod
    def receive_broadcast(self, coalition: Coalition) -> None:
        """
        Listener must receive a broadcast of the winning Coalition.
        This method should return as quickly as possible in order to not delay the rest of the broadcasting.
        
        Args:
            coalition: The Coalition that won the most recent competition for consciousness
        """
        pass
