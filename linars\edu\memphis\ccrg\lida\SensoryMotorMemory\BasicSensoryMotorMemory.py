# LIDA Cognitive Framework
"""
Default implementation of a Map-based SensoryMotorMemory.
"""

import logging
from typing import List, Dict, Any, Optional
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModuleImpl import FrameworkModuleImpl
from linars.edu.memphis.ccrg.lida.Framework.FrameworkModule import FrameworkModule
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskManager import TaskManager
from linars.edu.memphis.ccrg.lida.ActionSelection.Action import Action
from linars.edu.memphis.ccrg.lida.ActionSelection.ActionSelectionListener import ActionSelectionListener
from linars.edu.memphis.ccrg.lida.Environment.Environment import Environment
from linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemoryListener import SensoryMemoryListener
from linars.edu.memphis.ccrg.lida.SensoryMotorMemory.SensoryMotorMemory import SensoryMotorMemory
from linars.edu.memphis.ccrg.lida.SensoryMotorMemory.SensoryMotorMemoryListener import SensoryMotorMemoryListener

class BasicSensoryMotorMemory(FrameworkModuleImpl, SensoryMotorMemory, SensoryMemoryListener, ActionSelectionListener):
    """
    Default implementation of a Map-based SensoryMotorMemory.
    """

    # Default values
    DEFAULT_BACKGROUND_TASK_TICKS = 1

    def __init__(self):
        """
        Default constructor.
        """
        super().__init__()
        self.process_action_task_ticks = self.DEFAULT_BACKGROUND_TASK_TICKS
        self.listeners: List[SensoryMotorMemoryListener] = []
        self.action_algorithm_map: Dict[int, Any] = {}
        self.environment = None
        self.logger = logging.getLogger(self.__class__.__name__)

    def init(self, params=None) -> None:
        """
        Initialize this BasicSensoryMotorMemory.

        Args:
            params: Parameters for initialization, defaults to None
        """
        super().init(params)
        # print(f"BasicSensoryMotorMemory.init() called with params: {params}")
        print("BasicSensoryMotorMemory.init()")
        self.process_action_task_ticks = self.get_param("sensoryMotorMemory.processActionTaskTicks", self.DEFAULT_BACKGROUND_TASK_TICKS)

    def get_param(self, name: str, default_value: Any) -> Any:
        """
        Get a parameter value with a default.

        Args:
            name: The name of the parameter
            default_value: The default value

        Returns:
            The parameter value or the default value
        """
        parameters = getattr(self, "parameters", {})
        if parameters and name in parameters:
            return parameters[name]
        return default_value

    def add_action_algorithm(self, action_id: int, action: Any) -> None:
        """
        Adds an Algorithm to this SensoryMotorMemory.

        Args:
            action_id: Id of Action which is implemented by the algorithm
            action: An algorithm
        """
        self.action_algorithm_map[action_id] = action

    def add_listener(self, listener: ModuleListener) -> None:
        """
        Add a listener to this BasicSensoryMotorMemory.

        Args:
            listener: The listener to add
        """
        if isinstance(listener, SensoryMotorMemoryListener):
            self.add_sensory_motor_memory_listener(listener)
        else:
            self.logger.warning(f"Cannot add listener {listener} at tick {TaskManager.get_current_tick()}")

    def add_sensory_motor_memory_listener(self, listener: SensoryMotorMemoryListener) -> None:
        """
        Any non-environment communication should use listeners.

        Args:
            listener: The SensoryMotorMemoryListener to add
        """
        self.listeners.append(listener)

    def set_associated_module(self, module: FrameworkModule, module_usage: str) -> None:
        """
        Set an associated module for this BasicSensoryMotorMemory.

        Args:
            module: The module to associate with this BasicSensoryMotorMemory
            module_usage: How this BasicSensoryMotorMemory will use the module
        """
        if isinstance(module, Environment):
            self.environment = module

            # Import necessary operators
            from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
            from linars.org.opennars.operator.misc.say import Say
            from linars.org.opennars.operator.misc.add import Add
            from linars.org.opennars.operator.misc.add0 import Add0
            from linars.org.opennars.operator.misc.count import Count
            from linars.org.opennars.operator.misc.cut_str import CutStr
            from linars.org.opennars.operator.misc.reflect import Reflect
            from linars.org.opennars.operator.misc.system_nar import SystemNar
            from linars.org.opennars.operator.misc.search_sb import SearchSB
            from linars.org.opennars.operator.misc.search_sos import SearchSOS
            from linars.org.opennars.operator.misc.search_mos import SearchMOS
            from linars.org.opennars.operator.misc.search_mom import SearchMOM
            from linars.org.opennars.operator.misc.search_som import SearchSOM
            from linars.org.opennars.operator.misc.attent import Attent

            # Add operators to the NAR memory
            if hasattr(AgentStarter, 'nar') and AgentStarter.nar is not None:
                # Basic operators
                AgentStarter.nar.memory.add_operator(Say("^say"))
                AgentStarter.nar.memory.add_operator(Add())
                AgentStarter.nar.memory.add_operator(Add0())
                AgentStarter.nar.memory.add_operator(Count())
                AgentStarter.nar.memory.add_operator(CutStr())
                AgentStarter.nar.memory.add_operator(Reflect())
                AgentStarter.nar.memory.add_operator(SystemNar())

                # Search operators
                AgentStarter.nar.memory.add_operator(SearchSB())
                AgentStarter.nar.memory.add_operator(SearchSOS())
                AgentStarter.nar.memory.add_operator(SearchSOM("^search"))
                AgentStarter.nar.memory.add_operator(SearchMOM())
                AgentStarter.nar.memory.add_operator(SearchMOS())

                # Learn operator
                from linars.org.opennars.operator.misc.learn import Learn
                AgentStarter.nar.memory.add_operator(Learn("^learn"))

                # Response operator
                from linars.org.opennars.operator.misc.respond import Respond
                AgentStarter.nar.memory.add_operator(Respond())

                # Attention operator
                AgentStarter.nar.memory.add_operator(Attent())

                self.logger.info(f"Added operators to NAR memory at tick {TaskManager.get_current_tick()}")
            else:
                self.logger.warning(f"NAR instance not available at tick {TaskManager.get_current_tick()}")

            self.logger.debug(f"Setting up environment at tick {TaskManager.get_current_tick()}")
        else:
            self.logger.warning(f"Cannot add module {module} at tick {TaskManager.get_current_tick()}")

    def receive_action(self, action: Action) -> None:
        """
        Receive an action from ActionSelection.

        Args:
            action: The action to receive
        """
        if action is not None:
            # 获取动作名称
            action_name = action.get_name()
            print(f"接收到动作: {action_name}")

            # 将动作转换为NARS操作符格式并输入到NARS系统
            if action_name:
                # 处理特殊动作名称映射
                if action_name in ["get", "eat"]:
                    action_name = "eat"

                # 将动作转换为NARS操作符格式
                nars_input = f"(^{action_name},{{SELF}})! :|:"
                print(f"向NARS输入: {nars_input}")

                try:
                    # 导入AgentStarter并输入到NARS
                    from linars.edu.memphis.ccrg.lida.Framework.Initialization.agent_starter import AgentStarter
                    if AgentStarter.nar is not None:
                        AgentStarter.nar.add_input(nars_input)
                        AgentStarter.nar.cycles(10)
                    else:
                        print("AgentStarter.nar 为 None，无法输入动作")
                except Exception as e:
                    print(f"向NARS输入动作时发生错误: {e}")

            # 特殊处理"回应"动作
            if action_name == "回应":
                try:
                    from linars.org.opennars.operator.misc.respond_action import handle_respond_action
                    result = handle_respond_action(action_name)
                    print(f"回应动作处理结果: {result}")
                except Exception as e:
                    print(f"处理回应动作时发生错误: {e}")

            # 检查是否有对应的算法映射
            action_id = action.get_id()
            if action_id in self.action_algorithm_map:
                command = self.action_algorithm_map[action_id]
                self.send_actuator_command(command)
            else:
                # 直接使用动作名称作为命令
                self.send_actuator_command(action_name)
        else:
            self.logger.warning(f"Received null action at tick {TaskManager.get_current_tick()}")

    def send_actuator_command(self, command: Any) -> None:
        """
        Executes specified action algorithm.

        Args:
            command: Algorithm to execute in the agent's actuators or directly in the environment
        """
        if self.environment is not None:
            self.environment.process_action(command)
            for listener in self.listeners:
                listener.receive_actuator_command(command)
        else:
            self.logger.warning(f"Cannot send command, environment is None at tick {TaskManager.get_current_tick()}")

    def decay_module(self, ticks: int) -> None:
        """
        Decay this module.

        Args:
            ticks: The number of ticks to decay by
        """
        # Module-specific decay code
        pass

    def get_module_content(self, *params: Any) -> Any:
        """
        Get the content of this module.

        Args:
            params: Parameters specifying what content to return

        Returns:
            The content of this module
        """
        return None

    def receive_sensory_memory_content(self, content: Any) -> None:
        """
        Receive content from SensoryMemory.

        Args:
            content: The content to receive
        """
        # Research problem
        pass
