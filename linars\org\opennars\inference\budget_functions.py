"""
资源分配的预算函数
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, ClassVar
import enum
import math

from linars.org.opennars.entity.truth_value import TruthValue
from linars.org.opennars.entity.budget_value import BudgetValue
from linars.edu.memphis.ccrg.linars.term import Term
# 延迟导入Memory以避免循环导入
# from linars.edu.memphis.ccrg.linars.memory import Memory

class Activating(enum.Enum):
    """激活模式枚举"""
    Max = 0
    TaskLink = 1

class BudgetFunctions:
    """
    资源分配的预算函数
    """

    # ----- Belief evaluation -----
    @staticmethod
    def truth_to_quality(t: TruthValue) -> float:
        """
        仅根据真值确定判断的质量

        主要由置信度决定，但也偏好二元判断

        参数:
            t: 判断的真值

        返回:
            float: 仅根据真值的判断质量
        """
        # 检查t是否为None
        if t is None:
            return 0.0

        try:
            exp = t.get_expectation()
            return max(exp, (1 - exp) * 0.75)
        except Exception as e:
            print(f"错误: 获取真值质量时出错: {e}")
            return 0.0

    @staticmethod
    def rank_belief(judg, rank_truth_expectation: bool) -> float:
        """
        根据质量和原创性(stamp baseLength)确定判断的排名

        参数:
            judg: 要排名的判断
            rank_truth_expectation: 是否按真值期望排名

        返回:
            float: 仅根据真值的判断排名
        """
        # 检查judg和judg.truth是否为None
        if judg is None or not hasattr(judg, 'truth') or judg.truth is None:
            return 0.0

        if rank_truth_expectation:
            try:
                return judg.truth.get_expectation()
            except Exception as e:
                print(f"错误: 获取期望值时出错: {e}")
                return 0.0

        try:
            confidence = judg.truth.get_confidence()
            # originality = judg.stamp.get_originality()
            return confidence  # or(confidence, originality)
        except Exception as e:
            print(f"错误: 获取置信度时出错: {e}")
            return 0.0

    @staticmethod
    def revise(t_truth: TruthValue, b_truth: TruthValue, truth: TruthValue,
              feedback_to_links: bool, nal) -> BudgetValue:
        """
        评估修订的质量，然后降低前提的优先级

        参数:
            t_truth: 任务中判断的真值
            b_truth: 信念的真值
            truth: 修订结论的真值
            feedback_to_links: 是否向链接提供反馈
            nal: 派生上下文

        返回:
            BudgetValue: 新任务的预算
        """
        # 检查参数是否为None
        if t_truth is None or b_truth is None or truth is None or nal is None:
            return BudgetValue(0.0, 0.0, 0.0, None)

        # 检查nal.current_task是否为None
        if not hasattr(nal, 'current_task') or nal.current_task is None:
            return BudgetValue(0.0, 0.0, 0.0, nal.nar.narParameters if hasattr(nal, 'nar') else None)

        try:
            dif_t = truth.get_exp_dif_abs(t_truth)
            task = nal.current_task
            task.dec_priority(1 - dif_t)
            task.dec_durability(1 - dif_t)

            if feedback_to_links:
                # 检查nal.current_task_link和nal.current_belief_link是否为None
                if hasattr(nal, 'current_task_link') and nal.current_task_link is not None:
                    t_link = nal.current_task_link
                    t_link.dec_priority(1 - dif_t)
                    t_link.dec_durability(1 - dif_t)

                if hasattr(nal, 'current_belief_link') and nal.current_belief_link is not None:
                    b_link = nal.current_belief_link
                    dif_b = truth.get_exp_dif_abs(b_truth)
                    b_link.dec_priority(1 - dif_b)
                    b_link.dec_durability(1 - dif_b)

            dif = truth.get_confidence() - max(t_truth.get_confidence(), b_truth.get_confidence())
            priority = BudgetFunctions.or_op(dif, task.get_priority())
            durability = BudgetFunctions.ave_ari(dif, task.get_durability())
            quality = BudgetFunctions.truth_to_quality(truth)

            return BudgetValue(priority, durability, quality, nal.nar.narParameters if hasattr(nal, 'nar') else None)
        except Exception as e:
            print(f"错误: 修订预算时出错: {e}")
            return BudgetValue(0.0, 0.0, 0.0, nal.nar.narParameters if hasattr(nal, 'nar') else None)

    @staticmethod
    def update(task, b_truth: TruthValue, narParameters) -> BudgetValue:
        """
        更新信念

        参数:
            task: 包含新信念的任务
            b_truth: 先前信念的真值
            narParameters: NAR参数

        返回:
            BudgetValue: 更新任务的预算值
        """
        # 检查task和task.sentence是否为None
        if task is None or not hasattr(task, 'sentence') or task.sentence is None:
            return BudgetValue(0.0, 0.0, 0.0, narParameters)

        # 检查task.sentence.truth和b_truth是否为None
        if not hasattr(task.sentence, 'truth') or task.sentence.truth is None or b_truth is None:
            return BudgetValue(0.0, 0.0, 0.0, narParameters)

        try:
            t_truth = task.sentence.truth
            dif = t_truth.get_exp_dif_abs(b_truth)
            priority = BudgetFunctions.or_op(dif, task.get_priority())
            durability = BudgetFunctions.ave_ari(dif, task.get_durability())
            quality = BudgetFunctions.truth_to_quality(b_truth)
            return BudgetValue(priority, durability, quality, narParameters)
        except Exception as e:
            print(f"错误: 更新信念时出错: {e}")
            return BudgetValue(0.0, 0.0, 0.0, narParameters)

    # ----- Links -----
    @staticmethod
    def distribute_among_links(b: BudgetValue, n: int, narParameters) -> BudgetValue:
        """
        将任务的预算分配到其链接中

        参数:
            b: 原始预算
            n: 链接数量
            narParameters: NAR参数

        返回:
            BudgetValue: 每个链接的预算值
        """
        priority = b.get_priority() / math.sqrt(n)
        return BudgetValue(priority, b.get_durability(), b.get_quality(), narParameters)

    # ----- Concept -----
    @staticmethod
    def activate(receiver: BudgetValue, amount: BudgetValue, mode: Activating):
        """
        通过传入的TaskLink激活概念

        参数:
            receiver: 接收激活的预算
            amount: 新项目的预算
            mode: 激活模式
        """
        if mode == Activating.Max:
            BudgetFunctions.merge(receiver, amount)
        elif mode == Activating.TaskLink:
            old_pri = receiver.get_priority()
            receiver.set_priority(BudgetFunctions.or_op(old_pri, amount.get_priority()))
            receiver.set_durability(BudgetFunctions.ave_ari(receiver.get_durability(), amount.get_durability()))
            receiver.set_quality(receiver.get_quality())

    # ----- Bag functions, on all Items -----
    @staticmethod
    def apply_forgetting(budget: BudgetValue, forget_cycles: float, relative_threshold: float):
        """
        在项目使用后降低优先级，在Bag中调用

        经过恒定时间后，p应变为d*p。在此期间，
        项目被访问c*p次，每次p-q应乘以d^(1/(c*p))。
        参数"forgetRate"的直观含义是：经过这么多次访问后，
        优先级1将变为d，这是一个运行时可调整的系统参数。

        参数:
            budget: 先前的预算值
            forget_cycles: 新项目的预算
            relative_threshold: Bag的相对阈值
        """
        quality = budget.get_quality() * relative_threshold  # re-scaled quality
        p = budget.get_priority() - quality  # priority above quality

        if p > 0:
            quality += p * math.pow(budget.get_durability(), 1.0 / (forget_cycles * p))  # priority Durability

        budget.set_priority(quality)

    @staticmethod
    def merge(b: BudgetValue, a: BudgetValue):
        """
        当两个项目除预算值外相同时，将一个项目合并到Bag中的另一个项目

        参数:
            b: 要修改的预算基础值
            a: 进行调整的预算调整值
        """
        b.set_priority(max(b.get_priority(), a.get_priority()))
        b.set_durability(max(b.get_durability(), a.get_durability()))
        b.set_quality(max(b.get_quality(), a.get_quality()))

    # ----- Task derivation in LocalRules and SyllogisticRules -----
    @staticmethod
    def forward(truth: TruthValue, nal) -> BudgetValue:
        """
        前向推理结果和调整

        参数:
            truth: 结论的真值
            nal: 内存引用

        返回:
            BudgetValue: 结论的预算值
        """
        return BudgetFunctions.budget_inference(BudgetFunctions.truth_to_quality(truth), 1, nal)

    @staticmethod
    def backward(truth: TruthValue, nal) -> BudgetValue:
        """
        后向推理结果和调整，强情况

        参数:
            truth: 推导结论的信念的真值
            nal: 内存引用

        返回:
            BudgetValue: 结论的预算值
        """
        return BudgetFunctions.budget_inference(BudgetFunctions.truth_to_quality(truth), 1, nal)

    @staticmethod
    def backward_weak(truth: TruthValue, nal) -> BudgetValue:
        """
        后向推理结果和调整，弱情况

        参数:
            truth: 推导结论的信念的真值
            nal: 内存引用

        返回:
            BudgetValue: 结论的预算值
        """
        from linars.org.opennars.inference.truth_functions import TruthFunctions
        return BudgetFunctions.budget_inference(TruthFunctions.w2c(1, nal.nar.narParameters) *
                                              BudgetFunctions.truth_to_quality(truth), 1, nal)

    # ----- Task derivation in CompositionalRules and StructuralRules -----
    @staticmethod
    def compound_forward(truth: TruthValue, content: Term, nal) -> BudgetValue:
        """
        带有CompoundTerm结论的前向推理

        参数:
            truth: 结论的真值
            content: 结论的内容
            nal: 内存引用

        返回:
            BudgetValue: 结论的预算
        """
        complexity = (nal.nar.narParameters.COMPLEXITY_UNIT if content is None
                     else nal.nar.narParameters.COMPLEXITY_UNIT * content.get_complexity())
        return BudgetFunctions.budget_inference(BudgetFunctions.truth_to_quality(truth), complexity, nal)

    @staticmethod
    def compound_backward(content: Term, nal) -> BudgetValue:
        """
        带有CompoundTerm结论的后向推理，强情况

        参数:
            content: 结论的内容
            nal: 内存引用

        返回:
            BudgetValue: 结论的预算
        """
        return BudgetFunctions.budget_inference(1, content.get_complexity() * nal.nar.narParameters.COMPLEXITY_UNIT, nal)

    @staticmethod
    def compound_backward_weak(content: Term, nal) -> BudgetValue:
        """
        带有CompoundTerm结论的后向推理，弱情况

        参数:
            content: 结论的内容
            nal: 内存引用

        返回:
            BudgetValue: 结论的预算
        """
        from linars.org.opennars.inference.truth_functions import TruthFunctions
        return BudgetFunctions.budget_inference(TruthFunctions.w2c(1, nal.nar.narParameters),
                                              content.get_complexity() * nal.nar.narParameters.COMPLEXITY_UNIT, nal)

    @staticmethod
    def concept_activation(mem, t: Term) -> float:
        """
        获取概念的当前激活级别

        参数:
            mem: 内存
            t: 命名概念的Term

        返回:
            float: 概念的优先级值
        """
        # 使用延迟导入避免循环导入
        # 这里不再检查mem的类型，只要它有concept方法即可
        if hasattr(mem, 'concept'):
            c = mem.concept(t)
            return 0.0 if c is None else c.get_priority()
        return 0.0

    @staticmethod
    def budget_inference(qual: float, complexity: float, nal) -> BudgetValue:
        """
        所有推理步骤的通用处理

        参数:
            qual: 推理的质量
            complexity: 结论的语法复杂度
            nal: 内存引用

        返回:
            BudgetValue: 结论任务的预算
        """
        t = nal.current_task_link
        if t is None:
            t = nal.current_task

        priority = t.get_priority()
        durability = t.get_durability() / complexity
        quality = qual / complexity

        b_link = nal.current_belief_link
        if b_link is not None:
            priority = BudgetFunctions.or_op(priority, b_link.get_priority())
            durability = BudgetFunctions.and_op(durability, b_link.get_durability())
            target_activation = BudgetFunctions.concept_activation(nal.nar.memory, b_link.target)
            b_link.inc_priority(BudgetFunctions.or_op(quality, target_activation))
            b_link.inc_durability(quality)

        return BudgetValue(priority, durability, quality, nal.nar.narParameters)

    @staticmethod
    def budget_term_link_concept(c, task_budget: BudgetValue, term_link) -> BudgetValue:
        """
        术语链接概念的预算

        参数:
            c: 概念
            task_budget: 任务预算
            term_link: 术语链接

        返回:
            BudgetValue: 预算
        """
        return task_budget.clone()

    # ----- Utility functions -----
    @staticmethod
    def or_op(a: float, b: float) -> float:
        """
        两个值的逻辑OR

        参数:
            a: 第一个值
            b: 第二个值

        返回:
            float: OR结果
        """
        return a + b - (a * b)

    @staticmethod
    def and_op(a: float, b: float) -> float:
        """
        两个值的逻辑AND

        参数:
            a: 第一个值
            b: 第二个值

        返回:
            float: AND结果
        """
        return a * b

    @staticmethod
    def ave_ari(a: float, b: float) -> float:
        """
        两个值的算术平均值

        参数:
            a: 第一个值
            b: 第二个值

        返回:
            float: 平均结果
        """
        return (a + b) / 2
