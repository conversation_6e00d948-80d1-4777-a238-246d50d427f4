# LIDA Cognitive Framework
"""
A Coalition is a collection of Linkables that can compete for consciousness.
"""

from abc import ABC, abstractmethod
from typing import Any
from linars.edu.memphis.ccrg.lida.Framework.Shared.Activation.Learnable import Learnable

class Coalition(Learnable, ABC):
    """
    A Coalition is a collection of Linkables that can compete for consciousness.
    """
    
    @abstractmethod
    def get_content(self) -> Any:
        """
        Get the content of this Coalition.
        
        Returns:
            The content of this Coalition
        """
        pass
    
    @abstractmethod
    def set_content(self, content: Any) -> None:
        """
        Set the content of this Coalition.
        
        Args:
            content: The content to set
        """
        pass
    
    @abstractmethod
    def get_id(self) -> int:
        """
        Get the ID of this Coalition.
        
        Returns:
            The ID of this Coalition
        """
        pass
