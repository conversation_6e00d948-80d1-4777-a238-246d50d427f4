# LIDA认知框架
"""
LIDA框架中激励策略的接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
from linars.edu.memphis.ccrg.lida.Framework.Strategies.Strategy import Strategy

class ExciteStrategy(Strategy, ABC):
    """
    LIDA框架中激励策略的接口
    """

    @abstractmethod
    def excite(self, current_activation: float, base_level_activation: float, amount: float) -> float:
        """
        使用给定参数激励对象

        参数:
            current_activation: 对象的当前激活值
            base_level_activation: 对象的基础激活水平
            amount: 激励值

        返回:
            新的激活值
        """
        pass
