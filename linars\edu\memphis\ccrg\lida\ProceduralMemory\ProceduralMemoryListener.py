# LIDA Cognitive Framework
"""
A listener of the ProceduralMemory module.
"""

from abc import abstractmethod
from linars.edu.memphis.ccrg.lida.Framework.ModuleListener import ModuleListener
from linars.edu.memphis.ccrg.lida.ActionSelection.Behavior import Behavior

class ProceduralMemoryListener(ModuleListener):
    """
    A listener of the ProceduralMemory module.
    """
    
    @abstractmethod
    def receive_behavior(self, behavior: Behavior) -> None:
        """
        Receive a behavior from ProceduralMemory.
        
        Args:
            behavior: The behavior to receive
        """
        pass
