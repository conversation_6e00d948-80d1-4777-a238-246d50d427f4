"""
直接通过信念处理任务，两者都只包含两个术语。

在匹配过程中，新任务会与该概念中的现有直接任务进行比较，以执行：
  修订：在非重叠证据的判断或目标之间；
  满足：在句子和问题/目标之间；
  合并：在相同类型和标记的项目之间；
  转换：在不同继承关系之间。
"""
from linars.edu.memphis.ccrg.linars.compound_term import CompoundTerm
from linars.edu.memphis.ccrg.linars.concept import Concept
from linars.edu.memphis.ccrg.linars.term import Term
from linars.org.opennars.control.derivation_context import DerivationContext
from linars.org.opennars.entity.budget_value import BudgetValue
from linars.org.opennars.entity.sentence import Sentence
from linars.org.opennars.entity.stamp import Stamp
from linars.org.opennars.entity.task import Task
from linars.org.opennars.entity.truth_value import TruthValue
from linars.org.opennars.inference.budget_functions import BudgetFunctions
from linars.org.opennars.inference.temporal_rules import TemporalRules
from linars.org.opennars.inference.truth_functions import TruthFunctions
from linars.org.opennars.inference.utility_functions import UtilityFunctions
from linars.org.opennars.io.symbols import QUESTION_MARK, QUEST_MARK, VAR_QUERY
from linars.org.opennars.language.equivalence import Equivalence
from linars.org.opennars.language.implication import Implication
from linars.org.opennars.language.inheritance import Inheritance
from linars.org.opennars.language.similarity import Similarity
from linars.org.opennars.language.statement import Statement
from linars.org.opennars.language.variable import Variable

from typing import List, Optional
import math

class LocalRules:
    """
    直接通过信念处理任务，两者都只包含两个术语。
    """

    @staticmethod
    def match(task: Task, belief: Sentence, belief_concept: Concept, nal: DerivationContext) -> bool:
        """
        任务和信念具有相同的内容。

        参数:
            task: 任务
            belief: 信念
            belief_concept: 信念的概念
            nal: 派生上下文

        返回:
            如果任务被处理返回True，否则返回False
        """
        sentence = task.sentence

        if sentence.is_judgment():
            if LocalRules.revisible(sentence, belief, nal.nar.narParameters):
                return LocalRules.revision(sentence, belief, belief_concept, True, nal)
        else:
            if TemporalRules.matching_order(sentence, belief):
                u = [sentence.term, belief.term]
                if Variable.unify(nal.memory.random_number, VAR_QUERY, u):
                    LocalRules.try_solution(belief, task, nal, True)

        return False

    @staticmethod
    def revisible(s1: Sentence, s2: Sentence, narParameters) -> bool:
        """
        检查两个句子是否可以用于修订。

        参数:
            s1: 第一个句子
            s2: 第二个句子
            narParameters: NAR参数

        返回:
            如果可以在两个句子之间进行修订返回True
        """
        if (not s1.is_eternal() and not s2.is_eternal() and
            abs(s1.get_occurrence_time() - s2.get_occurrence_time()) > narParameters.REVISION_MAX_OCCURRENCE_DISTANCE):
            return False

        if s1.term.term_indices is not None and s2.term.term_indices is not None:
            for i in range(len(s1.term.term_indices)):
                if s1.term.term_indices[i] != s2.term.term_indices[i]:
                    return False

        is_revisible = s1.get_revisible()
        is_match = TemporalRules.matching_order_int(s1.get_temporal_order(), s2.get_temporal_order())
        is_re = CompoundTerm.replace_intervals(s1.term).equals(CompoundTerm.replace_intervals(s2.term))
        stam = not Stamp.base_overlap(s1.stamp, s2.stamp)

        return is_revisible and is_match and is_re and stam

    @staticmethod
    def revision(new_belief: Sentence, old_belief: Sentence, belief_concept: Concept, feedback_to_links: bool, nal: DerivationContext) -> bool:
        """
        信念修订。

        总结具有相同内容的两个信念的证据。

        参数:
            new_belief: 任务中的新信念
            old_belief: 具有相同内容的先前信念
            belief_concept: 信念的概念
            feedback_to_links: 是否向链接发送反馈
            nal: 派生上下文

        返回:
            如果任务被处理返回True，否则返回False
        """
        if new_belief.term is None:
            return False

        new_belief.stamp.already_anticipated_neg_confirmation = old_belief.stamp.already_anticipated_neg_confirmation
        new_truth = new_belief.truth.clone()
        old_truth = old_belief.truth
        use_new_belief_term = LocalRules.interval_projection(nal, new_belief.get_term(), old_belief.get_term(), belief_concept.recent_intervals, new_truth)

        truth = TruthFunctions.revision(new_truth, old_truth, nal.nar.narParameters)
        budget = BudgetFunctions.revise(new_truth, old_truth, truth, feedback_to_links, nal)

        if budget.above_threshold():
            counter = -1  # -1 is invalid
            if isinstance(new_belief.term, Implication) and isinstance(old_belief.term, Implication):
                counter = new_belief.term.counter + old_belief.term.counter  # add because the evidence adds up

            return nal.double_premise_task_revised(new_belief.term if use_new_belief_term else old_belief.term, truth, budget, counter)

        return False

    @staticmethod
    def interval_projection(nal: DerivationContext, new_belief_term: Term, old_belief_term: Term, recent_ivals: List[float], new_truth: TruthValue) -> bool:
        """
        间隔投影。

        根据哪个更常用来决定使用旧术语还是新术语，
        同时根据间隔差异调整真值置信度。

        参数:
            nal: 派生上下文
            new_belief_term: 新信念术语
            old_belief_term: 旧信念术语
            recent_ivals: 最近的间隔
            new_truth: 新真值

        返回:
            如果应该使用新信念术语返回True，否则返回False
        """
        from linars.org.opennars.inference.local_rules_helper import interval_projection
        return interval_projection(nal, new_belief_term, old_belief_term, recent_ivals, new_truth)

    @staticmethod
    def calc_task_achievement(t1: Optional[TruthValue], t2: TruthValue) -> float:
        """
        计算任务的达成度。

        参数:
            t1: 第一个真值
            t2: 第二个真值

        返回:
            任务的达成度
        """
        from linars.org.opennars.inference.local_rules_helper import calc_task_achievement
        return calc_task_achievement(t1, t2)

    @staticmethod
    def try_solution(belief: Sentence, task: Task, nal: DerivationContext, report: bool) -> bool:
        """
        检查句子是否为问题或目标提供了更好的答案。

        参数:
            belief: 提议的答案
            task: 要处理的任务
            nal: 派生上下文
            report: 是否报告解决方案

        返回:
            如果任务被处理返回True，否则返回False
        """
        problem = task.sentence
        memory = nal.memory
        old_best = task.get_best_solution()
        task.set_achievement(LocalRules.calc_task_achievement(task.sentence.truth, belief.truth))

        if old_best is not None:
            rate_by_confidence = old_best.get_term().equals(belief.get_term())
            new_q = LocalRules.solution_quality(rate_by_confidence, task, belief, memory, nal.time)
            old_q = LocalRules.solution_quality(rate_by_confidence, task, old_best, memory, nal.time)
            is_better_solution = new_q > old_q

            memory.emit("TrySolution", is_better_solution, task, belief)

            if not is_better_solution:
                if problem.is_goal() and memory.emotion is not None:
                    memory.emotion.adjust_satisfaction(old_q, task.get_priority(), nal)

                memory.emit("Unsolved", task, belief, "Lower quality")
                return False

        task.set_best_solution(memory, belief, nal.time)
        budget = LocalRules.solution_eval(task, belief, task, nal)

        if budget is not None and budget.above_threshold():
            # Solution Activated
            if problem.punctuation == QUESTION_MARK or problem.punctuation == QUEST_MARK:
                if task.is_input() and report:  # only show input tasks as solutions
                    memory.emit("Answer", task, belief)
                else:  # solution to quests and questions can be always showed
                    memory.emit("OutputHandler", task, belief)
            else:  # goal things only show silence related
                memory.emit("OutputHandler", task, belief)

            print(f"LocalRules.try_solution---task: {task.sentence} ---- belief:{belief}")

            nal.add_task(nal.get_current_task(), budget, belief, task.get_parent_belief())
            return True
        else:
            memory.emit("Unsolved", task, belief, "Insufficient budget")

        return False

    @staticmethod
    def solution_quality(rate_by_confidence: bool, prob_t: Task, solution: Sentence, memory, time) -> float:
        """
        评估判断作为问题解决方案的质量。

        参数:
            rate_by_confidence: 是否按置信度评分
            prob_t: 目标或问题
            solution: 要评估的解决方案
            memory: 内存
            time: 时间

        返回:
            判断作为解决方案的质量
        """
        from linars.org.opennars.inference.local_rules_helper import solution_quality
        return solution_quality(rate_by_confidence, prob_t, solution, memory, time)

    @staticmethod
    def solution_eval(problem: Task, solution: Sentence, task: Optional[Task], nal: DerivationContext) -> Optional[BudgetValue]:
        """
        评估信念作为问题解决方案的质量，然后奖励信念并降低问题的优先级。

        参数:
            problem: 要解决的问题(问题或目标)
            solution: 作为解决方案的信念
            task: 要立即处理的任务，或None表示继续处理
            nal: 派生上下文

        返回:
            必要时为新任务(激活的信念)的预算
        """
        if problem.sentence.punctuation != solution.punctuation and solution.term.has_var_query():
            return None

        budget = None
        feedback_to_links = False

        if task is None:
            task = nal.get_current_task()
            feedback_to_links = True

        judgment_task = task.sentence.is_judgment()
        rate_by_confidence = problem.get_term().has_var_query()
        quality = LocalRules.solution_quality(rate_by_confidence, problem, solution, nal.memory, nal.time)

        if problem.sentence.is_goal() and nal.nar.memory.emotion is not None:
            nal.nar.memory.emotion.adjust_satisfaction(quality, task.get_priority(), nal)

        if judgment_task:
            task.inc_priority(quality)
        else:
            task_priority = task.get_priority()
            budget = BudgetValue(UtilityFunctions.OR(task_priority, quality), task.get_durability(),
                                BudgetFunctions.truth_to_quality(solution.truth), nal.nar.narParameters)
            task.set_priority(min(1 - quality, task_priority))

        if feedback_to_links:
            t_link = nal.get_current_task_link()
            t_link.set_priority(min(1 - quality, t_link.get_priority()))
            b_link = nal.get_current_belief_link()
            b_link.inc_priority(quality)

        return budget

    @staticmethod
    def match_reverse(nal: DerivationContext):
        """
        任务和信念反向匹配。

        参数:
            nal: 派生上下文
        """
        task = nal.get_current_task()
        belief = nal.get_current_belief()
        sentence = task.sentence

        if TemporalRules.matching_order(sentence.get_temporal_order(), TemporalRules.reverse_order(belief.get_temporal_order())):
            if sentence.is_judgment():
                LocalRules.infer_to_sym(sentence, belief, nal)
            else:
                LocalRules.conversion(nal)

    @staticmethod
    def match_asym_sym(asym: Sentence, sym: Sentence, figure: int, nal: DerivationContext):
        """
        继承/蕴含匹配相似/等价。

        参数:
            asym: 继承/蕴含句子
            sym: 相似/等价句子
            figure: 共享术语的位置
            nal: 派生上下文
        """
        if nal.get_current_task().sentence.is_judgment():
            LocalRules.infer_to_asym(asym, sym, nal)
        else:
            LocalRules.convert_relation(nal)

    @staticmethod
    def infer_to_sym(judgment1: Sentence, judgment2: Sentence, nal: DerivationContext):
        """
        从一对反向的继承/蕴含产生相似/等价。
        {<S --> P>, <P --> S} |- <S <-> p>

        参数:
            judgment1: 第一个前提
            judgment2: 第二个前提
            nal: 派生上下文
        """
        s1 = judgment1.term
        t1 = s1.get_subject()
        t2 = s1.get_predicate()

        if isinstance(s1, Inheritance):
            content = Similarity.make(t1, t2)
        else:
            content = Equivalence.make(t1, t2, s1.get_temporal_order())

        value1 = judgment1.truth
        value2 = judgment2.truth
        truth = TruthFunctions.intersection(value1, value2, nal.nar.narParameters)
        budget = BudgetFunctions.forward(truth, nal)

        nal.double_premise_task(content, truth, budget, False, False)

    @staticmethod
    def infer_to_asym(asym: Sentence, sym: Sentence, nal: DerivationContext):
        """
        从相似/等价和继承/蕴含产生继承/蕴含。

        参数:
            asym: 非对称前提
            sym: 对称前提
            nal: 派生上下文
        """
        st1 = asym.term
        st2 = sym.term

        t1 = st1.get_subject()
        t2 = st1.get_predicate()
        t3 = st2.get_subject()
        t4 = st2.get_predicate()

        if t1.equals(t3):
            if isinstance(st1, Inheritance) and isinstance(st2, Similarity):
                content = Inheritance.make(t4, t2)
            elif isinstance(st1, Implication) and isinstance(st2, Equivalence):
                content = Implication.make(t4, t2, st1.get_temporal_order())
            else:
                return
        elif t1.equals(t4):
            if isinstance(st1, Inheritance) and isinstance(st2, Similarity):
                content = Inheritance.make(t3, t2)
            elif isinstance(st1, Implication) and isinstance(st2, Equivalence):
                content = Implication.make(t3, t2, st1.get_temporal_order())
            else:
                return
        elif t2.equals(t3):
            if isinstance(st1, Inheritance) and isinstance(st2, Similarity):
                content = Inheritance.make(t1, t4)
            elif isinstance(st1, Implication) and isinstance(st2, Equivalence):
                content = Implication.make(t1, t4, st1.get_temporal_order())
            else:
                return
        elif t2.equals(t4):
            if isinstance(st1, Inheritance) and isinstance(st2, Similarity):
                content = Inheritance.make(t1, t3)
            elif isinstance(st1, Implication) and isinstance(st2, Equivalence):
                content = Implication.make(t1, t3, st1.get_temporal_order())
            else:
                return
        else:
            return

        truth = TruthFunctions.reduce_conjunction(sym.truth, asym.truth, nal.nar.narParameters)
        budget = BudgetFunctions.forward(truth, nal)

        nal.double_premise_task(content, truth, budget, False, False)

    @staticmethod
    def conversion(nal: DerivationContext):
        """
        从反向的继承/蕴含产生继承/蕴含。
        <br>
        {<P --> S>} |- <S --> P>

        参数:
            nal: 派生上下文
        """
        truth = TruthFunctions.conversion(nal.get_current_belief().truth, nal.nar.narParameters)
        budget = BudgetFunctions.forward(truth, nal)

        LocalRules.converted_judgment(truth, budget, nal)

    @staticmethod
    def convert_relation(nal: DerivationContext):
        """
        在继承/蕴含和相似/等价之间切换。
        <br>
        {<S --> P>} |- <S <-> P> {<S <-> P>} |- <S --> P>

        参数:
            nal: 派生上下文
        """
        truth = nal.get_current_belief().truth

        if nal.get_current_task().get_term().is_commutative():
            truth = TruthFunctions.abduction(truth, 1.0, nal.nar.narParameters)
        else:
            truth = TruthFunctions.deduction(truth, 1.0, nal.nar.narParameters)

        budget = BudgetFunctions.forward(truth, nal)

        LocalRules.converted_judgment(truth, budget, nal)

    @staticmethod
    def converted_judgment(new_truth: TruthValue, new_budget: BudgetValue, nal: DerivationContext):
        """
        将判断转换为不同的关系。

        参数:
            new_truth: 新任务的真值
            new_budget: 新任务的预算值
            nal: 派生上下文
        """
        content = nal.get_current_task().get_term()
        belief_content = nal.get_current_belief().term
        order = TemporalRules.reverse_order(belief_content.get_temporal_order())

        subj_t = content.get_subject()
        pred_t = content.get_predicate()
        subj_b = belief_content.get_subject()
        pred_b = belief_content.get_predicate()

        if subj_t.has_var_query():
            other_term = pred_b if pred_t.equals(subj_b) else subj_b
            content = Statement.make(content.__class__, other_term, pred_t, order)

        if pred_t.has_var_query():
            other_term = pred_b if subj_t.equals(subj_b) else subj_b
            content = Statement.make(content.__class__, subj_t, other_term, order)

        if content is None:
            return

        nal.single_premise_task(content, nal.get_current_task().sentence.punctuation, new_truth, new_budget)
